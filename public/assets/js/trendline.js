var pluginTrendlineLinear={beforeDraw:function(e){var s=e.scales["y-axis-0"],n=e.chart.ctx;e.data.datasets.forEach(function(t,i){t.trendlineLinear&&addFitter(e.getDatasetMeta(i),n,t,s)}),n.setLineDash([])}};function addFitter(t,i,e,s){var n=e.trendlineLinear.style||e.borderColor,r=e.trendlineLinear.width||e.borderWidth,a=e.trendlineLinear.lineStyle||"solid";n=void 0!==n?n:"rgba(169,169,169, .6)",r=void 0!==r?r:3;var o=e.data.length-1,u=t.data[0]._model.x,h=t.data[o]._model.x,d=new LineFitter;e.data.forEach(function(t,i){d.add(i,t)}),i.lineWidth=r,"dotted"===a&&i.setLineDash([2,3]),i.beginPath(),i.moveTo(u,s.getPixelForValue(d.project(0))),i.lineTo(h,s.getPixelForValue(d.project(o))),i.strokeStyle=n,i.stroke()}function LineFitter(){this.count=0,this.sumX=0,this.sumX2=0,this.sumXY=0,this.sumY=0}Chart.plugins.register(pluginTrendlineLinear),LineFitter.prototype={add:function(t,i){this.count++,this.sumX+=t,this.sumX2+=t*t,this.sumXY+=t*i,this.sumY+=i},project:function(t){var i=this.count*this.sumX2-this.sumX*this.sumX;return(this.sumX2*this.sumY-this.sumX*this.sumXY)/i+t*((this.count*this.sumXY-this.sumX*this.sumY)/i)}};