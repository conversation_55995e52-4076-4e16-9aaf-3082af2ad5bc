function handleQuestionRadioChange() {
    window.questioner.refreshQuestions(this);
}

class Questions {
    constructor(fitment_keys) {
        this.fitment_keys = fitment_keys;
        this.setCounterman();


    }

    setCounterman() {
        let parts = search_result.parts;
        let countermans = [];
        let ii = 0;
        for (const i in parts) {
            countermans[ii] = [];
            for (const j in parts[i].questions) {
                if (countermans[ii][parts[i].questions[j]['Name']]===undefined) {
                    countermans[ii][parts[i].questions[j]['Name']] = [parts[i].questions[j]['Id']];
                } else {
                    countermans[ii][parts[i].questions[j]['Name']].push(parts[i].questions[j]['Id']);
                }

            }
            ii++;
        }

        this.counterman = countermans;

    }

    showPopup() {
        $('#questioner').modal({'show': true});
        let bb = this;
        $('#questioner input[type=radio]').on('change', handleQuestionRadioChange);
        $('.questioner_reset').on('click', function () {
            bb.reset();
        });
    }

    refreshQuestions(element) {
        this.setCounterman();
        $('.questioner_submit').attr('disabled', true);
        $('#questioner input[type=radio]').attr("checked", false);
        $('#questioner input[type=radio]').parent().removeClass('active');
        $(element).attr("checked", true);
        $(element).parent().addClass('active');

        let current_question = $(element).attr('name');

        let current_question_value = parseInt($(element).val());
        for (const i in this.counterman) {
            let todel = false;
            for (const j in this.counterman[i]) {
                if (this.counterman[i][current_question]) {
                    if (this.counterman[i][current_question].indexOf(current_question_value) === -1) {
                        todel = true;
                    }
                }

            }
            if (todel == true) {

                delete this.counterman[i];
            }
        }
        let submit_enable = true;
        let extra_fitments = [];

        for (const i in this.counterman) {
            for (const j in this.counterman[i]) {
                if (window.questioner.fitment_keys.indexOf(j) !== -1) {

                    if(this.counterman[i][j].length==1) {
                        if (!extra_fitments[j]) {
                            extra_fitments[j] = [];
                        }
                        if (extra_fitments[j].indexOf(this.counterman[i][j][0]) === -1) {
                            extra_fitments[j].push(this.counterman[i][j][0]);
                        }

                    }

                }
                for (const k in this.counterman[i][j]) {
                    if ($('#questioner input[name=' + j + '][value=' + this.counterman[i][j][k] + ']')) {
                        $('#questioner input[name=' + j + '][value=' + this.counterman[i][j][k] + ']').attr('checked', true);
                        $('#questioner input[name=' + j + '][value=' + this.counterman[i][j][k] + ']').parent().addClass('active');
                    }
                }

            }
        }
        for(const i in this.fitment_keys) {
            if( $('#questioner input[name=' + this.fitment_keys[i] + ']:not(:disabled)').length==1) {
                $('#questioner input[name=' + this.fitment_keys[i] + ']:not(:disabled)').attr('checked', true);
                $('#questioner input[name=' + this.fitment_keys[i] + ']:not(:disabled)').parent().addClass('active');


            }
        }
        console.log(this.fitment_keys);
        for(const i in this.fitment_keys) {
            if ($('#questioner input[name=' + this.fitment_keys[i] + ']:checked').length==0) {

                submit_enable = false;
            }
        }

        if (submit_enable == true) {
            $('.questioner_submit').attr('disabled', false);
            this.add_extras = "?";
            if (location.href.includes('?') == true) {
                this.add_extras = "&";
            }
            if(typeof mygarage !=='undefined')
            {
                let vehicle = mygarage.getCurrent();
                for (const key in window.questioner.fitment_keys) {
                    this.add_extras = this.add_extras + window.questioner.fitment_keys[key] + '=' + $('#questioner input[name=' + window.questioner.fitment_keys[key] + ']:checked').val() + "&";
                    vehicle[window.questioner.fitment_keys[key]] = $('#questioner input[name=' + window.questioner.fitment_keys[key] + ']:checked').val();
                    vehicle['values'][window.questioner.fitment_keys[key]] = $('#questioner input[name=' + window.questioner.fitment_keys[key] + ']:checked').attr('data-label');
                }

                let questioner = this;
                $('.questioner_submit').on('click', function () {
                    mygarage.garage[mygarage.getCurrentKey()] =  vehicle;

                    mygarage.save();
                    mygarage.setCurrentCookie(vehicle);
                    questioner.submitCallback();
                    return false;
                });
            }else{
                for (const key in window.questioner.fitment_keys) {
                        this.add_extras = this.add_extras + window.questioner.fitment_keys[key] + '=' + $('#questioner input[name=' + window.questioner.fitment_keys[key] + ']:checked').val() + "&";
                }
            }

        }


    }
    submitCallback()
    {
        location.href = location.href + this.add_extras;

        //
    }
    reset() {

        $('.questioner_submit').attr('disabled', true);
        $('#questioner input[type=radio]').off('change', handleQuestionRadioChange);
        $('#questioner input[type=radio]').attr("disabled", false);
        $('#questioner input[type=radio]').attr("checked", false);
        $('#questioner input[type=radio]').parent().removeClass('disabled');
        $('#questioner input[type=radio]').parent().removeClass('active');
        this.setCounterman();
        $('#questioner input[type=radio]').on('change', handleQuestionRadioChange);
    }

    onSuccces() {
        return true;
    }

    onFailure() {
        return false;
    }
}