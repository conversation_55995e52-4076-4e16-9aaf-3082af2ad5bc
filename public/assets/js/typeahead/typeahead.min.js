
!function(t,e){"function"==typeof define&&define.amd?define("bloodhound",["jquery"],function(n){return t.Bloodhound=e(n)}):"object"==typeof exports?module.exports=e(require("jquery")):t.Bloodhound=e(jQuery)}(this,function(t){var e=function(){"use strict";return{isMsie:function(){return!!/(msie|trident)/i.test(navigator.userAgent)&&navigator.userAgent.match(/(msie |rv:)(\d+(.\d+)?)/i)[2]},isBlankString:function(t){return!t||/^\s*$/.test(t)},escapeRegExChars:function(t){return t.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")},isString:function(t){return"string"==typeof t},isNumber:function(t){return"number"==typeof t},isArray:t.isArray,isFunction:t.isFunction,isObject:t.isPlainObject,isUndefined:function(t){return void 0===t},isElement:function(t){return!(!t||1!==t.nodeType)},isJQuery:function(e){return e instanceof t},toStr:function(t){return e.isUndefined(t)||null===t?"":t+""},bind:t.proxy,each:function(e,n){t.each(e,function(t,e){return n(e,t)})},map:t.map,filter:t.grep,every:function(e,n){var i=!0;return e?(t.each(e,function(t,r){if(!(i=n.call(null,r,t,e)))return!1}),!!i):i},some:function(e,n){var i=!1;return e?(t.each(e,function(t,r){if(i=n.call(null,r,t,e))return!1}),!!i):i},mixin:t.extend,identity:function(t){return t},clone:function(e){return t.extend(!0,{},e)},getIdGenerator:function(){var t=0;return function(){return t++}},templatify:function(e){return t.isFunction(e)?e:function(){return String(e)}},defer:function(t){setTimeout(t,0)},debounce:function(t,e,n){var i,r;return function(){var s,o,u=this,a=arguments;return s=function(){i=null,n||(r=t.apply(u,a))},o=n&&!i,clearTimeout(i),i=setTimeout(s,e),o&&(r=t.apply(u,a)),r}},throttle:function(t,e){var n,i,r,s,o,u;return o=0,u=function(){o=new Date,r=null,s=t.apply(n,i)},function(){var a=new Date,c=e-(a-o);return n=this,i=arguments,c<=0?(clearTimeout(r),r=null,o=a,s=t.apply(n,i)):r||(r=setTimeout(u,c)),s}},stringify:function(t){return e.isString(t)?t:JSON.stringify(t)},noop:function(){}}}(),n="0.11.1",i=function(){"use strict";return{nonword:n,whitespace:t,obj:{nonword:i(n),whitespace:i(t)}};function t(t){return(t=e.toStr(t))?t.split(/\s+/):[]}function n(t){return(t=e.toStr(t))?t.split(/\W+/):[]}function i(t){return function(n){return n=e.isArray(n)?n:[].slice.call(arguments,0),function(i){var r=[];return e.each(n,function(n){r=r.concat(t(e.toStr(i[n])))}),r}}}}(),r=function(){"use strict";function n(n){this.maxSize=e.isNumber(n)?n:100,this.reset(),this.maxSize<=0&&(this.set=this.get=t.noop)}function i(){this.head=this.tail=null}function r(t,e){this.key=t,this.val=e,this.prev=this.next=null}return e.mixin(n.prototype,{set:function(t,e){var n,i=this.list.tail;this.size>=this.maxSize&&(this.list.remove(i),delete this.hash[i.key],this.size--),(n=this.hash[t])?(n.val=e,this.list.moveToFront(n)):(n=new r(t,e),this.list.add(n),this.hash[t]=n,this.size++)},get:function(t){var e=this.hash[t];if(e)return this.list.moveToFront(e),e.val},reset:function(){this.size=0,this.hash={},this.list=new i}}),e.mixin(i.prototype,{add:function(t){this.head&&(t.next=this.head,this.head.prev=t),this.head=t,this.tail=this.tail||t},remove:function(t){t.prev?t.prev.next=t.next:this.head=t.next,t.next?t.next.prev=t.prev:this.tail=t.prev},moveToFront:function(t){this.remove(t),this.add(t)}}),n}(),s=function(){"use strict";var n;try{(n=window.localStorage).setItem("~~~","!"),n.removeItem("~~~")}catch(t){n=null}function i(t,i){this.prefix=["__",t,"__"].join(""),this.ttlKey="__ttl__",this.keyMatcher=new RegExp("^"+e.escapeRegExChars(this.prefix)),this.ls=i||n,!this.ls&&this._noop()}return e.mixin(i.prototype,{_prefix:function(t){return this.prefix+t},_ttlKey:function(t){return this._prefix(t)+this.ttlKey},_noop:function(){this.get=this.set=this.remove=this.clear=this.isExpired=e.noop},_safeSet:function(t,e){try{this.ls.setItem(t,e)}catch(t){"QuotaExceededError"===t.name&&(this.clear(),this._noop())}},get:function(t){return this.isExpired(t)&&this.remove(t),o(this.ls.getItem(this._prefix(t)))},set:function(t,n,i){return e.isNumber(i)?this._safeSet(this._ttlKey(t),s(r()+i)):this.ls.removeItem(this._ttlKey(t)),this._safeSet(this._prefix(t),s(n))},remove:function(t){return this.ls.removeItem(this._ttlKey(t)),this.ls.removeItem(this._prefix(t)),this},clear:function(){var t,e=function(t){var e,i,r=[],s=n.length;for(e=0;e<s;e++)(i=n.key(e)).match(t)&&r.push(i.replace(t,""));return r}(this.keyMatcher);for(t=e.length;t--;)this.remove(e[t]);return this},isExpired:function(t){var n=o(this.ls.getItem(this._ttlKey(t)));return!!(e.isNumber(n)&&r()>n)}}),i;function r(){return(new Date).getTime()}function s(t){return JSON.stringify(e.isUndefined(t)?null:t)}function o(e){return t.parseJSON(e)}}(),o=function(){"use strict";var n=0,i={},s=6,o=new r(10);function u(t){t=t||{},this.cancelled=!1,this.lastReq=null,this._send=t.transport,this._get=t.limiter?t.limiter(this._get):this._get,this._cache=!1===t.cache?new r(0):o}return u.setMaxPendingRequests=function(t){s=t},u.resetCache=function(){o.reset()},e.mixin(u.prototype,{_fingerprint:function(e){return(e=e||{}).url+e.type+t.param(e.data||{})},_get:function(t,e){var r,o,u=this;function a(t){e(null,t),u._cache.set(r,t)}function c(){e(!0)}r=this._fingerprint(t),this.cancelled||r!==this.lastReq||((o=i[r])?o.done(a).fail(c):n<s?(n++,i[r]=this._send(t).done(a).fail(c).always(function(){n--,delete i[r],u.onDeckRequestArgs&&(u._get.apply(u,u.onDeckRequestArgs),u.onDeckRequestArgs=null)})):this.onDeckRequestArgs=[].slice.call(arguments,0))},get:function(n,i){var r,s;i=i||t.noop,n=e.isString(n)?{url:n}:n||{},s=this._fingerprint(n),this.cancelled=!1,this.lastReq=s,(r=this._cache.get(s))?i(null,r):this._get(n,i)},cancel:function(){this.cancelled=!0}}),u}(),u=window.SearchIndex=function(){"use strict";var n="c",i="i";function r(n){(n=n||{}).datumTokenizer&&n.queryTokenizer||t.error("datumTokenizer and queryTokenizer are both required"),this.identify=n.identify||e.stringify,this.datumTokenizer=n.datumTokenizer,this.queryTokenizer=n.queryTokenizer,this.reset()}return e.mixin(r.prototype,{bootstrap:function(t){this.datums=t.datums,this.trie=t.trie},add:function(t){var r=this;t=e.isArray(t)?t:[t],e.each(t,function(t){var u,a;r.datums[u=r.identify(t)]=t,a=s(r.datumTokenizer(t)),e.each(a,function(t){var e,s,a;for(e=r.trie,s=t.split("");a=s.shift();)(e=e[n][a]||(e[n][a]=o()))[i].push(u)})})},get:function(t){var n=this;return e.map(t,function(t){return n.datums[t]})},search:function(t){var r,o,u=this;return r=s(this.queryTokenizer(t)),e.each(r,function(t){var e,r,s,a;if(o&&0===o.length)return!1;for(e=u.trie,r=t.split("");e&&(s=r.shift());)e=e[n][s];if(!e||0!==r.length)return o=[],!1;a=e[i].slice(0),o=o?function(t,e){var n=0,i=0,r=[];t=t.sort(),e=e.sort();var s=t.length,o=e.length;for(;n<s&&i<o;)t[n]<e[i]?n++:t[n]>e[i]?i++:(r.push(t[n]),n++,i++);return r}(o,a):a}),o?e.map(function(t){for(var e={},n=[],i=0,r=t.length;i<r;i++)e[t[i]]||(e[t[i]]=!0,n.push(t[i]));return n}(o),function(t){return u.datums[t]}):[]},all:function(){var t=[];for(var e in this.datums)t.push(this.datums[e]);return t},reset:function(){this.datums={},this.trie=o()},serialize:function(){return{datums:this.datums,trie:this.trie}}}),r;function s(t){return t=e.filter(t,function(t){return!!t}),t=e.map(t,function(t){return t.toLowerCase()})}function o(){var t={};return t[i]=[],t[n]={},t}}(),a=function(){"use strict";var t;function n(t){this.url=t.url,this.ttl=t.ttl,this.cache=t.cache,this.prepare=t.prepare,this.transform=t.transform,this.transport=t.transport,this.thumbprint=t.thumbprint,this.storage=new s(t.cacheKey)}return t={data:"data",protocol:"protocol",thumbprint:"thumbprint"},e.mixin(n.prototype,{_settings:function(){return{url:this.url,type:"GET",dataType:"json"}},store:function(e){this.cache&&(this.storage.set(t.data,e,this.ttl),this.storage.set(t.protocol,location.protocol,this.ttl),this.storage.set(t.thumbprint,this.thumbprint,this.ttl))},fromCache:function(){var e,n={};return this.cache?(n.data=this.storage.get(t.data),n.protocol=this.storage.get(t.protocol),n.thumbprint=this.storage.get(t.thumbprint),e=n.thumbprint!==this.thumbprint||n.protocol!==location.protocol,n.data&&!e?n.data:null):null},fromNetwork:function(t){var e,n=this;t&&(e=this.prepare(this._settings()),this.transport(e).fail(function(){t(!0)}).done(function(e){t(null,n.transform(e))}))},clear:function(){return this.storage.clear(),this}}),n}(),c=function(){"use strict";function t(t){this.url=t.url,this.prepare=t.prepare,this.transform=t.transform,this.transport=new o({cache:t.cache,limiter:t.limiter,transport:t.transport})}return e.mixin(t.prototype,{_settings:function(){return{url:this.url,type:"GET",dataType:"json"}},get:function(t,e){var n,i=this;if(e)return t=t||"",n=this.prepare(t,this._settings()),this.transport.get(n,function(t,n){e(t?[]:i.transform(n))})},cancelLastRequest:function(){this.transport.cancel()}}),t}(),h=function(){"use strict";return function(r){var s,o;return s={initialize:!0,identify:e.stringify,datumTokenizer:null,queryTokenizer:null,sufficient:5,sorter:null,local:[],prefetch:null,remote:null},!(r=e.mixin(s,r||{})).datumTokenizer&&t.error("datumTokenizer is required"),!r.queryTokenizer&&t.error("queryTokenizer is required"),o=r.sorter,r.sorter=o?function(t){return t.sort(o)}:e.identity,r.local=e.isFunction(r.local)?r.local():r.local,r.prefetch=function(r){var s;if(!r)return null;return s={url:null,ttl:864e5,cache:!0,cacheKey:null,thumbprint:"",prepare:e.identity,transform:e.identity,transport:null},r=e.isString(r)?{url:r}:r,!(r=e.mixin(s,r)).url&&t.error("prefetch requires url to be set"),r.transform=r.filter||r.transform,r.cacheKey=r.cacheKey||r.url,r.thumbprint=n+r.thumbprint,r.transport=r.transport?i(r.transport):t.ajax,r}(r.prefetch),r.remote=function(n){var r;if(!n)return;return r={url:null,cache:!0,prepare:null,replace:null,wildcard:null,limiter:null,rateLimitBy:"debounce",rateLimitWait:300,transform:e.identity,transport:null},n=e.isString(n)?{url:n}:n,!(n=e.mixin(r,n)).url&&t.error("remote requires url to be set"),n.transform=n.filter||n.transform,n.prepare=function(t){var e,n,i;return e=t.prepare,n=t.replace,i=t.wildcard,e||(e=n?function(t,e){return e.url=n(e.url,t),e}:t.wildcard?function(t,e){return e.url=e.url.replace(i,encodeURIComponent(t)),e}:function(t,e){return e})}(n),n.limiter=function(t){var n,i,r;return n=t.limiter,i=t.rateLimitBy,r=t.rateLimitWait,n||(n=/^throttle$/i.test(i)?function(t){return function(n){return e.throttle(n,t)}}(r):function(t){return function(n){return e.debounce(n,t)}}(r)),n}(n),n.transport=n.transport?i(n.transport):t.ajax,delete n.replace,delete n.wildcard,delete n.rateLimitBy,delete n.rateLimitWait,n}(r.remote),r};function i(n){return function(i){var r=t.Deferred();return n(i,function(t){e.defer(function(){r.resolve(t)})},function(t){e.defer(function(){r.reject(t)})}),r}}}();return function(){"use strict";var n;function r(t){t=h(t),this.sorter=t.sorter,this.identify=t.identify,this.sufficient=t.sufficient,this.local=t.local,this.remote=t.remote?new c(t.remote):null,this.prefetch=t.prefetch?new a(t.prefetch):null,this.index=new u({identify:this.identify,datumTokenizer:t.datumTokenizer,queryTokenizer:t.queryTokenizer}),!1!==t.initialize&&this.initialize()}return n=window&&window.Bloodhound,r.noConflict=function(){return window&&(window.Bloodhound=n),r},r.tokenizers=i,e.mixin(r.prototype,{__ttAdapter:function(){var t=this;return this.remote?function(e,n,i){return t.search(e,n,i)}:function(e,n){return t.search(e,n)}},_loadPrefetch:function(){var e,n,i=this;return e=t.Deferred(),this.prefetch?(n=this.prefetch.fromCache())?(this.index.bootstrap(n),e.resolve()):this.prefetch.fromNetwork(function(t,n){if(t)return e.reject();i.add(n),i.prefetch.store(i.index.serialize()),e.resolve()}):e.resolve(),e.promise()},_initialize:function(){var t=this;return this.clear(),(this.initPromise=this._loadPrefetch()).done(function(){t.add(t.local)}),this.initPromise},initialize:function(t){return!this.initPromise||t?this._initialize():this.initPromise},add:function(t){return this.index.add(t),this},get:function(t){return t=e.isArray(t)?t:[].slice.call(arguments),this.index.get(t)},search:function(t,n,i){var r,s=this;return r=this.sorter(this.index.search(t)),n(this.remote?r.slice():r),this.remote&&r.length<this.sufficient?this.remote.get(t,function(t){var n=[];e.each(t,function(t){!e.some(r,function(e){return s.identify(t)===s.identify(e)})&&n.push(t)}),i&&i(n)}):this.remote&&this.remote.cancelLastRequest(),this},all:function(){return this.index.all()},clear:function(){return this.index.reset(),this},clearPrefetchCache:function(){return this.prefetch&&this.prefetch.clear(),this},clearRemoteCache:function(){return o.resetCache(),this},ttAdapter:function(){return this.__ttAdapter()}}),r}()}),function(t,e){"function"==typeof define&&define.amd?define("typeahead.js",["jquery"],function(t){return e(t)}):"object"==typeof exports?module.exports=e(require("jquery")):e(jQuery)}(0,function(t){var e=function(){"use strict";return{isMsie:function(){return!!/(msie|trident)/i.test(navigator.userAgent)&&navigator.userAgent.match(/(msie |rv:)(\d+(.\d+)?)/i)[2]},isBlankString:function(t){return!t||/^\s*$/.test(t)},escapeRegExChars:function(t){return t.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")},isString:function(t){return"string"==typeof t},isNumber:function(t){return"number"==typeof t},isArray:t.isArray,isFunction:t.isFunction,isObject:t.isPlainObject,isUndefined:function(t){return void 0===t},isElement:function(t){return!(!t||1!==t.nodeType)},isJQuery:function(e){return e instanceof t},toStr:function(t){return e.isUndefined(t)||null===t?"":t+""},bind:t.proxy,each:function(e,n){t.each(e,function(t,e){return n(e,t)})},map:t.map,filter:t.grep,every:function(e,n){var i=!0;return e?(t.each(e,function(t,r){if(!(i=n.call(null,r,t,e)))return!1}),!!i):i},some:function(e,n){var i=!1;return e?(t.each(e,function(t,r){if(i=n.call(null,r,t,e))return!1}),!!i):i},mixin:t.extend,identity:function(t){return t},clone:function(e){return t.extend(!0,{},e)},getIdGenerator:function(){var t=0;return function(){return t++}},templatify:function(e){return t.isFunction(e)?e:function(){return String(e)}},defer:function(t){setTimeout(t,0)},debounce:function(t,e,n){var i,r;return function(){var s,o,u=this,a=arguments;return s=function(){i=null,n||(r=t.apply(u,a))},o=n&&!i,clearTimeout(i),i=setTimeout(s,e),o&&(r=t.apply(u,a)),r}},throttle:function(t,e){var n,i,r,s,o,u;return o=0,u=function(){o=new Date,r=null,s=t.apply(n,i)},function(){var a=new Date,c=e-(a-o);return n=this,i=arguments,c<=0?(clearTimeout(r),r=null,o=a,s=t.apply(n,i)):r||(r=setTimeout(u,c)),s}},stringify:function(t){return e.isString(t)?t:JSON.stringify(t)},noop:function(){}}}(),n=function(){"use strict";var t={wrapper:"twitter-typeahead",input:"tt-input",hint:"tt-hint",menu:"tt-menu",dataset:"tt-dataset",suggestion:"tt-suggestion",selectable:"tt-selectable",empty:"tt-empty",open:"tt-open",cursor:"tt-cursor",highlight:"tt-highlight"};return function(r){var s,o;return o=e.mixin({},t,r),{css:(s={css:i(),classes:o,html:(u=o,{wrapper:'<span class="'+u.wrapper+'"></span>',menu:'<div class="'+u.menu+'"></div>'}),selectors:n(o)}).css,html:s.html,classes:s.classes,selectors:s.selectors,mixin:function(t){e.mixin(t,s)}};var u};function n(t){var n={};return e.each(t,function(t,e){n[e]="."+t}),n}function i(){var t={wrapper:{position:"relative",display:"inline-block"},hint:{position:"absolute",top:"0",left:"0",borderColor:"transparent",boxShadow:"none",opacity:"1"},input:{position:"relative",verticalAlign:"top",backgroundColor:"transparent"},inputWithNoHint:{position:"relative",verticalAlign:"top"},menu:{position:"absolute",top:"100%",left:"0",zIndex:"100",display:"none"},ltr:{left:"0",right:"auto"},rtl:{left:"auto",right:" 0"}};return e.isMsie()&&e.mixin(t.input,{backgroundImage:"url(data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)"}),t}}(),i=function(){"use strict";var n;function i(e){e&&e.el||t.error("EventBus initialized without el"),this.$el=t(e.el)}return"typeahead:",n={render:"rendered",cursorchange:"cursorchanged",select:"selected",autocomplete:"autocompleted"},e.mixin(i.prototype,{_trigger:function(e,n){var i;return i=t.Event("typeahead:"+e),(n=n||[]).unshift(i),this.$el.trigger.apply(this.$el,n),i},before:function(t){var e;return e=[].slice.call(arguments,1),this._trigger("before"+t,e).isDefaultPrevented()},trigger:function(t){var e;this._trigger(t,[].slice.call(arguments,1)),(e=n[t])&&this._trigger(e,[].slice.call(arguments,1))}}),i}(),r=function(){"use strict";var t=/\s+/,e=function(){var t;t=window.setImmediate?function(t){setImmediate(function(){t()})}:function(t){setTimeout(function(){t()},0)};return t}();return{onSync:function(t,e,i){return n.call(this,"sync",t,e,i)},onAsync:function(t,e,i){return n.call(this,"async",t,e,i)},off:function(e){var n;if(!this._callbacks)return this;e=e.split(t);for(;n=e.shift();)delete this._callbacks[n];return this},trigger:function(n){var r,s,o,u,a;if(!this._callbacks)return this;n=n.split(t),o=[].slice.call(arguments,1);for(;(r=n.shift())&&(s=this._callbacks[r]);)u=i(s.sync,this,[r].concat(o)),a=i(s.async,this,[r].concat(o)),u()&&e(a);return this}};function n(e,n,i,r){var s;if(!i)return this;for(n=n.split(t),i=r?function(t,e){return t.bind?t.bind(e):function(){t.apply(e,[].slice.call(arguments,0))}}(i,r):i,this._callbacks=this._callbacks||{};s=n.shift();)this._callbacks[s]=this._callbacks[s]||{sync:[],async:[]},this._callbacks[s][e].push(i);return this}function i(t,e,n){return function(){for(var i,r=0,s=t.length;!i&&r<s;r+=1)i=!1===t[r].apply(e,n);return!i}}}(),s=function(t){"use strict";var n={node:null,pattern:null,tagName:"strong",className:null,wordsOnly:!1,caseSensitive:!1};return function(i){var r;(i=e.mixin({},n,i)).node&&i.pattern&&(i.pattern=e.isArray(i.pattern)?i.pattern:[i.pattern],r=function(t,n,i){for(var r,s=[],o=0,u=t.length;o<u;o++)s.push(e.escapeRegExChars(t[o]));return r=i?"\\b("+s.join("|")+")\\b":"("+s.join("|")+")",n?new RegExp(r):new RegExp(r,"i")}(i.pattern,i.caseSensitive,i.wordsOnly),function t(e,n){var i;for(var r=0;r<e.childNodes.length;r++)3===(i=e.childNodes[r]).nodeType?r+=n(i)?1:0:t(i,n)}(i.node,function(e){var n,s,o;(n=r.exec(e.data))&&(o=t.createElement(i.tagName),i.className&&(o.className=i.className),(s=e.splitText(n.index)).splitText(n[0].length),o.appendChild(s.cloneNode(!0)),e.parentNode.replaceChild(o,s));return!!n}))}}(window.document),o=function(){"use strict";var n;function i(n,i){var r;(n=n||{}).input||t.error("input is missing"),i.mixin(this),this.$hint=t(n.hint),this.$input=t(n.input),this.query=this.$input.val(),this.queryWhenFocused=this.hasFocus()?this.query:null,this.$overflowHelper=(r=this.$input,t('<pre aria-hidden="true"></pre>').css({position:"absolute",visibility:"hidden",whiteSpace:"pre",fontFamily:r.css("font-family"),fontSize:r.css("font-size"),fontStyle:r.css("font-style"),fontVariant:r.css("font-variant"),fontWeight:r.css("font-weight"),wordSpacing:r.css("word-spacing"),letterSpacing:r.css("letter-spacing"),textIndent:r.css("text-indent"),textRendering:r.css("text-rendering"),textTransform:r.css("text-transform")}).insertAfter(r)),this._checkLanguageDirection(),0===this.$hint.length&&(this.setHint=this.getHint=this.clearHint=this.clearHintIfInvalid=e.noop)}return n={9:"tab",27:"esc",37:"left",39:"right",13:"enter",38:"up",40:"down"},i.normalizeQuery=function(t){return e.toStr(t).replace(/^\s*/g,"").replace(/\s{2,}/g," ")},e.mixin(i.prototype,r,{_onBlur:function(){this.resetInputValue(),this.trigger("blurred")},_onFocus:function(){this.queryWhenFocused=this.query,this.trigger("focused")},_onKeydown:function(t){var e=n[t.which||t.keyCode];this._managePreventDefault(e,t),e&&this._shouldTrigger(e,t)&&this.trigger(e+"Keyed",t)},_onInput:function(){this._setQuery(this.getInputValue()),this.clearHintIfInvalid(),this._checkLanguageDirection()},_managePreventDefault:function(t,e){var n;switch(t){case"up":case"down":n=!s(e);break;default:n=!1}n&&e.preventDefault()},_shouldTrigger:function(t,e){var n;switch(t){case"tab":n=!s(e);break;default:n=!0}return n},_checkLanguageDirection:function(){var t=(this.$input.css("direction")||"ltr").toLowerCase();this.dir!==t&&(this.dir=t,this.$hint.attr("dir",t),this.trigger("langDirChanged",t))},_setQuery:function(t,e){var n,r,s,o;s=t,o=this.query,r=!!(n=i.normalizeQuery(s)===i.normalizeQuery(o))&&this.query.length!==t.length,this.query=t,e||n?!e&&r&&this.trigger("whitespaceChanged",this.query):this.trigger("queryChanged",this.query)},bind:function(){var t,i,r,s,o=this;return t=e.bind(this._onBlur,this),i=e.bind(this._onFocus,this),r=e.bind(this._onKeydown,this),s=e.bind(this._onInput,this),this.$input.on("blur.tt",t).on("focus.tt",i).on("keydown.tt",r),!e.isMsie()||e.isMsie()>9?this.$input.on("input.tt",s):this.$input.on("keydown.tt keypress.tt cut.tt paste.tt",function(t){n[t.which||t.keyCode]||e.defer(e.bind(o._onInput,o,t))}),this},focus:function(){this.$input.focus()},blur:function(){this.$input.blur()},getLangDir:function(){return this.dir},getQuery:function(){return this.query||""},setQuery:function(t,e){this.setInputValue(t),this._setQuery(t,e)},hasQueryChangedSinceLastFocus:function(){return this.query!==this.queryWhenFocused},getInputValue:function(){return this.$input.val()},setInputValue:function(t){this.$input.val(t),this.clearHintIfInvalid(),this._checkLanguageDirection()},resetInputValue:function(){this.setInputValue(this.query)},getHint:function(){return this.$hint.val()},setHint:function(t){this.$hint.val(t)},clearHint:function(){this.setHint("")},clearHintIfInvalid:function(){var t,e,n;n=(t=this.getInputValue())!==(e=this.getHint())&&0===e.indexOf(t),!(""!==t&&n&&!this.hasOverflow())&&this.clearHint()},hasFocus:function(){return this.$input.is(":focus")},hasOverflow:function(){var t=this.$input.width()-2;return this.$overflowHelper.text(this.getInputValue()),this.$overflowHelper.width()>=t},isCursorAtEnd:function(){var t,n,i;return t=this.$input.val().length,n=this.$input[0].selectionStart,e.isNumber(n)?n===t:!document.selection||((i=document.selection.createRange()).moveStart("character",-t),t===i.text.length)},destroy:function(){this.$hint.off(".tt"),this.$input.off(".tt"),this.$overflowHelper.remove(),this.$hint=this.$input=this.$overflowHelper=t("<div>")}}),i;function s(t){return t.altKey||t.ctrlKey||t.metaKey||t.shiftKey}}(),u=function(){"use strict";var n,i;function o(n,r){var s;(n=n||{}).templates=n.templates||{},n.templates.notFound=n.templates.notFound||n.templates.empty,n.source||t.error("missing source"),n.node||t.error("missing node"),n.name&&(s=n.name,!/^[_a-zA-Z0-9-]+$/.test(s))&&t.error("invalid dataset name: "+n.name),r.mixin(this),this.highlight=!!n.highlight,this.name=n.name||i(),this.limit=n.limit||5,this.displayFn=function(t){return t=t||e.stringify,e.isFunction(t)?t:function(e){return e[t]}}(n.display||n.displayKey),this.templates=function(n,i){return{notFound:n.notFound&&e.templatify(n.notFound),pending:n.pending&&e.templatify(n.pending),header:n.header&&e.templatify(n.header),footer:n.footer&&e.templatify(n.footer),suggestion:n.suggestion||function(e){return t("<div>").text(i(e))}}}(n.templates,this.displayFn),this.source=n.source.__ttAdapter?n.source.__ttAdapter():n.source,this.async=e.isUndefined(n.async)?this.source.length>2:!!n.async,this._resetLastSuggestion(),this.$el=t(n.node).addClass(this.classes.dataset).addClass(this.classes.dataset+"-"+this.name)}return n={val:"tt-selectable-display",obj:"tt-selectable-object"},i=e.getIdGenerator(),o.extractData=function(e){var i=t(e);return i.data(n.obj)?{val:i.data(n.val)||"",obj:i.data(n.obj)||null}:null},e.mixin(o.prototype,r,{_overwrite:function(t,e){(e=e||[]).length?this._renderSuggestions(t,e):this.async&&this.templates.pending?this._renderPending(t):!this.async&&this.templates.notFound?this._renderNotFound(t):this._empty(),this.trigger("rendered",this.name,e,!1)},_append:function(t,e){(e=e||[]).length&&this.$lastSuggestion.length?this._appendSuggestions(t,e):e.length?this._renderSuggestions(t,e):!this.$lastSuggestion.length&&this.templates.notFound&&this._renderNotFound(t),this.trigger("rendered",this.name,e,!0)},_renderSuggestions:function(t,e){var n;n=this._getSuggestionsFragment(t,e),this.$lastSuggestion=n.children().last(),this.$el.html(n).prepend(this._getHeader(t,e)).append(this._getFooter(t,e))},_appendSuggestions:function(t,e){var n,i;i=(n=this._getSuggestionsFragment(t,e)).children().last(),this.$lastSuggestion.after(n),this.$lastSuggestion=i},_renderPending:function(t){var e=this.templates.pending;this._resetLastSuggestion(),e&&this.$el.html(e({query:t,dataset:this.name}))},_renderNotFound:function(t){var e=this.templates.notFound;this._resetLastSuggestion(),e&&this.$el.html(e({query:t,dataset:this.name}))},_empty:function(){this.$el.empty(),this._resetLastSuggestion()},_getSuggestionsFragment:function(i,r){var o,u=this;return o=document.createDocumentFragment(),e.each(r,function(e){var r,s;s=u._injectQuery(i,e),r=t(u.templates.suggestion(s)).data(n.obj,e).data(n.val,u.displayFn(e)).addClass(u.classes.suggestion+" "+u.classes.selectable),o.appendChild(r[0])}),this.highlight&&s({className:this.classes.highlight,node:o,pattern:i}),t(o)},_getFooter:function(t,e){return this.templates.footer?this.templates.footer({query:t,suggestions:e,dataset:this.name}):null},_getHeader:function(t,e){return this.templates.header?this.templates.header({query:t,suggestions:e,dataset:this.name}):null},_resetLastSuggestion:function(){this.$lastSuggestion=t()},_injectQuery:function(t,n){return e.isObject(n)?e.mixin({_query:t},n):n},update:function(e){var n=this,i=!1,r=!1,s=0;function o(t){r||(r=!0,t=(t||[]).slice(0,n.limit),s=t.length,n._overwrite(e,t),s<n.limit&&n.async&&n.trigger("asyncRequested",e))}this.cancel(),this.cancel=function(){i=!0,n.cancel=t.noop,n.async&&n.trigger("asyncCanceled",e)},this.source(e,o,function(r){r=r||[],!i&&s<n.limit&&(n.cancel=t.noop,n._append(e,r.slice(0,n.limit-s)),s+=r.length,n.async&&n.trigger("asyncReceived",e))}),!r&&o([])},cancel:t.noop,clear:function(){this._empty(),this.cancel(),this.trigger("cleared")},isEmpty:function(){return this.$el.is(":empty")},destroy:function(){this.$el=t("<div>")}}),o}(),a=function(){"use strict";function n(n,i){var r=this;(n=n||{}).node||t.error("node is required"),i.mixin(this),this.$node=t(n.node),this.query=null,this.datasets=e.map(n.datasets,function(e){var n=r.$node.find(e.node).first();return e.node=n.length?n:t("<div>").appendTo(r.$node),new u(e,i)})}return e.mixin(n.prototype,r,{_onSelectableClick:function(e){this.trigger("selectableClicked",t(e.currentTarget))},_onRendered:function(t,e,n,i){this.$node.toggleClass(this.classes.empty,this._allDatasetsEmpty()),this.trigger("datasetRendered",e,n,i)},_onCleared:function(){this.$node.toggleClass(this.classes.empty,this._allDatasetsEmpty()),this.trigger("datasetCleared")},_propagate:function(){this.trigger.apply(this,arguments)},_allDatasetsEmpty:function(){return e.every(this.datasets,function(t){return t.isEmpty()})},_getSelectables:function(){return this.$node.find(this.selectors.selectable)},_removeCursor:function(){var t=this.getActiveSelectable();t&&t.removeClass(this.classes.cursor)},_ensureVisible:function(t){var e,n,i,r;n=(e=t.position().top)+t.outerHeight(!0),i=this.$node.scrollTop(),r=this.$node.height()+parseInt(this.$node.css("paddingTop"),10)+parseInt(this.$node.css("paddingBottom"),10),e<0?this.$node.scrollTop(i+e):r<n&&this.$node.scrollTop(i+(n-r))},bind:function(){var t,n=this;return t=e.bind(this._onSelectableClick,this),this.$node.on("click.tt",this.selectors.selectable,t),e.each(this.datasets,function(t){t.onSync("asyncRequested",n._propagate,n).onSync("asyncCanceled",n._propagate,n).onSync("asyncReceived",n._propagate,n).onSync("rendered",n._onRendered,n).onSync("cleared",n._onCleared,n)}),this},isOpen:function(){return this.$node.hasClass(this.classes.open)},open:function(){this.$node.addClass(this.classes.open)},close:function(){this.$node.removeClass(this.classes.open),this._removeCursor()},setLanguageDirection:function(t){this.$node.attr("dir",t)},selectableRelativeToCursor:function(t){var e,n,i;return n=this.getActiveSelectable(),e=this._getSelectables(),-1===(i=(i=((i=(n?e.index(n):-1)+t)+1)%(e.length+1)-1)<-1?e.length-1:i)?null:e.eq(i)},setCursor:function(t){this._removeCursor(),(t=t&&t.first())&&(t.addClass(this.classes.cursor),this._ensureVisible(t))},getSelectableData:function(t){return t&&t.length?u.extractData(t):null},getActiveSelectable:function(){var t=this._getSelectables().filter(this.selectors.cursor).first();return t.length?t:null},getTopSelectable:function(){var t=this._getSelectables().first();return t.length?t:null},update:function(t){var n=t!==this.query;return n&&(this.query=t,e.each(this.datasets,function(e){e.update(t)})),n},empty:function(){e.each(this.datasets,function(t){t.clear()}),this.query=null,this.$node.addClass(this.classes.empty)},destroy:function(){this.$node.off(".tt"),this.$node=t("<div>"),e.each(this.datasets,function(t){t.destroy()})}}),n}(),c=function(){"use strict";var t=a.prototype;function n(){a.apply(this,[].slice.call(arguments,0))}return e.mixin(n.prototype,a.prototype,{open:function(){return!this._allDatasetsEmpty()&&this._show(),t.open.apply(this,[].slice.call(arguments,0))},close:function(){return this._hide(),t.close.apply(this,[].slice.call(arguments,0))},_onRendered:function(){return this._allDatasetsEmpty()?this._hide():this.isOpen()&&this._show(),t._onRendered.apply(this,[].slice.call(arguments,0))},_onCleared:function(){return this._allDatasetsEmpty()?this._hide():this.isOpen()&&this._show(),t._onCleared.apply(this,[].slice.call(arguments,0))},setLanguageDirection:function(e){return this.$node.css("ltr"===e?this.css.ltr:this.css.rtl),t.setLanguageDirection.apply(this,[].slice.call(arguments,0))},_hide:function(){this.$node.hide()},_show:function(){this.$node.css("display","block")}}),n}(),h=function(){"use strict";function n(n,r){var s,o,u,a,c,h,l,f,d,p,g;(n=n||{}).input||t.error("missing input"),n.menu||t.error("missing menu"),n.eventBus||t.error("missing event bus"),r.mixin(this),this.eventBus=n.eventBus,this.minLength=e.isNumber(n.minLength)?n.minLength:1,this.input=n.input,this.menu=n.menu,this.enabled=!0,this.active=!1,this.input.hasFocus()&&this.activate(),this.dir=this.input.getLangDir(),this._hacks(),this.menu.bind().onSync("selectableClicked",this._onSelectableClicked,this).onSync("asyncRequested",this._onAsyncRequested,this).onSync("asyncCanceled",this._onAsyncCanceled,this).onSync("asyncReceived",this._onAsyncReceived,this).onSync("datasetRendered",this._onDatasetRendered,this).onSync("datasetCleared",this._onDatasetCleared,this),s=i(this,"activate","open","_onFocused"),o=i(this,"deactivate","_onBlurred"),u=i(this,"isActive","isOpen","_onEnterKeyed"),a=i(this,"isActive","isOpen","_onTabKeyed"),c=i(this,"isActive","_onEscKeyed"),h=i(this,"isActive","open","_onUpKeyed"),l=i(this,"isActive","open","_onDownKeyed"),f=i(this,"isActive","isOpen","_onLeftKeyed"),d=i(this,"isActive","isOpen","_onRightKeyed"),p=i(this,"_openIfActive","_onQueryChanged"),g=i(this,"_openIfActive","_onWhitespaceChanged"),this.input.bind().onSync("focused",s,this).onSync("blurred",o,this).onSync("enterKeyed",u,this).onSync("tabKeyed",a,this).onSync("escKeyed",c,this).onSync("upKeyed",h,this).onSync("downKeyed",l,this).onSync("leftKeyed",f,this).onSync("rightKeyed",d,this).onSync("queryChanged",p,this).onSync("whitespaceChanged",g,this).onSync("langDirChanged",this._onLangDirChanged,this)}return e.mixin(n.prototype,{_hacks:function(){var n,i;n=this.input.$input||t("<div>"),i=this.menu.$node||t("<div>"),n.on("blur.tt",function(t){var r,s,o;r=document.activeElement,s=i.is(r),o=i.has(r).length>0,e.isMsie()&&(s||o)&&(t.preventDefault(),t.stopImmediatePropagation(),e.defer(function(){n.focus()}))}),i.on("mousedown.tt",function(t){t.preventDefault()})},_onSelectableClicked:function(t,e){this.select(e)},_onDatasetCleared:function(){this._updateHint()},_onDatasetRendered:function(t,e,n,i){this._updateHint(),this.eventBus.trigger("render",n,i,e)},_onAsyncRequested:function(t,e,n){this.eventBus.trigger("asyncrequest",n,e)},_onAsyncCanceled:function(t,e,n){this.eventBus.trigger("asynccancel",n,e)},_onAsyncReceived:function(t,e,n){this.eventBus.trigger("asyncreceive",n,e)},_onFocused:function(){this._minLengthMet()&&this.menu.update(this.input.getQuery())},_onBlurred:function(){this.input.hasQueryChangedSinceLastFocus()&&this.eventBus.trigger("change",this.input.getQuery())},_onEnterKeyed:function(t,e){var n;(n=this.menu.getActiveSelectable())&&this.select(n)&&e.preventDefault()},_onTabKeyed:function(t,e){var n;(n=this.menu.getActiveSelectable())?this.select(n)&&e.preventDefault():(n=this.menu.getTopSelectable())&&this.autocomplete(n)&&e.preventDefault()},_onEscKeyed:function(){this.close()},_onUpKeyed:function(){this.moveCursor(-1)},_onDownKeyed:function(){this.moveCursor(1)},_onLeftKeyed:function(){"rtl"===this.dir&&this.input.isCursorAtEnd()&&this.autocomplete(this.menu.getTopSelectable())},_onRightKeyed:function(){"ltr"===this.dir&&this.input.isCursorAtEnd()&&this.autocomplete(this.menu.getTopSelectable())},_onQueryChanged:function(t,e){this._minLengthMet(e)?this.menu.update(e):this.menu.empty()},_onWhitespaceChanged:function(){this._updateHint()},_onLangDirChanged:function(t,e){this.dir!==e&&(this.dir=e,this.menu.setLanguageDirection(e))},_openIfActive:function(){this.isActive()&&this.open()},_minLengthMet:function(t){return(t=e.isString(t)?t:this.input.getQuery()||"").length>=this.minLength},_updateHint:function(){var t,n,i,r,s,u;t=this.menu.getTopSelectable(),n=this.menu.getSelectableData(t),i=this.input.getInputValue(),!n||e.isBlankString(i)||this.input.hasOverflow()?this.input.clearHint():(r=o.normalizeQuery(i),s=e.escapeRegExChars(r),(u=new RegExp("^(?:"+s+")(.+$)","i").exec(n.val))&&this.input.setHint(i+u[1]))},isEnabled:function(){return this.enabled},enable:function(){this.enabled=!0},disable:function(){this.enabled=!1},isActive:function(){return this.active},activate:function(){return!!this.isActive()||!(!this.isEnabled()||this.eventBus.before("active"))&&(this.active=!0,this.eventBus.trigger("active"),!0)},deactivate:function(){return!this.isActive()||!this.eventBus.before("idle")&&(this.active=!1,this.close(),this.eventBus.trigger("idle"),!0)},isOpen:function(){return this.menu.isOpen()},open:function(){return this.isOpen()||this.eventBus.before("open")||(this.menu.open(),this._updateHint(),this.eventBus.trigger("open")),this.isOpen()},close:function(){return this.isOpen()&&!this.eventBus.before("close")&&(this.menu.close(),this.input.clearHint(),this.input.resetInputValue(),this.eventBus.trigger("close")),!this.isOpen()},setVal:function(t){this.input.setQuery(e.toStr(t))},getVal:function(){return this.input.getQuery()},select:function(t){var e=this.menu.getSelectableData(t);return!(!e||this.eventBus.before("select",e.obj))&&(this.input.setQuery(e.val,!0),this.eventBus.trigger("select",e.obj),this.close(),!0)},autocomplete:function(t){var e,n;return e=this.input.getQuery(),!(!((n=this.menu.getSelectableData(t))&&e!==n.val)||this.eventBus.before("autocomplete",n.obj))&&(this.input.setQuery(n.val),this.eventBus.trigger("autocomplete",n.obj),!0)},moveCursor:function(t){var e,n,i,r;return e=this.input.getQuery(),n=this.menu.selectableRelativeToCursor(t),r=(i=this.menu.getSelectableData(n))?i.obj:null,!(this._minLengthMet()&&this.menu.update(e))&&!this.eventBus.before("cursorchange",r)&&(this.menu.setCursor(n),i?this.input.setInputValue(i.val):(this.input.resetInputValue(),this._updateHint()),this.eventBus.trigger("cursorchange",r),!0)},destroy:function(){this.input.destroy(),this.menu.destroy()}}),n;function i(t){var n=[].slice.call(arguments,1);return function(){var i=[].slice.call(arguments);e.each(n,function(e){return t[e].apply(t,i)})}}}();!function(){"use strict";var r,s,u;function l(e,n){e.each(function(){var e,i=t(this);(e=i.data(s.typeahead))&&n(e,i)})}function f(n){var i;return(i=e.isJQuery(n)||e.isElement(n)?t(n).first():[]).length?i:null}r=t.fn.typeahead,s={www:"tt-www",attrs:"tt-attrs",typeahead:"tt-typeahead"},u={initialize:function(r,u){var l;return u=e.isArray(u)?u:[].slice.call(arguments,1),l=n((r=r||{}).classNames),this.each(function(){var n,d,p,g,m,y,v,_,b,w,S;e.each(u,function(t){t.highlight=!!r.highlight}),n=t(this),d=t(l.html.wrapper),p=f(r.hint),g=f(r.menu),m=!1!==r.hint&&!p,y=!1!==r.menu&&!g,m&&(p=function(t,e){return t.clone().addClass(e.classes.hint).removeData().css(e.css.hint).css((n=t,{backgroundAttachment:n.css("background-attachment"),backgroundClip:n.css("background-clip"),backgroundColor:n.css("background-color"),backgroundImage:n.css("background-image"),backgroundOrigin:n.css("background-origin"),backgroundPosition:n.css("background-position"),backgroundRepeat:n.css("background-repeat"),backgroundSize:n.css("background-size")})).prop("readonly",!0).removeAttr("id name placeholder required").attr({autocomplete:"off",spellcheck:"false",tabindex:-1});var n}(n,l)),y&&(g=t(l.html.menu).css(l.css.menu)),p&&p.val(""),n=function(t,e){t.data(s.attrs,{dir:t.attr("dir"),autocomplete:t.attr("autocomplete"),spellcheck:t.attr("spellcheck"),style:t.attr("style")}),t.addClass(e.classes.input).attr({autocomplete:"off",spellcheck:!1});try{!t.attr("dir")&&t.attr("dir","auto")}catch(t){}return t}(n,l),(m||y)&&(d.css(l.css.wrapper),n.css(m?l.css.input:l.css.inputWithNoHint),n.wrap(d).parent().prepend(m?p:null).append(y?g:null));S=y?c:a,v=new i({el:n}),_=new o({hint:p,input:n},l),b=new S({node:g,datasets:u},l),w=new h({input:_,menu:b,eventBus:v,minLength:r.minLength},l),n.data(s.www,l),n.data(s.typeahead,w)})},isEnabled:function(){var t;return l(this.first(),function(e){t=e.isEnabled()}),t},enable:function(){return l(this,function(t){t.enable()}),this},disable:function(){return l(this,function(t){t.disable()}),this},isActive:function(){var t;return l(this.first(),function(e){t=e.isActive()}),t},activate:function(){return l(this,function(t){t.activate()}),this},deactivate:function(){return l(this,function(t){t.deactivate()}),this},isOpen:function(){var t;return l(this.first(),function(e){t=e.isOpen()}),t},open:function(){return l(this,function(t){t.open()}),this},close:function(){return l(this,function(t){t.close()}),this},select:function(e){var n=!1,i=t(e);return l(this.first(),function(t){n=t.select(i)}),n},autocomplete:function(e){var n=!1,i=t(e);return l(this.first(),function(t){n=t.autocomplete(i)}),n},moveCursor:function(t){var e=!1;return l(this.first(),function(n){e=n.moveCursor(t)}),e},val:function(t){var e;return arguments.length?(l(this,function(e){e.setVal(t)}),this):(l(this.first(),function(t){e=t.getVal()}),e)},destroy:function(){return l(this,function(t,n){!function(t){var n,i;n=t.data(s.www),i=t.parent().filter(n.selectors.wrapper),e.each(t.data(s.attrs),function(n,i){e.isUndefined(n)?t.removeAttr(i):t.attr(i,n)}),t.removeData(s.typeahead).removeData(s.www).removeData(s.attr).removeClass(n.classes.input),i.length&&(t.detach().insertAfter(i),i.remove())}(n),t.destroy()}),this}},t.fn.typeahead=function(t){return u[t]?u[t].apply(this,[].slice.call(arguments,1)):u.initialize.apply(this,arguments)},t.fn.typeahead.noConflict=function(){return t.fn.typeahead=r,this}}()});