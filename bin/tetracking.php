<?php

use Illuminate\Support\Carbon;

require __DIR__ . '/../vendor/autoload.php';
$app = new Illuminate\Foundation\Application(
    realpath(__DIR__ . '/..')
);
$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);
$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);
$app->make(App\Console\Kernel::class)->bootstrap();
$app->boot();

$tm  =  new \App\Library\Sophio\Catalog\TrackingManager(new \Sophio\Common\Repository\Settings());
$tm->getFileFromFTP(Carbon::now());