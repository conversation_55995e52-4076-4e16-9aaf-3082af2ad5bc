<?php

use App\Events\AdminQueueMailEvent;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Sophio\Common\Services\DimensionsService;
use Sophio\FBSInventoryImporter\Library\Jobs\AllPostImportJobs;
use Sophio\FBSInventoryImporter\Library\Jobs\PushInventoriesToWarehouse;
use Sophio\FBSOrder\src\Library\Actions\SendFulfillmentToSeller;

require __DIR__ . '/../vendor/autoload.php';

$app = new Illuminate\Foundation\Application(
    realpath(__DIR__ . '/..')
);
$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);
$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);
$app->make(App\Console\Kernel::class)->bootstrap();
$app->boot();

Cache::store('lock')->lock('.invoice.1801218',600)->get();
