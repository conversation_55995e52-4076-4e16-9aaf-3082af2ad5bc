<?php



use App\Library\Sophio\Products;
use Illuminate\Support\Facades\Storage;
use Sophio\Common\Models\FBS\BuyerProfile;
use Sophio\FBSFeedExporter\Library\DataFeedExportManager;
require __DIR__ . '/../vendor/autoload.php';

$app = new Illuminate\Foundation\Application(
    realpath(__DIR__ . '/..')
);
$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);
$app->make(App\Console\Kernel::class)->bootstrap();
$app->boot();
/*
$zoro = new \App\Library\Sophio\Exporters\ZoroExporter();
$zoro->candidates(100015);

$candidates = new Products();
$candidates->existingTractors();
*/
/*
$candidates = new Products();
$candidates->specificDatabase('sameday_autoparts_partsplus_aces',null);
 */
/*
$zoro = new \App\Library\Sophio\Exporters\ZoroExporter();
$zoro->exportCandidates('sameday_autoparts_partsplus_aces');

$candidates = new Products();
$candidates->existingCandidates();
*/
/*
$bp = BuyerProfile::where('id', 3)->first();
$de_manager = new DataFeedExportManager('sophio_fbs',['tenant_db'=>'sophio_fbs','emails'=>['<EMAIL>']]);
$de_manager->generateAndSendForBuyerProfile($bp);
*/
/*
$t = \Illuminate\Support\Carbon::createFromFormat("Y-m-d","2023-01-25");

$connect = [
    'host' =>'sftp.usautoparts.com',
    'username' => 'sophio',
    'password' => 'krPi@N^qKiz@',
    'port' => 990,
    'timeout' => 5,
    'recurseManually' => false,
    'root' => '/'
];
$connect['ssl'] = true;
//$connect['ignorePassiveAddress'] = true;
$connect['passive'] = false;
var_dump(openssl_get_cert_locations());
$storage = Storage::createFtpDriver($connect);
print_r($storage->allFiles('/'));
exit();
$ctx = stream_context_create(['ftps' => [
    'verify_peer' => true,
    'cafile' => '/etc/ssl/certs/digi.pem',
    'CN_match' => '*.usautoparts.com'
]]);
*/
$c = \Sophio\Common\Models\FBS\Customer::where('PK',289297)->first();
dd($c->settings);
$filename = "/tmp/datafeed_289297_2023-01-26.csv";
$filebase = "datafeed_289297_2023-01-26.csv";
$ftp_server = 'ftps://sftp.usautoparts.com:990/'.$filebase;
$ftp_user = 'sophio';
$ftp_password = 'krPi@N^qKiz@';

$ftp_certificate = '/etc/ssl/certs/digi.pem';

$ch = curl_init();
$fp = fopen($filename, 'r');
curl_setopt($ch, CURLOPT_VERBOSE, TRUE);
curl_setopt($ch, CURLOPT_URL, $ftp_server );
curl_setopt($ch, CURLOPT_USERPWD, $ftp_user . ':' . $ftp_password);
curl_setopt($ch, CURLOPT_TIMEOUT, 400);
curl_setopt($ch, CURLOPT_UPLOAD, 1);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 400);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
curl_setopt($ch, CURLOPT_INFILE, $fp);
curl_setopt($ch, CURLOPT_INFILESIZE, filesize($filename));
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
curl_setopt($ch, CURLOPT_CAINFO, $ftp_certificate);
curl_setopt($ch, CURLOPT_FTP_SSL, CURLFTPSSL_ALL);
curl_setopt($ch, CURLOPT_FTPSSLAUTH, CURLFTPAUTH_SSL);
$content =curl_exec($ch);
$error_no = curl_errno($ch);
$error_msg = curl_error($ch);
print_r([$content,$error_msg,$error_no]);
exit();

$dirHandle = opendir('ftps://sophio:<EMAIL>:990/', $ctx);
while (($file = readdir($dirHandle)) !== false) {
    echo "filename: $file\n";
}
closedir($dirHandle);