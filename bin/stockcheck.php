<?php

use Illuminate\Support\Facades\Config;
use Sophio\Common\Models\FBS\Item;
use Sophio\Common\Repository\Settings;
use Sophio\FBSOrder\Library\Actions\OrderLinkStockCheckProcessor;
use Sophio\FBSOrder\Library\Repository\SupplierForOrder;
use Sophio\FBSOrder\Models\WarehouseItem;

require __DIR__ . '/../vendor/autoload.php';

$app = new Illuminate\Foundation\Application(
    realpath(__DIR__ . '/..')
);
$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);
$app->make(App\Console\Kernel::class)->bootstrap();
$app->boot();
$contactpk = 169;
$settings = new Settings(['fbs_database' => Config::get('tenant_db')]);
$settings->set('alternateflag', 'false');
$supplier = new SupplierForOrder( $settings);
$supplier->setSupplierByContactpk($contactpk);


$itemList = collect();
$itemObj = new Item([
    'mfgcode' => 'WIX',
    'sku' => '51515',
    'qty_req' =>1,
    'price' =>1?? 0,
    'seq' => 1 ?? 0,
    'pk' => 1 ?? 0,
    'main_pk' => 1
]);
$itemObj->sources[$contactpk] = new WarehouseItem();
$itemObj->sources[$contactpk]->fill([
    'linecode' =>'WF',
    'mfgcode' => 'WIX',
    'product_number' => '51515',
    'part_number_unformatted' => unformatString( '51515'),
]);


$itemList->put(1, $itemObj);
$supplier->addLine(['pk' => 1, 'mfgsku' => mkMgfSku('WIX','51515')]);

$suppliers = [$contactpk => $supplier];
$driver = new OrderLinkStockCheckProcessor($settings);
($driver)($suppliers, $itemList);
 dd($itemList);

/*
$user_settings = DB::table('facetedapi.order_user_settings')
    ->where('customerId', '281923')
    ->join('facetedapi.order_settings', 'order_settings.id', '=', 'order_user_settings.order_setting_id')
    ->select('order_user_settings.*', 'order_settings.key')->get();
$settings = [];
foreach ($user_settings as $setting) {

    $settings[$setting->key] = $setting->value;
}
$settings['customerId'] = '281923';
$settings['fbs_database'] = 'sophio_fbs';

$settings['seller_database'] = 'partsgeek_aces';
$settings['market'] = 'B2B';
$settings['psp_low_correction'] = 0.85;
$settings['cost_mark_to_reject'] = 0.95;

$existing = DB::table('partsgeek_aces.wws_supplierlogs')
    ->where('action', 'stockcheck')
    ->orderBy('id', 'desc')
    ->limit(100)
    ->offset(1000)
    ->get();

$bad = 0;
$good = 0;
foreach ($existing as $request) {
    $disco = new \Sophio\FBSOrder\Library\Repository\StockDiscoveryManager($settings);
    $disco->setItems(json_decode($request->request, true));
    $disco->checkStocks();
    $disco->postProcess();
    $new_response = $disco->getFinal();
    $old_response = json_decode($request->response, true);
    $or = [];
    $nr = [];
    echo $request->request . "\n";
    foreach ($old_response as $k => $old_line) {
        $m = [];
        if ($old_line['qty_avail'] != $new_response[$k]['qty_avail']) {
            $m[] = $old_line['linecode'] . '-' . $old_line['part_number_unformatted'] . ' old qty ' . $old_line['qty_avail'] . ' new qty ' . $new_response[$k]['qty_avail'];
        }
        if ($old_line['sellprice'] != $new_response[$k]['sellprice']) {
            $m[] = $old_line['linecode'] . '-' . $old_line['part_number_unformatted'] . ' old sellprice ' . $old_line['sellprice'] . ' new sellprice ' . $new_response[$k]['sellprice'];
        }
        if (count($m) > 0) {
            echo implode('|', $m) . "\n";
            $bad++;
        } else {
            $good++;
        }
    }
}

echo "Total requests processed: " . count($existing) . "\n";
echo $bad . " requests were not the same\n";
echo $good . " requests were  the same\n";
*/
Config::set('tenant_db', 'partsgeek_aces');
$logchecks = \App\Models\Orders\LogCheck::where('created_at', '>', '2022-11-27')->where('type','STOCKCHECK')->orderBy('created_at', 'desc');

foreach ($logchecks->cursor() as $logcheck) {
    try {
        $resp_raw = \Illuminate\Support\Facades\Http::withHeaders(['Authorization' => 'Basic MUI5MDI0N0YtNDA4OS00OEQ5LThGMUItNjUyRTQyMTgxMkQwOkEwQTkzRDlBLTcyNEEtNEFFRi1CNjRBLTZBQzI5QjM5QjA0Wg=='])
            ->withBody($logcheck->items, 'application/json')
            ->post('https://faceted-catalog-api-us3.sophio.com/api/v1/bcbb2170-ebdd-4814-925b-dd2f1cbeabdd/checkstockapi');
        $resp = json_decode($resp_raw, true);

        $i = 0;
        $diff = false;
        echo "Logcheck  " . $logcheck->id . "\n";
        foreach (json_decode($logcheck->response, true) as $lineitem) {
            if ($lineitem['sellprice'] != $resp[$i]['sellprice'] ) {
                $diff = true;
            }
            $i++;
            if ($diff) {
                echo "Logcheck TEST " . $logcheck->id . " IS DIFFERENT\n";
                echo "had " . $logcheck->response . "\n";
                echo "got " . $resp_raw . "\n";
            } else {
                echo "Logcheck TEST " . $logcheck->id . " iS same \n";

            }
        }
        $resp_raw = \Illuminate\Support\Facades\Http::withHeaders(['Authorization' => 'Basic MUI5MDI0N0YtNDA4OS00OEQ5LThGMUItNjUyRTQyMTgxMkQwOkEwQTkzRDlBLTcyNEEtNEFFRi1CNjRBLTZBQzI5QjM5QjA0Wg=='])
            ->withBody($logcheck->items, 'application/json')
            ->post('https://newadmin.sophio.com/api/v1/bcbb2170-ebdd-4814-925b-dd2f1cbeabdd/checkstockapi');
        $resp = json_decode($resp_raw, true);

        $i = 0;
        $diff = false;
        foreach (json_decode($logcheck->response, true) as $lineitem) {
            //$lineitem['qty_avail'] != $resp[$i]['qty_avail'] ||
            if ($lineitem['sellprice'] != $resp[$i]['sellprice'] ) {
                $diff = true;
            }
            $i++;
            if ($diff) {
                echo "Logcheck NEW " . $logcheck->id . " IS DIFFERENT\n";
                echo "had " . $logcheck->response . "\n";
                echo "got " . $resp_raw . "\n";
            } else {
                echo "Logcheck NEW " . $logcheck->id . " iS same \n";

            }
        }
    } catch (Exception $e) {
        print_r($e->getMessage());
    }
}