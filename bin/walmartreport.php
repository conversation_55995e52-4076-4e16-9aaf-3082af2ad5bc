<?php

use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Sophio\FBSFeedExporter\Library\Actions\WalmartInventoryFeedFile;
use Sophio\Walmart\Actions\CreateFeedInventoryHistory;
use Sophio\Walmart\Jobs\CheckGeneralFeed;
use Sophio\Walmart\Jobs\DSVReportCheckJob;
use Sophio\Walmart\Jobs\SendInventoryFeed;
use Sophio\Walmart\Library\Feeds\InventoryFeeds;
use Sophio\Walmart\Library\GeneralFeeds;
use Sophio\Walmart\Library\Templates\DSVInventoryFeedTemplate;


set_time_limit(0);
require __DIR__ . '/../vendor/autoload.php';

$app = new Illuminate\Foundation\Application(
    realpath(__DIR__ . '/..')
);
$app->singleton(

    App\Console\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);
$app->make(App\Console\Kernel::class)->bootstrap();
$app->boot();

$lines =\Illuminate\Support\Facades\DB::select("SELECT li.cost, li.nwtosophio, group_concat(li.cprice), li.price, li.custpk, li.invpk, li.sku, li.linecode,profilepk,li.contactpk,i.invdate FROM sophio_fbs.wws_lineitems li 
JOIN sophio_fbs.wws_invoice i ON i.pk=li.invpk
WHERE li.custpk=288065 AND timeout>='2023-12-30'  GROUP BY sku,linecode,profilepk  HAVING COUNT(DISTINCT cprice)>1 
 ");
foreach($lines as $line){
    $lastcost=\Illuminate\Support\Facades\DB::select("select fihc.* from sophio_fbs.marketplaceitems mi   
JOIN walmart.feed_items_cost_history fihc ON fihc.marketSellerSku=mi.marketSellerSku AND fihc.contactpk =?
JOIN walmart.feeds f ON f.id=fihc.feed_id where mi.mfg_code=? AND mi.part_number_unformatted=?    order by f.created_at desc limit 1",[$line->contactpk,$line->linecode,$line->sku]);
    if($lastcost)
    if($lastcost[0]->errors!=null){
        $costs = \Illuminate\Support\Facades\DB::select("select fihc.*,mi.*,f.created_at from sophio_fbs.marketplaceitems mi  
JOIN walmart.feed_items_cost_history fihc ON fihc.marketSellerSku=mi.marketSellerSku AND fihc.contactpk =?
JOIN walmart.feeds f ON f.id=fihc.feed_id AND f.created_at<=? where   mi.mfg_code=? AND mi.part_number_unformatted=? and fihc.errors is null and f.status='PROCESSED'",[$line->contactpk,$line->invdate,$line->linecode,$line->sku]);
        if(count($costs)>0) {
           echo $costs[0]->marketSellerSku.' '.$costs[0]->contactpk.' '.$costs[0]->created_at."\n";
        }
    }
}










exit();
$dsv= new \Sophio\Walmart\Library\DSVInventoryReports();
//dd($dsv->reportRequestStatus('0a9b0eeb-ffd2-490f-87a2-bbb660be0ba0'));

DSVReportCheckJob::dispatch("0a9b0eeb-ffd2-490f-87a2-bbb660be0ba0");
$client = new \Sophio\Walmart\Library\WalmartClient();
$client->setMethod('GET');
$client->addHeader('Accept', 'application/json');
$client->addHeader('Connection', 'Keep-Alive');
$client->addHeader('Keep-Alive', '10');
//$client->addHeader( 'Accept-Encoding' ,  'gzip, deflate, br');
$client->setEndpoint('/v3/getReport?type=vendor_item&version=2');
echo ' get access token'."\n";
print_r( $client->getAcccessToken());


exit();
$dsv= new \Sophio\Walmart\Library\DSVInventoryReports();

dd($re);
