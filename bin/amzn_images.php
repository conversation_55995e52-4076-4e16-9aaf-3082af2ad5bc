<?php

use HighsideLabs\LaravelSpApi\Models\Credentials;
use SellingPartnerApi\Api\ProductPricingV0Api;
use Sophio\Common\Models\PIM\Asin;

require __DIR__ . '/../vendor/autoload.php';

$app = new Illuminate\Foundation\Application(
    realpath(__DIR__ . '/..')
);
$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);
$app->make(App\Console\Kernel::class)->bootstrap();
$app->boot();
$asins = DB::select("SELECT a.*,pm.part_number, ItemLevelGTIN FROM toolsequipments_aces.partmaster pm 
    JOIN partshare.FlattenedData FD ON FD.AAIABrandID = pm.mfg_code AND FD.PartNumber = pm.part_number
       left JOIN pnci_pim.images pi
                       ON pi.aaiabrandid = pm.mfg_code AND pi.part_number_unformatted = pm.part_number_unformatted
   
JOIN pnci_pim.asins a ON a.mfg_code=pm.mfg_code AND a.part_number_unformatted=pm.part_number_unformatted WHERE DATA IS NOT NULL  AND  FD.PartTypeID IS NOT NULL 
AND pi.id IS null
GROUP BY pm.part_number, pm.mfg_code
");
foreach($asins as $asin)
{
    echo $asin->part_number." ".$asin->asin."\n";
    $data=json_decode($asin->data,true);
    if(isset($data['images']) && is_array($data['images'])) {
        $main= [];
        $imgs=[];
        foreach($data['images'] as $images) {
            foreach($images['images'] as $image) {
                if(!isset($imgs['width'])) {
                    $imgs = $image;
                }elseif($imgs['width']<$image['width']) {
                    $imgs = $image;
                }
                if ($image['variant'] == 'MAIN') {
                        if(!isset($main['width'])) {
                            $main = $image;
                        }elseif($main['width']<$image['width']) {
                            $main = $image;
                        }
                }
            }
        }
        if(!isset($main['width'])) {
            $main = $imgs;
        }
        if(isset($main['width'])) {
            $ic = new \App\Library\Sophio\ImageCrawlResizer();
            $ic->resize = false;
            $imgparts = arraY_values(array_filter(explode('/', $main['link'])));
            try {
                $newimg = [
                    'image_url' => $main['link'],
                    'aaiabrandid' => $asin->mfg_code,
                    'part_number' => $asin->part_number,
                    'part_number_unformatted' => $asin->part_number_unformatted,
                    'upc_int' => $asin->ItemLevelGTIN,
                    'filename' => $imgparts[count($imgparts) - 1],
                    'original_path' => "original/" . $asin->mfg_code . "/" . $imgparts[count($imgparts) - 1],
                    'primary' => 1
                ];
                $img = $ic->addImage($newimg);
            } catch (Throwable $th) {

            }

        }
    }
    sleep(1);
}