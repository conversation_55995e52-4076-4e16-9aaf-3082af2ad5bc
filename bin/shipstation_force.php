<?php

use Sophio\Common\Models\FBS\Invoice;
use Sophio\Common\Models\FBS\Supplier;
use Sophio\Common\Repository\Settings;
use Sophio\Common\ShipStation\src\Library\Actions\OrderForSupplier;

require __DIR__ . '/../vendor/autoload.php';

$app = new Illuminate\Foundation\Application(
    realpath(__DIR__ . '/..')
);
$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);
$app->make(App\Console\Kernel::class)->bootstrap();
$app->boot();
config(['tenant_db'=>'sophio_fbs']);
$sup = Supplier::find(934);
$settings = new Settings(['fbs_database' => 'sophio_fbs', 'parallel_soap' => 0]);
$invoices = Invoice::whereHas('lineitem',function($query){
    $query->where('profilepk',934);
    $query->where('sup_ord_id','<>','');
    $query->where('track_num','');
})->where('invdate','>','2024-01-01')->whereNotIn('pk',[10115518,10115521]);
foreach($invoices->get()as $invoice)
{
    echo $invoice->pk."\n";
    $orderForSupplier = new OrderForSupplier($settings);
    $order = $orderForSupplier->send($invoice, $sup, []);
}
