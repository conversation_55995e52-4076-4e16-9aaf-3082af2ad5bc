<?php

use App\Events\AdminQueueMailEvent;
use App\Exceptions\ExceptionMailAction;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use OpenSpout\Reader\Common\Creator\ReaderEntityFactory;
use Ramsey\Uuid\Uuid;
use Sophio\Common\Models\FBS\Customer;
use Sophio\Common\Models\FBS\Invoice;
use Sophio\Common\Models\FBS\ProductWarehouse;
use Sophio\Common\Models\FBS\Supplier;
use Sophio\Common\Repository\Settings;
use Sophio\Common\Services\DimensionsService;
use Sophio\Common\Services\IsoCodesService;
use Sophio\Common\ShipStation\src\Library\CustomShipstationManager;
use Sophio\Common\ShipStation\src\Library\Service\DelToShipstationShipping;
use Sophio\Corcentric\Library\CorcentricManager;
use Sophio\FBSInventoryImporter\Library\Actions\DataFeeds\HistoryFeeds;
use Sophio\FBSInventoryImporter\Library\Jobs\PushInventoriesToWarehouse;
use Sophio\FBSOrder\Library\Actions\GetDataFeedPrice;
use Sophio\FBSOrder\Library\Actions\GetMainWarehouse;
use Sophio\FBSOrder\src\Library\Actions\SendFulfillmentToSeller;
use Sophio\FBSOrder\src\Library\Repository\CancelService;
use Sophio\HomeDepot\Library\CHUBManager;

require __DIR__ . '/../vendor/autoload.php';

$app = new Illuminate\Foundation\Application(
    realpath(__DIR__ . '/..')
);
$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);
$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);
$app->make(App\Console\Kernel::class)->bootstrap();
$app->boot();

dd(
    [
        preg_replace('~.*(\d{3})[^\d]{0,7}(\d{3})[^\d]{0,7}(\d{4}).*~', '($1) $2-$3',"9147510665"),
        preg_replace('~.*(\d{3})[^\d]{0,7}(\d{3})[^\d]{0,7}(\d{4}).*~', '($1) $2-$3',"(914) 751-0665"),
    ]
);
echo Str::replaceMatches(pattern: '/[^A-Za-z0-9@.+ ]++/', replace: '',    subject: '<EMAIL> ');
exit();
(new \Sophio\FBSInventoryImporter\Library\Actions\ZeroInactiveSuppliers())('sophio_fbs',["fbn_aces","network_affiliate_aces","partsgeek_aces","zoro","walmart_aces","tractorsupply","carparts_aces","softfork_aces","b2bnational","homedepot","b2ccentralaffiliates_aces"]);
exit();
$tracks =[
    '1Z8X330WYN29776897',
    '1Z906A60YW62222030',
    '9405511105501033032451',
    '000428920668',
    '998030179671301',
    '19066076190334',
    '1074950393532314'
];
foreach ($tracks as $track) {
    echo $track.' - '.getCarrierCodeFromTrackNum($track)."\n";
}
exit();

config(['tenant_db' =>'test_fbs','sophio.admin.default_store'=>374572,'sophio.admin.default_database'=>'test_fbs']);
$settings =new Settings([]);
$customer = Customer::find(282292);
$manager = new CHUBManager(new Settings([]), Customer::find(282292));
$poc = new \Sophio\HomeDepot\Library\HubXML\PurchaseOrderClient($settings);
$poc->setSellerDir('thehomedepot');
$poc->setCustomer($customer);
$huborder = $poc->processLocalFile('20013569885.md59D3B5508EB2ECE6BAE244565CD865769.neworders');
list( $acks,$invoices) = $poc->extractOrders($huborder);
$fac = new \Sophio\HomeDepot\Library\HubXML\FunctionalAcknowledgementClient($settings);
$fac->setCustomer($customer);
$fa = $fac->createAck($huborder, $acks);
$fac->sendAck($huborder, $fa,$invoices);
$fac->postSentAck($poc->getInvoices());
dd(config('sophio.admin.demo') );

// done
exit();
$customer = \Sophio\Common\Models\FBS\Customer::find(282292);
config(['tenant_db' => $customer->xml['ACESPARTSDB']]);
$omsid=308421374;
$item=[];
$pw = ProductWarehouse::whereHas('productfeed', function ($query) use ($omsid) {
    $query->where('omsid', $omsid);
})->first();
config(['tenant_db' => sophiosettings()->get('fbs_database')]);
if (!$pw) {
    $pw = ProductWarehouse::whereHas('productfeed', function ($query) use ($omsid) {
        $query->where('omsid', $omsid);
    })->first();
    if ($pw) {
        $item['itempk'] = $pw->product_sku;
        if ($pw->productfeed) {
            $item['itempk'] = $pw->productfeed->product_sku;
        }

        $item['sku'] = strtoupper($pw->part_number_unformatted);
        $item['skuform'] = $pw->product_number;
        if ($pw->partsharepricing) {
            $item['nwtosophio'] = $pw->partsharepricing->itemaltaffiliate;
        }
        $item['linecode'] = $pw->mfgcode;

    }
} else {
    if ($pw->productfeed) {
        $item['itempk'] = $pw->productfeed->product_sku;
    }

    $item['sku'] = $pw->part_number_unformatted;
    $item['skuform'] = $pw->product_number;
    $item['cprice'] = $pw->sellprice; //@TODO this is wrong, must be the price we set on 1st of month
    $fdb = DB::select('select * from homedepot.feedprices where mfgcode=? and part_number_unformatted=? and feed_date<=? order by feed_date desc limit 1',[$pw->mfgcode,$pw->part_number_unformatted, \Illuminate\Support\Carbon::today()->firstOfMonth()->toDateString()]);
    if($fdb && isset($fdb[0])) {
        $item['cprice'] = $fdb[0]->price;
    }
    $item['linecode'] = $pw->mfgcode;
    if ($pw->partsharepricing) {
        $item['nwtosophio'] = $pw->partsharepricing->itemaltaffiliate;
    }
}
dd($item);
/*
$m = \Sophio\Common\Models\Partshare\FlattenedData::where('AAIABrandID','DHTM')->where('PartNumber','SPOA10N20M0BK')->first()->sales->sum('itemtotal');
dd($m);
*/
$m = \Sophio\Common\Models\Partshare\FlattenedData::where('AAIABrandID','DHTM')->where('PartNumber','RJ152BK')->first()->sales->sum('itemtotal');
dd($m);
dd(\Illuminate\Support\Carbon::now()->startOfMonth()->toDateString());
config(['tenant_db'=>'sophio_fbs']);
config(['sophio.admin.default_database'=>'sophio_fbs']);
$settings = new Settings();
$settings->setSettingsByCustomer(\Sophio\Common\Models\FBS\Customer::find(289297));
$cprice = (new \Sophio\FBSOrder\Library\Actions\GetCPrice($settings))('PWS', '17-503',\Sophio\Common\Models\FBS\Customer::find(289297));
dd($cprice);
$i = new \App\Library\Sophio\Jobs\FBS\UpdateQtyInWarehouse([[

    "database"=>
"softfork_aces",
"pk"=>
14626409,
"mfgcode"=>
"BAR",
"part_number_unformatted"=>
"1800701",
"contactpk"=>
200859,
"qtyrtsc"=>
0
],
    [

        "database"=>
            "softfork_aces",
        "pk"=>
            722356356,
        "mfgcode"=>
            "BAR",
        "part_number_unformatted"=>
            "1800701",
        "contactpk"=>
            189,
        "qtyrtsc"=>
            0
    ],
]);
$i->handle();
exit();
(new ExceptionMailAction())(new Exception('Testing another mail exception and another  '.time()));
exit();
dd(config('backpack'));
config(['tenant_db'=>'sophio_fbs']);
$invoice = Invoice::find(10169879);
dd($invoice->lineitem->count());
$user = \Sophio\Common\SophioUser::find(1168);

print_r( $user->getRoleNames());exit();
$customer = $user->related_models->where('profitable_type','customer');
print_r($user->related_models);
exit();
foreach($user->related_models as $rm) {
    print_r([$rm->profilable_type,$rm->profilable_id,$rm->related->NAME,$rm->related->company]);

}
 exit();
$uuid = Uuid::uuid4();

printf(
    "UUID: %s\nVersion: %d\n",
    $uuid->toString(),
    $uuid->getFields()->getVersion()
);
function Luhn($digits) {

    $sum = 0;
    foreach (str_split(strrev($digits)) as $i => $digit) {
        $sum += ($i % 2 == 0) ? array_sum(str_split($digit * 2)) : $digit;
    }

    return $digits . (10 - ($sum % 10)) % 10;
}
printf("uniqid('php_'): %s\r\n", uniqid('php_',13232));
$first = substr(str_replace('.','',microtime(true)),-5);
$second = str_pad(random_int(0, 1000),4,'0',STR_PAD_LEFT);
echo  $first.$second."\n";
$first = substr(str_replace('.','',microtime(true)),-5);
$second = str_pad(random_int(0, 1000),4,'0',STR_PAD_LEFT);
echo  $first.$second."\n";
exit();
dd(\Illuminate\Support\Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now()->startOfDay()));

$xml='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ns8:errors xmlns:ns2="http://walmart.com/mp/orders" xmlns:ns3="http://walmart.com/mp/v3/orders" xmlns:ns4="com.walmart.services.common.model.money" xmlns:ns5="com.walmart.services.common.model.name" xmlns:ns6="com.walmart.services.common.model.address" xmlns:ns7="com.walmart.services.common.model.address.validation" xmlns:ns8="http://walmart.com/">
    <ns8:error>
        <ns8:code>INVALID_REQUEST_CONTENT.GMP_ORDER_API</ns8:code>
        <ns8:field>data</ns8:field>
        <ns8:description>Unable to process this request. Trying to Ship Line with current status CANCELLED or The Line: 1  of PO: 108908379134883  doesn\'t have enough quantity to shiprequested quantity :: 1 qtyAvailableToShip :: 0</ns8:description>
        <ns8:info>Request content is invalid.</ns8:info>
        <ns8:severity>ERROR</ns8:severity>
        <ns8:category>DATA</ns8:category>
        <ns8:causes/>
        <ns8:errorIdentifiers/>
    </ns8:error>
    <ns8:error>
        <ns8:code>INVALID_REQUEST_CONTENT.GMP_ORDER_API</ns8:code>
        <ns8:field>data</ns8:field>
        <ns8:description>Unable to process this request. Trying to Ship Line with current status CANCELLED or The Line: 2  of PO: 108908379134883  doesn\'t have enough quantity to shiprequested quantity :: 1 qtyAvailableToShip :: 0</ns8:description>
        <ns8:info>Request content is invalid.</ns8:info>
        <ns8:severity>ERROR</ns8:severity>
        <ns8:category>DATA</ns8:category>
        <ns8:causes/>
        <ns8:errorIdentifiers/>
    </ns8:error>
    <ns8:error>
        <ns8:code>INVALID_REQUEST_CONTENT.GMP_ORDER_API</ns8:code>
        <ns8:field>data</ns8:field>
        <ns8:description>Unable to process this request. Trying to Ship Line with current status CANCELLED or The Line: 4  of PO: 108908379134883  doesn\'t have enough quantity to shiprequested quantity :: 1 qtyAvailableToShip :: 0</ns8:description>
        <ns8:info>Request content is invalid.</ns8:info>
        <ns8:severity>ERROR</ns8:severity>
        <ns8:category>DATA</ns8:category>
        <ns8:causes/>
        <ns8:errorIdentifiers/>
    </ns8:error>
</ns8:errors>';
$xml = simplexml_load_string($xml, "SimpleXMLElement",LIBXML_NOCDATA,  "ns8",true);
$json = json_encode($xml);
$array = json_decode($json,TRUE);
dd($array);
$invoice =Invoice::find( 10125936);

$l=[];
foreach($invoice->lineitem->where('profilepk',199) as $lineitem)
{
    $l[] = $lineitem;
}
dd($l);
$settings =  new Settings();
$supplier= Supplier::find(112);
$invoice =Invoice::find(10124873);
$customShipStation = new CustomShipstationManager($settings);
$customShipStation->setSupplier($supplier);
if (isset($supplier->SETTINGS['TRACKINGNUMBERTYPE']) && $supplier->SETTINGS['TRACKINGNUMBERTYPE']==='SHIPSTATIONCUSTOM' ){
    $customShipStation = new CustomShipstationManager($settings);
    $customShipStation->setSupplier($supplier);
    if ($settings->get('mock_tracking') == true) {
        echo 'demo\n';
    //    $customShipStation->fakeShipnotify($invoice);
    } else {
        if (in_array($invoice->custtype, ['B2B', 'AF'])) {
            try {
                $customShipStation->setSupplier(Supplier::find(199));
                $customShipStation->simulateShipQuote($invoice);
            } catch (\Throwable  $e) {
                Log::channel('shipstation')->error($invoice->pk . ' ' . $e->getMessage());
            }

        }
    }
}else{
    echo 'no TRACKINGNUMBERTYPE';
}
exit();
$invoices = Invoice::filterDate(['invdate','LASTMONTH'])
    ->whereIn('invstatus',['F','3'])
    ->where('custtype','NAT')
    ->where('track_num','<>','')
    ->where('CCRESULTX','NOT LIKE','%CORTIME%')->get();

foreach($invoices as $invoice) {
    $settings->setSettingsByInvoice($invoice);
    $cm = new CorcentricManager($settings, $invoice->store);
    $cm->setInvoice($invoice);
    echo "Check status for ".$invoice->pk." ".$invoice->invstatus;
    $result=  $cm->checkStatusRequest();
    echo "Finish status for ".$invoice->pk." ".$invoice->invstatus;

}
dd($invoices->count());

$s =  \Sophio\Common\Models\SystemLog::create(['task' => 'test', 'status' => 'NEW','user_id'=>1]);

$s2=\Sophio\Common\Models\SystemLog::find($s->id);
sleep(10);
$s2->message="prep";
$s2->save();
sleep(5);
$s->refresh();
$s->message="test";
$s->status=\Sophio\Common\Enums\SystemLogStatus::SUCCESS;
$s->save();
exit();
(new \Sophio\FBSOrder\Library\Actions\FindInactiveCustomers())();
exit();
$dzip = '80050000';
$zip = str_pad(substr($dzip, 0, -4),5,"0",STR_PAD_LEFT);
$zip4 = substr($dzip, -4);

$reader = ReaderEntityFactory::createXLSXReader();
$reader->setShouldFormatDates(true);
$reader->open(Storage::disk('fbs')->path('autozone/Storelist_20240311.xlsx'));
$rowno=0;
foreach ($reader->getSheetIterator() as $sheetIndex => $sheet) {
    foreach ($sheet->getRowIterator() as $row) {
        if($rowno==0) { $rowno++; continue; }
        $row = $row->toArray();
        $acccountnum=100000 + (int)$row[0];
        $dzip=$row[4];
        $zip = str_pad(substr($dzip, 0, -4),5,"0",STR_PAD_LEFT);
        $zip4 = substr($dzip, -4);

        $customer = \Sophio\Common\Models\FBS\Customer::where('custtype','NAT')->where('accountnum',(string)$acccountnum)->first();
        if($customer->zip!==$zip) {
            echo  'account '.$acccountnum.' zip '.$row[4].' old: '.$customer->zip.' '.$customer->zip4.' NEW: '.$zip.' '.$zip4."\n";
            $customer->zip = $zip;
            $customer->zip4 = $zip4;
        }
        if ((int)$customer->zip4 == 0) {
            $customer->st_zip = $customer->zip;
        } else {
            $customer->st_zip = $customer->zip . '-' . $customer->zip4;
        }
        $customer->save();
        $rowno++;
    }
}
echo $rowno."\n";
//\Illuminate\Support\Facades\File::makeDirectory(config('filesystems.disks.fbs.root').'/'.dirname( 'folder/test/123.csv' ),0777,true);
exit();
$dt = Carbon::now();
dd($dt->setTimezone('CST')->format('D, M j, Y g:i A T'));
config(['tenant_db'=>'sophio_fbs']);
config(['master_tenant_db'=>'sophio_fbs']);
config(['sophio.admin.default_database'=>'sophio_fbs']);

$settings = new Settings();

$lineitem = \Sophio\Common\Models\FBS\LineItem::find(********);

$fbs_warehouse = (new GetMainWarehouse($settings))($lineitem );
dd($fbs_warehouse);

dd((bool)Config::get('sophio.admin.mail_to_users') );
$dt = \Illuminate\Support\Carbon::now()->setTimezone(-5)->addDays(3);
dd( $dt->dayOfWeek ,$dt->dayName );

$array= ['deliverytime'=>1,['branch'=>1,'branchname'=>'San Fernando']];
$xml =  array2xml($array, false,'<LASTSTOCKCHECK/>');
dd(str_replace("<?xml version=\"1.0\"?>\n", '', $xml));
$dt = \Illuminate\Support\Carbon::now();
dd($dt->dayOfWeek);
(new HistoryFeeds())();
dd((float)str_replace(',','',('1,2345.67')));
$supplier = Supplier::where('PK', 831)->first();
dd($supplier->SETTINGS['SHIPSTATIONSTOREID']);
dd(\Illuminate\Support\Carbon::parse('2023 October')->endOfMonth()->toDateString());
$i = \Sophio\Common\Models\FBS\Invoice::find(10027092);
dd($i->completed);
$settings =new Settings();
$lineitem = \Sophio\Common\Models\FBS\LineItem::find(10009699);
dd($settings->get('STRIPETRANSFEE'),$lineitem->invoice->invtotal,($lineitem->invoice->paymethod === 'ST' && (int)$lineitem->invoice->ccprocfee == 0) ? ($lineitem->invoice->invtotal*($settings->get('STRIPETRANSFEE')??0.0395)) : $lineitem->invoice->ccprocfee );
$statementHeader = \Sophio\Common\Models\FBS\StatementHeader::find(100320);
if(isset($statementHeader->supplier->SETTINGS['BILLINGEMAIL'])){

    $emails = array_filter(explode(',',"<EMAIL>, <EMAIL>"));
}
if(count($emails)==0)
{
    if(count($emails)==0)
    {
        $emails = array_filter(explode(',',$statementHeader->supplier->SETTINGS['KEYBUSINESSEMAIL']));
    }
}
dd($emails);
config(['tenant_db'=>'sophio_fbs']);
$sservice = new DelToShipstationShipping(new Settings());
$invoice = \Sophio\Common\Models\FBS\Invoice::find(10017384);
$r = $sservice->getMapping($invoice, '' , '', '','WALMART');
dd($r);
$now = \Illuminate\Support\Carbon::now();
if ($now->hour >= 20 || $now->hour <= 6) {
    dd('yes');
}else{
    dd('no');
}
$invoice = \Sophio\Common\Models\FBS\Invoice::find(10006862);
$s = new CancelService(new Settings(), $invoice);
$lineitem = $invoice->lineitem->first();
$s->sendSupplierOrderCancellation(Supplier::find($lineitem->profilepk), $lineitem);
dd(IsoCodesService::extractUpc('01808709831600'),strlen(IsoCodesService::extractUpc('01808709831600')));
echo \Illuminate\Support\Carbon::parse("2023-11-01T04:40:31.0430000","PST")->setTimezone(config('app.timezone'))."\n";
dd(  \Illuminate\Support\Carbon::parse("2023-11-01T04:40:31.0430000","PST")->setTimezone(config('app.timezone'))->diffInSeconds(\Illuminate\Support\Carbon::now(),false),false);

echo preg_replace(
    '/\([A-za-z0-9 ]+\)/','',
    'Sun Oct 22 2023 06:23:26 GMT-0400 (Eastern Daylight Time)',-1
)."\n";
echo \Illuminate\Support\Carbon::parse( preg_replace(
    '/\([A-za-z0-9 ]+\)/','',
    'Sun Oct 22 2023 06:23:26 GMT-0400 (Eastern Daylight Time)',-1
    ))->setTimezone(config('app.timezone') )->format('Y-m-d H:i:s')."\n";
exit();

$profiles= Supplier::whereIn('sup', ['WHD', 'PAR'])->get();
foreach ($profiles as $profile) {
    if (isset($profile->SETTINGS['NOTOKTOSEND']) && strtolower($profile->SETTINGS['NOTOKTOSEND']) == "true") {
        echo "found ".$profile->getSupplierDisplayName()."\n";
        ProductWarehouse::where('contactpk', $profile->contactpk)->update(['qty_avail' => 0]);
    }
}

exit();
print_r(\Illuminate\Support\Carbon::now()->subDay()->setHour(11)->setMinute(0)->setSecond(0)->setMilli(0)->setTimezone( '-05:00'));
exit();
dd(\Carbon\Carbon::parse('20231005')->addSeconds(Carbon::now()->secondsSinceMidnight()),\Carbon\Carbon::parse('2023-10-04T20:23:27.262Z')->setTimezone(Config::get('app.timezone')));
$statementService = new \Sophio\FBSStatements\Library\StatementService();
$statement = \Sophio\Common\Models\FBS\StatementHeader::find(100191);
$statementService->missingCreditsFile($statement);
exit();
 $invoice = \Sophio\Common\Models\FBS\Invoice::first();
 foreach($invoice->getAttributes() as $a) {
     echo $a.'-';
 }
exit();
$s = new \Sophio\FBSStatements\Library\StatementService();
$s->returnAlloAndPenalties(\Sophio\Common\Models\FBS\StatementHeader::find(100041));
exit();
echo Carbon::parse('2023-08-31')->startOfMonth()->subMonths(2)->startOfMonth()."\n".Carbon::parse('2023-08-31')->startOfMonth()->subMonths(2)->endOfMonth();;
dd(backpack_user());
var_dump(config('sophio.admin.no_orderapi_inform'));exit();
config(['tenant_db'=>'sophio_fbs']);
$dim_service = new DimensionsService();
$dim_lines[] = [
    'width' => 10,
    'depth' => 5,
    'length' => 4,
    'weight' => 1,
    'qty' =>1
];
$dims = $dim_service->simpleOnePack($dim_lines);
dd($dims);
$supplier = \Sophio\Common\Models\FBS\Supplier::find(112);

dd($supplier->SETTINGS);

(new SendFulfillmentToSeller(new \Sophio\Common\Repository\Settings(['fbs_database'=>'sophio_fbs'])))(\Sophio\Common\Models\FBS\Invoice::find(21570887));
exit();
$suppliers = \Sophio\Common\Models\FBS\Supplier::all();
$statementtype=[];
foreach($suppliers as $supplier) {
    if(isset($supplier->SETTINGS['STATEMENTLOCATION'])) {
        if(!isset($statementtype[$supplier->SETTINGS['STATEMENTLOCATION']])) {
            $statementtype[$supplier->SETTINGS['STATEMENTLOCATION']]=[$supplier->PK];
        }else{
            $statementtype[$supplier->SETTINGS['STATEMENTLOCATION']][]= $supplier->PK;
        }
    }
}
PushInventoriesToWarehouse::dispatch( 'sophio_fbs' ,  490, ['<EMAIL>'], $settings = ['onlysummary'=>"1","pushToWarehouse"=>true,'postImport'=>true,'startdate'=> \Illuminate\Support\Carbon::now()]);
exit();
$name = 'October';
if(Carbon::now()->month<Carbon::parse('1-'.$name.'-'.Carbon::now()->year)->month) {
  echo Carbon::parse('1-'.$name.'-'.(Carbon::now()->year-1))->startOfMonth().' '.
    Carbon::parse('1-'.$name.'-'.(Carbon::now()->year-1))->endOfMonth();
}else{
    echo Carbon::parse('1-'.$name.'-'.Carbon::now()->year)->startOfMonth() .' '. Carbon::parse('1-'.$name.'-'.Carbon::now()->year)->endOfMonth();
}


$invoice = \Sophio\Common\Models\FBS\Invoice::find(9510499);
dd($invoice->getShippedLineItemsTotal());
$string = 'Trans Id:ch_3NBKvUJQgAIihRqA0FjmRYK6 - Stripe Invoice Id: in_1NBKvTJQgAIihRqAtGVOLxR4 - {  "id": "in_1NBKvTJQgAIihRqAtGVOLxR4",  "object": "invoice",  "account_country": "US",  "account_name": "Sophio LLC",  "account_tax_ids": null,  "amount_due": 0,  "amount_paid": 0,  "amount_remaining": 0,  "amount_shipping": 0,  "application": null,  "application_fee_amount": null,  "attempt_count": 0,  "attempted": false,  "auto_advance": false,  "automatic_tax": {    "enabled": false,    "status": null  },  "billing_reason": "manual",  "charge": null,  "collection_method": "send_invoice",  "created": **********,  "currency": "usd",  "custom_fields": null,  "customer": "cus_MjA9sZDAiIcXv6",  "customer_address": {    "city": "Maitland",    "country": "US",    "line1": "2130 Chippewa Trail",    "line2": null,    "postal_code": "32751",    "state": "FL"  },  "customer_email": "<EMAIL>",  "customer_name": "Larry Griller",  "customer_phone": "+***********",  "customer_shipping": null,  "customer_tax_exempt": "none",  "customer_tax_ids": [    {      "type": "us_ein",      "value": "88-3918333"    }  ],  "default_payment_method": null,  "default_source": null,  "default_tax_rates": [],  "description": "Invoice: 1710231 Fulfillment by Sophio",  "discount": null,  "discounts": [],  "due_date": 1685033559,  "ending_balance": null,  "footer": null,  "from_invoice": null,  "hosted_invoice_url": null,  "invoice_pdf": null,  "last_finalization_error": null,  "latest_revision": null,  "lines": {    "object": "list",    "data": [],    "has_more": false,    "total_count": 0,    "url": "/v1/invoices/in_1NBKvTJQgAIihRqAtGVOLxR4/lines"  },  "livemode": true,  "metadata": {    "order_id": "1710231"  },  "next_payment_attempt": null,  "number": null,  "on_behalf_of": null,  "paid": false,  "paid_out_of_band": false,  "payment_intent": null,  "payment_settings": {    "default_mandate": null,    "payment_method_options": null,    "payment_method_types": null  },  "period_end": **********,  "period_start": **********,  "post_payment_credit_notes_amount": 0,  "pre_payment_credit_notes_amount": 0,  "quote": null,  "receipt_number": null,  "rendering_options": null,  "shipping_cost": null,  "shipping_details": null,  "starting_balance": 0,  "statement_descriptor": null,  "status": "draft",  "status_transitions": {    "finalized_at": null,    "marked_uncollectible_at": null,    "paid_at": null,    "voided_at": null  },  "subscription": null,  "subtotal": 0,  "subtotal_excluding_tax": 0,  "tax": null,  "test_clock": null,  "total": 0,  "total_discount_amounts": [],  "total_excluding_tax": 0,  "total_tax_amounts": [],  "transfer_data": null,  "webhooks_delivered_at": **********} <STRIPEPAYRESPONSE>{  "id": "in_1NBKvTJQgAIihRqAtGVOLxR4",  "object": "invoice",  "account_country": "US",  "account_name": "Sophio LLC",  "account_tax_ids": null,  "amount_due": 4760,  "amount_paid": 4760,  "amount_remaining": 0,  "amount_shipping": 0,  "application": null,  "application_fee_amount": null,  "attempt_count": 1,  "attempted": true,  "auto_advance": false,  "automatic_tax": {    "enabled": false,    "status": null  },  "billing_reason": "manual",  "charge": "ch_3NBKvUJQgAIihRqA0FjmRYK6",  "collection_method": "send_invoice",  "created": **********,  "currency": "usd",  "custom_fields": null,  "customer": "cus_MjA9sZDAiIcXv6",  "customer_address": {    "city": "Maitland",    "country": "US",    "line1": "2130 Chippewa Trail",    "line2": null,    "postal_code": "32751",    "state": "FL"  },  "customer_email": "<EMAIL>",  "customer_name": "Larry Griller",  "customer_phone": "+***********",  "customer_shipping": null,  "customer_tax_exempt": "none",  "customer_tax_ids": [    {      "type": "us_ein",      "value": "88-3918333"    }  ],  "default_payment_method": null,  "default_source": null,  "default_tax_rates": [],  "description": "Invoice: 1710231 Fulfillment by Sophio",  "discount": null,  "discounts": [],  "due_date": 1685033559,  "ending_balance": 0,  "footer": null,  "from_invoice": null,  "hosted_invoice_url": "https://invoice.stripe.com/i/acct_1KqK0GJQgAIihRqA/live_YWNjdF8xS3FLMEdKUWdBSWloUnFBLF9OeEZRaUhlTmFrQVBOamlYdjdNekhHSnJDVmw1WDczLDc1NDg3OTYz0200HC1WZn15?s=ap",  "invoice_pdf": "https://pay.stripe.com/invoice/acct_1KqK0GJQgAIihRqA/live_YWNjdF8xS3FLMEdKUWdBSWloUnFBLF9OeEZRaUhlTmFrQVBOamlYdjdNekhHSnJDVmw1WDczLDc1NDg3OTYz0200HC1WZn15/pdf?s=ap",  "last_finalization_error": null,  "latest_revision": null,  "lines": {    "object": "list",    "data": [      {        "id": "il_1NBKvTJQgAIihRqAuJbKoRc3",        "object": "line_item",        "amount": 4760,        "amount_excluding_tax": 4760,        "currency": "usd",        "description": null,        "discount_amounts": [],        "discountable": true,        "discounts": [],        "invoice_item": "ii_1NBKvTJQgAIihRqAdtxiJFfX",        "livemode": true,        "metadata": {},        "period": {          "end": **********,          "start": **********        },        "plan": null,        "price": {          "id": "price_1NBKvTJQgAIihRqACNEkpy6x",          "object": "price",          "active": false,          "billing_scheme": "per_unit",          "created": **********,          "currency": "usd",          "custom_unit_amount": null,          "livemode": true,          "lookup_key": null,          "metadata": {},          "nickname": null,          "product": "prod_NxFQJ7LOMKIIq2",          "recurring": null,          "tax_behavior": "unspecified",          "tiers_mode": null,          "transform_quantity": null,          "type": "one_time",          "unit_amount": 4760,          "unit_amount_decimal": "4760"        },        "proration": false,        "proration_details": {          "credited_items": null        },        "quantity": 1,        "subscription": null,        "tax_amounts": [],        "tax_rates": [],        "type": "invoiceitem",        "unit_amount_excluding_tax": "4760"      }    ],    "has_more": false,    "total_count": 1,    "url": "/v1/invoices/in_1NBKvTJQgAIihRqAtGVOLxR4/lines"  },  "livemode": true,  "metadata": {    "order_id": "1710231"  },  "next_payment_attempt": null,  "number": "7A9A39BC-0749",  "on_behalf_of": null,  "paid": true,  "paid_out_of_band": false,  "payment_intent": "pi_3NBKvUJQgAIihRqA0o2ngQce",  "payment_settings": {    "default_mandate": null,    "payment_method_options": null,    "payment_method_types": null  },  "period_end": **********,  "period_start": **********,  "post_payment_credit_notes_amount": 0,  "pre_payment_credit_notes_amount": 0,  "quote": null,  "receipt_number": null,  "rendering_options": null,  "shipping_cost": null,  "shipping_details": null,  "starting_balance": 0,  "statement_descriptor": null,  "status": "paid",  "status_transitions": {    "finalized_at": 1684947160,    "marked_uncollectible_at": null,    "paid_at": 1684947161,    "voided_at": null  },  "subscription": null,  "subtotal": 4760,  "subtotal_excluding_tax": 4760,  "tax": null,  "test_clock": null,  "total": 4760,  "total_discount_amounts": [],  "total_excluding_tax": 4760,  "total_tax_amounts": [],  "transfer_data": null,  "webhooks_delivered_at": **********}</STRIPEPAYRESPONSE>';
preg_match('/Trans Id:(\w+)/', $string,$matches );
dd($matches);
config(['tenant_db'=>'sophio_fbs']);
$settings = new \Sophio\Common\Repository\Settings([]);
$store =\Sophio\Common\Models\FBS\Store::where('STOREPK',config('sophio.admin.default_store'))->first();
$settings->setSettingsByStore($store);
$c = new \Sophio\Corcentric\Library\CorcentricManager($settings,$store);
//$c->submitTransaction(\Sophio\Common\Models\FBS\Invoice::find(1683821));
$c->setInvoice(\Sophio\Common\Models\FBS\Invoice::find(1683821));
$c->submitTransactionResponse('<?xml version="1.0"?><corResponse xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><corRequestID>527273-1683494</corRequestID><corRequestType>S</corRequestType><corVendorCode>ADNB9999</corVendorCode><corCustomerCode>AZNP1808</corCustomerCode><corCommunityCode>ADN</corCommunityCode><corAuthorizationCode /><corTransactionType>P</corTransactionType><corTransactionNumber>1683494</corTransactionNumber><corTransactionDate>20230406</corTransactionDate><corPurchaseOrderNumber>13672717</corPurchaseOrderNumber><corPurchaseOrderDate /><corTransactionAmount>2.61</corTransactionAmount><corAuthorizationAmount>2.61</corAuthorizationAmount><corCurrencyCode>USD</corCurrencyCode><corPointOfSale><corPointOfSaleName /><corPointOfSaleAddress1 /><corPointOfSaleAddress2 /><corPointOfSaleCity /><corPointOfSaleStateProvince /><corPointOfSalePostalCode /><corPointOfSaleCountryCode /></corPointOfSale><corReferences><corReference><corReferenceType>DT</corReferenceType><corReferenceValue>Delivered</corReferenceValue></corReference></corReferences><corSections><corSection><corSectionNumber>1</corSectionNumber><corComments><corComment><corSectionCommentSequence>1</corSectionCommentSequence><corSectionCommentType>NOT</corSectionCommentType><corSectionComment>Customer Ship To: AUTOZONE  SHARON  1808 - Tracking number: FDX:396721626410 </corSectionComment></corComment></corComments><corLineDetails><corLineDetail><corLineDetailSequence>1</corLineDetailSequence><corLineDetailResponseStatus>5</corLineDetailResponseStatus><corLineDetailResponseUnitPrice>2.6100</corLineDetailResponseUnitPrice><corLineDetailResponseCorePrice>0.0000</corLineDetailResponseCorePrice><corLineDetailResponseComment>Part Number not found in the system, price validation not performed: BRFC7
</corLineDetailResponseComment><corLineDetailType>P</corLineDetailType><corLineDetailItem>BRFC7</corLineDetailItem><corLineDetailManufacturerCode>MOT</corLineDetailManufacturerCode><corLineDetailDescription>CAP - FILLER             3L8Z2</corLineDetailDescription><corLineDetailVMRSCode /><corLineDetailQuantity>1</corLineDetailQuantity><corLineDetailUnitPrice>2.61</corLineDetailUnitPrice><corLineDetailCorePrice>0</corLineDetailCorePrice><corLineDetailFET /><corLineDetailNotes><corLineDetailNote /></corLineDetailNotes><corLineDetailUOM>EA</corLineDetailUOM></corLineDetail><corLineDetail><corLineDetailSequence>2</corLineDetailSequence><corLineDetailResponseStatus>5</corLineDetailResponseStatus><corLineDetailResponseUnitPrice>2.6100</corLineDetailResponseUnitPrice><corLineDetailResponseCorePrice>0.0000</corLineDetailResponseCorePrice><corLineDetailResponseComment>Part Number not found in the system, price validation not performed: BRFC7
</corLineDetailResponseComment><corLineDetailType>P</corLineDetailType><corLineDetailItem>BRFC7</corLineDetailItem><corLineDetailManufacturerCode>MOT</corLineDetailManufacturerCode><corLineDetailDescription>CAP - FILLER             3L8Z2</corLineDetailDescription><corLineDetailVMRSCode /><corLineDetailQuantity>1</corLineDetailQuantity><corLineDetailUnitPrice>2.61</corLineDetailUnitPrice><corLineDetailCorePrice>0</corLineDetailCorePrice><corLineDetailFET /><corLineDetailNotes><corLineDetailNote /></corLineDetailNotes><corLineDetailUOM>EA</corLineDetailUOM></corLineDetail></corLineDetails></corSection></corSections><corTaxes /><corBaseImage xsi:nil="true" /><corResponseID>1124372</corResponseID><corResponseStatusCode>2</corResponseStatusCode><corResponseMessages><corResponseMessage><corResponseMessageType>2</corResponseMessageType><corResponseMessageCode>88</corResponseMessageCode><corResponseMessageComment>Part Number not found in the system, price validation not performed BRFC7</corResponseMessageComment></corResponseMessage></corResponseMessages></corResponse>',
    );
exit();
config(['tenant_db'=>'sophio_fbs']);
$dim_service = new DimensionsService();
$invoice = \Sophio\Common\Models\FBS\Invoice::findOrFail(21570666);
$dim_lines = [];
foreach($invoice->lineitem as $line) {
    $dim_lines[] = [
        'width' => $line->skuwidth,
        'depth' => $line->skuheight,
        'length' => $line->skulength,
        'weight' => $line->weight,
        'qty' => (int)$line->qty
    ];
}
$dims = $dim_service->simpleOnePack($dim_lines);
dd($dims);
$h = new \Sophio\FBSInventoryImporter\Library\Actions\DataFeeds\HistoryFeeds();
$h();
exit();
$t = \Sophio\Common\Models\FBS\ProductWarehouse::with(['productfeed'=>function($query){
    $query->with('hio')->whereNotNull('omsid')
        ->whereNotIn('pt_num',[2476, 19188])
        ->where(function($x) {
            $x->whereNull('weight')->orWhere('weight','<',100);
        })
        ->whereHas('hio');
}])->whereHas('productfeed',function($query){
    $query->with('hio')->whereNotNull('omsid')
        ->whereNotIn('pt_num',[2476, 19188])
        ->where(function($x) {
            $x->whereNull('weight')->orWhere('weight','<',100);
        })
        ->whereHas('hio');
})->whereHas('marketplaceSupplier',function($query){
    $query->where('market','HD');
})->groupByRaw('product_warehouse.part_number_unformatted,mfgcode')->select(DB::raw('sum(qty_avail) as sum_qty,product_sku'));
$t->chunk(10000,function($parts) {
    foreach ($parts as $part) {
        if (isset($part->productfeed->hio)) {
            echo ".";
        }
    }
});
exit();
$gtin =  $upc = $s = "0096361549654";

if(strlen($s)<14) {
    $gtin = str_pad($s, 14, "0", STR_PAD_LEFT);
}
if(strlen($s)<12) {
    $upc = str_pad($s, 12, "0", STR_PAD_LEFT);
}else{
    if (preg_match('/[^0]]/u', substr($s,0,mb_strlen($s)-12),$m)===false) {

    }else{
        $upc = substr($s, -12);
    }

}
dd([ $m,substr($s,0,mb_strlen($s)-12),$upc,\IsoCodes\Gtin12::validate($upc),\IsoCodes\Gtin14::validate($gtin)]);
exit();
$candidates =new \Sophio\Common\Models\PIM\Product();
foreach($candidates->lazy() as $candidate) {
    if($candidate->gtin!="" && $candidate->gtin!=null )
    if(\Sophio\Common\Services\IsoCodesService::extractGtin($candidate->gtin)===false) {
        echo $candidate->gtin." invalid\n";
    }
}




exit();
$candidate = \Sophio\Common\Models\PIM\Product::where('mfg_code','WIX')->where('part_number_unformatted','51515')->first();
if(Cache::has('country_'.$candidate->countryoforigin)) {
    dd('cache');
}
Cache::set('country_'.$candidate->countryoforigin, $candidate->country);
dd(Cache::get('country_'.$candidate->countryoforigin));
exit();
$ftpuser = \Sophio\Common\Models\FTPUser::firstOrCreate(['username'=>'user2','pass'=>\Illuminate\Support\Facades\DB::raw("PASSWORD('thepassword')")]);
$ftpuser->save();
\App\Library\Sophio\Jobs\System\LocalFTPPAMUser::dispatch($ftpuser->username,'create');

echo $hour = Carbon::now()->hour;
exit();
event(new AdminQueueMailEvent('QueueJobGeneral', ['<EMAIL>'], [
    'subject' => 'Hello there',
    'job_name' => '',

    'description' => 'Hooray'
]));
exit();
$date = Carbon::now();
$collection = collect();
$collection->add(['linecode'=>'WIX','partno'=>'51515','qty'=>1,'mfgsku'=>'WIX-51515']);
$collection->add(['linecode'=>'WIX','partno'=>'61515','qty'=>0,'mfgsku'=>'WIX-61515']);
$collection->add(['linecode'=>'WIX','partno'=>'71515','qty'=>0,'mfgsku'=>'WIX-71515']);
$collection = $collection->filter(function($value,$key){return $value['mfgsku']!='WIX-61515';});
foreach($collection as $item) {
    print_r($item);
}
dd($collection->values()->toArray());
/*
$actions = new ActionsManager(\Config::get('tenant_db'), 'db_from_fbs');
$actions->applyRules();
(new \App\Library\Sophio\Actions\SyncCentralToFBS())();
exit();
$x = new \Sophio\FBSInventoryImporter\Library\Strategies\FileNameLookup\DateInFilename();
dd($x->getFilenameLookup('INVXLP520079YYYYMMDD.txt',$date));
*/