<?php

use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Sophio\Walmart\Jobs\CheckFeed;
use Sophio\Walmart\Library\Feeds\CostFeeds;
use Sophio\Walmart\Library\ItemsFeeds;

require __DIR__ . '/../vendor/autoload.php';

$app = new Illuminate\Foundation\Application(
    realpath(__DIR__ . '/..')
);
$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);
$app->make(App\Console\Kernel::class)->bootstrap();
$app->boot();
config(['tenant_db'=>'sophio_fbs']);
$buyer_profile = \Sophio\Common\Models\FBS\BuyerProfile::find(1);
$settings =  new \Sophio\Common\Repository\Settings();
$sup = \Sophio\Common\Models\FBS\Supplier::find(971);
$costfeeds = new CostFeeds(  ['tenant_db'=>'sophio_fbs','buyer_db' => $buyer_profile->customer->xml['ACESPARTSDB']]);
$m = $costfeeds->modelBySupplierFull($sup);
dd($m->get()->count());
config(['logging.default'=>'walmart']);
Config::set('tenant_db', 'sophio_fbs');

$feeds = new ItemsFeeds();
$feeds->setSettings( [
    'mfgcode' => '',
    'contactpk' => '',
    'onlynew' => 'Yes',
    'batch_size' => 1000,
    'limit' => 1000,
    'total_limit' => 1000,
    'image_source' => 'hosted',
    'nofeeditem' => 'yes',
    'feed_delay' => 120,
    'mailto' => [config('sophio.walmart.mail_senders')]
]);
$partgrabber = $feeds->getCandidates();
$total = $partgrabber->count();

exit();
$feeds = new ItemsFeeds();

$feeds->setSettings($settings);
$partgrabber = $feeds->getCandidates();
dd($partgrabber->count());
$partgrabber->setOffset($settings['offset']);
$partgrabber->setCondition(function ($candidate) {
    $candidate->whereHas('walmartfeeditem',function($query){
        $query->where('data','like','%3440047.jpg%')->where('updated_at','<','2022-07-14 00:00:00')->where('itemid','>',0);
    });
    return $candidate;
});
$partgrabber->execute();
$parts = $partgrabber->getParts();
if(is_array($parts))
foreach($parts as $part)
{
    $part->candidate->walmartfeeditem->updated_at = \Carbon\Carbon::now();
    $part->candidate->walmartfeeditem->save();
}

$feed = $feeds->sendFeed($parts);
if($feed && $feed->feedid!="" && $feed->feedid!=null) {
    Log::channel('walmart')->error('Dispatching to later check'.$feed->feedid);
    CheckFeed::dispatch($feed)->delay(now()->addMinutes(120));
}
/*
$settings = [
    'mfgcode' => null,
    'onlynew' => 'Yes',
    'contactpk' => 202379,
    'batch_size' => 1,
    'limit' => 1,
    'total_limit' =>1,
    'image_source' =>'hosted',
    'offset' => 0

];
$feeds = new \Sophio\Walmart\Library\Feeds();
$feeds->setSettings($settings);
$partgrabber = $feeds->getCandidates();
$partgrabber->setOffset($settings['offset']);
$partgrabber->execute();

$feed = $feeds->sendFeed($partgrabber->getParts(),true);

*/