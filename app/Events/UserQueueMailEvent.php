<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UserQueueMailEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;
    public $messages;
    public $emails;
    public $cc;
    public $mailClass;
    /**
     * Create a new event instance.
     *
     * @return void
     */

    public function __construct($mailClass,$emails,$messages,$cc=[])
    {
        $this->mailClass = $mailClass;
        $this->emails = $emails;
        $this->messages = $messages;
        $this->cc = $cc;
        //
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}
