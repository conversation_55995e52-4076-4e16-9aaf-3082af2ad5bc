<?php

namespace App\Http\Controllers\User;

class MyAccountController extends \Backpack\CRUD\app\Http\Controllers\MyAccountController
{
    public function getAccountInfoForm()
    {
        $this->data['title'] = trans('backpack::base.my_account');
        $this->data['user'] = $this->guard()->user();

        return view(backpack_view('my_account'), $this->data);
    }
    public function getMySettings()
    {
        return   redirect(sophio_route('fbs/supplier.edit',['id'=>backpack_user()->user_key]));

    }
}