<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Sophio\Common\Actions\SupplierLogCreate;
use Sophio\Common\Models\FBS\Customer;
use Sophio\Common\Models\FBS\Invoice;
use Sophio\Common\Models\FBS\LineItem;
use Sophio\Common\Models\FBS\Store;
use Sophio\Common\Repository\Settings;
use Sophio\FBSOrder\Library\Repository\OrderAPI;
use Sophio\FBSReturns\src\Models\ReturnReasons;

class OrderController extends Controller
{

    protected Store $store;
    protected Customer $customer;

    protected function getSettings(): Settings
    {
        config(['tenant_db'=>config('sophio.admin.default_database')]);
        $old_settings = [];
        if(request()->get('customerId','')!=='') {

            $customer = Customer::find(request()->get('customerId'));
            if($customer) {
                $old_settings['customerId'] = request()->get('customerId');
                $this->customer  = Customer::find(request()->get('customerId'));
            }else{
                $old_settings['customerId'] = sophioapi()->getApi()->customer->pk;
                $this->customer  = sophioapi()->getApi()->customer;
            }

        }else{
            $old_settings['customerId'] = sophioapi()->getApi()->customer->pk;
            $this->customer  = sophioapi()->getApi()->customer;
        }

        $old_settings['fbs_database'] = config('sophio.admin.default_database');

        $old_settings['seller_database'] =  $this->customer->xml['ACESPARTSDB'] ?? config('sophio.admin.default_database');;
        $store = Store::where('STOREPK',  $this->customer->storepk)->first();

        if ($store) {
            $this->store = $store;
        } else {
            (new SupplierLogCreate())([
                'storepk' => '',
                'timein' => now(),
                'accountnum' => $this->customer->accountnum??'',
                'apitype' => 'SophioApi',
                'action' => url()->current(),
                'request' => request()->getContent(),
                'clientip' => request()->server('REMOTE_ADDR'),
                'response' => json_encode(['error' => 'Wrong Store PK', 'success' => false])
            ]);
            echo response(json_encode(['error' => 'Wrong Store PK', 'success' => false]), 401);
            exit();
        }
        $settings = new Settings($old_settings, $this->store);


        $settings->setSettingsByCustomer($this->customer);
        // $settings->set('datafeed_table', 'partsgeek_datafeed');
        $settings->set('customerId',  $this->customer->pk);
        $settings->set('userId',  $this->customer->pk);
        $settings->set('noadjustedcost', false);
        $settings->set('nodatafeedcost', false);
        $settings->set('nopspcost', false);
        $settings->set('order_database', sophioapi()->getDB());
        if (request()->get('postalcode') === "T") {
            $settings->set('return_postalcode', true);
        }
        if (request()->get('contactpk') === "T") {
            $settings->set('return_contactpk', true);
        }
        config(['customerPk'=>  $this->customer->pk]);

        return $settings;
    }

    public function returnrequest(Request $request)
    {
        $settings = $this->getSettings();
        $orderAPIservice = new OrderAPI($settings, $this->store, $this->customer);
        $order_raw_request = request()->getContent();
        $ok = $orderAPIservice->returnRequest($order_raw_request);
        if($ok===true){
            return response($orderAPIservice->getSupplierLog()->response);
        }else{
            return response($orderAPIservice->getSupplierLog()->response, 400);
        }

    }

    public function checkstock(Request $request)
    {
        ini_set('max_execution_time', 120);
        $settings = $this->getSettings();
        if($this->customer->custtype=='MEM') {
            $settings->set('ManagerApproval', 2);
        }
        return (new OrderAPI($settings, $this->store, $this->customer))->stockCheck($request->json()->all());
    }

    public function getReturnReasons()
    {
        return response()->json(ReturnReasons::select(['pk as retcode', 'name as retname', 'help as long_description'])->get());
    }

    public function order()
    {
        $settings = $this->getSettings();
        $order_raw_request = request()->getContent();
        try {
            $order_request = json_decode($order_raw_request, true, 512, JSON_THROW_ON_ERROR);
        }catch (\JsonException  $e) {
            (new SupplierLogCreate())([
                'storepk' => '',
                'timein' => now(),
                'accountnum' =>'',
                'apitype' => 'SophioApi',
                'action' => 'order',
                'request' => request()->getContent(),
                'clientip' => request()->server('REMOTE_ADDR'),
                'response' => json_encode(['error' => 'Empty or malformed body. Please send a valid JSON!', 'success' => false])
            ]);
            echo response(json_encode(['error' => 'Empty or malformed body. Please send a valid JSON!', 'success' => false]), 400);
            exit();
        }

        if (isset($order_request['storepk']) && (int)$order_request['storepk']!==(int)$this->store->STOREPK) {
            $store= Store::where('STOREPK', $order_request['storepk'])->first();
            if($store) {
                $this->store = $store;
            }else{
                $this->store = Store::where('STOREPK', config('sophio.admin.default_store'))->first();
            }
            $settings->setSettingsByStore($this->store);
        }
        if ($order_request['customerId'] !== $this->customer->pk && (int)sophioapi('allow_proxy') === 1) {
            $customer = Customer::find($order_request['customerId']);

            if ($customer == null) {
                (new SupplierLogCreate())([
                    'storepk' => '',
                    'timein' => now(),
                    'accountnum' =>'',
                    'apitype' => 'SophioApi',
                    'action' => 'order',
                    'request' => request()->getContent(),
                    'clientip' => request()->server('REMOTE_ADDR'),
                    'response' => json_encode(['error' => 'Unrecognized customerId. Please contact Sophio Support.', 'success' => false])
                ]);
                echo response(json_encode(['error' => 'Unrecognized customerId. Please contact Sophio Support.', 'success' => false]), 400);
                exit();
            } else {
                $this->customer = $customer;
            }
            $settings->setSettingsByCustomer($this->customer);
            $settings->set('customerId',$this->customer->pk);
            $settings->set('order_database', sophioapi()->setByCustomer($customer, $this->store->STOREPK)->db);
            $settings->set('noadjustedcost', false);
            $settings->set('nodatafeedcost', false);
            $settings->set('nopspcost', false);

            if (request()->get('postalcode') === "T") {
                $settings->set('return_postalcode', true);
            }
            if (request()->get('contactpk') === "T") {
                $settings->set('return_contactpk', true);
            }
            Config::set(['customerPk' => $order_request['customerId']]);
            Config::set(['storePk' => $this->store->STOREPK]);

        }else{
            $customer = Customer::find($order_request['customerId']);
            if ($customer == null) {
                Config::set(['customerPk' => $this->customer->pk]);
            }else{
                Config::set(['customerPk' => $order_request['customerId']]);
            }

            Config::set(['storePk' => $this->store->STOREPK]);
        }

        $orderAPIservice = new OrderAPI($settings, $this->store, $this->customer);

        $ok = $orderAPIservice->createOrder($order_raw_request);
        if($ok===true) {
            return response($orderAPIservice->getSupplierLog()->response);
        }else{
            return response($orderAPIservice->getSupplierLog()->response, 400);
        }


    }
    public function getinvoices()
    {
        $settings = $this->getSettings();
        $orderAPIservice = new OrderAPI($settings, $this->store, $this->customer);
        if (request()->get('when') == "" && request()->get('invpk') == "" && request()->get('ponumber') == "") {
            return response()->json([]);
        } else {
            $credits = $orderAPIservice->getInvoces(request()->get('when'), request()->get('invpk'), request()->get('ponumber'));
            return response()->json($credits);

        }
    }

    public function getcredits()
    {
        $settings = $this->getSettings();
        $orderAPIservice = new OrderAPI($settings, $this->store, $this->customer);
        if (request()->get('when') == "" && request()->get('invpk') == "" && request()->get('ponumber') == "") {
            return response()->json([]);
        } else {
            $credits = $orderAPIservice->getCredits(request()->get('when'), request()->get('invpk'), request()->get('ponumber'));
            return response()->json($credits);

        }
    }

    public function trackingapi()
    {
        $settings = $this->getSettings();
        $orderId = request()->get('orderId');
        $po_number = request()->get('customerpoNumber');
        $invoiceNumber = request()->get('invoiceNumber');
        if ($orderId === null && $po_number ==null && $invoiceNumber === null) {
            $response = response()->json(['success' => false, 'message' => 'Request has no order Id, po number or invoice number!'], 200, [], JSON_PRESERVE_ZERO_FRACTION);
            (new SupplierLogCreate())([
                'storepk' => Config::get('storePk'),
                'timein' => now(),
                'accountnum' => $this->customer->accountnum,
                'apitype' => 'SophioApi',
                'action' => 'ordercheck',
                'request' => request()->getContent(),
                'clientip' => request()->server('REMOTE_ADDR'),
                'response' => $response->getContent()
            ]);
            return $response;
        }
        $orderAPIservice = new OrderAPI($settings, $this->store, $this->customer);
        if ($orderId !==null) {
            $orderAPIservice->tracking($orderId, 'orderId');
        } elseif ($po_number !== null) {
            $orderAPIservice->tracking($po_number, 'poNumber');
        } else {
            $orderAPIservice->tracking($invoiceNumber, 'invno');
        }
        return response($orderAPIservice->getSupplierLog()->response,200,['Content-Type'=>'application/json']);

    }

    public function insertinvoice()
    {
        Config::set('tenant_db', request()->get('database'));
        $invs = request()->json()->all();
        foreach ($invs as $inv) {

            Invoice::insertOrIgnore($inv);
        }
        return response()->json(['success' => 'ok']);
    }

    public function insertlineitem()
    {
        Config::set('tenant_db', request()->get('database'));
        $invs = request()->json()->all();
        foreach ($invs as $inv) {

            LineItem::insertOrIgnore($inv);
        }
        return response()->json(['success' => 'ok']);
    }

    public function insertcustomer()
    {
        Config::set('tenant_db', request()->get('database'));
        $invs = request()->json()->all();
        foreach ($invs as $inv) {

            Customer::insertOrIgnore($inv);
        }
        return response()->json(['success' => 'ok']);
    }
}