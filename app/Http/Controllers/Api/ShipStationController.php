<?php

namespace App\Http\Controllers\Api;

use Illuminate\Support\Facades\Log;
use LaravelShipStation\Models\Order;
use Sophio\Common\Models\FBS\Invoice;
use Sophio\Common\Models\FBS\Supplier;
use Sophio\Common\Repository\Settings;
use Sophio\Common\ShipStation\src\Library\HookManager;
use Sophio\Common\ShipStation\src\Library\ShipStationManager;

class ShipStationController
{
    public function index()
    {
        
        $settings = new Settings();

        $ssManager = new ShipStationManager($settings);
        if(request()->get('profilepk','')=='') {

        }else{
            $supplier = Supplier::where('PK',request()->get('profilepk'))->first();
            $ssManager->setSupplierCreds($supplier,true);
            Log::channel('shipstation')->error('set supplier '.request()->get('profilepk'));
        }
    
        
        $ssManager->createClient();

        $hookManager = new HookManager($ssManager,$settings);
        $type = request()->get('action');
        switch ($type) {
            case 'SHIP_NOTIFY':
                Log::channel('shipstation')->error('SHIP_NOTIFY');
                Log::channel('shipstation')->error(request()->json()->all());
                $hookManager->ShipNotify(request()->json()->all());
                break;
            case 'ITEM_SHIP_NOTIFY' :
                Log::channel('shipstation')->error('ITEM_SHIP_NOTIFY');
                Log::channel('shipstation')->error(request()->json()->all());
                $hookManager->ItemShipNotify(request()->json()->all());
                break;
        }
        return response()->json([]);
    }
    public function getfreightestimate()
    {
        $settings = new Settings();
        $ssManager = new ShipStationManager($settings);
        $supplier  =Supplier::find(753);
        $ssManager->setSupplierCreds($supplier);
        $ssManager->createClient();
        $invoice = new Invoice();
        $order = new Order();
        $invoice->st_zip = request()->get('ship_zip');
        $invoice->st_ctryid = request()->get('st_CtryID');
        $carriers = ['fedex'=>'fedex','stamps_com'=>'stamps_com','ups'=>'ups'];
        foreach($carriers as $carrier)
        {
          dd($ssManager->getRates($order, $invoice, $supplier, ['carrierCode' => $carrier]));
        }
    }
}