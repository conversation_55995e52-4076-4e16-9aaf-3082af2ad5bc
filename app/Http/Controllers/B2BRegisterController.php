<?php

namespace App\Http\Controllers;

use App\Mail\GeneralMail;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Sophio\Common\Models\FBS\Customer;
use Sophio\Common\Models\FBS\Pricing;
use Sophio\Common\Repository\Settings;
use Sophio\Common\Services\TwilioService;
use Sophio\Common\Services\VerifyCodeService;
use Illuminate\Http\Request;

class B2BRegisterController extends Controller
{
    public function __construct()
    {
        $this->middleware('guest');
    }

    protected function validator(array $data)
    {
        return Validator::make($data, [
            'email' => ['required', 'string', 'email', 'max:255', 'unique:Sophio\Common\Models\FBS\Customer' ],
            'file' => ['required','mimes:pdf']
        ]);

    }

    public function index(Request $request)
    {
         if (request()->post()) {
            $this->validator($request->all())->validate();
            $remote_filename = request()->file('file')->getClientOriginalName();

            Storage::disk('tmp')->putFileAs('', request()->file('file'), $remote_filename);
            session(['b2bregister' => request()->except('file')]);
            session(['remote_filename' => $remote_filename]);

            $newuser = request()->session()->get('newuser');

            /*
            try {
                $ts = new TwilioService($settings);
                $ts->sendMessage($settings->get('TWILIO_FROM', env('TWILIO_FROM')), request()->get('phone'), 'Your  verify code is ' . $verificationcode);

            } catch (\Throwable  $e) {
                throw ValidationException::withMessages(['phone' => 'Phone number is in incorrect format.']);
            }
            */
            $vs = new VerifyCodeService();
            $vs->inSession()->generateNewCode();
            $verificationcode = rand(1000, 9999);
            $newuser['verificationcode'] = $verificationcode;
            $newuser['verificationtime'] = Carbon::now()->addMinutes(10);
            request()->session()->put('newuser', $newuser);
            return view('nonlogged.b2bregister.verifycode', ['newuser' => $newuser]);

        }
        return view('nonlogged.b2bregister.b2bregister');
    }

    public function resendverify(Request $request)
    {
        $vs = new VerifyCodeService();
        $vs->inSession()->generateNewCode();
        $verificationcode = rand(1000, 9999);
        $newuser['verificationcode'] = $verificationcode;
        $newuser['verificationtime'] = Carbon::now()->addMinutes(10);
        request()->session()->put('newuser', $newuser);
        return view('nonlogged.b2bregister.verifycode', ['newuser' => $newuser]);
    }

    public function verify(Request $request)
    {
        $settings =new Settings();
        if (request()->post()) {
            $vs = new VerifyCodeService();
            $vs->loadFromSession();
            Validator::make([
                'verifycode' => request()->get('verifycode'),
                'time' => Carbon::now()
            ], [
                'verifycode' => ['required', 'in:' . $vs->getCode()],
                'time' => ['required', 'before_or_equal' . $vs->getTime()]
            ]);
            $customer = $this->create();
            $mail = new GeneralMail([
                'subject' => 'Thank you for registering',
                'contentView' => 'emails.b2bregister_cust_finish',
                'view' => 'emails.generalmail',
                'content' => [
                    'store'=>$settings->getStore()
                ],
                'custpk' => $customer->pk
            ]);
            if (config('sophio.admin.demo') === true) {
                $emails =  config('sophio.admin.mail_senders.developers');
            } else {
                $emails = [$customer->email];
            }
            Mail::to($emails)->queue($mail);
            $mail = new GeneralMail([
                'subject' => 'New B2B Customer - '.$customer->company,
                'contentView' => 'emails.b2bregister_app',
                'view' => 'emails.generalmail',
                'content' => [
                    'customer'=>$customer
                ],
                'attach' => [
                    Storage::disk('fbs')->path($customer->xml['AGREEMENTFILE'])
                ],
                'custpk' => $customer->pk
            ]);
            if (config('sophio.admin.demo') === true) {
                $emails =  config('sophio.admin.mail_senders.developers');
            } else {
                $emails =  $settings->getStore()->ADVSET['NEWACCOUNTEMAIL']??config('sophio.admin.mail_senders.default_fbs');
            }
            Mail::to($emails)->queue($mail);
            return view('nonlogged.b2bregister.finish', ['customer' => $customer]);
        }
        $vs = new VerifyCodeService();
        $vs->inSession()->generateNewCode();
        $verificationcode = random_int(1000, 9999);
        $newuser['verificationcode'] = $verificationcode;
        $newuser['verificationtime'] = Carbon::now()->addMinutes(10);
        request()->session()->put('newuser', $newuser);
        return view('nonlogged.b2bregister.verifycode', ['newuser' => $newuser]);
    }

    public function create()
    {
        $data = session()->get('b2bregister');
        $customer = Customer::create(
            [
                'storepk'=>sophiosettings()->getStore()['STOREPK'],
                'email' => $data['email'],
                'company' => $data['company'],
                'firstname' => $data['firstname'],
                'lastname' => $data['lastname'],
                'address' => $data['address'],
                'address2' => $data['address2'] ?? '',
                'city' => $data['city'],
                'state' => $data['state'],
                'country' => $data['countryid'],
                'countryid' => $data['countryid'],
                'zip' => $data['zip'],
                'phone' => $data['phone'],
                'mobile' => $data['phone'],
                'custtype' => $data['custtype'],
                'cctype' => $data['cctype'],
                'xml' => ['ANNUALPVOL' => $data['annualpvol'], 'ordmethod' => $data['ordmethod']],
                'notes' => $data['notes'],
                'heardfrom' => $data['referral'],
                'url' => $data['url'],
                'accountnum' => $data['accountnum'] ?? '',
                'st_name'=>$data['company'],
                'st_addr' => $data['address'],
                'st_addr2' => $data['address2'] ?? '',
                'st_city' => $data['city'],
                'st_state' => $data['state'],
                'st_phone' => $data['phone'],
                'st_zip' => $data['zip'],
                'st_ctryid' => $data['countryid'],
                'st_ctry' => $data['countryid'],
                'active' => 'F',
                'taxid' => $data['taxid'] ?? '',
                'taxexpire' => $data['taxexpire'] ?? '',
                'taxable' =>  $data['taxid']? 1:0,
            ]
        );

        \Illuminate\Support\Facades\File::makeDirectory(config('filesystems.disks.fbs.root') . '/customers/' . $customer->pk, 0755, true);
        Storage::disk('fbs')->put('/customers/' . $customer->pk . '/' . session()->get('remote_filename'), Storage::disk('tmp')->get(session()->get('remote_filename')));
        $customer->xml['AGREEMENTFILE'] = '/customers/' . $customer->pk . '/' . session()->get('remote_filename');
        $customer->xml['AGREEMENTCOMPANYNAME'] = $data['agreement-company'];
        $customer->xml['AGREEMENTADDRESS'] = $data['agreement-address'];
        $customer->xml['AGREEMENTCITY'] = $data['agreement-city'];
        $customer->xml['AGREEMENTSTATE'] = $data['agreement-state'];
        $customer->xml['AGREEMENTZIP'] = $data['agreement-zip'];
        $customer->xml['AGREEMENTDDESCRIBEDITEMS'] = $data['agreement-described-items'];
        $customer->xml['AGREEMENTDDESCRIBEDEXEMPTION'] = $data['agreement-described-exemption'];
        $customer->xml['AGREEMENTTAXID'] = $data['agreement-taxid'];
        $customer->xml['AGREEMENTSIGNATURE'] = $data['agreement-signature'];
        $customer->xml['AGREEMENTDATE'] = $data['agreement-date'];
        $customer->xml['AGREEMENTTITLE'] = $data['agreement-purchaser-title'];


        $customer->userid = $data['b2busername'];
        $customer->password = $data['b2bpassword'];

        $customer->xml['SHIPFROMCOMPANY'] = 'Fulfillment Manager';
        $customer->xml['SHIPFROMNAME'] = $data['company'];
        $customer->xml['SHIPFROMADDRESS'] = '200 Enterprise Road';
        $customer->xml['SHIPFROMADDRESS2'] = '';
        $customer->xml['SHIPFROMCITY'] = 'Somerville';
        $customer->xml['SHIPFROMSTATE'] = 'TN';
        $customer->xml['SHIPFROMPOSTAL'] = '38068';
        $customer->xml['SHIPFROMCOUNTRY'] = 'US';
        $customer->xml['SHIPFROMPHONE'] = '**************';
        $customer->xml['MYSQLPRICING'] = true;
        $customer->xml['ACESPARTSDB'] ='fbn_aces';
        $customer->save();

        $pricing= new Pricing();

        $pricing->setTable('fbn_aces.wws_pricing');
        $pricing->accountnum = $customer->pk;
        $pricing->storepk = $customer->storepk;
        $pricing->entered = Carbon::now();
        $pricing->updated = Carbon::now();
        $pricing->timestamp = Carbon::now();
        $pricing->markdn = 1.00;
        $pricing->markup = 1.00;
        $pricing->markuplist = 1.00;
        $pricing->cstmarkup = 1.081081;
        $pricing->rangelo =0;
        $pricing->rangeHi = 9999999;
        $pricing->marketplc = 'B2B';
        $pricing->shipping  = 0.0;
        $pricing->comment = 'Added by B2B registration';
        $pricing->guid = Str::uuid();
        $pricing->save();
        return $customer;
    }
}