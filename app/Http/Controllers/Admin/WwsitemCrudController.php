<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\WwsitemRequest;
use App\Library\Sophio\Catalog\ProductsIndex;
use App\Models\NeedsContent;
use App\Models\Wwsitem;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Backpack\CRUD\app\Library\Widget;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Route;
use Prologue\Alerts\Facades\Alert;
use Sophio\Common\Controllers\BaseTenantCrudController;
use Sophio\Common\Models\Catalog\LocalBrands;
use Sophio\Common\Models\PIM\Asin;
use Sophio\FBSOrder\Library\Rules\Supplier\ActiveSuppliersNetworkList;

/**
 * Class WwsitemCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class WwsitemCrudController extends BaseTenantCrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation{
        destroy as traitDestroy;
    }
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\BulkDeleteOperation;

    /**
     * Configure the CrudPanel object. Apply settings to all operations.
     *
     * @return void
     */
    public function setup()
    {
        parent::setup();
        Config::set('tenant_db', \Route::current()->parameter('database'));
        CRUD::setModel(\App\Models\Wwsitem::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/dbs/' . \Route::current()->parameter('database') . '/wwsitem');
        CRUD::setEntityNameStrings('wwsitem', \Route::current()->parameter('database') . ' \  wwsitems');
        /*
        $catalog = DB::select("select users.id as user_id,catalogs.* from facetedapi.catalogs
INNER JOIN facetedapi.catalog_user on catalogs.id=catalog_user.catalog_id INNER JOIN facetedapi.users on  
catalog_user.user_id=users.id WHERE catalogs.database='" . \Route::current()->parameter('database') . "'")[0];
        $this->clientId = $catalog->clientId;
        session(['clientId' => $catalog->clientId]);
        */
        $this->crud->enableExportButtons();

    }




    /**
     * Define what happens when the List operation is loaded.
     *
     * @see  https://backpackforlaravel.com/docs/crud-operation-list-entries
     * @return void
     */
    protected function setupListOperation()
    {
        $this->crud->addColumns([
            [
                'name' => 'sku', 'escaped' => false, 'limit' => 1000,
                'value' => function ($entry) {
                    if ($entry->product) {
                        return '<a  target="_blank" href="' . sophio_route('pim/product.edit', ['id' => $entry->product->id]) . '">' . $entry->sku . '
                        ';
                    } else {
                        return $entry->sku;
                    }
                }
            ],
            'linecode', 'BrandID',

            'description',
            'mfr',
            [
                'name' => 'list',
                'label' => 'List',
                'type' => 'number',
                'decimals' => 2, 'orderable' => true
            ],
            'stock',
            'pt_num',
            'category_level1',
            [
                'name' => 'image', // The db column name
                'label' => 'Image', // Table column heading
                'type' => 'image',
           //     'prefix' => 'https://images-us1.sophio.com'
            ],
            ['name' => 'supplier.NAME', 'label' => 'Supplier']
        ]);
        $this->addCustomCrudFilters();
        /**
         * Columns can be defined using the fluent syntax or array syntax:
         * - CRUD::column('price')->type('number');
         * - CRUD::addColumn(['name' => 'price', 'type' => 'number']);
         */
//	CRUD::column('image')->type('image');


    }

    protected function setupShowOperation()
    {
        $this->addCustomCrudFilters();
        $this->crud->addColumns($this->getFields());

        /**
         * Columns can be defined using the fluent syntax or array syntax:
         * - CRUD::column('price')->type('number');
         * - CRUD::addColumn(['name' => 'price', 'type' => 'number']);
         */

    }


    /**
     * Define what happens when the Create operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-create
     * @return void
     */
    protected function setupCreateOperation()
    {
        $this->crud->set('show.setFromDb', false);
        $this->crud->setValidation(WwsitemRequest::class);
        $this->crud->addFields($this->getFields());


        /**
         * Fields can be defined using the fluent syntax or array syntax:
         * - CRUD::field('price')->type('number');
         * - CRUD::addField(['name' => 'price', 'type' => 'number']));
         */
    }

    /**
     * Define what happens when the Update operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-update
     * @return void
     */
    protected function setupUpdateOperation()
    {
        CRUD::setValidation(WwsitemRequest::class);
        $this->setupCreateOperation();
        $this->productTopBar( $this->crud->getEntry($this->crud->getCurrentEntryId()));

    }

    public function getFields()
    {
        return [

            [
                'name' => 'pk',
                'label' => 'Product Key',
                'type' => 'number',
                'tab' => 'Identification'
            ],
            [
                'name' => 'sku',
                'label' => 'SKU',
                'type' => 'text',
                'tab' => 'Identification'
            ],
            [
                'name' => 'model_number',
                'label' => 'Model Number',
                'type' => 'text',
                'tab' => 'Identification'
            ],
            [
                'name' => 'description',
                'label' => 'Description',
                'type' => 'textarea',
                'tab' => 'Identification'
            ],
            [
                'name' => 'product_name',
                'label' => 'Product Name',
                'type' => 'text',
                'tab' => 'Identification'
            ],
            [
                'name' => 'linecode',
                'label' => 'Linecode',
                'type' => 'text',
                'tab' => 'Identification'
            ],
            [
                'name' => 'mfr',
                'label' => 'Manufacturer',
                'type' => 'select_from_array',
                'tab' => 'Identification',
                'options' => (function () {
                    $opts = LocalBrands::get();
                    $r = [];
                    foreach ($opts as $opt) {
                        $r[$opt->mfg_name] = $opt->mfg_name;
                    }
                    return $r;
                })()
            ],
            [
                'name' => 'qty_per_application',
                'label' => 'Qty/Application',
                'type' => 'number',
                'tab' => 'Packaging'
            ],
            [
                'name' => 'uom',
                'label' => 'UOM',
                'type' => 'text',
                'tab' => 'Packaging'
            ],
            [
                'name' => 'gtin',
                'label' => 'GTIN',
                'type' => 'text',
                'tab' => 'Packaging'
            ],
            [
                'name' => 'upc',
                'label' => 'UPC',
                'type' => 'text',
                'tab' => 'Packaging'
            ],
            [
                'name' => 'currency',
                'label' => 'Currency',
                'type' => 'select_from_array',
                'tab' => 'Pricing',
                'options' => ['USD' => 'USD', 'CAD' => 'CAD']
            ],
            [
                'name' => 'stock',
                'label' => 'Stock',
                'type' => 'number', 'tab' => 'Availability'

            ], [
                'name' => 'list',
                'label' => 'List',
                'type' => 'number', 'tab' => 'Pricing',
                'decimals' => 2,
            ], [
                'name' => 'coreprice',
                'label' => 'Core price', 'tab' => 'Pricing',
                'type' => 'number',
                'decimals' => 2,
            ], [
                'name' => 'cost',
                'label' => 'Cost', 'tab' => 'Pricing',
                'type' => 'number',
                'decimals' => 2,
            ],
            [
                'name' => 'minqty',
                'label' => 'Min Qty', 'tab' => 'Availability',
                'type' => 'number'
            ], [
                'name' => 'height',
                'label' => 'Height',
                'type' => 'number',
                'decimals' => 2,
                'tab' => 'Properties'
            ], [
                'name' => 'width',
                'label' => 'Width',
                'type' => 'number',
                'decimals' => 2,
                'tab' => 'Properties'
            ],
            [
                'name' => 'length',
                'label' => 'length',
                'type' => 'number',
                'decimals' => 2,
                'tab' => 'Properties'
            ],
            [
                'name' => 'weight',
                'label' => 'weight',
                'type' => 'number',
                'decimals' => 2,
                'tab' => 'Properties'
            ],
            [
                'name' => 'dimension_uom',
                'label' => 'dimension_uom',
                'type' => 'select_from_array',
                'options' => ['in' => 'in', 'cm' => 'cm'],
                'tab' => 'Properties'
            ],
            [
                'name' => 'weight_uom',
                'label' => 'weight_uom',
                'type' => 'select_from_array',
                'options' => ['lb' => 'lb', 'kg' => 'kg'],
                'tab' => 'Properties'
            ],
            [
                'name' => 'notes',
                'label' => 'notes',
                'type' => 'textarea',
                'tab' => 'Notes'
            ],
            /*
            [
                'name' => 'category_level1',
                'label' => 'Category (level 1)',
                'type' => 'select_from_array', 'tab' => 'Taxonomies',
                'options' => (function () {
                    $opts = Category::get();
                    $r = [];
                    foreach ($opts as $opt) {
                        $r[$opt->CategoryName] = $opt->CategoryName;
                    }
                    return $r;
                })()
            ],
            */
            [
                'name' => 'category_level1',
                'label' => 'Category level 1', 'tab' => 'Taxonomies',
            ],
            /*
            [
                'name' => 'category_level2',
                'label' => 'Category level 2 (subcategory)', 'tab' => 'Taxonomies',
                'type' => 'selectNSubcat',
                'data_source' => url("api/codemaster/subcategories?clientId=" . $this->clientId),
                'parent_attribute' => 'category_level1',
                'parent_key' => 'CategoryName',
                'attribute' => 'SubCategoryName',
                'model' => 'App\Models\NonApps\SubCategory',
                'allows_null' => true, 'minimum_input_length ' => '',
                'placeholder' => ''
            ],
            */
            [
                'name' => 'category_level2',
                'label' => 'Category level 2 (subcategory)', 'tab' => 'Taxonomies',
            ],
            /*
            [
                'name' => 'category_level3',
                'label' => 'Category Level 3 (part type)', 'tab' => 'Taxonomies',
                'type' => 'selectNSubcat',
                'data_source' => url("api/codemaster/parts?clientId=" . $this->clientId),
                'parent_attribute' => 'category_level2',
                'parent_key' => 'SubCategoryName',
                'attribute' => 'PartTerminologyName',
                'model' => 'App\Models\NonApps\Part',
                'allows_null' => true, 'minimum_input_length ' => '',
                'placeholder' => ''
            ],
            */
            [
                'name' => 'category_level3',
                'label' => 'Category Level 3 (part type)', 'tab' => 'Taxonomies',
                ],
            [
                'name' => 'ptype',
                'label' => 'ptype', 'tab' => 'Taxonomies',
                'type' => 'text'
            ],
            [
                'name' => 'contactpk',
                'label' => 'contactpk', 'tab' => 'Supplier Info',
                'type' => 'number'
            ],
            [
                'name' => 'zipcode',
                'label' => 'zipcode', 'tab' => 'Supplier Info',
                'type' => 'text'
            ],

            [ // image
                'label' => 'Main image', 'tab' => 'Media',
                'name' => 'image',
                'type' => 'image',
                'upload' => true,
                'crop' => true, // set to true to allow cropping, false to disable
                'disk' => 'imagespool'

            ],
            [ // image
                'label' => 'Image 2', 'tab' => 'Media',
                'name' => 'image2',
                'type' => 'image',
                'upload' => true,
                'crop' => true, // set to true to allow cropping, false to disable
                'disk' => 'imagespool'

            ],
            [ // image
                'label' => 'Image 3', 'tab' => 'Media',
                'name' => 'image3',
                'type' => 'image',
                'upload' => true,
                'crop' => true, // set to true to allow cropping, false to disable
                'disk' => 'imagespool'

            ],
            [ // image
                'label' => 'Image 4', 'tab' => 'Media',
                'name' => 'image4',
                'type' => 'image',
                'upload' => true,
                'crop' => true, // set to true to allow cropping, false to disable
                'disk' => 'imagespool'

            ],
            [ // image
                'label' => 'Image 5', 'tab' => 'Media',
                'name' => 'image5',
                'type' => 'image',
                'upload' => true,
                'crop' => true, // set to true to allow cropping, false to disable

                'disk' => 'imagespool'

            ],
            [
                'name' => 'keywords', 'tab' => 'Extras',
                'label' => 'keywords',
                'type' => 'text'
            ],
            [
                'name' => 'BrandID', 'tab' => 'Identification',
                'label' => 'BrandID',
                'type' => 'text'
            ],


        ];
    }

    protected function addCustomCrudFilters()
    {
        $this->crud->addFilter(
            [ // add a "simple" filter called Draft
                'type' => 'simple',
                'name' => 'checkbox',
                'label' => 'No main image',
            ],
            false, // the simple filter has no values, just the "Draft" label specified above
            function () { // if the filter is active (the GET parameter "draft" exits)
                $this->crud->addClause('where', 'image', '');
            }
        );

        $this->crud->addFilter([ // dropdown filter
            'name' => 'currency',
            'type' => 'dropdown',
            'label' => 'Currency',
        ], ['USD' => 'USD', 'CAD' => 'CAD'], function ($value) {
            // if the filter is active
            $this->crud->addClause('where', 'currency', $value);
        });
        $this->crud->addFilter(
            [ // text filter
                'type' => 'text',
                'name' => 'sku',
                'label' => 'SKU',
            ],
            false,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'sku', 'LIKE', "%$value%");
            }
        );

        /*
        $this->crud->addFilter(
            [
                'type' => 'dropdown',
                'name' => 'category_level1',
                'label' => 'Main Category'

            ],
            function () {
                $opts = Category::where('clientId', $this->clientId)->get();
                $r = [];
                foreach ($opts as $opt) {
                    $r[$opt->CategoryName] = $opt->CategoryName;
                }
                return $r;
            },
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'category_level1', $value);
            }
        );
        */
        $this->crud->addFilter(
            [
                'name' => 'list',
                'type' => 'range',
                'label' => 'List Price',
                'label_from' => 'min value',
                'label_to' => 'max value',
            ],
            false,
            function ($value) { // if the filter is active
                $range = json_decode($value);
                if ($range->from && $range->to) {
                    $this->crud->addClause('where', 'list', '>=', (float)$range->from);
                    $this->crud->addClause('where', 'list', '<=', (float)$range->to);
                }
            }
        );
        $this->crud->addFilter(
            [ // add a "simple" filter called Draft
                'type' => 'dropdown',
                'name' => 'wBrandID',
                'label' => 'With Brand',
            ],
            ['0' => 'No', '1' => 'Yes'], // the simple filter has no values, just the "Draft" label specified above
            function ($value) { // if the filter is active (the GET parameter "draft" exits)
                if ($value == 0) {
                    $this->crud->addClause('where', 'BrandID', '');
                } else {
                    $this->crud->addClause('where', 'BrandID', '<>', '');
                }
            }
        );
        CRUD::filter('BrandID')
            ->type('select2')
            ->values(LocalBrands::pluck('mfg_name', 'mfg_code')->toArray())
            ->label('Brand')
            ->whenActive(function ($value) {
                CRUD::addClause('where', 'BrandID', '=', $value);
            });

        $this->crud->addFilter([
            'name' => 'mfr',
            'type' => 'text',
            'label' => 'Manufacturer',
        ],
            null
            , function ($value) {

                CRUD::addClause('where', 'mfr', '=', $value);
            });
        $this->crud->addFilter([
            'name' => 'contactpk',
            'type' => 'select2',
            'label' => 'Supplier',
        ],
            function () {
                $invs = (new ActiveSuppliersNetworkList)()->get();
                $return = [];
                foreach ($invs as $in) {
                    $return[$in->contactpk] = $in->getSupplierDisplayName() ?? '';
                }
                return $return;
            }
            , function ($value) {

                CRUD::addClause('where', 'contactpk', '=', $value);
            });
        $this->crud->addFilter(
            [ // add a "simple" filter called Draft
                'type' => 'dropdown',
                'name' => 'wlinecode',
                'label' => 'With Linecode',
            ],
            ['0' => 'No', '1' => 'Yes'], // the simple filter has no values, just the "Draft" label specified above
            function ($value) { // if the filter is active (the GET parameter "draft" exits)
                if ($value == 0) {
                    $this->crud->addClause('where', 'linecode', '');
                } else {
                    $this->crud->addClause('where', 'linecode', '<>', '');
                }
            }
        );

        $this->crud->addFilter([
            'name' => 'linecode',
            'type' => 'text',
            'label' => 'LineCode',
        ],
            null
            , function ($value) {

                CRUD::addClause('where', 'linecode', '=', $value);
            });
        CRUD::filter('pt_num')
            ->type('select2_ajax')
            ->select_attribute('PartTerminologyName')
            ->select_key('PartTerminologyID')
            ->values(sophio_route('flattenpartshare.fetchParttype'))
            ->method('POST')
            ->whenActive(function ($value) {
                $this->crud->query->where('pt_num', $value);
                /*$this->crud->query->where(function ($q) use ($value) {
                    $q->where('PartTypeID', $value)->orWhereHas('product', function ($query) use ($value) {
                        $query->where('aaia_parttype_id', $value);
                    });
                });*/
            });
    }
    public function destroy($id)
    {
        $this->crud->hasAccessOrFail('delete');

        // get entry ID from Request (makes sure its the last ID for nested resources)
        $id = $this->crud->getCurrentEntryId() ?? $id;
        $index = new ProductsIndex();

        return $this->crud->delete($id);
    }
    public function setupOthersRoutes($segment, $routeName, $controller)
    {
        Route::any($segment . '/remove', [
            'as' => $routeName . '.remove',
            'uses' => $controller . '@remove',
            'operation' => 'remove',
        ]);
    }
    public function remove()
    {
        if(request()->get('BrandID')) {

            $wwws = Wwsitem::join('pnci_pim.products', function($q){
                $q->on('products.part_number_unformatted','=','wws_items.part_number_unformatted');
                $q->on('products.aaiabrandid','=','wws_items.BrandID');
                ;
            })->where('aaia_parttype_id','>',0)->where('BrandID',request()->get('BrandID'))
                ->join('aces_meta.aces_taxonomy', function($q){
                    $q->on('aaia_parttype_id','=','PartTerminologyID');
                });
            if(request()->get('pt_num') && request()->get('pt_num') !==0) {
                $wwws->where('aaia_parttype_id',request()->get('pt_num'));

            }elseif(request()->get('pt_num') && request()->get('pt_num') ===0){
                $wwws->where('pt_num','=',0);
            }
            $wwws->delete();
            Alert::add('success', 'Removed selected items!')->flash();

        }else{
            Alert::add('fail', 'No brand selected!')->flash();
        }
        return back();
    }
    protected function productTopBar($wwsitem)
    {
        $content = [
            [
                'type' => 'text',

                'label' => $wwsitem->mfr . ' ' . $wwsitem->sku,
            ],
        ];

        if ($wwsitem->partshare) {
            $content[] =
                [
                    'type' => 'link',
                    'href' => sophio_route('flattenpartshare.show', ['id' => $this->crud->getCurrentEntry()->partshare->PID]),
                    'label' => 'Partshare',

                ];
        }
        $product = $wwsitem->product;

        if($product){
            $content[] =
                [
                    'type' => 'link',
                    'href' => sophio_route('pim/product.edit', ['id' => $product->id]),
                    'label' => 'PIM',

                ];
            $asin = Asin::where('mfg_code', $product->aaiabrandid)->where('part_number_unformatted', $product->part_number_unformatted)->first();
            if ($asin) {
                $content [] = [
                    'type' => 'link',
                    'href' => sophio_route('flattenpartshare.amazon', ['id' => $product->partshareflatten->PID]),
                    'label' => 'Amazon',
                ];
            }



        }
        $nc = NeedsContent::where('aaiabrandid', $wwsitem->BrandID)->where('part_number_unformatted', $wwsitem->part_number_unformatted)->first();

        if ($nc) {

            $content [] = [
                'type' => 'link',
                'href' => sophio_route('needscontent.index', ['aaiabrandid' =>  $nc->aaiabrandid, 'part_number_unformatted' => $nc->part_number_unformatted]),
                'label' => 'Needs Content',
            ];
        }


        Widget::add([
            'type' => 'topbar',
            'content' => $content
        ])->to('before_content');
    }
}
