<?php

namespace App\Http\Controllers\Admin\FBS;

use App\Http\Requests\ProductwarehouseRequest;
use App\Library\Sophio\Actions\FBS\StockCheckWarehouse;
use App\Library\Sophio\Services\ProductWarehouseOverrideService;
use Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
use Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
use Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Route;
use Prologue\Alerts\Facades\Alert;
use Sophio\Common\Controllers\BaseTenantCrudController;
use Sophio\Common\Models\FBS\Marketplace;
use Sophio\Common\Models\FBS\ProductWarehouse;
use Sophio\Common\Models\FBS\Supplier;
use Sophio\Common\Repository\Settings;
use Sophio\Common\Services\ProductInformation;
use Sophio\FBSOrder\Library\Rules\Supplier\ActiveSuppliersNetworkList;

/**
 * Class ProductwarehouseCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class ProductwarehouseCrudController extends BaseTenantCrudController
{
    use UpdateOperation;
    use CreateOperation;
    use DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation {
        search as parentSearch;
    }


    /**
     * Configure the CrudPanel object. Apply settings to all operations.
     *
     * @return void
     */
    protected $sups;

    public function setup()
    {
        if (!backpack_user()->hasAnyRole(['store', 'store-admin', 'super-admin'])) {
           $this->crud->denyAccess(['list', 'create', 'delete', 'show', 'update', 'bulkDelete']);
        }
        parent::setup();
        CRUD::setModel(ProductWarehouse::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/dbs/' . \Route::current()->parameter('database') . '/productwarehouse');
        CRUD::setEntityNameStrings('Line', 'Inventory');
        $this->crud->enableExportButtons();
        $s = (new ActiveSuppliersNetworkList())()->get();
        $this->sups = [];
        foreach ($s as $ss) {
            $this->sups[$ss->contactpk] = $ss->PK . ' ' . $ss->NAME;
        }


    }

    public function setupOrderRoutes($segment, $routeName, $controller)
    {
        Route::post($segment . '/parttype', [
            'as' => $routeName . '.parttype',
            'uses' => $controller . '@parttype',
            'operation' => 'parttype',
        ]);
    }

    public function parttype()
    {
        $term = request()->get('term');
        $pts = \DB::select("select PartTerminologyID,PartTerminologyName from aaia_pcdb.Parts where PartTerminologyName like '%" . $term . "%'");
        $pt = [];
        foreach ($pts as $p) {
            $pt[$p->PartTerminologyID] = $p->PartTerminologyName;
        }
        return $pt;
    }

    public function search()
    {
        $search = request()->get('search');
        if ($search && isset($search['value']) && $search['value'] !== "") {
            $this->crud->query->orderByRaw(' case when product_number= "' . $search['value'] . '" then 1 else 0 end DESC');
        }

        return $this->parentSearch();
    }

    public function blacklist()
    {
        Config::set('tenant_db', \Route::current()->parameter('database'));
        $productWarehouse = ProductWarehouse::where('pk', \Route::current()->parameter('id'))->first();
        if ($productWarehouse->override) {
            Alert::add('info', 'Already blacklisted!')->flash();
            return redirect()->back();
        } else {
            $pwo = new ProductWarehouseOverrideService();
            $pwo->blacklist($productWarehouse);
            Alert::add('success', 'Added to blacklist!')->flash();
            return redirect()->back();
        }
    }

    public function whitelist()
    {
        Config::set('tenant_db', \Route::current()->parameter('database'));
        $productWarehouse = ProductWarehouse::where('pk', \Route::current()->parameter('id'))->first();
        if ($productWarehouse->override) {
            $productWarehouse->override->delete();
            Alert::add('success', 'Removed from blacklist!')->flash();
            return redirect()->back();
        } else {
            Alert::add('info', 'Item not found on blacklist!')->flash();
            return redirect()->back();
        }
    }

    public function rtsc()
    {
        Config::set('tenant_db', \Route::current()->parameter('database'));
        $productWarehouse = ProductWarehouse::where('pk', \Route::current()->parameter('id'))->first();
        $settings = new Settings(['fbs_database' => Config::get('tenant_db'), 'ManagerApproval' => 2]);
        $settings->set('alternateflag', request()->get('alternateflag', 'no'));
        $settings->set('allow_not_matched', true);
        $stw=new StockCheckWarehouse();
        $itemList = ($stw)($productWarehouse, $settings);
        $productInfo = [];
        foreach ($itemList as $item) {
            foreach ($item->sources as $warehouse) {

                if (isset($warehouse->rtsc) && isset($warehouse->rtsc['attributes'])) {
                    if(isset($warehouse->rtsc['attributes']['mfgcode'])) {
                        if (!isset($productInfo[mkMgfSku($warehouse->rtsc['attributes']['mfgcode'], $warehouse->rtsc['attributes']['partno'])])) {
                            $productInfo[mkMgfSku($warehouse->rtsc['attributes']['mfgcode'], $warehouse->rtsc['attributes']['partno'])] = new ProductInformation($warehouse->rtsc['attributes']['mfgcode'], unformatString($warehouse->rtsc['attributes']['partno']));
                            $productInfo[mkMgfSku($warehouse->rtsc['attributes']['mfgcode'], $warehouse->rtsc['attributes']['partno'])]->setProduct();
                            $productInfo[mkMgfSku($warehouse->rtsc['attributes']['mfgcode'], $warehouse->rtsc['attributes']['partno'])]->setProductFeed();
                        }
                    }

                }
            }
        }

        $supplier = Supplier::where('contactpk', $productWarehouse->contactpk)->first();
        return view('admin.fbs.productwarehouse.stockcheck', ['itemList' => $itemList, 'pk' => \Route::current()->parameter('id'), 'productWarehouse' => $productWarehouse, 'productInfo' => $productInfo,
            'supplier' =>$supplier,'supplierlog'=>$stw->driver->supplierlogs[$supplier->PK]??'']);
    }

    /**
     * Define what happens when the List operation is loaded.
     *
     * @see  https://backpackforlaravel.com/docs/crud-operation-list-entries
     * @return void
     */
    protected function setupListOperation()
    {

       // $this->crud->removeButtonFromStack('create', 'top');
        /*
        Widget::add([
                'type' => 'topbar',
                'content'  => $content
            ]
        )->to('before_content');
        */

        $this->crud->addButtonFromView('line', 'productwarehousebuttons', 'productwarehousebuttons', 'beginning');
        CRUD::button('historyfeed')->stack('line')->view('crud::buttons.quick')->meta([
            'access' => true,
            'label' => 'Price History',
            'icon' => 'la la-history',
            'wrapper' => [
                'element' => 'a',
                'href' => (function ($entry) {
                    return sophio_route('historyfeed.index', ['linecode' => $entry->linecode, 'part_number_unformatted' => $entry->part_number_unformatted, 'contactpk' => $entry->contactpk]);
                }),
                'target' => '_blank',

            ]
        ]);

        $this->crud->setOperationSetting('lineButtonsAsDropdown', true);
        CRUD::setResponsiveTable(true);
        CRUD::setOperationSetting('showEntryCount', false);
        $this->crud->addColumns([
            [
                'name' => 'manufacturer.mfg_name',
                'label' => 'Manufacturer',
                'searchLogic' => false,
            ],
            [
                'name' => 'productfeed.imageprimary.image_url',
                'label' => 'Image',
                'type' => 'image',
                'height' => '50px',
                'width' => '50px',

            ],
            ['name' => 'mfgcode', 'searchLogic' => false],
            ['name' => 'linecode', 'searchLogic' => false],
            ['name' => 'product_number', 'searchLogic' => function ($query, $column, $searchTerm) {
                $query->where('product_number', '=', $searchTerm . '')->orWhere('part_number_unformatted',$searchTerm);
            }],

            [
                'name' => 'sellprice',
                'label' => 'Sell Price',
                'type' => 'number',
                'decimals' => 2,
                'searchLogic' => false
            ],
            [
                'name' => 'cost',
                'label' => 'Cost',
                'type' => 'number',
                'decimals' => 2,
                'searchLogic' => false
            ],
            [
                'name' => 'coreprice',
                'label' => 'Core Price',
                'type' => 'number',
                'decimals' => 2,
                 'searchLogic' => false
            ],
            [
                'name' => 'wd_min_qty',
                'label' => 'WD Min Qty',
                'type' => 'number',
                'searchLogic' => false

            ],
            [
                'name' => 'cost_partshare',
                'label' => 'Partshare Cost',
                'type' => 'number',
                'decimals' => 2,
                'searchLogic' => false
            ],
            ['name' => 'qty_avail', 'searchLogic' => false],

            ['name' => 'wddescription', 'label' => 'Description', 'searchLogic' => false],
            [
                'name' => 'contactpk',
                'type' => 'text',
                'label' => 'Supplier',
                'limit' => 100,
                'searchLogic' => false,
                'escaped' => false,
                'value' => function ($entry) {
                    return wordwrap($entry->supplier->getSupplierDisplayName(), 20, '<br>') ?? '';
                },
                'wrapper' => [

                    'href' => function ($crud, $column, $entry, $related_key) {
                        return backpack_url('dbs/' . \Route::current()->parameter('database') . '/fbs/supplier/' . ($entry->supplier->PK ?? '') . '/edit');
                    }
                ]
            ]

        ]);
        if(sophiosettings()->getStore()['STORETYPE']==='TE')
        {
            $this->crud->addColumn([     'name' => 'dbsource',
                'label' => 'Source',
                'type' => 'test',

                'searchLogic' => false]);
        }

        $this->addCustomCrudFilters();
    }

    protected function setupShowOperation()
    {
        $this->addCustomCrudFilters();
        $this->crud->addColumns($this->getFields());
        $this->crud->addColumn([
            'name' => 'part_number_unformatted',
            'label' => 'part_number_unformatted',
            'type' => 'text'
        ]);
        $this->crud->addColumn([
            'name' => 'productfeed.imageprimary.image_url',
            'label' => 'Image',
            'type' => 'image',
            'height' => '100px',
            'width' => '100px',

        ]);
        /**
         * Columns can be defined using the fluent syntax or array syntax:
         * - CRUD::column('price')->type('number');
         * - CRUD::addColumn(['name' => 'price', 'type' => 'number']);
         */

    }

    /**
     * Define what happens when the Create operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-create
     * @return void
     */
    protected function setupCreateOperation()
    {
        $this->crud->set('show.setFromDb', false);
        CRUD::setValidation(ProductwarehouseRequest::class);

        $this->crud->addFields($this->getFields());

        /**
         * Fields can be defined using the fluent syntax or array syntax:
         * - CRUD::field('price')->type('number');
         * - CRUD::addField(['name' => 'price', 'type' => 'number']));
         */
    }

    /**
     * Define what happens when the Update operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-update
     * @return void
     */
    protected function setupUpdateOperation()
    {
        $this->setupCreateOperation();
    }

    public function getFields()
    {
        return [

            [
                'name' => 'linecode',
                'label' => 'Linecode',
                'type' => 'text',
                'tab' => 'Identification'
            ],
            [
                'name' => 'mfgcode',
                'label' => 'mfgcode',
                'type' => 'text',
                'tab' => 'Identification'
            ],
            [
                'name' => 'product_number',
                'label' => 'product_number',
                'type' => 'text',
                'tab' => 'Identification'
            ],
            [
                'name' => 'part_number_unformatted',
                'label' => 'part_number_unformatted',
                'target'  => 'product_number',
                'type'  => 'slug',
                'separator' => '', // separator to use
                'trim' => true, // trim whitespace
                'lower' => true, // convert to lowercase
                'strict' => true, // strip special characters except replacement
                'remove' => '/[*+~.()!:@]/g', // remove characters to match regex, defaults to null
                'tab' => 'Identification'
            ],
            [
                'name' => 'catalog_part_number',
                'label' => 'catalog_part_number',
                'type' => 'text',
                'tab' => 'Identification'
            ],


            [
                'name' => 'listprice',
                'label' => 'listprice',
                'type' => 'number',
                'decimals' => 2,
                'tab' => 'Pricing'
            ],
            [
                'name' => 'sellprice',
                'label' => 'sellprice',
                'type' => 'number',
                'decimals' => 2,
                'tab' => 'Pricing'
            ],
            [
                'name' => 'cost',
                'label' => 'Cost',
                'type' => 'number',
                'decimals' => 2,
                'tab' => 'Pricing'
            ],
            [
                'name' => 'coreprice',
                'label' => 'coreprice',
                'type' => 'number',
                'decimals' => 2,
                'tab' => 'Pricing'
            ],
            [
                'name' => 'map',
                'label' => 'map',
                'type' => 'number',
                'decimals' => 2,
                'tab' => 'Pricing'
            ],
            [
                'name' => 'qty_avail',
                'label' => 'qty_avail',
                'type' => 'number',
                'tab' => 'Availability'
            ],

            [
                'name' => 'wddescription',
                'label' => 'wddescription',
                'type' => 'text',
                'tab' => 'Identification'
            ],
            [
                'name' => 'wd_min_qty',
                'label' => 'wd_min_qty',
                'type' => 'number',
                'tab' => 'Availability'
            ],

            [
                'name' => 'zipcode',
                'label' => 'zipcode',
                'type' => 'text',
                'tab' => 'Supplier Info'
            ],
            [
                'name' => 'sup',
                'label' => 'sup',
                'type' => 'text',
                'tab' => 'Supplier Info'
            ],
            [
                'name' => 'product_sku',
                'label' => 'product_sku',
                'type' => 'text',
                'tab' => 'Identification'
            ],

            [
                'name' => 'matching_type',
                'label' => 'matching_type',
                'type' => 'text',
                'tab' => 'Supplier Info'
            ],
            [
                'name' => 'contactpk',
                'label' => 'Supplier',
                'type' => 'select2_from_array',
                'options' => $this->sups,
                'tab' => 'Supplier Info'
            ],

            [
                'name' => 'dbsource',
                'label' => 'dbsource',
                'type' => 'text',
                'default' => '',
                'tab' => 'Supplier Info'
            ],
            [
                'name' => 'manualpricing',
                'label' => 'manualpricing',
                'type' => 'datetime',
                'tab' => 'Pricing'
            ],
            [
                'name' => 'changedstock',
                'label' => 'changedstock',
                'type' => 'datetime',
                'tab' => 'Availability'
            ],
            [
                'name' => 'cost_partshare',
                'label' => 'cost_partshare',
                'type' => 'number',
                'decimals' => 2,
                'tab' => 'Pricing'
            ],
            [
                'name' => 'core_partshare',
                'label' => 'core_partshare',
                'type' => 'number',
                'decimals' => 2,
                'tab' => 'Pricing'
            ]

        ];
    }

    protected function addCustomCrudFilters()
    {


        $this->crud->addFilter(
            [ // text filter
                'type' => 'text',
                'name' => 'product_number',
                'label' => 'Product Number',
            ],
            false,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'product_number', trim($value));
            }
        );
        $this->crud->addFilter(
            [ // text filter
                'type' => 'text',
                'name' => 'part_number_unformatted',
                'label' => 'Product Number No formatting',
            ],
            false,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'part_number_unformatted', trim($value));
            }
        );
        $this->crud->addFilter(
            [ // text filter
                'type' => 'text',
                'name' => 'mfgcode',
                'label' => 'MfgCode',
            ],
            false,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'mfgcode', trim($value));
            }
        );
        $this->crud->addFilter(
            [ // text filter
                'type' => 'text',
                'name' => 'linecode',
                'label' => 'linecode',
            ],
            false,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'linecode', trim($value));
            }
        );

        $this->crud->addFilter(
            [
                'name' => 'sellprice',
                'type' => 'range',
                'label' => 'Sell Price',
                'label_from' => 'min value',
                'label_to' => 'max value',
            ],
            false,
            function ($value) { // if the filter is active
                $range = json_decode($value);
                if ($range->from && $range->to) {
                    $this->crud->addClause('where', 'sellprice', '>=', (float)$range->from);
                    $this->crud->addClause('where', 'sellprice', '<=', (float)$range->to);
                }
            }
        );
        $this->crud->addFilter(
            [ // add a "simple" filter called Draft
                'type' => 'simple',
                'name' => 'hascore',
                'label' => 'With Core price',
            ],
            false, // the simple filter has no values, just the "Draft" label specified above
            function () { // if the filter is active (the GET parameter "draft" exits)
                $this->crud->addClause('where', 'coreprice', '>', 0);
            }
        );
        $this->crud->addFilter(
            [ // add a "simple" filter called Draft
                'type' => 'simple',
                'name' => 'hasstock',
                'label' => 'With Stock',
            ],
            false, // the simple filter has no values, just the "Draft" label specified above
            function () { // if the filter is active (the GET parameter "draft" exits)
                $this->crud->addClause('where', 'qty_avail', '>', 0);
            }
        );
        $this->crud->addFilter(
            [ // add a "simple" filter called Draft
                'type' => 'simple',
                'name' => 'partshare',
                'label' => 'With Partshare Cost',
            ],
            false, // the simple filter has no values, just the "Draft" label specified above
            function () { // if the filter is active (the GET parameter "draft" exits)
                $this->crud->addClause('whereNotNull', 'cost_partshare');
            }
        );
        $this->crud->addFilter(
            [ // add a "simple" filter called Draft
                'type' => 'text',
                'name' => 'productgtin',
                'label' => 'GTIN',
            ],
            false, // the simple filter has no values, just the "Draft" label specified above
            function ($value) { // if the filter is active (the GET parameter "draft" exits)
                $this->crud->query->whereHas('product', function ($query)use($value) {$query->where('gtin',$value);});
            }
        );

        $this->crud->addFilter(
            [ // add a "simple" filter called Draft
                'type' => 'simple',
                'name' => 'product_sku',
                'label' => 'With WHI Info',
            ],
            false, // the simple filter has no values, just the "Draft" label specified above
            function () { // if the filter is active (the GET parameter "draft" exits)
                $this->crud->addClause('whereNotNull', 'product_sku');
            }
        );

        CRUD::filter('pt_num')
            ->label('Part type')
            ->type('select2_ajax')
            ->values(sophio_route('productwarehouse.parttype'))
            ->method('POST')

            ->whenActive(function ($value) {
                $this->crud->query->whereHas('productfeed', function ($q) use ($value) {
                    $q->where('pt_num', $value);
                });
            });
        $sups = $this->sups;
        $this->crud->addFilter([ // dropdown filter
            'name' => 'contactpk',
            'type' => 'dropdown',
            'label' => 'Supplier',
        ],
            function () use ($sups) {
                return $sups;
            }
            , function ($value) {
                $this->crud->addClause('where', 'contactpk', '=', $value);
            });
        $this->crud->addFilter([ // dropdown filter
            'name' => 'market',
            'type' => 'dropdown',
            'label' => 'Market',
        ],
            function () {
                $invs = Marketplace::all();
                $return = [];
                foreach ($invs as $in) {
                    $return[trim($in->market)] = trim($in->market) ?? '';
                }
                return $return;
            }
            , function ($value) {

                $this->crud->addClause('market', $value);
            });
        $this->crud->addFilter(
            [ // daterange filter
                'type' => 'date_range',
                'name' => 'lastupdate',
                'label' => 'Last Update',
                // 'date_range_options' => [
                // 'format' => 'YYYY/MM/DD',
                // 'locale' => ['format' => 'YYYY/MM/DD'],
                // 'showDropdowns' => true,
                // 'showWeekNumbers' => true
                // ]
            ],
            false,
            function ($value) { // if the filter is active, apply these constraints
                $dates = json_decode($value);
                $this->crud->addClause('where', 'lastupdate', '>=', $dates->from);
                $this->crud->addClause('where', 'lastupdate', '<=', $dates->to);
            }
        );
    }
}
