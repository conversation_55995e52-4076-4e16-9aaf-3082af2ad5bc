<?php

namespace App\Http\Controllers\Admin\FBS;

use App\Http\Controllers\Admin\Charts\FBS\LineController;
use App\Http\Controllers\Admin\Charts\FBS\NetworkPurchasesPieController;
use App\Http\Controllers\Admin\Charts\FBS\NetworkSalesBarController;
use App\Http\Controllers\Admin\Charts\FBS\SalesByMonthReportChartController;
use App\Library\Sophio\Catalog\Reports\LocalBrandsReport;
use App\Library\Sophio\Catalog\Reports\TeBrands;
use Sophio\Common\SophioUser;
use Backpack\CRUD\app\Library\Widget;
use Illuminate\Routing\Controller;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\In;
use Sophio\Common\Models\FBS\Customer;
use Sophio\Common\Models\FBS\Defect;
use Sophio\Common\Models\FBS\Invoice;
use Sophio\Common\Models\FBS\LineItem;
use Sophio\Common\Models\FBS\Marketplace;
use Sophio\Common\Models\FBS\Supplier;
use Sophio\FBSOrder\Library\Actions\Reports\BelowCost;
use Sophio\FBSOrder\Library\Actions\Reports\Cancellations;
use Sophio\FBSOrder\Library\Actions\Reports\CsrReport;
use Sophio\FBSOrder\Library\Actions\Reports\Dow;
use Sophio\FBSOrder\Library\Actions\Reports\Dows;
use Sophio\FBSOrder\Library\Actions\Reports\EndofDay;
use Sophio\FBSOrder\Library\Actions\Reports\NetworkDashboard;
use Sophio\FBSOrder\Library\Actions\Reports\NetworkMonthEnd;
use Sophio\FBSOrder\Library\Actions\Reports\NetworkSaleByState;
use Sophio\FBSOrder\Library\Actions\Reports\ReturnPerformance;
use Sophio\FBSOrder\Library\Actions\Reports\SalesByCustomer;
use Sophio\FBSOrder\Library\Actions\Reports\SalesByCusttype;
use Sophio\FBSOrder\Library\Actions\Reports\SalesByMfr;
use Sophio\FBSOrder\Library\Actions\Reports\SalesByMonth;
use Sophio\FBSOrder\Library\Actions\Reports\SalesByUser;
use Sophio\FBSOrder\Library\Actions\Reports\ShippingAnalysis;
use Sophio\FBSOrder\Library\Actions\Reports\TaxReport;
use Sophio\FBSOrder\Library\Actions\Reports\WHMCSEndofDay;
use Sophio\FBSOrder\Library\Jobs\Opportunities;

class ReportsController extends Controller
{
    public function __construct()
    {
        ini_set('max_execution_time', 0);
        ini_set('memory_limit', '1024M');
        $this->middleware(function ($request, $next) {
            if (backpack_user() && backpack_user()->hasRole(['supplier']) && !backpack_user()->hasRole(['returns|store-admin|super-admin'])) {
                $request->merge(['profilepk' => backpack_user()->user_key]);
                $request->merge(['supplierpk' => backpack_user()->user_key]);
            }
            return $next($request);
        });
    }

    public function networkdashboard()
    {

        $nd = new NetworkDashboard();
        $items = $nd();
        return view('admin.fbs.reports.networkdashboard', ['nd' => $nd]);

    }


    public function shippinganalysis()
    {
        $shippingAnalysis = new ShippingAnalysis();

        switch (request()->get('action')) {
            case 'SUMMARY':
                $result = $shippingAnalysis->summary(request()->get('when') ?? 'LASTMONTH', request()->get('custpk'), request()->get('custtype'), request()->get('profilepk'), request()->get('cost') ?? 10);
                break;
            case 'ORPHANS':
                $result = $shippingAnalysis->orphans(request()->get('when') ?? 'LASTMONTH', request()->get('type'), request()->get('custpk'), request()->get('custtype'), request()->get('profilepk'), request()->get('cost') ?? 10);
                break;
            case 'EXCEPTIONS':
                $result = $shippingAnalysis->exceptions(request()->get('when') ?? 'LASTMONTH', request()->get('type'), request()->get('custpk'), request()->get('custtype'), request()->get('profilepk'), request()->get('cost') ?? 10);
            break;
            case 'DEACTIVATE':
                $result = $shippingAnalysis->deactivate(request()->get('when') ?? 'LASTMONTH',
                    request()->get('custpk'), request()->get('custtype'),
                    request()->get('profilepk'), request()->get('cost') ?? 30, request()->get('margin') ?? 10);
                break;
            case  'RUNDEACTIVATE':
                $result = $shippingAnalysis->runDeactivate(request()->get('when') ?? 'LASTMONTH',
                    request()->get('custpk'), request()->get('custtype'),
                    request()->get('profilepk'), request()->get('cost') ?? 30, request()->get('margin') ?? 10);
                break;
            default:
                $result = null;
        }
        $custs = Cache::remember('last30_customers_sold', 3600, function () {
            return \Sophio\Common\Models\FBS\Invoice::with('seller')->whereIn('invstatus', ['F', 'R', '8', 'K'])->where('invdate', '>', \Illuminate\Support\Carbon::now()->subDays(30))->groupBy('custpk')->orderByRaw('COUNT(*) DESC')->get()->pluck('custpk');
        });
        return view('admin.fbs.reports.shippinganalysis', ['result' => $result, 'customers' => Customer::whereIn('pk', $custs)->get()]);
    }
    public function salesbydows()
    {
        $filename = 'sales-by-dows';
        $type = request()->get('type');
        $supplier = request()->get('supplier');
        $when = request()->get('when');
        $lineitems = (new Dows)($filename, $when, $type, $supplier);
        $controller = new LineController();
        $data = $lineitems->get();
        $labels = [];
        $series = [];
        if (request()->get('type') !== 'supplier') {
            foreach ($data as $row) {
                $labels[] = \Carbon\Carbon::parse($row->completed)->dayName . " (" . \Carbon\Carbon::parse($row->completed)->format('m/d/y') . ")";
                $series[] = our_format($row->sales);
            }

        } else {
            $series = [];
            $labels = [];
            $dates = [];
            foreach ($data as $row) {
                if (!isset($series[$row->NAME])) {
                    $series[$row->NAME] = [];
                }
                $series[$row->NAME][\Carbon\Carbon::parse($row->completed)->format('ymd')] = our_format($row->sales);
                $labels[] = \Carbon\Carbon::parse($row->completed)->dayName . " (" . \Carbon\Carbon::parse($row->completed)->format('m/d/y') . ")";
                $dates[] = \Carbon\Carbon::parse($row->completed)->format('ymd');
            }
            $dates = array_values(array_unique($dates));
            $controller->multiple = true;
            foreach ($series as $sup => $serie) {
                foreach ($dates as $d) {
                    if (!isset($serie[$d])) {
                        $series[$sup][$d] = 0;
                    }
                }
            }
            foreach ($series as $sup => $serie) {
                ksort($serie);
                $series[$sup] = array_values($serie);
            }
        }

        $controller->labels = array_values(array_unique($labels));

        $controller->series = $series;

        $controller->setupReal();
        $before_content = [
            'type' => 'div',
            'class' => 'row',
            'content' => [ // widgets
                [
                    'type' => 'ownchart',
                    'wrapperClass' => 'col-md-12',
                    'controller' => $controller,
                    'content' => [
                        'header' => 'Chart',
                    ]
                ],
            ],
        ];
        Widget::add($before_content)->to('before_content');
        return view('admin.fbs.reports.salesbydows', [
            'type' => $type, 'data' => $data, 'marketplaces' => Marketplace::all(), 'when' => $when,
            'filename' => $filename
        ]);
    }
    public function salesbydow()
    {
        $filename = 'sales-by-dow';
        $type = request()->get('type');
        $custtype = request()->get('custtype');
        $when = request()->get('when');
        $lineitems = (new Dow)($filename, $when, $type, $custtype);
        $controller = new LineController();
        $data = $lineitems->get();
        $labels = [];
        $series = [];
        if (request()->get('type') !== 'CUSTTYPE') {
            foreach ($data as $row) {
                $labels[] = \Carbon\Carbon::parse($row->completed)->dayName . " (" . \Carbon\Carbon::parse($row->completed)->format('m/d/y') . ")";
                $series[] = our_format($row->sales);
            }

        } else {
            $series = [];
            $labels = [];
            $dates = [];
            foreach ($data as $row) {
                if (!isset($series[$row->custtype])) {
                    $series[$row->custtype] = [];
                }
                $series[$row->custtype][\Carbon\Carbon::parse($row->completed)->format('ymd')] = our_format($row->sales);
                $labels[] = \Carbon\Carbon::parse($row->completed)->dayName . " (" . \Carbon\Carbon::parse($row->completed)->format('m/d/y') . ")";
                $dates[] = \Carbon\Carbon::parse($row->completed)->format('ymd');
            }
            $dates = array_values(array_unique($dates));
            $controller->multiple = true;
            foreach ($series as $custtype => $serie) {
                foreach ($dates as $d) {
                    if (!isset($serie[$d])) {
                        $series[$custtype][$d] = 0;
                    }
                }
            }
            foreach ($series as $custtype => $serie) {
                ksort($serie);
                $series[$custtype] = array_values($serie);
            }
        }

        $controller->labels = array_values(array_unique($labels));

        $controller->series = $series;

        $controller->setupReal();
        $before_content = [
            'type' => 'div',
            'class' => 'row',
            'content' => [ // widgets
                [
                    'type' => 'ownchart',
                    'wrapperClass' => 'col-md-12',
                    'controller' => $controller,
                    'content' => [
                        'header' => 'Chart',
                    ]
                ],
            ],
        ];
        Widget::add($before_content)->to('before_content');
        return view('admin.fbs.reports.salesbydow', [
            'type' => $type, 'data' => $data, 'marketplaces' => Marketplace::all(), 'when' => $when,
            'filename' => $filename
        ]);
    }

    public function opportunities()
    {
        setLayoutBoxed();
        $supplier = Supplier::find(request()->get('profilepk'));
        $op = (new Opportunities())(request()->get('action'),request()->get('when'),$supplier);
        return view('admin.fbs.reports.opportunities', [
            'action' => request()->get('action'), 'when' => request()->get('when'),  'data'=>$op,
            'filename'=>'opportunities_'.request()->get('action').'_'.request()->get('profilepk').'_'.request()->get('when')
        ]);
    }

    public function returnallowance()
    {
        return view('comingsoon');
    }

    public function penalties()
    {
        return view('comingsoon');
    }

    public function salesbymonth()
    {

        $controller = new SalesByMonthReportChartController();
        $sales = (new SalesByMonth)(request()->get('custtype'), request()->get('custpk'), request()->get('profilepk'));
        $series = [];
        foreach ($sales as $sale) {
            if (!isset($series[(int)$sale->y])) {
                $series[(int)$sale->y] = [];
            }
            $series[$sale->y][$sale->m] = (float)number_format($sale->v, '2', '.', '');
        }
        foreach ($series as $year => $m) {
            for ($i = 1; $i <= 12; $i++) {
                if (!isset($m[$i])) {
                    $m[$i] = 0;
                }
            }
            ksort($m);
            $series[(int)$year] = $m;
        }
        $controller->series = $series;
        $controller->setupReal();
        $before_content = [
            'type' => 'div',
            'class' => 'row',
            'content' => [ // widgets
                [
                    'type' => 'ownchart',
                    'wrapperClass' => 'col-md-12',
                    'controller' => $controller,
                    'content' => [
                        'header' => 'Sales By Month/Year',
                    ]
                ],
            ],
        ];
        Widget::add($before_content)->to('before_content');


        $custs = Cache::remember('last30_customers_sold', 3600, function () {
            return \Sophio\Common\Models\FBS\Invoice::with('seller')->whereIn('invstatus', ['F', 'R', '8', 'K'])->where('invdate', '>', \Illuminate\Support\Carbon::now()->subDays(30))->groupBy('custpk')->orderByRaw('COUNT(*) DESC')->get()->pluck('custpk');
        });

        $customers = Customer::whereIn('pk', $custs)->get();

        return view('admin.fbs.reports.salesbymonth', ['customers' => $customers, 'series' => array_reverse($series, true)]);
    }

    public function salesbycusttype()
    {
        $sales = (new SalesByCusttype)(request()->get('when') ?? 'YTD', request()->get('custtype'), request()->get('profilepk'), request()->get('group') ?? '', request('type') ?? 'summary',request()->get('mfg_code'));
        return view('admin.fbs.reports.salesbycusttype', ['sales' => $sales]);
    }

    public function salesbymfr()
    {
        $sales = (new SalesByMfr())(request()->get('when') ?? 'TODAY', request()->get('custtype'), request()->get('profilepk'),   request()->get('mfg_code'),   request()->get('group'));
        return view('admin.fbs.reports.salesbymfr', ['sales' => $sales]);
    }
    public function tebrands()
    {
        $tebrands = (new TeBrands())();
        return view('admin.catalog.reports.tebrands', ['tebrands' => $tebrands]);
    }
    public function localbrands()
    {
        $tebrands = (new LocalBrandsReport())();
        return view('admin.catalog.reports.localbrands', ['tebrands' => $tebrands]);
    }
    public function endofday()
    {
        ini_set('max_execution_time', 0);
        ini_set('memory_limit', '1024M');
        if (request()->get('when')) {
            $report = (new EndofDay)(request()->get('custtype'),
                request()->get('account'), request()->get('when'),
                null,
                (request()->get('ftp') === 'yes' ? true : false),
                (request()->get('email') === 'yes' ? true : false),
                (request()->get('override') === 'yes' ? true : false),
                (request()->get('with_returns') === 'yes' ? true : false),
                (request()->get('store_to_table') === 'yes' ? true : false),
                (request()->get('include_cancelled') === 'yes' ? true : false)

            );
            return view('admin.fbs.reports.endofdayresult', $report);
        }
        setLayoutBoxed();
        return view('admin.fbs.reports.endofdayform');
    }

    public function whmcsendofday()
    {
        if (request()->post()) {

            $report = (new WHMCSEndofDay)(request()->get('when'), false, (request()->get('ftp') === 'yes' ? true : false), false);
            return view('admin.fbs.reports.whmcsendofdayresult', ['report' => $report]);
        }
        setLayoutBoxed();
        return view('admin.fbs.reports.whmcsendofdayform');
    }

    public function salesprojection()
    {

        return view('admin.fbs.reports.salesprojection', ['markets' => (new \Sophio\FBSOrder\Library\Actions\Reports\ProjectionByCusttype())(request('when')??'WEEKAGOTODAY'),
            'suppliers' => (new \Sophio\FBSOrder\Library\Actions\Reports\ProjectionBySupplier())(request('when')??'WEEKAGOTODAY'),
            'returns' => (new \Sophio\FBSOrder\Library\Actions\Reports\ProjectionReturns())(request('when')??'WEEKAGOTODAY')
        ]);
    }

    public function trackingreportitemsales()
    {
        return view('comingsoon');
    }


    public function salesbyparttype()
    {
        return view('comingsoon');
    }

    public function networksalebystate()
    {
        $data = (new NetworkSaleByState())(request('when')??'LASTMONTH',request()->get('send_ftp')?true:false);
        [$start,$end] = getDatesFromWhen(request('when'));
        $filename = "sophio-salesbystate-".$start->month.'-'.$start->year.'.csv';
        return view('admin.fbs.reports.networksalebystate',['data'=>$data,'filename'=>$filename]);
    }

    public function corereport()
    {
        return view('comingsoon');
    }

    public function salesbycategory()
    {
        return view('comingsoon');
    }

    public function trackingreporttopcust()
    {
        return view('comingsoon');
    }

    public function getoutemails()
    {
        return view('comingsoon');
    }

    public function projection()
    {
        return view('comingsoon');
    }

    public function cancellations()
    {
        $custs = Cache::remember('last30_customers_sold', 3600, function () {
            return \Sophio\Common\Models\FBS\Invoice::with('seller')->whereIn('invstatus', ['F', 'R', '8', 'K'])->where('invdate', '>', \Illuminate\Support\Carbon::now()->subDays(30))->groupBy('custpk')->orderByRaw('COUNT(*) DESC')->get()->pluck('custpk');
        });
        $cancellations = new Cancellations();
        $lineitems = [];
        switch (request()->get('type')) {
            case 'DETAIL':
                $lineitems = $cancellations->detail(request()->get('when') ?? 'THISWEEK', request()->get('custpk'), request()->get('custtype'), request()->get('profilepk'));
                break;
            case 'SUMMARY':
                $lineitems = $cancellations->summary(request()->get('when') ?? 'THISWEEK', request()->get('custpk'), request()->get('custtype'), request()->get('profilepk'));
                break;
        }

        return view('admin.fbs.reports.cancellations', ['customers' => Customer::whereIn('pk', $custs)->get(), 'rows' => $lineitems]);
    }

    public function salesbyinvstatus()
    {
        return view('comingsoon');
    }

    public function networkbelowcost()
    {
        $belowcost = (new BelowCost())(request()->get('group'),request()->get('when','MTD'),request()->get('custtype'),request()->get('custpk'),request()->get('profilepk'));
        $custs = Cache::remember('last30_customers_sold', 3600, function () {
            return \Sophio\Common\Models\FBS\Invoice::with('seller')->whereIn('invstatus', ['F', 'R', '8', 'K'])->where('invdate', '>', \Illuminate\Support\Carbon::now()->subDays(30))->groupBy('custpk')->orderByRaw('COUNT(*) DESC')->get()->pluck('custpk');
        });
        [$start, $end] = getDatesFromWhen(request()->get('when') ?? "LASTMONTH",true);

        $params['customers'] = Customer::whereIn('pk', $custs)->get();
        $params['start'] = $start;
        $params['end'] = $end;
        $params['data'] =$belowcost;
        return view('admin.fbs.reports.belowcost',$params);
    }

    public function refundanalysis()
    {
        return view('comingsoon');
    }
    public function networkmonthend()
    {
        $networkmonthend = new NetworkMonthEnd();
        $networkmonthend(request('when') ??'LASTMONTH');
        [$start,$end] = getDatesFromWhen(request('when') );
        return view('admin.fbs.reports.networkmonthend',['data'=>$networkmonthend,'date'=>$start->format('m/y')]);
    }

    public function salesbycustomer()
    {
        $custs = Cache::remember('last30_customers_sold', 3600, function () {
            return \Sophio\Common\Models\FBS\Invoice::with('seller')->whereIn('invstatus', ['F', 'R', '8', 'K'])->where('invdate', '>', \Illuminate\Support\Carbon::now()->subDays(30))->groupBy('custpk')->orderByRaw('COUNT(*) DESC')->get()->pluck('custpk');
        });
        [$start, $end] = getDatesFromWhen(request()->get('when') ?? "LASTMONTH");

        $params = (new SalesByCustomer)(request()->get('when') ?? "LASTMONTH", request()->get('custpk'), request()->get('custtype'));
        $params['customers'] = Customer::whereIn('pk', $custs)->get();
        $params['start'] = $start;
        $params['end'] = $end;
        return view('admin.fbs.reports.salesbycustomer', $params);
    }
    public function csrreport()
    {
        $when = request()->get('when') ?? 'MTD';

        if (!$when) {
            $when = 'YESTERDAY';
        }
        [$start, $end] = getDatesFromWhen($when);
        $custtype = request()->get('custtype');
        $data = (new CsrReport())($custtype, $when);
        $filename = 'sales-by-user';
        return view('admin.fbs.reports.csrreport', [
            'data' => $data, 'marketplaces' => Marketplace::all(), 'filename' => $filename, 'start' => Carbon::parse($start)->toDateString(), 'end' => Carbon::parse($end)->toDateString()
        ]);

    }
    public function salesbyuser()
    {

        $when = request()->get('when') ?? 'MTD';

        if (!$when) {
            $when = 'YESTERDAY';
        }
        [$start, $end] = getDatesFromWhen($when);
        $group = request()->get('group','salesrep');
        $pivot = request()->get('pivot','normal');
        $data = (new SalesByUser)($group, $when,$pivot);
        $filename = 'sales-by-user';
        return view('admin.fbs.reports.salesbyuser', [
            'group'=>$group,       'pivot'=>$pivot,
            'data' => $data, 'marketplaces' => Marketplace::all(), 'filename' => $filename, 'start' => Carbon::parse($start)->toDateString(), 'end' => Carbon::parse($end)->toDateString()
        ]);

    }


    public function taxreportdetail()
    {
        $taxreport = new TaxReport();
        $type = strtoupper(request()->get('type') ?? 'RICK');
        $when = request()->get('when') ?? 'LASTMONTH';
        switch ($type) {
            case 'RICK':
                $data = $taxreport->rick($when);
                [$start, $end] = getDatesFromWhen($when);
                $data['start'] = $start;
                $data['end'] = $end;
                return view('admin.fbs.reports.taxreport', $data);
        }

    }


    public function returnperformance()
    {
        $when = request()->get('when') ?? 'LAST30';


        $custtype = request()->get('custtype');
        $group = request()->get('group', 'DAY');
        switch ($group) {
            case 'DAY' :
                if (!$when) {
                    $when = 'THISMONTH';
                }
                break;
            case 'WEEK' :
                if (!$when) {
                    $when = 'LAST30';
                }
                break;
            case 'MONTH' :
                if (!$when) {
                    $when = 'YTD';
                }
                break;
        }
        [$start, $end] = getDatesFromWhen($when);
        $data = (new ReturnPerformance)($when, $custtype, $group);
        return view('admin.fbs.reports.returnperformance', ['data' => $data, 'when' => $when, 'custtype' => $custtype, 'group' => $group, 'start' => $start, 'end' => $end, 'filename' => 'returnperformance', 'marketplaces' => Marketplace::all()]);
    }
    public function notcompleted()
    {
        $when = request()->get('when') ?? 'LASTMONTH';
        $invoices = Invoice::filterDate(['invdate',$when])
            ->whereNull('completed');
        $l = new LineItem;
        $i = new Invoice;
        $invoices->join($l->getTable(), 'invpk', $i->getTable() . '.pk');
        $invoices->groupBy($i->getTable() . '.pk');
        $invoices->select($i->getTable() . '.*');
        $invoices->where($l->getTable() . '.track_num'  , '<>',"");
        $invoices->where($l->getTable() . '.' . 'qty', '>',0);
      //  return view('admin.fbs.reports.notcompleted',['when'=>$when,'invoices'=>$invoices]);
    }
}