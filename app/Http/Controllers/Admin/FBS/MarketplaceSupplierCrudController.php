<?php

namespace App\Http\Controllers\Admin\FBS;

use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Sophio\Common\Controllers\BaseTenantCrudController;
use Sophio\Common\Models\FBS\Marketplace;
use Sophio\Common\Models\FBS\MarketplaceSupplier;
use Sophio\Common\Models\FBS\Store;
use Sophio\Common\Models\FBS\Supplier;
use Sophio\FBSOrder\Library\Rules\Supplier\ActiveSuppliersNetworkList;

class MarketplaceSupplierCrudController extends BaseTenantCrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;

    protected $sups;
    protected $suppliers;
    protected $markets;
    protected $stores;
    use SupplierTrait;
    public function setup()
    {
        $this->crud->setModel(MarketplaceSupplier::class);
        $this->crud->setRoute(config('backpack.base.route_prefix') . '/dbs/' . \Route::current()->parameter('database') . '/fbs/marketplacesupplier');
        CRUD::setEntityNameStrings('marketplace supplier', 'Marketplace suppliers');
        $this->suppliers = (new ActiveSuppliersNetworkList())()->get();
        $this->sups = [];
        foreach ($this->suppliers as $ss) {
            if(isset($ss->contactpk)) {
                $this->sups[$ss->contactpk] = $ss->PK . ' ' . $ss->NAME;
            }

        }
        $this->markets = Marketplace::all();
        $this->stores  = Store::all();

    }

    protected function setupListOperation()
    {
        CRUD::button('create')->stack('top')->view('crud::buttons.quick')->meta([
            'access' => true,
            'label' => 'Create',
            'icon' => 'la la-plus',
            'wrapper' => [
                'element' => 'a',
                'href' => sophio_route('fbs/marketplacesupplier.create',['contactpk'=>request()->get('contactpk')]),

            ]
        ]);
        if(request()->get('contactpk','')!='') {
            $this->topbarSupplier(Supplier::where('contactpk',request()->get('contactpk'))->first());
        }else{
            $this->crud->addColumn(           ['name' => 'contactpk', 'limit'=>1000,'label' => 'Supplier', 'value' => function ($entry) {
                return $entry->supplier->NAME ?? '';
            },
                'wrapper' => [
                    'href' => function ($crud, $column, $entry, $related_key) {
                        return backpack_url('dbs/' . \Route::current()->parameter('database') . '/fbs/supplier/' . ($entry->supplier->PK ?? '') . '/edit');
                    }
                ]]);
        }
        $sups = $this->sups;
        $suppliers = $this->suppliers;
        $markets = $this->markets;
        $this->crud->addColumns([
            ['name' => 'storepk', 'label' => 'Store', 'value' => function ($entry) {
                return $entry->store->STORENAME ?? '';
            },
                'wrapper' => [
                    'href' => function ($crud, $column, $entry, $related_key) {
                        return backpack_url('dbs/' . \Route::current()->parameter('database') . '/fbs/supplier/' . ($entry->store->PK ?? '') . '/edit');
                    }
                ]],
            'market',
            ['name' => 'testorder', 'label' => 'Test order', 'value' => function ($entry) {
                return 'Create';
            },
                'wrapper' => [
                    'href' => function ($crud, $column, $entry, $related_key)use($suppliers) {
                        $s = $suppliers->where('contactpk',$entry->contactpk)->first();

                        if($s) {
                            $profilepk = $s->PK;
                        }else{
                            $profilepk = '';
                        }
                        return sophio_route('fbs/fbsorder.testorder',['custtype'=>$entry->market,'pk'=>$profilepk]);
                    }
                ]],


        ]);

        $this->crud->addFilter([ // dropdown filter
            'name' => 'market',
            'type' => 'dropdown',
            'label' => 'Market',
        ],
            function () use ($markets) {

                $return = [];
                foreach ($markets as $in) {
                    $return[trim($in->market)] = trim($in->market) ?? '';
                }
                return $return;
            }
            , function ($value) {

                $this->crud->addClause('where','market', $value);
            });
        $this->crud->addFilter([ // dropdown filter
            'name' => 'contactpk',
            'type' => 'dropdown',
            'label' => 'Supplier',
        ],
            function () use ($sups) {
                return $sups;
            }
            , function ($value) {
                $this->crud->addClause('where', 'contactpk', '=', $value);
            });
    }

    protected function setupCreateOperation()
    {
        $sups = $this->sups;
        $markets = $this->markets;
        $stores = [];
        foreach($this->stores as $store)
        {
            $stores[$store->STOREPK] = $store->STORENAME;
        }
        $markets=[];
        foreach ($this->markets as $market)
        {
            $markets[$market->market] = $market->market;
        }
        $this->crud->addFields([
            [
                'name' => 'storepk',
                'label' => 'Store',
                'type' => 'select2_from_array',
                'options' =>$stores
            ],
            [
                'name' => 'market',
                'label' => 'Market',
                'type' => 'select2_from_array',
                'options' =>$markets
            ],
            [
                'name' => 'contactpk',
                'label' => 'Supplier',
                'type' => 'select2_from_array',
                'options' =>$this->sups,
                'default'=>request()->get('contactpk')
            ]

        ]);
    }

    protected function setupUpdateOperation()
    {
        $this->setupCreateOperation();
    }
}