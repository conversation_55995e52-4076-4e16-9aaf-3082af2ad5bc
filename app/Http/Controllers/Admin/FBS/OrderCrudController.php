<?php

namespace App\Http\Controllers\Admin\FBS;

use App\Library\Sophio\Traits\AuditOperation;
use Backpack\CRUD\app\Exceptions\AccessDeniedException;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Str;
use Prologue\Alerts\Facades\Alert;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Sophio\Common\Controllers\BaseTenantCrudController;
use Sophio\Common\Enums\Causer;
use Sophio\Common\Models\FBS\Customer;
use Sophio\Common\Models\FBS\Invoice;
use Sophio\Common\Models\FBS\LineItem;
use Sophio\Common\Models\FBS\Supplier;
use Sophio\Common\Repository\Settings;
use Sophio\Common\Services\LockService;
use Sophio\Common\Services\LogTrack;
use Sophio\Common\Services\ProductInformation;
use Sophio\Corcentric\Library\CorcentricManager;
use Sophio\FBSOrder\Library\Actions\AutofulfillOrder;
use Sophio\FBSOrder\Library\Actions\CalculateProfit;
use Sophio\FBSOrder\Library\Actions\CancelInvoice;
use Sophio\FBSOrder\Library\Actions\CheckWithRTSC;
use Sophio\FBSOrder\Library\Actions\ForceCompleted;
use Sophio\FBSOrder\Library\Actions\InvoiceLastSupplierLogs;
use Sophio\FBSOrder\Library\Actions\ManualPurchaseFromSupplier;
use Sophio\FBSOrder\Library\Actions\ManualSuppliersList;
use Sophio\FBSOrder\Library\Actions\SendTrackingMailtoCustomer;
use Sophio\FBSOrder\Library\Actions\SendTrackingNumberRequest;
use Sophio\FBSOrder\Library\Actions\VoidLineItem;
use Sophio\FBSOrder\Library\Jobs\ProcessNewOrder;
use Sophio\FBSOrder\Library\Jobs\SendCancelToSaasUrl;
use Sophio\FBSOrder\Library\Repository\ChargeService;
use Sophio\FBSOrder\Library\Repository\InvoiceCreator\TestInvoice;
use Sophio\FBSOrder\Library\Repository\InvoiceManager;
use Sophio\FBSOrder\Library\Repository\OrderManager;
use Sophio\FBSOrder\Library\Repository\TransFeesService;
use Sophio\FBSOrder\Library\Rules\Invoice\canComplete;
use Sophio\FBSOrder\Library\Rules\Invoice\CanOrderManually;
use Sophio\FBSOrder\src\Library\Actions\CancelLineItem;
use Sophio\FBSOrder\src\Library\Actions\CancelOrder;
use Sophio\FBSOrder\src\Library\Actions\CheckFulfillmetStatusAtSeller;
use Sophio\FBSOrder\src\Library\Repository\CancelService;
use Sophio\FBSOrder\src\Library\Repository\PaymentDetailManager;
use Sophio\FBSOrder\src\Library\Rules\Invoice\CanCancelOrder;
use Sophio\FBSReturns\Library\RefundService;

class OrderCrudController extends BaseTenantCrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation {
        search as backpack_search;

    }
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CloneOperation;

    use AuditOperation;
    use OrderTrait;
    use CHUBTrait;
    use CorTrait;
    use ParTrait;
    use WalmartTrait;
    use ShippingTrait;

    public function setup()
    {
        parent::setup();
        CRUD::setModel(Invoice::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/dbs/' . \Route::current()->parameter('database') . '/fbs/fbsorder');
        CRUD::setEntityNameStrings('Order', 'Orders');
        if (!backpack_user()->hasRole('super-admin')) {
            $this->crud->denyAccess(['delete']);
        }

        if (!backpack_user()->can('manage order')) {
            $this->crud->denyAccess(['update']);
        }


    }

    public function search()
    {
        $this->crud->with('lineitem')->with('seller')->with('user')->with('status');
        $response = $this->backpack_search();

        if ($this->crud->filters()->whereNotNull('currentValue')->count() > 0) {
            $q = clone $this->crud->query;
            $q->getQuery()->groups = null;


            $q = $q->selectRaw('sum(invtotal) as total,count(distinct wws_invoice.pk) as cnt')->offset(0)->first();
            if ($q) {
                $response['invtotal'] = our_format($q->total);
                $response['actual_total'] = $q->cnt;
            } else {
                $response['invtotal'] = 0;
                $response['actual_total'] = 0;
            }

        } else {
            $response['invtotal'] = 0;
            $response['actual_total'] = 0;

        }

        return $response;
    }

    public function setupOrderRoutes($segment, $routeName, $controller)
    {
        Route::post($segment . '/{id}/changeorderstatus', [
            'as' => $routeName . '.changeorderstatus',
            'uses' => $controller . '@changeorderstatus',
            'operation' => 'changeorderstatus',
        ]);
        Route::post($segment . '/{id}/changenotes', [
            'as' => $routeName . '.changenotes',
            'uses' => $controller . '@changenotes',
            'operation' => 'changenotes',
        ]);

        Route::post($segment . '/find', [
            'as' => $routeName . '.find',
            'uses' => $controller . '@find',
            'operation' => 'find',
        ]);
        Route::get($segment . '/{id}/whdautocheck', [
            'as' => $routeName . '.whdautocheck',
            'uses' => $controller . '@whdautocheck',
            'operation' => 'whdautocheck',
        ]);
        Route::get($segment . '/{id}/whdmanualcheck', [
            'as' => $routeName . '.whdmanualcheck',
            'uses' => $controller . '@whdmanualcheck',
            'operation' => 'whdmanualcheck',
        ]);
        Route::get($segment . '/{id}/deleteorder', [
            'as' => $routeName . '.deleteorder',
            'uses' => $controller . '@deleteorder',
            'operation' => 'deleteorder',
        ]);


        Route::any($segment . '/{id}/cancelorder', [
            'as' => $routeName . '.cancelorder',
            'uses' => $controller . '@cancelorder',
            'operation' => 'cancelorder',
        ]);
        Route::any($segment . '/{id}/postcancel', [
            'as' => $routeName . '.postcancel',
            'uses' => $controller . '@postcancel',
            'operation' => 'postcancel',
        ]);
        Route::any($segment . '/{id}/unlock', [
            'as' => $routeName . '.unlock',
            'uses' => $controller . '@unlock',
            'operation' => 'unlock',
        ]);
        Route::any($segment . '/{id}/autofulfill', [
            'as' => $routeName . '.autofulfill',
            'uses' => $controller . '@autofulfill',
            'operation' => 'autofulfill',
        ]);
        Route::any($segment . '/{id}/charge', [
            'as' => $routeName . '.charge',
            'uses' => $controller . '@charge',
            'operation' => 'charge',
        ]);
        Route::any($segment . '/{id}/paymentstatus', [
            'as' => $routeName . '.paymentstatus',
            'uses' => $controller . '@paymentstatus',
            'operation' => 'paymentstatus',
        ]);
        Route::any($segment . '/{id}/manualsubmit', [
            'as' => $routeName . '.manualsubmit',
            'uses' => $controller . '@manualsubmit',
            'operation' => 'manualsubmit',
        ]);
        Route::any($segment . '/{id}/refund', [
            'as' => $routeName . '.refund',
            'uses' => $controller . '@refund',
            'operation' => 'refund',
        ]);
        Route::any($segment . '/{id}/cancellineitem/{lineitempk}', [
            'as' => $routeName . '.cancellineitem',
            'uses' => $controller . '@cancellineitem',
            'operation' => 'cancellineitem',
        ]);
        Route::any($segment . '/{id}/sendtrackingrequestnumber', [
            'as' => $routeName . '.sendtrackingrequestnumber',
            'uses' => $controller . '@sendtrackingrequestnumber',
            'operation' => 'sendtrackingrequestnumber',
        ]);
        Route::any($segment . '/{id}/sendtrackingcustomer',  [
            'as' => $routeName . '.sendtrackingcustomer',
            'uses' => $controller . '@sendtrackingcustomer',
            'operation' => 'sendtrackingcustomer',
        ]);
        Route::get($segment . '/{id}/sellerfulfillmentstatus', [
            'as' => $routeName . '.sellerfulfillmentstatus',
            'uses' => $controller . '@sellerfulfillmentstatus',
            'operation' => 'sellerfulfillmentstatus',
        ]);
        Route::get($segment . '/{id}/sendsupplierordercancellation', [
            'as' => $routeName . '.sendsupplierordercancellation',
            'uses' => $controller . '@sendsupplierordercancellation',
            'operation' => 'sendsupplierordercancellation',
        ]);
        Route::get($segment . '/{id}/assigntomanager', [
            'as' => $routeName . '.assigntomanager',
            'uses' => $controller . '@assigntomanager',
            'operation' => 'assigntomanager',
        ]);
        Route::any($segment . '/missingtrackingnumbers', [
            'as' => $routeName . '.missingtrackingnumbers',
            'uses' => $controller . '@missingtrackingnumbers',
            'operation' => 'missingtrackingnumbers',
        ]);
        Route::any($segment . '/paymentreconciliation', [
            'as' => $routeName . '.paymentreconciliation',
            'uses' => $controller . '@paymentreconciliation',
            'operation' => 'paymentreconciliation',
        ]);
        Route::any($segment . '/paymentlogdetail', [
            'as' => $routeName . '.paymentlogdetail',
            'uses' => $controller . '@paymentlogdetail',
            'operation' => 'paymentlogdetail',
        ]);
        Route::any($segment . '/batchprocess', [
            'as' => $routeName . '.batchprocess',
            'uses' => $controller . '@batchprocess',
            'operation' => 'batchprocess',
        ]);
        Route::any($segment . '/testorder', [
            'as' => $routeName . '.testorder',
            'uses' => $controller . '@testorder',
            'operation' => 'testorder',
        ]);
        Route::any($segment . '/{id}/canceltosaas', [
            'as' => $routeName . '.canceltosaas',
            'uses' => $controller . '@canceltosaas',
            'operation' => 'canceltosaas',
        ]);
        Route::any($segment . '/{id}/complete', [
            'as' => $routeName . '.complete',
            'uses' => $controller . '@complete',
            'operation' => 'complete',
        ]);
        Route::any($segment . '/{id}/reset', [
            'as' => $routeName . '.reset',
            'uses' => $controller . '@reset',
            'operation' => 'reset',
        ]);
        Route::any($segment . '/{id}/recalc', [
            'as' => $routeName . '.recalc',
            'uses' => $controller . '@recalc',
            'operation' => 'recalc',
        ]);
    }
    public function reset($id)
    {
        $id = $this->crud->getCurrentEntryId() ?? $id;
        $invoice = Invoice::find($id);
        $invoice->invstatus = 'O';
        $invoice->noforward_update = true;
        $invoice->norecalc = true;
        $invoice->track_num = '';
        $invoice->completed = null;
        foreach($invoice->lineitem as $lineitem)
        {
            $lineitem->sup_ord_id = '';
            $lineitem->qty= $lineitem->qty_ord;
            $lineitem->list1 = [];
            $lineitem->list2 =[];
            $lineitem->track_num = '';
            $lineitem->lineStatus = 'O';
            $lineitem->whse = '';
            $lineitem->save();
        }
        $invoice->save();
        return redirect(sophio_route('fbs/fbsorder.main', ['id' => $invoice->pk]));
    }
    public function complete($id)
    {
        $id = $this->crud->getCurrentEntryId() ?? $id;
        $invoice = Invoice::find($id);
        $this->mainTopBar($invoice);
        $canComplete = new canComplete();
        $canComplete($invoice);
        if (request()->post()) {

            $settings = new Settings();
            $settings->setSettingsByInvoice($invoice);
            (new ForceCompleted($settings))($invoice);
            return view('admin.fbs.order.complete', ['invoice' => $invoice, 'canComplete' => $canComplete, 'completed' => true]);
        }
        return view('admin.fbs.order.complete', ['invoice' => $invoice, 'canComplete' => $canComplete, 'completed' => false]);
    }
    public function recalc($id)
    {

        $id = $this->crud->getCurrentEntryId() ?? $id;
        $invoice = Invoice::find($id);
        $this->mainTopBar($invoice);
        $old_invoice = (clone $invoice);
        $invoice->recalcInvTotal = true;
        $invoice->recalc($invoice);
        $changed = false;
        if (request()->post()) {
            foreach ($invoice->lineitem as $lineitem) {
                $lineitem->save();
            }
            unset($invoice->lineitem);
            $invoice->STORENOTES = backpack_user()->name . " issued invoice recalc " . Carbon::now() . "\n" . $invoice->STORENOTES;
            $invoice->save();
            $invoice->refresh();
            if ($invoice->custtype === 'NAT') {
                $settings = new Settings();
                $settings->setSettingsByInvoice($invoice);
                $cm = new CorcentricManager($settings, $invoice->store);
                $cm->setInvoice($invoice);
                $cm->submitTransactionRequest();
                $cm->checkStatusRequest();
            }
            $changed = true;
        }
        return view('admin.fbs.order.recalc', ['invoice' => $invoice, 'old_invoice' => $old_invoice, 'changed' => $changed]);

    }

    public function deleteorder($id)
    {
        $id = $this->crud->getCurrentEntryId() ?? $id;
        $this->crud->hasAccessOrFail('delete');
        $invoice = $this->crud->getEntry($id);
        $invoice->lineitem()->delete();
        $this->crud->delete($id);
        Alert::add('success', 'Order deleted!')->flash();
        return redirect(sophio_route('fbs/fbsorder.index'));
    }

    public function canceltosaas($id)
    {
        $id = $this->crud->getCurrentEntryId() ?? $id;
        $invoice = $this->crud->getEntry($id);
        $update_url = "";
        if (isset($invoice->seller->xml['UPDATECUSTOMERURL'])) {
            $update_url = $invoice->seller->xml['UPDATECUSTOMERURL'];
        }
        foreach ($invoice->lineitem as $lineitem) {
            if (in_array($invoice->custtype, ['AF', 'B2B']) && $update_url !== "" && (int)$lineitem->mrktplcid != 0) {
                $s = explode('-', $invoice->invno);
                switch ($invoice->invstatus) {
                    case '5':
                        $reason = 'Supplier Cancelled (No stock)';
                        break;
                    case 'X':
                        $reason = 'Buyer Cancelled';
                        break;
                    default:
                        $reason = 'We cancelled';
                }
                if (isset($s[1])) {
                    LogTrack::add('Informing SaaS about cancellation...', 'cancel');
                    SendCancelToSaasUrl::dispatch(config('tenant_db'), [
                        'update_url' => $update_url,
                        'invoicepk' => $invoice->pk,
                        'storepk' => $invoice->storepk,
                        'af_invpk' => $s[1],
                        'itempk' => $lineitem->mrktplcid,
                        'reason' => $reason,
                        'accountnum' => $invoice->seller->accountnum,
                        'tried' => 0,
                    ]);
                    Alert::add('success', 'Informing SaaS about cancellation!')->flash();
                } else {
                    Alert::add('error', 'invno does not have 2 components!')->flash();
                }

            } else {
                if ($update_url == "") {
                    Alert::add('error', 'There is no url configured!')->flash();
                }

                if ($lineitem->mrktplcid == 0) {
                    Alert::add('error', 'No mrktplcId (AF itempk) set!')->flash();
                }
            }
        }
        return back();
    }

    public function testorder()
    {

        if (request()->post()) {
            /*
            $invoice = new Invoice();
            $invoice->custtype = request()->get('custtype');
            $invoice->custpk = request()->get('custpk');
            $invoice->storepk = $settings->getStore()->STOREPK;
            $supplier = Supplier::find(request()->get('profilepk'));
            $pw = ProductWarehouse::where('contactpk',$supplier->contactpk)->where('qty_avail','>',0)
                ->where('wd_min_qty',0)->where('cost','<',5)->where('matching_type',1)
                ->orderBy('qty_avail','DESC')->first();
            $lineitem = new LineItem();
            $lineitem->storepk = $settings->getStore()->STOREPK;

            $lineitem->sku = $pw->part_number_unformatted;
            $lineitem->itemform = $lineitem->skuform = $pw->product_number;
            $lineitem->linecode = $pw->mfgcode;
            $lineitem->wdlinecode = $pw->linecode;
            $lineitem->descript  = request()->get('description')??$pw->wddescription;
            $lineitem->cost = request()->get('cost') && request()->get('cost')!=''? (float)request()->get('cost') : $pw->cost;
            $lineitem->price = $pw->sellprice;
            $lineitem->coreprice = $pw->coreprice;
            $lineitem->itemtotal = $pw->cost;
            $lineitem->timein = $lineitem->timestamp = Carbon::now();
            $lineitem->stock = $pw->qty_avail;
            $lineitem->itempk = $pw->product_sku??-1;
            $lineitem->profilepk = $supplier->PK;
            $lineitem->contactpk = $supplier->contactpk;
            */

            $it = new TestInvoice(new Settings());
            $invoice = $it->createTestInvoice(request()->all());
            return redirect(sophio_route('fbs/fbsorder.whdorder', ['id' => $invoice->pk, 'supplierpk' => request()->get('profilepk'), 'ManagerApproval' => 2, 'alternateflag' => 'yes']));
            /*
            if(request()->get('fullfil')=='true') {
                return redirect(sophio_route('fbs/fbsorder.main', ['id' => $invoice->pk]));
            }else{
                return redirect(sophio_route('fbs/fbsorder.whdorder', ['id' => $invoice->pk,'supplierpk'=>request()->get('pk'),'ManagerApproval'=>2,'alternateflag'=>'yes']));
            }
            */

        }
        return view('admin.fbs.order.testorder');
    }
    public function sendtrackingcustomer()
    {
        $id = $this->crud->getCurrentEntryId();
        $invoice = $this->crud->getEntry($id);
        $tracknums = explode(',', $invoice->track_num);
        foreach($tracknums as $tracknum) {
            (new SendTrackingMailtoCustomer())($invoice,sophiosettings(),$tracknum,true);
        }
        Alert::add('success', 'Send mail to customer with tracking numbers!')->flash();
        return redirect(sophio_route('fbs/fbsorder.main', ['id' => $invoice->pk]));
    }
    public function sendtrackingrequestnumber($id)
    {
        $id = $this->crud->getCurrentEntryId() ?? $id;
        $invoice = $this->crud->getEntry($id);

        $profilepks = $invoice->lineitem->where('sup_ord_id', '<>', '')->groupBy('profilepk');
        if (request()->get('profilepk', '') == '' && $profilepks->count() == 0) {
            return view('admin.error', ['message' => 'There is no supplier attached to any of the lines of the order! Please check the order!']);
        }
        $this->mainTopBar($invoice);
        if (request()->get('profilepk', '') == '' && $profilepks->count() > 1) {
            $already=[];
            foreach($invoice->lineitem as $lineitem) {
                if($lineitem->track_num!=="") {
                    if(!in_array($lineitem->profilepk,$already)) {
                        $already[] = $lineitem->profilepk;
                    }
                }
            }
            return view('admin.fbs.order.sendtrackingrequestnumber', ['invoice' => $invoice, 'profilepks' => $profilepks, 'sent' => null,'already'=>$already]);
        } else {
            $s = new SendTrackingNumberRequest();
            if($invoice->track_num==="" || request()->get('resend')) {
                $s($invoice, request()->get('profilepk') ?? $profilepks->first()->first()->supplier->PK, request()->get('override'));
                return view('admin.fbs.order.sendtrackingrequestnumber', ['invoice' => $invoice, 'profilepks' => $profilepks, 'sent' => $s]);
            } else{
                return view('admin.fbs.order.sendtrackingrequestnumber', ['invoice' => $invoice, 'profilepks' => $profilepks, 'sent' => null,'already'=>[$profilepks->first()->first()->supplier->PK]]);
            }

        }
    }

    public function sendsupplierordercancellation($id)
    {
        $id = $this->crud->getCurrentEntryId() ?? $id;
        $invoice = $this->crud->getEntry($id);
        $this->mainTopBar($invoice);
        $profilepks = $invoice->lineitem->where('sup_ord_id', '<>', '')->groupBy('profilepk');
        if ($profilepks->count() == 0) {
            return view('admin.fbs.order.sendsupplierordercancellation', ['invoice' => $invoice, 'profilepks' => [], 'sent' => null]);
        } else
            if (request()->get('profilepk', '') == '' && $profilepks->count() > 1) {
                return view('admin.fbs.order.sendsupplierordercancellation', ['invoice' => $invoice, 'profilepks' => $profilepks, 'sent' => null]);
            } else {
                $s = new CancelService(new Settings(), $invoice);
                if (request()->get('profilepk')) {
                    $supplier = Supplier::find(request()->get('profilepk'));
                    $line = $invoice->lineitem->where('profilepk', request()->get('profilepk'))->first();
                } else {
                    $supplier = $profilepks->first()->first()->supplier;
                    $line = $profilepks->first()->first();
                }
                $sent = $s->sendSupplierOrderCancellation($supplier, $line);
                return view('admin.fbs.order.sendsupplierordercancellation', ['invoice' => $invoice, 'profilepks' => $profilepks, 'sent' => $sent, 'supplier' => $supplier]);
            }
    }

    public function assigntomanager()
    {
        $settings = new Settings(['fbs_database' => Config::get('tenant_db')]);
        $id = $this->crud->getCurrentEntryId();
        $invoice = $this->crud->getEntry($id);
        $settings->setSettingsByInvoice($invoice);
        $invoice->user_pk = request()->get('user_pk') ?? $settings->get('MANAGERUSERID');
        $invoice->STORENOTES = "Order assigned to Manager (" . $invoice->user_pk . ") by " . backpack_user()->name . " at " . Carbon::now() . "\n" . $invoice->STORENOTES;
        $invoice->invstatus = '3';
        $invoice->norecalc = true;
        $invoice->save();
        $lock = Cache::store('lock')->lock('.invoice.' . $invoice->pk, 0, $invoice->user_pk);
        Alert::add('success', 'Order was assigned to Manager!')->flash();
        return redirect(sophio_route('fbs/fbsorderdashboard.openorders'));

    }

    public function find()
    {
        if (request()->post()) {
            if (request()->get('query')) {
                $invoice = Invoice::where('ponumber', trim(request()->get('query')))->first();
                if ($invoice) {
                    return redirect(sophio_route('fbs/fbsorder.main', ['id' => $invoice->pk]));
                }
                if(is_numeric(trim(request()->get('query')))) {
                    $invoice = Invoice::where('pk', (int)trim(request()->get('query')))->first();
                    if ($invoice) {
                        return redirect(sophio_route('fbs/fbsorder.main', ['id' => $invoice->pk]));
                    }
                }

                $carrier = getCarrierFromTrackNum(trim(request()->get('query')));
                $invoices = Invoice::where('wws_invoice.track_num', '=', $carrier . ':' . trim(request()->get('query')))->get();
                if (count($invoices) == 1) {
                    return redirect(sophio_route('fbs/fbsorder.main', ['id' => $invoices[0]->pk]));
                } elseif (count($invoices) > 0) {
                    return redirect(sophio_route('fbs/fbsorder.index', ['track_num' => $carrier . ':' . trim(request()->get('query'))]));
                } else {
                    $lineitem = LineItem::where('wws_lineitems.track_num', '=', $carrier . ':' . trim(request()->get('query')))->first();
                    if ($lineitem) {
                        return redirect(sophio_route('fbs/fbsorder.main', ['id' => $lineitem->invpk]));
                    }
                }

            }
        }
        Alert::add('danger', 'Nothing found!')->flash();
        return back();
    }

    public function batchprocess()
    {
        return view('comingsoon');
    }

    public function paymentlogdetail()
    {
        return view('comingsoon');
    }

    public function paymentreconciliation()
    {
        return view('comingsoon');
    }

    public function missingtrackingnumbers()
    {
        return view('comingsoon');

    }

    public function fetchLineitem()
    {
        return $this->fetch(LineItem::class);

    }

    public function manualsubmit($id)
    {
        $settings = new Settings(['fbs_database' => Config::get('tenant_db')]);
        $id = $this->crud->getCurrentEntryId() ?? $id;
        $invoice = $this->crud->getEntry($id);

        if ((new CanOrderManually)($invoice, $settings)) {
            return view('admin.fbs.order.manualsubmit', ['invoice' => $invoice]);
        } else {
            return redirect(backpack_url('dbs/' . config('tenant_db') . '/fbs/fbsorder/whdmanualcheck?id=' . $invoice->pk));
        }
    }

    public function changenotes($id)
    {
        if (request()->post()) {
            $id = $this->crud->getCurrentEntryId() ?? $id;
            $invoice = $this->crud->getEntry($id);
            $invoice->STORENOTES = request()->get('note');
            $invoice->norecalc = true;
            $invoice->save();
            Alert::add('success', 'Notes updated!')->flash();
        } else {
            Alert::add('danger', 'Needs to come from a form!')->flash();
        }

        return back();
    }

    public function changeorderstatus($id)
    {
        if (request()->post()) {
            $id = $this->crud->getCurrentEntryId() ?? $id;
            $invoice = $this->crud->getEntry($id);
            $invoice->invstatus = request()->get('invstatus');
            $invoice->STORENOTES = request()->get('note') . "\n" . $invoice->STORENOTES;
            $invoice->norecalc = true;
            $invoice->save();
            Alert::add('success', 'Invoice status changed!')->flash();
        } else {
            Alert::add('danger', 'Needs to come from a form!')->flash();
        }

        return back();
    }

    public function unlock()
    {
        if (auth()->user()->can('manager-approval')) {
            Cache::store('lock')->lock('.invoice.' . $this->crud->getCurrentEntryId())->forceRelease();
        }
        return back();
    }

    public function postcancel()
    {
        $settings = new Settings(['fbs_database' => Config::get('tenant_db')]);
        $id = $this->crud->getCurrentEntryId();
        $invoice = $this->crud->getEntry($id);
        $settings->setSettingsByInvoice($invoice);
        $settings->setSettingsByCustomer($invoice->seller);
        $cancelService = new CancelService($settings, $invoice);
        $cancelService->postCancel();
        if ($cancelService->getRequeireScreen() == true) {
            return view('admin.fbs.order.postcancel', ['invoice' => $invoice]);
        } else {
            return redirect(backpack_url('dbs/' . config('tenant_db') . '/fbs/fbsorder/' . $invoice->pk."/main"));
        }

    }

    public function confirmcancel()
    {
        $settings = new Settings(['fbs_database' => Config::get('tenant_db')]);
        $id = $this->crud->getCurrentEntryId();
        $invoice = $this->crud->getEntry($id);
        $settings->setSettingsByInvoice($invoice);
        $settings->setSettingsByCustomer($invoice->seller);
        $cancelService = new CancelService($settings, $invoice);
    }

    public function cancelorder()
    {
        setLayoutBoxed();
        $id = $this->crud->getCurrentEntryId();
        $invoice = $this->crud->getEntry($id);
        $this->mainTopBar($invoice);
        $error = "";
        $settings = new Settings(['fbs_database' => Config::get('tenant_db')]);
        $canCancel = false;
        if (request()->post()) {
            $cancelOrder = new CancelOrder();
            $status = $cancelOrder($invoice, (string)request()->get('newstatus'), request()->get('note'), backpack_user(), Causer::SELLER, 'adminui-cancel');
            if ($status == true) {
                // return redirect(backpack_url('dbs/' . config('tenant_db') . '/fbs/fbsorder/' . $invoice->pk . '/postcancel'));
                return view('admin.fbs.order.cancelpost', ['invoice' => $invoice, 'error' => $error, 'action' => 'order', 'cancelled' => true]);

            } else {
                $error = $cancelOrder->getError();
            }
        } else {
            $CanCancelOrder = new CanCancelOrder();
            $canCancel = $CanCancelOrder($invoice, backpack_user());
            if ($canCancel === false) {
                $error = implode('<br>', LogTrack::get('invoice'));
            }
        }

        return view('admin.fbs.order.cancelorder', ['invoice' => $invoice, 'error' => $error, 'canCancel' => $canCancel, 'cancelled' => false, 'override' => request()->get('override')]);
    }

    public function refund()
    {
        $settings = new Settings();
        $id = $this->crud->getCurrentEntryId();
        $invoice = $this->crud->getEntry(\Route::current()->parameter('id'));
        $error = null;
        $this->mainTopBar($invoice);
        $refunded = false;
        $pdm = new PaymentDetailManager($settings);
        [$paid, $owned] = $pdm->getPaidVsOwned($invoice);
        $refund_lines = [];
        $amount = 0;
        if (request()->post()) {
            $refundService = new RefundService(new Settings([]));
            if (request()->get('refund_force', '') !== '') {
                $amount = $invoice->invtotal;
                $invoice->STORENOTES = auth()->user()->name. " forces refund of ".$amount." ".Carbon::now()."\n".$invoice->STORENOTES;
            } else
                if (request()->get('refund_remain', '') !== '') {
                    $amount = $paid + $owned;
                    $invoice->STORENOTES = auth()->user()->name. "  refund remaining of ".$amount." ".Carbon::now()."\n".$invoice->STORENOTES;
                } else {
                    if (request()->get('lineitem', '') !== '' && count(request()->get('lineitem')) == 0) {
                        $error = "No Lines selected. Nothing to Refund!";
                    } else {
                        if (in_array($invoice->ccresult ,['APPROVED','PARTIAL REFUNDED']) && $invoice->ccpaid > 0 && in_array($invoice->paymethod, ['PP', 'ST']) && Str::contains($invoice->CCRESULTX, 'Trans Id:')) {
                            $refund_lines = request()->get('lineitem');
                            $amount = $invoice->lineitem->whereIn('pk', $refund_lines)->sum('itemtotal');
                            $invoice->STORENOTES = auth()->user()->name. "  refund lines worth  of ".$amount." ".Carbon::now()."\n".$invoice->STORENOTES;
                        } else {
                            $error = "Cannot refund. Check if invoice payment is approved, ccpaid is not zero or if the payment was ever made!";
                        }
                    }

                }
            if ($amount > 0) {

                $invoice->norecalc = true;
                $invoice->noforward_update = true;
                $invoice->save();
                $pm = $refundService->refundStripe($invoice, null, $amount, (request()->get('refund_force', '') !== '') ? true : false);
                if ($pm !== null) {
                    if (count($refund_lines) > 0) {
                        $refund_lineitems = $invoice->lineitem->whereIn('pk', $refund_lines);
                    } else {
                        $refund_lineitems = $invoice->lineitem;
                    }
                    foreach ($refund_lineitems as $lineitem) {
                        $lineitem->lineStatus = 'R';
                        $lineitem->save();
                    }
                    $refunded = true;
                } else {
                    $invoice->invstatus = '3';
                    // $invoice->save();
                    $error = $refundService->getError();
                }
            } else {
                $error = "Amount was zero, nothing to refund!";
            }

        }
        // call this again because if we made action( a refund), number may have changed
        [$paid, $owned] = $pdm->getPaidVsOwned($invoice);
        return view('admin.fbs.order.refund', ['invoice' => $invoice, 'error' => $error, 'refunded' => $refunded, 'paid' => $paid, 'owned' => $owned,'amount'=>$amount]);
    }

    public function cancellineitem()
    {
        \Config::set('tenant_db', request()->route()->parameter('database'));
        $settings = new Settings();
        $id = $this->crud->getCurrentEntryId();
        $invoice = $this->crud->getEntry(\Route::current()->parameter('id'));
        $this->mainTopBar($invoice);
        $lineitem = $invoice->lineitem->find(\Route::current()->parameter('lineitempk'));
        $error = null;
        $cancelled = false;
        if (request()->post()) {
            if (request()->get('status') == '5') {
                $voidAction = new VoidLineItem();
                $voided = $voidAction($lineitem->invoice, $lineitem, $settings);
                if ($voided == false) {
                    $error = $voidAction->getError();
                } else {
                    $error = "";
                }
                return view('admin.fbs.order.voidlineitem', ['invoice' => $lineitem->invoice, 'voided' => $voided, 'error' => $error]);
            } else {
                $cancelLineitem = new CancelLineItem();
                if ($cancelLineitem($invoice, $lineitem, backpack_user(), Causer::SELLER, request()->get('status'), 'ui-linecancel', true)) {
                    if ($invoice->lineitem->count() == 1 || $invoice->lineitem->whereIn('lineStatus', ['X', '4'])->count() == $invoice->lineitem->count()) {

                        (new CancelInvoice())($invoice, request()->get('status'), "Cancelled via lineitem cancel", backpack_user(), Causer::SELLER, 'adminui-cancel');
                        return view('admin.fbs.order.cancelpost', ['invoice' => $invoice, 'lineitem' => $lineitem, 'action' => 'lineitem']);
                    }
                    $cancelled = true;
                } else {
                    $error = $cancelLineitem->getError();
                }
            }
        }
        return view('admin.fbs.order.cancellineitem', ['invoice' => $invoice, 'lineitem' => $lineitem, 'error' => $error, 'messages' => LogTrack::get('cancel'), 'cancelled' => $cancelled]);
    }

    protected function setupListOperation()
    {
        if (request()->get('openorder')) {
            CRUD::setEntityNameStrings('fbsorders', 'Open Orders');
        }
        CRUD::enableExportButtons();
        CRUD::setOperationSetting('lineButtonsAsDropdown', true);
        $this->doList();
        $this->crud->enableDetailsRow();

    }

    protected function showDetailsRow($id)
    {
        $id = $this->crud->getCurrentEntryId() ?? $id;
        $this->data['entry'] = $this->crud->getEntry($id);


        $this->crud->setColumns([
            [
                'name' => 'track_num',
                'label' => 'Tracking number',

            ],
            [
                'name' => 'supplier',
                'label' => 'Supplier',
                'type' => 'select',
                'attribute' => 'NAME',

                'entity' => 'supplier',
                'wrapper' => [

                    'href' => function ($crud, $column, $entry, $related_key) {
                        return backpack_url('dbs/' . \Route::current()->parameter('database') . '/fbs/supplier/' . $related_key . '/edit');
                    },
                    'target' => '_blank',

                ],
            ],

            [
                'name' => 'invno',
                'label' => 'Invoice#',

            ],
            [
                'name' => 'ccresult',
                'label' => 'Payment',

            ],
            [
                'name' => 'lineitem',
                'label' => 'Items',
                'type' => 'relationship',
                'attribute' => 'LinecodeSkuQtyColor',
                'entity' => 'lineitem',
                'escaped' => false,
                'priority' => 1000,
                'limit' => 100,

            ],


        ]);
        $this->data['crud'] = $this->crud;
        return view('admin.fbs.order.details', $this->data);
    }

    protected function setupShowOperation()
    {

        if (backpack_user()->can('manage order')) {
            $this->crud->addButtonFromView('line', 'processneworder', 'processneworder', 'end');
        }
        $this->doShow();
    }

    protected function setupCreateOperation()
    {
        $this->doCreate();
    }

    protected function setupUpdateOperation()
    {
        $id = $this->crud->getCurrentEntryId();
        $invoice = $this->crud->getEntry($id);
        $this->mainTopBar($invoice);
        $this->setupCreateOperation();
    }

    public function clone()
    {
        $id = \Route::current()->parameter('id');
        $this->crud->hasAccessOrFail('clone');
        $lineitems = $this->crud->model->findOrFail($id)->lineitem;
        $invoice = $this->crud->model->findOrFail($id);
        $invoice->invstatus = 'O';
        $invoice->invno = 'TEST' . $invoice->invno;
        $invoice->ponumber = 'TEST' . $invoice->ponumber;
        $invoice->ccpaid = 0;
        $invoice->CCRESULTX = 'APPROVED';
        $invoice->ccresult = 'NEW';
        $invoice->track_num = '';
        $invoice->completed = null;
        $clonedEntry = $invoice->replicate();

        $push = $clonedEntry->push();
        foreach ($lineitems as $lineitem) {
            $clonedLineitem = LineItem::findOrFail($lineitem->pk)->replicate();
            $clonedLineitem->invpk = $clonedEntry->pk;
            $clonedLineitem->sup_ord_id = "";
            $clonedLineitem->profilepk = 0;
            $clonedLineitem->track_num = "";
            $clonedLineitem->list2 = [];
            $clonedLineitem->lineStatus = "O";
            $clonedLineitem->save();
        }

        return (string)$push;
    }


    public function main($database, $id = null)
    {

        $id = $id ?? request()->get('id');
        \Config::set('tenant_db', request()->route()->parameter('database'));

        $invoice = Invoice::where('pk', $id)->first();
        if (!$invoice) {
            return view('admin.nonexisting',
                [
                    'resource_name' => 'invoice',
                    'resource_id' => $id
                ]);
        }

        $this->mainTopBar($invoice);

        if (!backpack_user()->can('manage order')) {
            throw new AccessDeniedException(trans('backpack::crud.unauthorized_access', ['access' => 'RTSC']));
        }

        /*
        if ($invoice->user_pk != backpack_user()->id) {
            $invoice->user_pk = backpack_user()->id;
            $invoice->save();
            activity()->performedOn($invoice)->log('Taking ownership');
        }
        */
        $warning = [];
        $lock = false;
        $in_processing = false;
        if (backpack_user()->getAuthIdentifier() !== $invoice->user_pk && $invoice->user_pk > 0) {
            $warning = ['type' => 'another_user_own', 'user' => $invoice->user_pk];
        }
        $lock = Cache::store('lock')->lock('.invoice.' . $invoice->pk, 3600, backpack_user()->getAuthIdentifier());
        if ($lock->get()) {
            if ($invoice->user_pk == 0 && backpack_user()->getAuthIdentifier() !== $invoice->user_pk) {
                $invoice->user_pk = backpack_user()->getAuthIdentifier();
                $invoice->norecalc = true;
                $invoice->save();
            }
        } else {
            $lockService = new LockService();
            $user_id = $lockService->getOwnerOfKey('.invoice.' . $invoice->pk);
            if ($user_id == 0) {
                $in_processing = true;
            }
            $warning = ['type' => 'another_user_edits', 'user' => $invoice->user_pk];
        }
        return view('admin.fbs.order.main', [
                'invoice' => $invoice,
                'warning' => $warning,
                'lock' => $lock,
                'in_processing' => $in_processing
            ]
        );
    }

    public function whdmanualcheck($database, $id = null)
    {
        ini_set('max_execution_time', '120');
        $id = $id ?? request()->get('id');
        if (!backpack_user()->can('manage order')) {
            throw new AccessDeniedException(trans('backpack::crud.unauthorized_access', ['access' => 'RTSC']));
        }
        \Config::set('tenant_db', request()->route()->parameter('database'));
        $invoice = Invoice::where('pk', $id)->first();
        if (!$invoice) {
            return view('admin.nonexisting',
                [
                    'resource_name' => 'invoice',
                    'resource_id' => $id
                ]);
        }
        /*
        if ($invoice->user_pk != backpack_user()->id) {
            $invoice->user_pk = backpack_user()->id;
            $invoice->save();
            activity()->performedOn($invoice)->log('Taking ownership');
        }
        */
        $settings = new Settings(['fbs_database' => Config::get('tenant_db')]);
        if (auth()->user()->can('manager-approval')) {
            $settings->set('ManagerApproval', 2);
        }
        $settings->setSettingsByInvoice($invoice);
        $settings->setSettingsByCustomer($invoice->seller);


        return view('admin.fbs.order.whd.manualcheck', [
                'invoice' => $invoice,
                'settings' => $settings,
                'all_suppliers' => (new ManualSuppliersList())($invoice, $settings)
            ]
        );
    }

    public function whdautocheck($database, $id)
    {


        ini_set('max_execution_time', '120');
        $id = $id ?? request()->get('id');

        \Config::set('tenant_db', request()->route()->parameter('database'));

        $invoice = Invoice::where('pk', $id)->first();
        /*
        if ($invoice->user_pk != backpack_user()->id) {
            $invoice->user_pk = backpack_user()->id;
            $invoice->save();
            activity()->performedOn($invoice)->log('Taking ownership');
        }
        */

        $settings = new Settings(['fbs_database' => Config::get('tenant_db')]);
        $settings->setSettingsByInvoice($invoice);

        $transFeesService = new TransFeesService($settings);
        $calculateProfit = new CalculateProfit($transFeesService);
        if (auth()->user()->can('manager-approval')) {
             $settings->set('ManagerApproval', 2);

        }
        if (request()->get('alternateflag') != '') {
            $settings->set('alternateflag', request()->get('alternateflag'));
        }
        try {
            $return = (new CheckWithRTSC($settings)) ($invoice);
        } catch (\SoapFault|ErrorException $e) {
            config(['tenant_db' => config('sophio.admin.default_database')]);
            return view('admin.error', ['message' => 'It is not you! Communication error with WHI Orderlink service! Try again in a minute. If this persist contact support team.']);
        }

        if (isset($return['disco'])) {
            $suppliers = $return['disco']->getSuppliers();
            $itemList = $return['disco']->getItemList();
            $result = $return['disco']->getResult();
        }
        $productInfo = [];
        foreach ($itemList as $item) {
            foreach ($item->sources as $warehouse) {
                if (isset($warehouse->rtsc) && isset($warehouse->rtsc['attributes']) && isset($warehouse->rtsc['attributes']['mfgcode'])) {
                    if (!isset($productInfo[mkMgfSku($warehouse->rtsc['attributes']['mfgcode'], $warehouse->rtsc['attributes']['partno'])])) {
                        $productInfo[mkMgfSku($warehouse->rtsc['attributes']['mfgcode'], $warehouse->rtsc['attributes']['partno'])] = new ProductInformation($warehouse->rtsc['attributes']['mfgcode'], unformatString($warehouse->rtsc['attributes']['partno']));
                        $productInfo[mkMgfSku($warehouse->rtsc['attributes']['mfgcode'], $warehouse->rtsc['attributes']['partno'])]->setProduct();
                        $productInfo[mkMgfSku($warehouse->rtsc['attributes']['mfgcode'], $warehouse->rtsc['attributes']['partno'])]->setProductFeed();
                    }
                }
            }
        }
        //$all_suppliers = Supplier::whereIn('SUP', ['WHD', 'GP', 'PAR', 'UAS',"FED"])->where('ACTIVE', 1)->storemarket($invoice->storepk, $invoice->custtype)->get();
        $all_suppliers = Supplier::whereIn('SUP', ['WHD', 'GP', 'PAR', 'UAS',"FED"])->where('ACTIVE', 1)->get();
        $has_more = false;
        foreach($all_suppliers as $supplier) {
            if(!in_array($supplier->contactpk,array_keys($suppliers))){
                $has_more = true;
            }
        }
        if($has_more==false) {
            $all_suppliers=[];
        }
        return view('admin.fbs.order.whd.autocheck', [
                'invoice' => $invoice,
                'valid' => $return['valid'],
                'errors' => $return['errors'],
                'suppliers' => $suppliers,
                'itemList' => $itemList,
                'result' => $result,
                'settings' => $settings,
                'sup_logs' => [],
                'productInfo' => $productInfo,
                'calculateProfit' => $calculateProfit,

                'all_suppliers' => $all_suppliers
            ]
        );
    }


    public function whdorder()
    {

        ini_set('max_execution_time', '120');
        config(['logging.default' => 'fbsorder']);

        \Config::set('tenant_db', request()->route()->parameter('database'));
        $invoice = Invoice::where('pk', request()->get('id'))->first();
        if (!$invoice) {
            return view('admin.nonexisting',
                [
                    'resource_name' => 'invoice',
                    'resource_id' => request()->get('id')
                ]);
        }

        $settings = new Settings(['fbs_database' => Config::get('tenant_db')]);
        if (auth()->user()->can('manager-approval')) {
            if (request()->get('showall')) {
                $settings->set('ShowAllLineitems', true);
            }
            $settings->set('ManagerApproval', 2);
        }

        if (request()->get('alternateflag') != '') {

            $settings->set('alternateflag', request()->get('alternateflag'));
        }
        $settings->set('allow_not_matched', true);

        $settings->set('contactpk_only', request()->get('contactpk'));

        //$settings->set('ignore_unresolved', true);
        $supplier = Supplier::where('PK', request()->get('supplierpk'))->first();
        if (!$supplier) {
            return view('admin.nonexisting',
                [
                    'resource_name' => 'supplier',
                    'resource_id' => request()->get('supplierpk')
                ]);
        }
        $orderManager = new OrderManager();
        $result = $orderManager->whdorder($invoice, $supplier, $settings);
        if(isset($supplier->SETTINGS['SPECIALORDERBRANCHINJECT']) && $supplier->SETTINGS['SPECIALORDERBRANCHINJECT'] === 'TRUE') {
            foreach ($result['parts'] as $k => $part) {
                if (!isset($part['attributes'])) {
                    $result['parts'][$k]['attributes'] = [
                        'linecode' => $part['linecode'],
                        'sku' => $part['partno'],
                        'partno' => $part['partno'],
                        'cost' => 0,
                        'core' => 0,
                        'qtyavail' => 0,

                    ];
                    $result['parts'][$k]['attributes']['errcode'] = 'error';
                    $result['parts'][$k]['attributes']['linecode'] = $part['linecode'];
                    $result['parts'][$k]['attributes']['sku'] = $part['partno'];
                    $result['parts'][$k]['attributes']['cost'] = 0.0;
                    $result['parts'][$k]['attributes']['coreprice'] = 0.0;
                    $result['parts'][$k]['attributes']['qtyavail'] = 0;
                    $result['parts'][$k]['altbranch'] = [];
                    $result['parts'][$k]['attributes']['qtyreq'] = $part['qtyreq'];
                    $result['parts'][$k]['attributes']['mfgcode'] = $part['linecode'];
                    $result['parts'][$k]['attributes']['desc'] = "Not Found! Make sure linecode and part number are correct!";
                    $result['parts'][$k]['attributes']['minqty'] = 1;
                    $result['parts'][$k]['attributes']['uom'] = '';
                    $result['parts'][$k]['attributes']['branch'] = '003';
                    $result['parts'][$k]['attributes']['found_qtyavail'] = 0;
                    $result['parts'][$k]['attributes']['alternateflag'] = 'no';
                    $result['parts'][$k]['altbranch'] = [[
                        'alt_branch' => $supplier->SETTINGS['SPECIALORDERBRANCH'],
                        'branch_name' => 'Special order',
                        'branch_qtyavail' => 0,
                        'viewonly' => 'no'
                    ]];
                }
            }
        }
        return view('admin.fbs.order.whd.order', [
            'invoice' => $invoice,
            'supplier' => $supplier,
            'parts' => $result['parts'],
            'canOrder' => $result['canOrder'],
            'selected_branch' => $result['selected_branch'],
            'orderlinkErrors' => LogTrack::get('orderlink')
        ]);
        if($settings->getStore()->STORETYPE=='TE') {
            $result = $orderManager->ordersp($invoice, $supplier, $settings);

            return view('admin.fbs.order.whd.ordersp', [
                'invoice' => $invoice,
                'supplier' => $supplier,
                'parts' => $result['parts'],
                'canOrder' => $result['canOrder'],
                'selected_branch' => $result['selected_branch'],
                'orderlinkErrors' => LogTrack::get('orderlink')
            ]);
        }else{
            $result = $orderManager->whdorder($invoice, $supplier, $settings);

            return view('admin.fbs.order.whd.order', [
                'invoice' => $invoice,
                'supplier' => $supplier,
                'parts' => $result['parts'],
                'canOrder' => $result['canOrder'],
                'selected_branch' => $result['selected_branch'],
                'orderlinkErrors' => LogTrack::get('orderlink')
            ]);
        }

    }

    public function orderprint()
    {
        config(['logging.default' => 'fbsorder']);

        \Config::set('tenant_db', request()->route()->parameter('database'));
        $invoice = Invoice::where('pk', request()->get('id'))->first();
        return view('admin.fbs.order.orderprint', [
            'invoice' => $invoice
        ]);
    }


    /**
     * @throws NotFoundExceptionInterface
     * @throws ContainerExceptionInterface
     * @throws \Exception
     */
    public function whdconfirm()
    {

        ini_set('max_execution_time', '120');

        if (!request()->post()) {
            return redirect()->back();
        }
        $settings = new Settings(['fbs_database' => request()->route()->parameter('database'), 'seller_database' => request()->route()->parameter('database'), 'parallel_soap' => 0]);
        if (backpack_user()->hasRole('super-admin')) {
            $settings->set('ManagerApproval', 2);
        }
        \Config::set('tenant_db', request()->route()->parameter('database'));
        $invoice = Invoice::where('pk', request()->get('id'))->first();
        $this->mainTopBar($invoice);
        if (!$invoice) {
            return view('admin.nonexisting',
                [
                    'resource_name' => 'invoice',
                    'resource_id' => request()->get('id')
                ]);
        }
        $sup = Supplier::where('PK', request()->get('supplierpk'))->first();

        if (!$sup) {
            return view('admin.nonexisting',
                [
                    'resource_name' => 'supplier',
                    'resource_id' => request()->get('supplierpk')
                ]);
        }


        if (!(new CanOrderManually)($invoice, $settings)) {
            return view('admin.fbs.order.ordersendnotallowed', ['invoice' => $invoice, 'supplier' => $sup]);
        }
        $lineitems = request()->get('lineitem');
        foreach ($lineitems as $lpk) {
            $l = LineItem::find($lpk);
            if ($l->sup_ord_id !== "") {
                LogTrack::add('Line ' . $l->pk . ' already has a purchase order id ' . $l->sup_ord_id . '!', 'suppliers', false);
                return view('admin.fbs.order.ordersendnotallowed', ['invoice' => $invoice, 'supplier' => $sup]);
            }
        }
        $return = (new ManualPurchaseFromSupplier())($invoice, $sup, $settings, [
            'lineitem' => request()->get('lineitem'),
            'branch' => request()->get('branch'),
            'rtsc' => request()->get('rtsc'),
            'branches' => request()->get('branches'),
            'qty' => request()->get('qty'),
            'comments' => request()->get('comments'),
        ]);


        return view('admin.fbs.order.whd.confirm', ['type' => $return['type'] ?? null, 'response' => $return['response'] ?? null, 'request' => $return['request'] ?? null, 'invoice' => $invoice, 'supplier' => $sup,
            'rawResponse' => $return['rawResponse'] ?? null]);
        //  return view('admin.fbs.order.whd.confirm', ['orderconf' => $return['orderconf'] ?? null, 'invoice' => $invoice, 'supplier' => $sup, 'request' => $return['request']]);
    }

    public function lastsupplierlogs($id)
    {

        $invoice = Invoice::where('pk', request()->get('id'))->first();
        if(!request()->ajax()) {
           $this->mainTopBar($invoice);
        }

        $settings = new Settings();
        config(['tenant_db' => \Route::current()->parameter('database')]);
        $logs = (new InvoiceLastSupplierLogs())($invoice, $settings);
        if (isset($invoice->seller->settings['ACESPARTSDB'])) {
            $settings = new Settings(['fbs_database' => $invoice->seller->settings['ACESPARTSDB']]);
            Config::set('tenant_db', $invoice->seller->settings['ACESPARTSDB']);
            $logs = $logs->merge((new InvoiceLastSupplierLogs())($invoice, $settings));

        }
        if ($invoice->custtype == "NAT") {
            $settings = new Settings(['fbs_database' => config('sophio.admin.sparklink_order_database')]);
            Config::set('tenant_db', config('sophio.admin.sparklink_order_database'));
            $logs = $logs->merge((new InvoiceLastSupplierLogs())($invoice, $settings));
        }
        if(!request()->ajax()) {
            return view('admin.fbs.order.lastsuplogs', ['invoice' => $invoice, 'sup_logs' => $logs]);
        }else{
            return view('admin.fbs.order.lastsupplierlogs', ['invoice' => $invoice, 'sup_logs' => $logs]);
        }

    }

    public function paymentstatus()
    {
        $invoice = Invoice::where('pk', $this->crud->getCurrentEntryId())->first();
        $settings = new Settings(['fbs_database' => config('sophio.admin.default_database')]);
        $settings->setSettingsByInvoice($invoice);
        $pdm = new PaymentDetailManager($settings);
        $details = $pdm->paymentStatus($invoice);
        $this->mainTopBar($invoice);
        return view('admin.fbs.order.paymentstatus', ['invoice' => $invoice, 'details' => $details]);

    }


    public function sellerfulfillmentstatus()
    {
        $pk = request()->get('id');
        \Config::set('tenant_db', request()->route()->parameter('database'));
        $invoice = Invoice::where('pk', request()->get('id'))->first();
        $settings = new Settings(['fbs_database' => Config::get('tenant_db')]);
        $settings->setSettingsByInvoice($invoice);
        return response()->json(['response' => (new CheckFulfillmetStatusAtSeller($settings))($invoice)]);
    }


    public function duptosophio()
    {
        $pk = request()->get('id');
        $customer = Customer::where('pk', 226160)->first();
        \Config::set('tenant_db', request()->route()->parameter('database'));
        $invoice = Invoice::where('pk', request()->get('id'))->first();
        $settings = new Settings(['fbs_database' => Config::get('tenant_db')]);
        $creator = new InvoiceManager($settings);
        foreach ($invoice->toArray() as $key => $value) {
            $creator->setField($key, $value);
        }
        $creator->setField('pk', 10000000 + $invoice->pk);
        $creator->setField('pk', 10000000 + $invoice->pk);
        $creator->setField('ponumber', 'TEST' . $invoice->ponumber);
        $creator->setField('invno', 'TEST' . $invoice->invno);
        $creator->setField('invstatus', "O");
        $lines = [];
        foreach ($invoice->lineitem as $lineitem) {
            $line = $lineitem->toArray();
            unset($line['pk']);
            unset($line['sup_ord_id']);
            $line['custpk'] = 226160;
            unset($line['qty']);
            unset($line['list1']);
            unset($line['list2']);
            unset($line['cost']);
            unset($line['profilepk']);
            unset($line['contactpk']);
            unset($line['track_num']);
            unset($line['supinvpk']);
            $lines[] = $line;
        }
        $creator->setField('lines', $lines);

        $creator->setSeller($customer);
        $creator->setFields([
            'st_name' => $customer->st_name,
            'st_addr' => $customer->st_addr,
            'st_addr2' => $customer->st_addr2,
            'st_city' => $customer->st_city,
            'st_state' => $customer->st_state,
            'st_zip' => $customer->st_zip,
            'st_phone' => $customer->st_phone,
            'st_country' => $customer->st_country,
            'st_ctryid' => $customer->st_ctryid,
        ]);
        $creator->setFields([
            'CCRESULTX' => '',
            'ccpaid' => '',
            'XML' => '',
            'SHIPTRAKID' => [],
            'track_num' => '',
            'heardfrom' => 'TESTING',
            'MARKETDATA' => '',
            'supinvpk' => '',
            'lastshipdt' => '',
            'mktplcordn' => '',
            'supstmntid' => '',
            'accountnum' => $customer->accoutnum,
        ]);
        $newinvoice = $creator->create();
        return redirect(backpack_url('dbs/' . config('tenant_db') . '/fbs/fbsorder/whdmanualcheck?id=' . $newinvoice->pk));

    }

    public function autofulfill()
    {

        $invoice = $this->crud->getEntry($this->crud->getCurrentEntryId());
        $change = true;
        foreach ($invoice->lineitem as $lineitem) {
            if ($lineitem->lineStatus != 'O') {
                $change = false;

            }else{
                if($lineitem->sup_ord_id=='CHECK') {
                    $lineitem->sup_ord_id='';
                    $lineitem->profilepk=0;
                    $lineitem->save();
                }
            }
        }
        if ($change) {
            $invoice->invstatus = 'O';
        }
        $invoice->save();
        (new AutofulfillOrder())($invoice, sophiosettings());

        /*
         * ProcessNewOrder::dispatch($this->crud->getCurrentEntryId(), ['tenant_db' => Config::get('tenant_db'), 'fbs_database' => Config::get('tenant_db'), 'no_autoStatus' => true]);
         */
        Alert::add('success', 'Invoice added to processing queue!')->flash();
        return back();
    }

    public function charge()
    {
        $charge = new ChargeService();
        $charge->charge(Invoice::find($this->crud->getCurrentEntryId()));

        return redirect(backpack_url('dbs/' . config('tenant_db') . '/fbs/fbsorder/' . $this->crud->getCurrentEntryId().'/main'));
    }

    public function processneworder($pk)
    {
        $settings = ['fbs_database' => request()->route()->parameter('database')];
        \Config::set('tenant_db', request()->route()->parameter('database'));
        \Config::set('fbs_db', request()->route()->parameter('database'));
        $invoice = Invoice::where('pk', request()->get('id'))->first();
        (new \Sophio\FBSOrder\Library\Actions\AutofulfillOrder())($invoice);
        return view('admin.fbs.order.newapi', ['invoice' => $invoice, 'message' => 'Invoice processed!', 'settings' => $settings,]);
    }
}