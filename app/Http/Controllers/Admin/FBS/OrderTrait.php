<?php

namespace App\Http\Controllers\Admin\FBS;

use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Sophio\Common\SophioUser;
use Backpack\CRUD\app\Library\Widget;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Sophio\Common\Models\FBS\Customer;
use Sophio\Common\Models\FBS\Invoice;
use Sophio\Common\Models\FBS\LineItem;
use Sophio\Common\Models\FBS\Lookups;
use Sophio\Common\Models\FBS\Marketplace;
use Sophio\Common\Models\FBS\Supplier;
use Sophio\FBSOrder\Library\Rules\Supplier\ActiveSuppliersNetworkList;

trait OrderTrait
{
    public $lineFilters;

    public function doCreate()
    {
        $this->crud->addFields([
            [
                'name' => 'custpk',
                'label' => 'Customer (customerpk)',
                'tab' => 'Customer',
                'type' => 'text'
            ],
            [
                'name' => 'accountnum',
                'label' => 'Account Number',
                'tab' => 'Customer',
                'type' => 'text'
            ],
            [
                'name' => 'custtype',
                'label' => 'Customer Type',
                'tab' => 'Customer',
                'type' => 'select_from_array',
                'options' => Marketplace::pluck('market', 'market')->toArray(),
            ],

            [
                'name' => 'taxid',
                'label' => 'TaxID',
                'tab' => 'Customer',
                'type' => 'text'
            ],

            [
                'name' => 'ponumber',
                'label' => 'PO Number',
                'tab' => 'Order Information',
                'type' => 'text'
            ],

            [
                'name' => 'mktplcordn',
                'label' => 'Marketplace Order number',
                'tab' => 'Order Information',
                'type' => 'text',

            ],
            [
                'name' => 'invno',
                'label' => 'Invoice Number',
                'tab' => 'Order Information',
                'type' => 'text'
            ],
            [
                'name' => 'invstatus',
                'label' => 'Status',
                'tab' => 'Order Information',
                'type' => 'select_from_array',
                'options' => Lookups::where('type', 'INVSTATUS')->pluck('cdata1', 'cdata')->toArray()
            ],
            [
                'name' => 'invdate',
                'label' => 'Invoice Date',
                'tab' => 'Order Information',
                'type' => 'datetime'
            ],
            [
                'name' => 'completed',
                'label' => 'Complete Date',
                'tab' => 'Order Information',
                'type' => 'datetime'
            ],
            [
                'name' => 'NOTE',
                'label' => 'NOTE',
                'tab' => 'Order Information',
                'type' => 'text'
            ],
            [
                'name' => 'STORENOTES',
                'label' => 'STORENOTES',
                'tab' => 'Order Information',
                'type' => 'textarea'
            ],
            [
                'name' => 'invtotal',
                'label' => 'Invoice Total',
                'tab' => 'Pricing',
                'decimals' => 2,
                'prefix' => '$',
                'type' => 'number', 'attributes' => ["step" => "any"]
            ],
            [
                'name' => 'costtotal',
                'label' => 'Cost Total',
                'tab' => 'Pricing',
                'decimals' => 2,
                'prefix' => '$',
                'type' => 'number', 'attributes' => ["step" => "any"]
            ],

            [
                'name' => 'handling',
                'label' => 'Handling',
                'tab' => 'Pricing',
                'decimals' => 2,
                'prefix' => '$',
                'type' => 'number', 'attributes' => ["step" => "any"]
            ],
            [
                'name' => 'tax',
                'label' => 'tax',
                'tab' => 'Pricing',
                'decimals' => 2,
                'prefix' => '$',
                'type' => 'number', 'attributes' => ["step" => "any"]
            ],
            [
                'name' => 'taxrate',
                'label' => 'taxrate',
                'suffix' => '%',
                'tab' => 'Pricing',
                'decimals' => 2,

                'type' => 'number', 'attributes' => ["step" => "any"]
            ],
            [
                'name' => 'transfee',
                'label' => 'Transaction Feeds',
                'tab' => 'Pricing',
                'decimals' => 2,
                'prefix' => '$',
                'type' => 'number', 'attributes' => ["step" => "any"]
            ],
            [
                'name' => 'weight',
                'label' => 'Weight',
                'tab' => 'Shipping',
                'decimals' => 2,
                'suffix' => 'lbs',
                'type' => 'number', 'attributes' => ["step" => "any"]
            ],
            [
                'name' => 'cctype',
                'label' => 'CC Type',
                'tab' => 'Payment',
                'type' => 'text'
            ],
            [
                'name' => 'ccresult',
                'label' => 'CC Result',
                'tab' => 'Payment',
                'type' => 'select_from_array',
                'options' => [
                    'APPROVED' => 'APPROVED',
                    'DENIED' => 'DENIED',
                    'REFUNDED' => 'REFUNDED',
                    'PARTIAL REFUNDED' => 'PARTIAL REFUNDED',
                    'ONHOLD' => 'ONHOLD',
                    'PAID' => 'PAID',
                    'OPENACCNT' => 'OPENACCNT'
                ],
            ],
            [
                'name' => 'CCRESULTX',
                'label' => 'CC Result X',
                'tab' => 'Payment',
                'type' => 'textarea'
            ],
            [
                'name' => 'paymethod',
                'label' => 'Pay method',
                'tab' => 'Payment',
                'type' => 'text'
            ],
            [
                'name' => 'ccpaid',
                'label' => 'CC Paid',
                'tab' => 'Payment',
                'type' => 'text'
            ],

            [
                'name' => 'ebayordern',
                'label' => 'Ebay order N',
                'tab' => 'Misc',
                'type' => 'text'
            ],
            [
                'name' => 'email',
                'label' => 'email',
                'tab' => 'Customer',
                'type' => 'text'
            ],
            [
                'name' => 'company',
                'label' => 'Company',
                'tab' => 'Customer',
                'type' => 'text'
            ],
            [
                'name' => 'firstname',
                'label' => 'firstname',
                'tab' => 'Customer',
                'type' => 'text'
            ],
            [
                'name' => 'lastname',
                'label' => 'lastname',
                'tab' => 'Customer',
                'type' => 'text'
            ],
            [
                'name' => 'address',
                'label' => 'address',
                'tab' => 'Customer',
                'type' => 'text'
            ],
            [
                'name' => 'address2',
                'label' => 'address2',
                'tab' => 'Customer',
                'type' => 'text'
            ],
            [
                'name' => 'city',
                'label' => 'city',
                'tab' => 'Customer',
                'type' => 'text'
            ],
            [
                'name' => 'county',
                'label' => 'county',
                'tab' => 'Customer',
                'type' => 'text'
            ],
            [
                'name' => 'state',
                'label' => 'state',
                'tab' => 'Customer',
                'type' => 'select_from_array',
                'options' => Lookups::where('type', 'STATE')->pluck('cdata1', 'cdata')->toArray()
            ],
            [
                'name' => 'zip',
                'label' => 'zip',
                'tab' => 'Customer',
                'type' => 'text'
            ],
            [
                'name' => 'track_num',
                'label' => 'Tracking Numbers',
                'tab' => 'Shipping',
                'type' => 'text'
            ],

            [
                'name' => 'service',
                'label' => 'Service',
                'tab' => 'Shipping',
                'type' => 'text'
            ],

            [
                'name' => 'carrier',
                'label' => 'carrier',
                'tab' => 'Shipping',
                'type' => 'text'
            ],
            [
                'name' => 'SHIPCOSTS',
                'label' => 'SHIPCOSTS',
                'tab' => 'Shipping',
                'type' => 'text'
            ],

            [
                'name' => 'shippings',
                'label' => 'Shippings',
                'tab' => 'Shipping',
                'type' => 'text'
            ],
            [
                'name' => 'st_name',
                'label' => 'ST name',
                'tab' => 'Shipping',
                'type' => 'text'
            ],
            [
                'name' => 'st_company',
                'label' => 'ST company',
                'tab' => 'Shipping',
                'type' => 'text'
            ],
            [
                'name' => 'st_email',
                'label' => 'ST email',
                'tab' => 'Shipping',
                'type' => 'text'
            ],
            [
                'name' => 'st_addr',
                'label' => 'ST Address',
                'tab' => 'Shipping',
                'type' => 'text'
            ],
            [
                'name' => 'st_addr2',
                'label' => 'ST Address 2',
                'tab' => 'Shipping',
                'type' => 'text'
            ],
            [
                'name' => 'st_city',
                'label' => 'ST city',
                'tab' => 'Shipping',
                'type' => 'text'
            ],
            [
                'name' => 'st_state',
                'label' => 'ST state',
                'tab' => 'Shipping',
                'type' => 'select_from_array',
                'options' => Lookups::where('type', 'STATE')->pluck('cdata1', 'cdata')->toArray()
            ],
            [
                'name' => 'st_zip',
                'label' => 'ST zip',
                'tab' => 'Shipping',
                'type' => 'text'
            ],
            [
                'name' => 'st_phone',
                'label' => 'ST phone',
                'tab' => 'Shipping',
                'type' => 'text'
            ],
            [
                'name' => 'st_country',
                'label' => 'ST country',
                'tab' => 'Shipping',
                'type' => 'select_from_array',
                'options' => Lookups::where('type', 'COUNTRY')->pluck('cdata1', 'cdata')->toArray()
            ],
            [
                'name' => 'salesrep',
                'label' => 'Sales Representative',
                'tab' => 'Users',
                'type' => 'text'
            ],
            [
                'name' => 'ip',
                'label' => 'IP',
                'tab' => 'Users',
                'type' => 'text'
            ],
            [
                'name' => 'user_pk',
                'label' => 'last admin ID',
                'tab' => 'Users',
                'type' => 'text'
            ],
            /*
            [
                'name' => 'attributes.SHIPTRAKID',
                'label' => 'SHIPTRAKID',
                'tab' => 'Shipping',
                'type' => 'table'
            ],
            */
            /*
            [
                'type' => "relationship",
                'name' => 'lineitem',
                'ajax' => true,
                'entity' => 'lineitem',
                'add_button_label' => 'New line item',
                'tab' => 'Items',
                'force_delete' => true,
                'subfields' => [
                    [
                        'name' => 'mfr',
                        'label' => 'Mfg Code',
                        'type' => 'text'
                    ],
                    [
                        'name' => 'sku',
                        'label' => 'SKU',
                        'type' => 'text'
                    ],
                    [
                        'name' => 'price',
                        'label' => 'Price',
                        'type' => 'number','attributes' => ["step" => "any"]
                    ],
                    [
                        'name' => 'qty',
                        'label' => 'Quantity',
                        'type' => 'number','attributes' => ["step" => "any"]
                    ],
                ]
            ],
            */

            [
                'name' => 'MARKETDATA',
                'label' => 'MARKETDATA',
                'tab' => 'Marketplace',
                'type' => 'textarea',
                'attributes' => ['readonly' => 'readonly']
            ],
        ]);
    }

    public function doShow()
    {
        $this->crud->addColumns(
            [

                [
                    'name' => 'pk',
                    'label' => 'Invoice PK',
                    'type' => 'text'
                ],
                [
                    'name' => 'custpk',
                    'label' => 'Customer',
                    'type' => 'text',
                    'value' => function ($entry) {
                        return $entry->seller->company . " (" . $entry->custpk . ")";
                    },
                    'wrapper' => [

                        'href' => function ($crud, $column, $entry, $related_key) {
                            return backpack_url('dbs/' . \Route::current()->parameter('database') . '/fbs/customer/' . $entry->custpk . '/edit');
                        },
                        'target' => '_blank',

                    ],
                ],
                [
                    'name' => 'custtype',
                    'label' => 'Customer Type',
                    'type' => 'select_from_array',
                    'options' => Marketplace::pluck('market', 'market')->toArray(),
                    'wrapper' => [

                        'href' => function ($crud, $column, $entry, $related_key) {
                            return backpack_url('dbs/' . \Route::current()->parameter('database') . '/fbs/marketplace/' . $entry->marketplace->pk . '/show');
                        },
                        'target' => '_blank',

                    ],
                ],

                [
                    'name' => 'taxid',
                    'label' => 'TaxID',
                    'type' => 'text'
                ],
                [
                    'name' => 'ponumber',
                    'label' => 'PO Number',
                    'type' => 'text'
                ],

                [
                    'name' => 'invdate',
                    'label' => 'Invoice Date',
                    'type' => 'datetime'
                ],

                [
                    'name' => 'completed',
                    'label' => 'Completed Date',
                    'type' => 'datetime'
                ],
                [
                    'name' => 'invtotal',
                    'label' => 'Invoice Total',
                    'decimals' => 2,
                    'prefix' => '$',
                    'type' => 'number', 'attributes' => ["step" => "any"]
                ],
                [
                    'name' => 'handling',
                    'label' => 'Handling',
                    'decimals' => 2,
                    'prefix' => '$',
                    'type' => 'number', 'attributes' => ["step" => "any"]
                ],
                [
                    'name' => 'weight',
                    'label' => 'Weight',
                    'decimals' => 2,
                    'suffix' => 'lbs',
                    'type' => 'number', 'attributes' => ["step" => "any"]
                ],
                [
                    'name' => 'cctype',
                    'label' => 'CC Type',
                    'type' => 'text'
                ],
                [
                    'name' => 'ccresult',
                    'label' => 'CC Result',
                    'type' => 'text'
                ],
                [
                    'name' => 'CCRESULTX',
                    'label' => 'CC Result X',
                    'type' => 'text',
                    'limit' => 10000
                ],
                [
                    'name' => 'paymethod',
                    'label' => 'Pay method',
                    'type' => 'text'
                ],
                [
                    'name' => 'status',
                    'label' => 'Status',
                    'type' => 'relationship',
                    'entity' => 'status',
                    'wrapper' => [
                        'element' => 'span',
                        'class' => function ($crud, $column, $entry, $related_key) {
                            $color = match ($entry->status->cdata) {
                                'O' => 'secondary',
                                'F' => 'success',
                                'W' => 'primary',
                                'X', '4', '9', 'D' => 'danger',
                                '3' => 'info',
                                default => 'warning'
                            };
                            return 'badge badge-' . $color;
                        }

                    ]
                ],
                [
                    'name' => 'lineitem',
                    'label' => 'Line items',

                    'type' => 'relationship',

                    'attribute' => 'LinecodeSku',
                    'entity' => 'lineitem',
                    'wrapper' => [

                        'href' => function ($crud, $column, $entry, $related_key) {
                            return backpack_url('dbs/' . \Route::current()->parameter('database') . '/fbs/fbslineitem/' . $related_key . '/show');
                        },
                        'target' => '_blank',

                    ],
                ],
                [
                    'name' => 'SHIPTRAKID',
                    'label' => 'Shipping tracking',
                    'type' => 'json',
                    'wrapper' => ['element' => 'pre', 'style' => "max-width:800px", 'class' => 'text-wrap'],
                    'limit' => 10000,
                    'escaped' => true

                ],
                [
                    'name' => 'STORENOTES',
                    'label' => 'Store notes',
                    'type' => 'text',
                    'wrapper' => ['element' => 'div', 'style' => "max-width:800px", 'class' => 'text-wrap'],
                    'limit' => 10000,
                    'escaped' => false
                ],

                [
                    'name' => 'MARKETDATA',
                    'label' => 'Marketplace',
                    'type' => 'raw_xml_pretty',

                ],
                [
                    'name' => 'ebayordern',
                    'label' => 'Ebay order N',
                    'type' => 'text'
                ],
                [
                    'name' => 'email',
                    'label' => 'email',
                    'type' => 'text'
                ],
                [
                    'name' => 'company',
                    'label' => 'Company',
                    'type' => 'text'
                ],
                [
                    'name' => 'st_name',
                    'label' => 'ST name',
                    'type' => 'text'
                ],
                [
                    'name' => 'st_addr',
                    'label' => 'ST Address',
                    'type' => 'text'
                ],
                [
                    'name' => 'st_addr2',
                    'label' => 'ST Address 2',
                    'type' => 'text'
                ],
                [
                    'name' => 'st_city',
                    'label' => 'ST city',
                    'type' => 'text'
                ],
                [
                    'name' => 'st_state',
                    'label' => 'ST state',
                    'type' => 'select_from_array',
                    'options' => Lookups::where('type', 'STATE')->pluck('cdata1', 'cdata')->toArray()
                ],
                [
                    'name' => 'st_zip',
                    'label' => 'ST zip',
                    'type' => 'text'
                ],
                [
                    'name' => 'st_phone',
                    'label' => 'ST phone',
                    'type' => 'text'
                ],
                [
                    'name' => 'st_country',
                    'label' => 'ST country',
                    'type' => 'select_from_array',
                    'options' => Lookups::where('type', 'COUNTRY')->pluck('cdata1', 'cdata')->toArray()
                ],
            ]);
    }

    public function doList()
    {

        $this->crud->setOperationSetting('showEntryCount', false);


        $this->lineFilters = [];
        if (request()->get('openorder')) {
            $this->crud->orderBy('invdate', 'asc');
        } else {
            $this->crud->orderBy('invdate', 'desc');
        }
        $custs = Cache::remember('last30_customers_array_sold', 3600, function () {
            $custs = [];
            $c = \Sophio\Common\Models\FBS\Invoice::with('seller')->whereIn('invstatus', ['F', 'R', '8', 'K'])
                ->where('invdate', '>', \Illuminate\Support\Carbon::now()->subDays(30))->groupBy('custpk')->orderByRaw('COUNT(*) DESC')->get()->pluck('custpk');
            $customers = Customer::whereIn('pk', $c)->select('pk', 'company')->get();
            foreach ($customers as $customer) {
                $custs[$customer->pk] = $customer->company;
            }
            return $custs;
        });
        $this->crud->addFilter([
            'name' => 'pk',
            'type' => 'text',
            'label' => 'Invoice PK',

        ],
            false
            , function ($value) {
                $this->crud->addClause('where', 'pk', '=', trim($value));
            });
        $this->crud->addFilter([ // dropdown filter
            'name' => 'ponumber',
            'type' => 'text',
            'label' => 'PO number',
        ],
            false
            , function ($value) {
                $this->crud->addClause('where', 'ponumber', '=', trim($value));
            });
        $this->crud->addFilter([ // dropdown filter
            'name' => 'sku',
            'type' => 'text',
            'label' => 'Sku',
        ],
            false
            , function ($value) {
                $this->lineFilters['sku'] = trim($value);

            }
        );

        $this->crud->addFilter([ // dropdown filter
            'name' => 'mfgcode',
            'type' => 'text',
            'label' => 'Mfgcode',
        ],
            false
            , function ($value) {
                $this->lineFilters['linecode'] = trim($value);

            }
        );
        $this->crud->query->select('wws_invoice.*');
        $this->crud->addFilter([
            'type' => 'date_range',
            'name' => 'from_to',
            'label' => 'Invoice Date'
        ],
            false,
            function ($value) { // if the filter is active, apply these constraints
                $dates = json_decode($value);
                $this->crud->addClause('where', 'invdate', '>=', Carbon::parse($dates->from));
                $this->crud->addClause('where', 'invdate', '<=', Carbon::parse($dates->to));
            });
        $this->crud->addFilter([ // dropdown filter
            'name' => 'custpk',
            'type' => 'select2',
            'label' => 'Customer',
        ],
            $custs
            , function ($value) {
                $this->crud->addClause('where', 'wws_invoice.custpk', '=', trim($value));
            });


        $this->crud->addFilter([
            'type' => 'dropdown',
            'name' => 'openorder',
            'label' => 'Open Order'
        ],
            [
                'open' => 'All',
                'priority' => 'Priority',
                'needsreview' => 'Needs Review',
                'WAL_Shipping_Not_Sent' => 'Walmart Shipping Not Sent',
                'NAT_Invoice_Not_Sent' => 'NAT Invoice Not sent',
                /*
                'HD_Shipping_Not_Sent' => 'HD Shipping not sent',
                'HD_Invoice_Not_Sent'=>'HD Invoice not sent'
                */
            ],
            function ($value) { // if the filter is active
                switch ($value) {
                    case 'WAL_Shipping_Not_Sent':
                        $this->crud->addClause('where', 'invstatus', '=', 'F');
                        $this->crud->addClause('where', 'track_num', '<>', '');
                        $this->crud->addClause('where', 'custtype', '=', 'WAL');
                        $this->crud->addClause('where', 'invdate', '>=', Carbon::now()->subDays(30));
                        $this->crud->addClause('where', 'SHIPTRAKID', 'NOT LIKE', "%WALMARTSHIPUPDATE%");
                        break;
                    case 'NAT_Invoice_Not_Sent':
                        $this->crud->addClause('whereIn', 'invstatus', ['F', '3']);
                        $this->crud->addClause('where', 'track_num', '<>', '');
                        $this->crud->addClause('where', 'custtype', '=', 'NAT');
                        $this->crud->addClause('where', 'invdate', '>', Carbon::now()->subDays(7));
                        $this->crud->addClause('where', 'CCRESULTX', 'NOT LIKE', "%CORTIME%");
                        break;
                    case 'HD_Shipping_Not_Sent':
                        $this->crud->addClause('where', 'invstatus', 'F');
                        $this->crud->addClause('where', 'track_num', '<>', '');
                        $this->crud->addClause('where', 'custtype', '=', 'HD');
                        $this->crud->addClause('where', 'invdate', '>', Carbon::now()->subDays(7));
                        $this->crud->addClause('where', 'SHIPTRAKID', 'NOT LIKE', "%CHUBTRACKINGIDUPDATE%");
                        break;
                    default:
                        $this->crud->addClause('whereNotIn', 'invstatus', ['X', '4', '5', 'R', '8', '9']);
                        //$this->lineFilters['lineStatus'] =['X', '4', '5','R','8','F'];
                        $this->crud->addClause('where', 'invdate', '>', Carbon::now()->subDays(30));
                        $this->lineFilters['qty'] = true;
                        if ($value == 'open') {
                            $this->lineFilters['track_num'] = '';

                        } elseif ($value == 'priority') {
                            /* $this->crud->query->join(  'wws_lineitems','invpk','wws_invoice.pk')->where('wws_lineitems.track_num', '')->where(function ($query) {
                                 $query->where('sup_ord_id', '')->orWhere('sup_ord_id', 'CHECK NOTES');
                             });
                            */
                            $this->lineFilters['track_num'] = '';
                            $this->lineFilters['sup_ord_id'] = '';

                        } elseif ($value == 'needsreview') {
                            $this->lineFilters['track_num'] = '';
                            $this->crud->addClause('where', 'invstatus', '=', '3');
                        }
                }

            });
        $this->crud->addFilter([ // dropdown filter
            'name' => 'track_num',
            'type' => 'text',
            'label' => 'Tracking Number',
        ],
            false
            , function ($value) {
                $this->crud->query->where('wws_invoice.track_num', 'LIKE', '%' . trim($value) . '%');
            }
        );
        $this->crud->addFilter([
            'type' => 'simple',
            'name' => 'notracknum',
            'label' => 'Missing tracking'
        ],
            false,
            function () { // if the filter is active
                $this->lineFilters['track_num'] = '';

            });
        $this->crud->addFilter([
            'type' => 'select2',
            'name' => 'user_pk',
            'label' => 'User Assigned'
        ],
            function () {
                $users = SophioUser::all();
                $u = [];
                foreach ($users as $uu) {
                    $u[$uu->id] = $uu->name;
                }
                return $u;
            },
            function ($value) { // if the filter is active

                $this->crud->addClause('where', 'user_pk', $value);
            });
        $this->crud->addFilter([ // dropdown filter
            'name' => 'invstatus',
            'type' => 'dropdown',
            'label' => 'Status',
        ],
            function () {
                $invs = Lookups::where('type', 'INVSTATUS')->orderBy('cdata1')->get();
                $return = [];
                foreach ($invs as $in) {
                    $return[$in->cdata] = $in->cdata1 ?? '';
                }
                return $return;
            }
            , function ($value) {
                $this->crud->addClause('where', 'invstatus', $value);
            });
        $this->crud->addFilter([ // dropdown filter
            'name' => 'custtype',
            'type' => 'dropdown',
            'label' => 'Market',
        ],
            function () {
                $invs = Marketplace::all();
                $return = [];
                foreach ($invs as $in) {
                    $return[$in->market] = $in->market ?? '';
                }
                return $return;
            }
            , function ($value) {
                $this->crud->addClause('where', 'custtype', trim($value));
            });

        $this->crud->addFilter([ // dropdown filter
            'name' => 'profilepk',
            'type' => 'select2',
            'label' => 'Supplier',
        ],
            function () {
                $invs = (new ActiveSuppliersNetworkList)()->get();
                $return = [];
                foreach ($invs as $in) {
                    $return[$in->PK] = $in->getSupplierDisplayName() ?? '';
                }
                return $return;
            }
            , function ($value) {

                $this->lineFilters['profilepk'] = trim($value);
            });
        $this->crud->addFilter([
            'type' => 'simple',
            'name' => 'notcompleted',
            'label' => 'Not Completed'
        ],
            false,
            function () { // if the filter is active
                $this->crud->addClause('whereNull', 'completed');
                $this->crud->query->whereHas('lineitem', function ($query) {
                    $query->where(function ($q) {
                        $q->where('qty', '=', 0)
                            ->where('track_num', '');
                    });
                });
                $this->crud->query->whereHas('lineitem', function ($query) {
                    $query->where('track_num', '<>', '');
                });
            });
        $this->crud->addFilter([
            'type' => 'text',
            'name' => 'phone',
            'label' => 'Buyer Phone'
        ],
            false,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'phone', trim($value));

            });
        $this->crud->addFilter([
            'type' => 'text',
            'name' => 'lastname',
            'label' => 'Buyer Last Name'
        ],
            false,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'st_name', 'LIKE', '%' . trim($value) . '%');

            });
        $this->crud->addFilter([
            'type' => 'text',
            'name' => 'zip',
            'label' => 'Ship To Zip'
        ],
            false,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'st_zip', trim($value));

                // $this->crud->addClause('where','timein','>',\Carbon\Carbon::now()->subDays(30));
            });
        $this->crud->addFilter([ // dropdown filter
            'name' => 'carrier',
            'type' => 'dropdown',
            'label' => 'Carrier',
        ],
            ['FEDEX' => 'Fedex', 'stamps_com' => 'USPS', 'UPS' => 'UPS']
            , function ($value) {
                if ($value == 'UPS') {
                    $this->crud->addClause('where', 'carrier', 'UPS');
                } elseif ($value == 'FEDEX') {
                    $this->crud->addClause('whereIn', 'carrier', ['FDX', 'fedex']);
                } elseif ($value == 'stamps_com') {
                    $this->crud->addClause('whereIn', 'carrier', ['USP', 'stamps_com', 'USPS']);
                }


            }
        );
        $this->crud->addFilter([
            'type' => 'simple',
            'name' => 'multiline',
            'label' => 'Multiline'
        ],
            false,
            function () { // if the filter is active

                $this->crud->query->has('lineitem', '>', 1);
            });
        $this->crud->addFilter([
            'type' => 'simple',
            'name' => 'notprofitable',
            'label' => 'Not Profitable'
        ],
            false,
            function () { // if the filter is active
                $this->crud->addClause('whereRaw', 'invtotal-costtotal-handlingc<0');
            });
        if (request()->get('custtype') == 'HD') {
            $this->crud->addFilter([
                'type' => 'simple',
                'name' => 'notinvoiced',
                'label' => 'Not Invoiced'
            ],
                false,
                function () { // if the filter is active
                    switch (request()->get('custtype')) {
                        case 'HD':
                            $this->crud->addClause('whereRaw', 'SHIPTRAKID NOT LIKE \'%CHUBINVOICEDUPDATE%\'');
                            break;
                    }

                });
        }

        if (count($this->lineFilters) > 0) {
            $l = new LineItem;
            $i = new Invoice;
            $this->crud->query->join($l->getTable(), 'invpk', $i->getTable() . '.pk');
            $this->crud->query->groupBy($i->getTable() . '.pk');
            $this->crud->query->select($i->getTable() . '.*');

            foreach ($this->lineFilters as $column => $value) {
                if ($column === 'sup_ord_id' && $value == '') {
                    $this->crud->query->where(function ($query) use ($l) {
                        $query->where($l->getTable() . '.sup_ord_id', '');
                    });
                } elseif ($column == 'lineStatus') {
                    $this->crud->query->whereNotIn($l->getTable() . '.' . $column, $value);

                } elseif ($column == 'qty') {
                    $this->crud->query->where($l->getTable() . '.' . $column, '>', 0);
                } elseif ($column == 'with_track_num') {
                    $this->crud->query->where($l->getTable() . '.track_num', '<>', "");
                } else {
                    $this->crud->query->where($l->getTable() . '.' . $column, $value);
                }


            }
        }
        $this->crud->addColumns(
            [

                [
                    'name' => 'pk',
                    'label' => 'Invoice PK',
                    'type' => 'text',
                    'searchLogic' => false,
                    'wrapper' => [

                        'href' => function ($crud, $column, $entry, $related_key) {
                            return sophio_route('fbs/fbsorder.main', ['id' => $entry->pk]);
                        },


                    ],
                ],
                [
                    'name' => 'custtype',
                    'label' => 'Market',
                    'type' => 'text',
                    'searchLogic' => false
                ],
                [
                    'name' => 'custpk',
                    'label' => 'Customer',
                    'type' => 'text',
                    'searchLogic' => false,
                    'value' => function ($entry) {
                        return $entry->seller->company ?? '';
                    },
                    'wrapper' => [
                        'href' => function ($crud, $column, $entry, $related_key) {
                            return  sophio_route('fbs/customer.edit', ['id' => $entry->custpk]);
                        },
                        'target' => '_blank',

                    ],
                ],

                [
                    'name' => 'user_pk',
                    'priority' => 50,
                    'label' => 'User Assigned',
                    'type' => 'text',
                    'searchLogic' => false,
                    'value' => function ($entry) {
                        return $entry->user->name ?? 'System';
                    },
                    'wrapper' => [
                        'href' => function ($crud, $column, $entry, $related_key) {
                            return backpack_url('dbs/' . \Route::current()->parameter('database') . '/fbs/fbsorder?user_pk=' . $entry->user_pk);
                        },
                        'target' => '_blank',

                    ],
                ],

                [
                    'name' => 'invdate',
                    'label' => 'Invoice Date',
                    'type' => 'datetime',
                    'format' => 'M/D/YY h:mm A',
                    'searchLogic' => false
                ],
                [
                    'name' => 'ponumber',
                    'label' => 'PO Number',
                    'type' => 'text',
                    'searchLogic' => function ($query, $column, $searchTerm) {
                        $query->orWhere('ponumber', $searchTerm);
                    }
                ],
                [
                    'name' => 'invtotal',
                    'label' => 'Invoice Total',
                    'decimals' => 2,
                    'prefix' => '$',
                    'type' => 'number', 'attributes' => ["step" => "any"],
                    'searchLogic' => false
                ],
                [
                    'name' => 'nettotal',
                    'label' => 'Net Total',
                    'decimals' => 2,
                    'priority' => 100,
                    'prefix' => '$',
                    'type' => 'number', 'attributes' => ["step" => "any"],
                    'searchLogic' => false,
                    'value' => function ($entry) {
                        return $entry->lineitem->sum(function ($i) {
                            return $i->qty_ord * $i->price;
                        });
                    }
                ],
                [
                    'name' => 'order cost',
                    'label' => 'order Cost',
                    'decimals' => 2,
                    'prefix' => '$',
                    'priority' => 100,
                    'type' => 'number', 'attributes' => ["step" => "any"],
                    'searchLogic' => false,
                    'value' => function ($entry) {
                        return $entry->lineitem->sum(function ($i) {
                            return $i->qty_ord * $i->cost;
                        }) ?? false;
                    }
                ],
                [
                    'name' => 'margin',
                    'label' => 'Margin',
                    'decimals' => 2,
                    'priority' => 100,
                    'suffix' => '%',

                    'searchLogic' => false,
                    'value' => function ($entry) {
                        if ($entry->lineitem->sum(function ($i) {
                                return $i->qty_ord * $i->price;
                            }) > 0) {
                            if ($entry->lineitem->sum(function ($i) {
                                    return $i->qty_ord * $i->cost;
                                }) == 0) {
                                return false;
                            }
                            return number_format(100 * ($entry->lineitem->sum(function ($i) {
                                        return $i->qty_ord * $i->price;
                                    }) - $entry->lineitem->sum(function ($i) {
                                        return $i->qty_ord * $i->cost;
                                    })) / $entry->lineitem->sum(function ($i) {
                                    return $i->qty_ord * $i->price;
                                }), '2', '.');
                            // return ($entry->invtotal - $entry->handlingc - $entry->transfees - $entry->ccprocfee - $entry->lineitem->sum(function ($i) {return $i->qty_ord * $i->scost;}) )/
                            //   ($entry->invtotal - $entry->tax - $entry->coretotal-$entry->handling);
                        } else {
                            return false;
                        }

                    }
                ],

                [
                    'name' => 'invstatus',
                    'label' => 'Status',
                    'type' => 'text',
                    'searchLogic' => false,
                    'value' => function ($entry) {
                        return Lookups::where('type', 'INVSTATUS')->where('cdata', $entry->invstatus)->first()->cdata1 ?? '';
                    }
                ],

                [
                    'name' => 'lineitem.supplier.NAME',
                    'label' => 'Supplier Name',
                    'type' => 'relationship',
                    'searchLogic' => false,
                    'priority' => 1,
                ],
            ]);

        Widget::add([
                'type' => 'orderlistextra',
                'content' => ""
            ]
        )->to('before_content');
    }

    public function mainTopBar(Invoice $invoice)
    {

        $content = [
            [
                'type' => 'link',
                'href' => backpack_url('dbs/' . request()->route()->parameter('database') . '/fbs/fbsorder/' . $invoice->pk . '/main'),
                'label' => 'Main Order Screen',

            ],
            [
                'type' => 'link',
                'href' => backpack_url('dbs/' . request()->route()->parameter('database') . '/fbs/fbsorder/orderprint?id=' . $invoice->pk),
                'label' => 'Print order',
                'target' => 'print_frame'
            ],

            [
                'type' => 'link',
                'href' => backpack_url('dbs/' . request()->route()->parameter('database') . '/fbs/fbslineitem?invpk=' . $invoice->pk),
                'label' => 'Lineitems',

            ],
            [
                'type' => 'link',
                'href' => backpack_url('dbs/' . request()->route()->parameter('database') . '/fbs/fbsorder/' . $invoice->pk . '/audit'),
                'label' => 'Audit',

            ],
            [
                'type' => 'menu',

                'label' => 'Advanced',
                'menu' => [
                    [
                        'type' => 'link',
                        'href' => backpack_url('dbs/' . request()->route()->parameter('database') . '/fbs/fbsorder/' . $invoice->pk . '/edit'),
                        'label' => 'CRUD edit',

                    ],
                    [
                        'type' => 'link',
                        'href' => backpack_url('dbs/' . request()->route()->parameter('database') . '/fbs/fbsorder/' . $invoice->pk . '/recalc'),
                        'label' => 'Recalculate',

                    ],
                    [
                        'type' => 'link',
                        'href' => backpack_url('dbs/' . request()->route()->parameter('database') . '/fbs/fbsorder/' . $invoice->pk . '/autofulfill'),
                        'label' => 'Automatic fulfill (force)',

                    ],
                    [
                        'type' => 'link',
                        'href' => backpack_url('dbs/' . request()->route()->parameter('database') . '/fbs/fbsorder/' . $invoice->pk . '/whdautocheck'),
                        'label' => 'WHD auto-check (force)',

                    ],

                    [
                        'type' => 'link',
                        'href' => sophio_route('maillog.index', ['invpk' => $invoice->pk]),
                        'label' => 'Mail Log'

                    ],
                    [
                        'type' => 'link',
                        'href' => sophio_route('fbs/fbsorder.lastsupplierlogs', ['id' => $invoice->pk]),
                        'label' => 'Supplier Log'

                    ],
                    [
                        'type' => 'link',
                        'href' => sophio_route('fbs/fbsorder.deleteorder', ['id' => $invoice->pk]),
                        'label' => 'Delete Order'

                    ],
                    [
                        'type' => 'link',
                        'href' => sophio_route('fbs/fbsorder.complete', ['id' => $invoice->pk]),
                        'label' => 'Complete Order'

                    ],
                    [
                        'type' => 'link',
                        'href' => backpack_user()->hasRole(['super-admin']) ? sophio_route('fbs/fbsorder.reset', ['id' => $invoice->pk]) : '#',
                        'label' => 'Reset Order',
                        'attributes' => [
                            'onclick' => "return confirm('Please note reset will wipe existing purchases info. Click OK only if you know what are you doing!!!')"]
                    ],
                ]
            ],
            [
                'type' => 'menu',

                'label' => 'Returns',
                'menu' => [
                    [
                        'type' => 'link',
                        'href' => backpack_url('dbs/' . request()->route()->parameter('database') . '/fbs/returns/' . $invoice->pk . '/rma'),
                        'label' => 'Create RMA',

                    ],
                    [
                        'type' => 'link',
                        'href' => backpack_url('dbs/' . request()->route()->parameter('database') . '/fbs/purchaseorder?invpk=' . $invoice->pk),
                        'label' => 'Open return requests',

                    ],
                    [
                        'type' => 'link',
                        'href' => backpack_url('dbs/' . request()->route()->parameter('database') . '/fbs/returns?invpk=' . $invoice->pk),
                        'label' => 'Returns',

                    ],
                ]
            ],


        ];

        switch ($invoice->custtype) {
            case 'HD':
                $content[] = [
                    'type' => 'menu',

                    'label' => 'CHUB',
                    'menu' => [

                        [
                            'type' => 'link',
                            'href' => backpack_url('dbs/' . request()->route()->parameter('database') . '/fbs/fbsorder/' . $invoice->pk . '/chub/confirmation'),
                            'label' => 'Send Confirmation',

                        ],
                        [
                            'type' => 'link',
                            'href' => backpack_url('dbs/' . request()->route()->parameter('database') . '/fbs/fbsorder/' . $invoice->pk . '/chub/invoice'),
                            'label' => 'Send invoice',

                        ],
                    ]
                ];
                break;
            case 'AF':
                $content[] = [
                    'type' => 'menu',

                    'label' => 'Payments',
                    'menu' => [
                        [
                            'type' => 'link',
                            'href' =>
                                sophio_route('fbs/fbsorder.refund', ['id' => $invoice->pk]),
                            'label' => 'Refund',

                        ],
                        [
                            'type' => 'link',
                            'href' => backpack_url('dbs/' . request()->route()->parameter('database') . '/fbs/paymentdetail?invpk=' . $invoice->pk),
                            'label' => 'Payment History',

                        ],
                        [
                            'type' => 'link',
                            'href' => backpack_url('dbs/' . request()->route()->parameter('database') . '/fbs/fbsorder/' . $invoice->pk . "/paymentstatus"),
                            'label' => 'Payment Status',
                            'attributes' => [
                                "data-toggle" => "modal",
                                "data-target" => "#payment_status_modal"
                            ]
                        ],

                    ]
                ];
                $content[] = [
                    'type' => 'menu',

                    'label' => 'SaaS',
                    'menu' => [
                        [
                            'type' => 'link',
                            'href' =>
                                sophio_route('fbs/fbsorder.canceltosaas', ['id' => $invoice->pk]),
                            'label' => 'Send Cancel to SaaS Website',

                        ],


                    ]
                ];
                break;
            case 'NAT':
                $content[] = [
                    'type' => 'menu',

                    'label' => 'Payments',
                    'menu' => [
                        [
                            'type' => 'link',
                            'href' => backpack_url('dbs/' . request()->route()->parameter('database') . '/fbs/paymentdetail?invpk=' . $invoice->pk),
                            'label' => 'Payment History',

                        ],

                    ]
                ];
                $content[] = [
                    'type' => 'menu',

                    'label' => 'Corcentric',
                    'menu' => [

                        [
                            'type' => 'link',
                            'href' => backpack_url('dbs/' . request()->route()->parameter('database') . '/fbs/fbsorder/' . $invoice->pk . '/cor/transaction'),
                            'label' => 'Send Transaction',

                        ],
                        [
                            'type' => 'link',
                            'href' => backpack_url('dbs/' . request()->route()->parameter('database') . '/fbs/fbsorder/' . $invoice->pk . '/cor/check'),
                            'label' => 'Check',

                        ],
                    ]
                ];
                break;
            case 'WAL':
                $content[] = [
                    'type' => 'menu',

                    'label' => 'Walmart',
                    'menu' => [
                        [
                            'type' => 'link',
                            'href' => backpack_url('dbs/' . request()->route()->parameter('database') . '/fbs/fbsorder/' . $invoice->pk . "/walmart/acknowledgement"),
                            'label' => 'Acknowledgement',

                        ],
                        [
                            'type' => 'link',
                            'href' => backpack_url('dbs/' . request()->route()->parameter('database') . '/fbs/fbsorder/' . $invoice->pk . "/walmart/shipping"),
                            'label' => 'Shipping',

                        ],
                        [
                            'type' => 'link',
                            'href' => backpack_url('dbs/' . request()->route()->parameter('database') . '/fbs/fbsorder/' . $invoice->pk . "/walmart/cancel"),
                            'label' => 'Cancellation',

                        ],
                        [
                            'type' => 'link',
                            'href' => 'https://supplierone.wal-mart.com/dsv-orders/' . $invoice->ponumber,
                            'label' => 'View in SupplierOne',
                            ['attributes' => ['target' => '_blank',]],


                        ],
                        [
                            'type' => 'link',
                            'href' => route('walmart/remit.index', ['ponumber' => $invoice->ponumber]),
                            'label' => 'Remit',
                            ['attributes' => ['target' => '_blank',]],


                        ],
                    ]
                ];
                break;
        }
        if ($invoice->lineitem->where('sup', 'PAR')->count() > 0) {
            $content[] = [
                'type' => 'menu',

                'label' => 'Parts Authority',
                'menu' => [
                    [
                        'type' => 'link',
                        'href' => backpack_url('dbs/' . request()->route()->parameter('database') . '/fbs/fbsorder/' . $invoice->pk . "/par/status"),
                        'label' => 'Order Status',

                    ],

                ]
            ];
        }

        if ($invoice->invstatus === "O") {
            $content[] = [
                'type' => 'link',
                'href' => backpack_url('dbs/' . request()->route()->parameter('database') . '/fbs/fbsorder/' . $invoice->pk . '/autofulfill'),
                'label' => 'Auto fulfill',

            ];
        }


        $content[] = [
            'type' => 'menu',

            'label' => 'Supplier',
            'menu' => [

                [
                    'type' => 'link',
                    'href' => sophio_route('fbs/fbsorder.sendtrackingrequestnumber', ['id' => $invoice->pk]),
                    'label' => 'Request Tracking',

                ],

                [
                    'type' => 'link',
                    'href' => sophio_route('fbs/fbsorder.sendsupplierordercancellation', ['id' => $invoice->pk]),
                    'label' => 'Cancel Confirm',

                ],
            ]
        ];
        if ($invoice->track_num !== "") {
            $content[] =
                ['type' => 'menu',

                    'label' => 'Shipping',
                    'menu' => [[
                        'type' => 'link',
                        'href' => backpack_url('dbs/' . request()->route()->parameter('database') . '/fbs/fbsorder/shipping?id=' . $invoice->pk),
                        'label' => 'Shipping',
                    ],[
                        'type' => 'link',
                        'href' => sophio_route('fbs/fbsorder.sendtrackingcustomer' ,['id'=> $invoice->pk]),
                        'label' => 'Send Tracking Mail to Customer',
                    ],

                        ]
                ];
        } else {
            $content[] =
                [
                    'type' => 'link',
                    'href' => backpack_url('dbs/' . request()->route()->parameter('database') . '/fbs/fbsorder/shipping?id=' . $invoice->pk),
                    'label' => 'Shipping',

                ];
        }

        Widget::add([
            'type' => 'topbar',
            'content' => $content
        ])->to('before_content');
    }
}