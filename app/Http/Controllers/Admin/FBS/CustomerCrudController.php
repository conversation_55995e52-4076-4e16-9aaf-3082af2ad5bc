<?php

namespace App\Http\Controllers\Admin\FBS;

use App\Library\Sophio\AutozoneCustomerService;
use App\Library\Sophio\AutozoneDiscounts;
use App\Library\Sophio\BarcodeLookup\APIClient;
use App\Library\Sophio\Jobs\FBS\AutozoneStoreUpdateJob;
use App\Library\Sophio\Traits\AuditOperation;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Prologue\Alerts\Facades\Alert;
use Sophio\Common\Controllers\BaseTenantCrudController;
use Sophio\Common\Models\ApiKeys;
use Sophio\Common\Models\FBS\Customer;
use Sophio\Common\Models\FBS\CustShipTo;
use Sophio\Common\Models\FBS\Lookups;
use Sophio\Common\Models\FBS\Marketplace;
use Sophio\Common\Models\SystemLog;
use Sophio\Common\Repository\Settings;
use Sophio\Common\ShipStation\src\Library\ShipStationManager;
use Sophio\FBSOrder\Library\Rules\Supplier\ActiveSuppliersNetworkList;
use Sophio\FBSStatements\Library\CustomerStatementService;

class CustomerCrudController extends BaseTenantCrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use AuditOperation;
    use CustomerTrait;

    public function __construct()
    {


        parent::__construct();
    }

    public function setupOtherRoutes($segment, $routeName, $controller)
    {
        Route::any($segment . '/updateAutozoneCustomers', [
            'as' => $routeName . '.updateAutozoneCustomers',
            'uses' => $controller . '@updateAutozoneCustomers',
            'operation' => 'updateAutozoneCustomers',
        ]);
        Route::any($segment . '/importAutozoneNexpart', [
            'as' => $routeName . '.importAutozoneNexpart',
            'uses' => $controller . '@importAutozoneNexpart',
            'operation' => 'importAutozoneNexpart',
        ]);
        Route::any($segment . '/importAutozoneDiscounts', [
            'as' => $routeName . '.importAutozoneDiscounts',
            'uses' => $controller . '@importAutozoneDiscounts',
            'operation' => 'importAutozoneDiscounts',
        ]);
        Route::any($segment . '/{id}/statement', [
            'as' => $routeName . '.statement',
            'uses' => $controller . '@statement',
            'operation' => 'statement',
        ]);
        Route::any($segment . '/{id}/apikeys', [
            'as' => $routeName . '.apikeys',
            'uses' => $controller . '@apikeys',
            'operation' => 'apikeys',
        ]);
        Route::any($segment . '/{id}/createapikey', [
            'as' => $routeName . '.createapikey',
            'uses' => $controller . '@createapikey',
            'operation' => 'createapikey',
        ]);
        Route::any($segment . '/{id}/shipstationsuppliercarrier', [
            'as' => $routeName . '.shipstationsuppliercarrier',
            'uses' => $controller . '@shipstationsuppliercarrier',
            'operation' => 'shipstationsuppliercarrier',
        ]);
        Route::any($segment . '/{id}/simplesuppliercarrier', [
            'as' => $routeName . '.simplesuppliercarrier',
            'uses' => $controller . '@simplesuppliercarrier',
            'operation' => 'simplesuppliercarrier',
        ]);
        Route::any($segment . '/{id}/suppliersetting', [
            'as' => $routeName . '.suppliersetting',
            'uses' => $controller . '@suppliersetting',
            'operation' => 'suppliersetting',
        ]);
        Route::any($segment . '/{id}/activateb2b', [
            'as' => $routeName . '.activateb2b',
            'uses' => $controller . '@activateb2b',
            'operation' => 'activateb2b',
        ]);

        Route::any($segment . '/missingfromnexpart', [
            'as' => $routeName . '.missingfromnexpart',
            'uses' => $controller . '@missingfromnexpart',
            'operation' => 'missingfromnexpart',
        ]);

    }
    public function suppliersetting($id)
    {

        setLayoutBoxed();
        $suppliers = (new ActiveSuppliersNetworkList())()->get();
        $customer = Customer::find($this->crud->getCurrentEntryId() ?? $id);

        $setting = request()->get('setting');
        if (request()->post()) {

            foreach (request()->get($setting) as $profilepk => $value) {
                if ($value == null) {
                    $value = "";
                }
                $customer->xml[  $setting.'-'.$profilepk] = $value;
            }

            $customer->save();
            if (request()->ajax()) {
                return response()->json([]);
            }
            Alert::add('success', 'Saved!')->flash();
            return back();
        }
        $lookups = Lookups::where('type',$setting)->get();

        return view('admin.fbs.customer.suppliersetting', [
            'customer' => $customer,
            'suppliers' => $suppliers,
            'suppliersetting' => $setting,
            'lookups' => $lookups,

        ]);
    }
    public function simplesuppliercarrier($id)
    {

        setLayoutBoxed();
        $suppliers = (new ActiveSuppliersNetworkList())()->get();
        $customer = Customer::find($this->crud->getCurrentEntryId() ?? $id);

        $carrier = request()->get('carrier');
        if (request()->post()) {

            foreach (request()->get($carrier . 'ACCOUNT') as $profilepk => $value) {
                if ($value == null) {
                    $value = "";
                }
                $customer->xml[$carrier . 'ACCOUNT' . $profilepk] = $value;
            }
            if(request()->get($carrier . 'POSTALCODE')) {
                foreach (request()->get($carrier . 'POSTALCODE') as $profilepk => $value) {
                    if ($value == null) {
                        $value = "";
                    }
                    $customer->xml[$carrier . 'POSTALCODE' . $profilepk] = $value;
                }
            }

            $customer->save();
            if (request()->ajax()) {
                return response()->json([]);
            }
            Alert::add('success', 'Saved!')->flash();
            return back();
        }

        return view('admin.fbs.customer.simplecarrier', [
            'customer' => $customer,
            'suppliers' => $suppliers,
            'carrier' => $carrier
        ]);
    }
    public function shipstationsuppliercarrier($id)
    {

        setLayoutBoxed();
        $suppliers = (new ActiveSuppliersNetworkList())()->get();
        $customer = Customer::find($this->crud->getCurrentEntryId() ?? $id);

        $carrier = request()->get('carrier');
        $supplier_sophio_carriers = [];
        $codes=[
          'FEDEX'=>'fedex',
            'STAMPS'=>'stamps_com',
            'UPS'=>'ups'
        ];

        foreach($suppliers as $supplier) {
            if(isset($supplier->SETTINGS['TRACKINGNUMBERTYPE']) && in_array($supplier->SETTINGS['TRACKINGNUMBERTYPE'], ['SOPHIOFBS','Shipstation'])) {
                $supplier_sophio_carriers[$supplier->PK] =[];
                $shipstationManager = new ShipStationManager(new Settings(['tenant_db' => \Route::current()->parameter('database')]));
                $shipstationManager->setSupplierCreds($supplier);
                $shipstationManager->createClient();
                $table = $shipstationManager->listCarriers();

                foreach ($table  as  $c) {
                    if(\Illuminate\Support\Str::contains($c->nickname,'Sophio') && $c->code==$codes[$carrier]) {
                        $supplier_sophio_carriers[$supplier->PK][] = $c;
                    }
                }
            }

        }
        if (request()->post()) {

            foreach (request()->get($carrier . 'ACCOUNT') as $profilepk => $value) {
                if ($value == null) {
                    $value = "";
                }
                $customer->xml[$carrier . 'ACCOUNT' . $profilepk] = $value;
            }
            $customer->save();
            if (request()->ajax()) {
                return response()->json([]);
            }
            Alert::add('success', 'Saved!')->flash();
            return back();
        }


        return view('admin.fbs.customer.shipstationcarrier', [
            'customer' => $customer,
            'suppliers' => $suppliers,
            'carrier' => $carrier,
            'supplier_sophio_carriers'=>$supplier_sophio_carriers
        ]);
    }

    public function statement($id)
    {
        set_time_limit(0);
        setLayoutBoxed();
        $id = $this->crud->getCurrentEntryId() ?? $id;
        $customer = $this->crud->getEntry($id);
        $when = request()->get('when') ?? 'LASTMONTH';
        $period = request()->get('period');
        $filename = "";
        if ($period) {
            [$start, $end] = explode(' - ', $period);
            $period = [\Illuminate\Support\Carbon::parse($start)->startOfDay(), \Illuminate\Support\Carbon::parse($end)->endOfDay()];

        } else {
            [$start, $end] = getDatesFromWhen($when, 'm/d/y');
        }
        $csService = new CustomerStatementService($customer, $when, $period);
        $invoices = $csService->getInvoices();
        $returns = $csService->getReturns();
        //$aging = $csService->getAging();
        $aging = null;
        $noprint = request()->get('noprint') ?? 'false';
        if (request()->get('send') == 'true') {

            $csService->sendFTP();
            $csService->sendEmail();
            $noprint = 'true';
            return view('admin.fbs.customer.statement_sent', [
                'invoices' => $invoices,
                'returns' => $returns,
                'aging' => $aging,
                'customer' => $customer,
                'balance' => 0.0,
                'start' => $start,
                'end' => $end,
                'pdf' => Storage::disk('fbs')->url('customer_statement/' . $csService->getBasePDFFilename() . '.pdf')

            ]);


        }
        if (request()->get('print')) {
            return view('admin.fbs.customer.statement_print', [
                'invoices' => $invoices,
                'returns' => $returns,
                'aging' => $aging,
                'customer' => $customer,
                'balance' => 0.0,
                'start' => $start,
                'end' => $end
            ]);

        } else {
            if ($noprint === 'true') {
            } else {
                $csService->generatePDFByURL();
            }
            return view('admin.fbs.customer.statement', [
                'invoices' => $invoices,
                'returns' => $returns,
                'aging' => $aging,
                'customer' => $customer,
                'balance' => 0.0,
                'start' => $start,
                'end' => $end,
                'pdf' => Storage::disk('fbs')->url('customer_statement/' . $csService->getBasePDFFilename() . '.pdf')

            ]);

        }
    }

    public function setup()
    {
        \Config::set('tenant_db', \Route::current()->parameter('database'));
        $this->crud->setModel(Customer::class);
        $this->crud->setRoute(config('backpack.base.route_prefix') . '/dbs/' . \Route::current()->parameter('database') . '/fbs/customer');
        CRUD::setEntityNameStrings('customer', 'Customer');
        Customer::created(function($entry){
           /*
            CustShipTo::create([

                'custpk'=>$entry->pk,
                'email' =>$entry->email,
                'storepk'=>$entry->storepk,
                'st_name'=>$entry->st_name,
                'st_addr'=>$entry->st_addr,
                'st_addr2'=>$entry->st_addr2,
                'st_city'=>$entry->st_city,
                'st_state'=>$entry->st_state,
                'st_zip'=>$entry->st_zip,
                'st_ctryid'=>$entry->st_ctryid,
                'st_phone'=>$entry->st_phone,
                'apofpo'=>0,
                'intl'=>0,
                'st_company'=>$entry->company,
                'dealerid'=>0,
                'shiptype'=>'',
                'st_dest'=>'',
                'st_email'=>'',
                'st_type'=>'',
                'notes'=>''
            ]);
           */
        });
        /*
    Customer::saved(function ($entry) {
        if (isset($entry->settings['FTPLOCATION']) && isset($entry->settings['FTPUSERID']) && isset($entry->settings['FTPPASSWORD']) && ($entry->settings['FTPUSERID'] != '') && ($entry->settings['FTPPASSWORD'] != '')) {
            if ($entry->settings['FTPLOCATION'] == config('sophio.admin.ftp_domain')) {
           //     (new SyncFTPuser())($entry->settings['FTPUSERID'], $entry->settings['FTPPASSWORD']);
            }
        }
    });

     * This is a bit more complicated: a custom Unique rule must be made, but also in ftpusers we
     * would need to store the entity (customer/supplier) id, so we can perform uniqueness
     * (otherwise you don't know if the texted value is a new attempt or texts existing value that already belongs
     * to entity
    $this->crud->setValidation([
        'FTPUSERID'=>['unique:facetednew.ftpusers,username,pk',new LocalFtpUser()]
        ]
    );
    */
        $this->crud->query->with('buyerprofile')->with('user');
    }

    public function updateAutozoneCustomers()
    {
        ini_set('max_execution_time', 0);
        ini_set('memory_limit', '2G');
        $files = collect(Storage::disk('fbs')->files('autozone'))->sortByDesc(function ($file) {
            return Storage::disk('fbs')->lastModified($file);
        });
        $settings = new Settings();
        $a = new AutozoneCustomerService($settings);
        if (request()->post()) {
            $remote_filename = request()->file('file')->getClientOriginalName();
            Storage::disk('fbs')->putFileAs('autozone/', request()->file('file'), $remote_filename);
            /*request()->get('format_input_type')*/
            $a = new AutozoneCustomerService($settings);
            switch ( request()->get('format_input_type')) {
                case 'complete_old':        $files = $a->updateCompleteListOld('autozone/' . $remote_filename);break;
                case 'complete_new':        $files = $a->updateCompleteListNew('autozone/' . $remote_filename);break;
                default: $a->updateSimpleList('autozone/' . $remote_filename);
            }
         //   AutozoneStoreUpdateJob::dispatch(config('tenant_db'), 'autozone/' . $remote_filename, request()->get('format_input_type'), backpack_user()->email);
            return view('admin.fbs.customer.updateautozonecustomers', ['files' => $files, 'uploaded' => true, 'asc' => $a, 'lastlog' => null]);
        }

        $lastlog = SystemLog::where('task', 'AutozoneImportCustomers')->orderBy('created_at', 'desc')->first();
        return view('admin.fbs.customer.updateautozonecustomers', ['files' => $files, 'uploaded' => [], 'asc' => $a, 'lastlog' => $lastlog]);
    }

    public function missingfromnexpart()
    {
        $files = collect(Storage::disk('fbs')->files('autozone'))->filter(function ($file) {
            return Str::contains($file, 'missing', true);
        })->sortByDesc(function ($file) {
            return Storage::disk('fbs')->lastModified($file);
        });
        $settings = new Settings();
        $a = new AutozoneCustomerService($settings);
        $url = $a->missingFromNexpart();
        return view('admin.fbs.customer.importAutozoneNexpart', ['files' => $files, 'uploaded' => [], 'missingCount' => $a->missingFromNexpartCount()]);
    }

    public function importAutozoneNexpart()
    {
        $files = collect(Storage::disk('fbs')->files('autozone'))->filter(function ($file) {
            return Str::contains($file, 'missing', true);
        })->sortByDesc(function ($file) {
            return Storage::disk('fbs')->lastModified($file);
        });
        $settings = new Settings();
        $a = new AutozoneCustomerService($settings);
        if (request()->post()) {
            $remote_filename = request()->file('file')->getClientOriginalName();
            Storage::disk('fbs')->putFileAs('autozone/', request()->file('file'), $remote_filename);


            $a->importNexpart('autozone/' . $remote_filename);
            $url = $a->missingFromNexpart();
            return view('admin.fbs.customer.importAutozoneNexpart', ['files' => $files, 'uploaded' => true, 'missingCount' => $a->missingFromNexpartCount(), 'url' => $url]);
        }


        return view('admin.fbs.customer.importAutozoneNexpart', ['files' => $files, 'uploaded' => [], 'missingCount' => $a->missingFromNexpartCount()]);
    }

    public function importAutozoneDiscounts()
    {
        $settings = new Settings();
        $a = new AutozoneDiscounts();

        if (request()->post()) {
            $remote_filename = request()->file('file')->getClientOriginalName();
            Storage::disk('fbs')->putFileAs('autozone/', request()->file('file'), $remote_filename);
            $c = $a->import('autozone/' . $remote_filename, request()->get('effectiveDate'));
            Alert::add('success', 'Imported ' . $c . ' discount lines')->flash();
            //return redirect(sophio_route('fbs/discount/{db}/db.index',['db'=>'b2bnational']));
        }
        return view('admin.fbs.customer.importAutozoneDiscounts');
    }

    public function destroy($id)
    {
        $this->crud->hasAccessOrFail('delete');
        $model = $this->crud->where('pk', $id)->first();
        if (isset($model->FTPLOCATION) && isset($model->FTPUSERID) && isset($model->FTPPASSWORD)) {
            if ($model->FTPLOCATION == config('sophio.admin.ftp_domain')) {
                //   (new SyncFTPuser())($model->FTPUSERID, $model->FTPPASSWORD, 'delete');
            }
        }

        return $this->crud->delete($id);
    }

    protected function setupListOperation()
    {

        if (!backpack_user()->hasRole(['super-admin'])) {
            $this->crud->denyAccess(['create', 'delete', 'update']);

        } else {
            $this->crud->addButtonFromView('line', 'GenerateFeed', 'ddatafeedcustomer', 'end');
        }

        $this->crud->addColumns([
            ['name' => 'pk', 'type' => 'text'],
            'storepk', 'custtype', 'accountnum',
            'company', 'lastname', 'firstname',
            'email',
            'city', 'state', 'zip',
            [
                'name' => 'impersonate',
                'type' => 'text',
                'value' => function ($entry) {
                    return 'Impersonate';
                },
                'wrapper' => [
                    'href' => function ($crud, $column, $entry, $related_key) {
                        return sophio_route('customer/{custpk}/users.index', ['custpk' => $entry->pk]);
                    },
                ],

            ],
            [
                'name' => 'orders',
                'type' => 'text',
                'wrapper' => [
                    'href' => function ($crud, $column, $entry, $related_key) {
                        return sophio_route('fbs/fbsorder.index', ['custpk' => $entry->pk]);
                    },
                ],
                'value' => function ($entry) {
                    return 'View Orders';
                }
            ],
            [
                'name' => 'returns',
                'type' => 'text',
                'wrapper' => [
                    'href' => function ($crud, $column, $entry, $related_key) {
                        return sophio_route('fbs/returns.index', ['custpk' => $entry->pk]);
                    },
                ],
                'value' => function ($entry) {
                    return 'View Returns';
                }
            ],


        ]);
        if (backpack_user()->hasRole('super-admin')) {
            $this->crud->addColumns([[
                'name' => 'pricing',
                'type' => 'text',
                'wrapper' => [
                    'href' => function ($crud, $column, $entry, $related_key) {
                        if (isset($entry->xml['ACESPARTSDB']) && $entry->xml['ACESPARTSDB'] !== "" && $entry->accountnum!=="") {
                            return sophio_route('fbs/pricing/{accountnum}/accountnum.index', ['accountnum' => $entry->accountnum]);


                        } else {
                            return "#";
                        }

                    },
                ],
                'value' => function ($entry) {
                    if (isset($entry->xml['ACESPARTSDB']) && $entry->xml['ACESPARTSDB'] !== "") {
                        return 'Pricing';
                    }
                }
            ]]);
        }
        $this->crud->addFilter([ // dropdown filter
            'name' => 'PK',
            'type' => 'text',
            'label' => 'PK',
        ],
            false
            , function ($value) {
                $this->crud->addClause('where', 'pk', '=', trim($value));
            });
        $this->crud->addFilter([ // dropdown filter
            'name' => 'accountnum',
            'type' => 'text',
            'label' => 'accountnum',
        ],
            false
            , function ($value) {
                $this->crud->addClause('where', 'accountnum', '=', trim($value));
            });
        $this->crud->addFilter(
            [
                'name' => 'custtype',
                'type' => 'dropdown',
                'label' => 'Customer Type',
            ],
            function () {
                $m = Marketplace::all();
                $x = [];
                foreach ($m as $v) {
                    $x[$v->market] = $v->market;
                }
                return $x;
            }
            , function ($value) {
            $this->crud->addClause('where', 'custtype', trim($value));
        });

        $this->crud->addFilter([ // dropdown filter
            'name' => 'company',
            'type' => 'text',
            'label' => 'Company',
        ],
            false
            , function ($value) {
                $this->crud->addClause('where', 'company', 'LIKE', "%" . trim($value) . "%");
            });

        $this->crud->addFilter([
            'type' => 'simple',
            'name' => 'active',
            'label' => 'Active'
        ],
            false,
            function () { // if the filter is active
                $this->crud->query->whereHas('lineitem');
            });
        $this->crud->addFilter([ // dropdown filter
            'name' => 'lastname',
            'type' => 'text',
            'label' => 'lastname',
        ],
            false
            , function ($value) {
                $this->crud->addClause('where', 'lastname', '=', trim($value));
            });
        $this->crud->addFilter([ // dropdown filter
            'name' => 'email',
            'type' => 'text',
            'label' => 'email',
        ],
            false
            , function ($value) {
                $this->crud->addClause('where', 'email', '=', trim($value));
            });
        $this->crud->addFilter([ // dropdown filter
            'name' => 'city',
            'type' => 'text',
            'label' => 'city',
        ],
            false
            , function ($value) {
                $this->crud->addClause('where', 'city', '=', trim($value));
            });
        $st = Lookups::where('type', 'STATE')->get();
        $states = [];
        foreach ($st as $v) {
            $states[$v->cdata] = $v->cdata1;
        }

        $this->crud->addFilter([ // dropdown filter
            'name' => 'state',
            'type' => 'select2',
            'label' => 'state',
        ],
            $states
            , function ($value) {
                $this->crud->addClause('where', 'state', '=', trim($value));
            });
        $this->crud->addFilter([ // dropdown filter
            'name' => 'zip',
            'type' => 'text',
            'label' => 'zip',
        ],
            false
            , function ($value) {
                $this->crud->addClause('where', 'zip', '=', trim($value));
            });
        $this->crud->addFilter([ // dropdown filter
            'name' => 'phone',
            'type' => 'text',
            'label' => 'phone',
        ],
            false
            , function ($value) {
                $this->crud->addClause('where', 'phone', '=', trim($value));
            });
        $this->crud->addFilter([
            'type' => 'simple',
            'name' => 'buyerprofile',
            'label' => 'Has Buyer Profile'
        ],
            false,
            function () { // if the filter is active
                $this->crud->addClause('whereHas', 'buyerprofile'); // apply the "active" eloquent scope
            });
    }


    protected function setupUpdateOperation()
    {
        CRUD::setOperationSetting('strippedRequest', function ($request) {
            $input = $request->only(CRUD::getAllFieldNames());
            if ($input['webshop'] === "") {

                unset($input['webshop']);
            }

            return $input;
        });
        $this->setupCreateOperation();


        $this->customerTopBar($this->crud->getCurrentEntry());

    }

    protected function setupCreateOperation()
    {

        $this->crud->addFields($this->getFields());

    }

    protected function getFields()
    {
        $m = Marketplace::all();
        $marketplaces = [];
        foreach ($m as $v) {
            $marketplaces[$v->market] = $v->market;
        }
        $st = Lookups::where('type', 'STATE')->get();
        $states = [];
        foreach ($st as $v) {
            $states[$v->cdata] = $v->cdata1;
        }

        return [
            [
                'name' => 'active',
                'label' => 'Active (enable/disable the customer)',
                'type' => 'select_from_array',
                'options' => [
                    'T' => 'Enabled',
                    'F' => 'Disabled'
                ],
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Account'
            ],

            [
                'name' => 'custtype',
                'label' => 'custtype',
                'type' => 'select_from_array',
                'options' => $marketplaces,
                'tab' => 'Account',
                          'wrapper' => [
        'class' => 'form-group col-md-3',
    ],
            ],
            [
                'name' => 'accountnum',
                'label' => 'Account Number',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Account'
            ],
            [
                'name' => 'storepk',
                'label' => 'Storepk',
                'type' => 'text',
                'default' => config('sophio.admin.default_store'),
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Account'
            ],
            [
                'name' => 'company',
                'label' => 'Company',
                'type' => 'text',
                'tab' => 'Contact'
            ],
            [
                'name' => 'lastname',
                'label' => 'Last Name',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-6',
                ],
                'tab' => 'Contact'
            ],
            [
                'name' => 'firstname',
                'label' => 'First Name',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-6',
                ],
                'tab' => 'Contact'
            ],
            [
                'name' => 'address',
                'label' => 'Address',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-6',
                ],
                'tab' => 'Contact'
            ],
            [
                'name' => 'address2',
                'label' => 'Address2',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-6',
                ],
                'tab' => 'Contact'
            ],
            [
                'name' => 'city',
                'label' => 'City',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Contact'
            ],
            [
                'name' => 'county',
                'label' => 'county',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Contact'
            ],
            [
                'name' => 'state',
                'label' => 'state',
                'type' => 'select2_from_array',
                'options'=>$states,
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Contact'
            ],
            [
                'name' => 'zip',
                'label' => 'zip',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Contact'
            ],
            [
                'name' => 'zip4',
                'label' => 'zip4',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Contact'
            ],
            [
                'name' => 'country',
                'label' => 'country',
                'type' => 'text',
                'default' => 'USA',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Contact'
            ],
            [
                'name' => 'countryid',
                'label' => 'countryid',
                'type' => 'text',
                'default' => 'US',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Contact'
            ],

            [
                'name' => 'foreign',
                'label' => 'foreign',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Contact'
            ],
            [
                'name' => 'phone',
                'label' => 'phone',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Contact'
            ],
            [
                'name' => 'fax',
                'label' => 'fax',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Contact'
            ],

            [
                'name' => 'email',
                'label' => 'email',
                'type' => 'text',
                'tab' => 'Contact'
            ],
            [
                'name' => 'url',
                'label' => 'url',
                'type' => 'text',
                'tab' => 'Contact',
                'limit' => 1000
            ],
            [
                'name' => 'notes',
                'label' => 'notes',
                'type' => 'textarea',
                'tab' => 'Contact'
            ],
            [
                'name' => 'RATESHOP',
                'label' => 'Rate Shop',
                'type' => 'select_from_array',
                'options' => ['BESTWAY' => 'Always Rate Shopping ', 'FROMORDER' => 'Use Specific Carrier /  Service Requested'],
                'default' => 'BESTWAY',
                'store_in' => 'xml',
                'fake' => true,
                'hint'=>'Applies only to markets that use rate shopping. Customer may supply a shipping service. We can use that our do rate shop.',
                'wrapper' => [
                    'class' => 'form-group col-md-6',
                ],
                'tab' => 'Shipping'
            ],
            /*
            [
                'name' => 'PAYSHIPPING',
                'label' => 'Shipping Payer',
                'type' => 'select_from_array',
                'options' => ['US' => 'US', 'CUSTOMER' => 'CUSTOMER'],
                'default' => 'US',
                'store_in' => 'xml',
                'fake' => true,
                'wrapper' => [
                    'class' => 'form-group col-md-6',
                ],
                'tab' => 'Shipping'
            ],
            */
            [
                'name' => 'carrier',
                'label' => 'Shipping carrier Code',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-6',
                ],
                'tab' => 'Shipping'
            ],
            [
                'name' => 'service',
                'label' => 'Shipping Service Code',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-6',
                ],
                'tab' => 'Shipping'
            ],
            [
                'name' => 'separatoroc',
                'type' => 'custom_html',
                'value' => '<div class="hr-text">Override carriers</div>',
                'tab' => 'Shipping',
                'wrapper' => [
                    'class' => 'form-group col-sm-12 mb-0 pb-0',
                ],
            ],
            [
                'name' => 'OVERRIDECARRIERS',
                'label' => 'Override carriers',
                'type' => 'select_from_array',
                'store_in' => 'xml',
                'options' => ['FALSE' => 'Disabled, use store settings', 'TRUE' => 'Override store settings'],
                'default' => 'FALSE',
                'hint'=>'Overrides the allowed carriers as defined in store settings. If a supplier has a disabled carrier, it still not be used, even it is enabled by customer. Has effect only for rate shopping.',
                'fake' => true,
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],
            [
                'name' => 'CARRIERS',
                'label' => 'Carriers',
                'type' => 'select_multiple_tags',
                'allows_multiple' => true,
                'options' => ['USPS'=>'USPS','FEDEX'=>'FEDEX','UPS'=>'UPS'],
                'tab' => 'Shipping',
                'fake' => true,
                'limit' => 1000,
                'wrapper' => [
                    'class' => 'form-group col-md-6',
                ],
                'store_in' => 'xml'
            ],

            [
                'name' => 'separator10',
                'type' => 'custom_html',
                'value' => '<div class="hr-text">Ship To</div>',
                'tab' => 'Shipping',
                'wrapper' => [
                    'class' => 'form-group col-sm-12 mb-0 pb-0',
                ],
            ],
            [
                'name' => 'st_name',
                'label' => 'Ship To Name',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-6',
                ],
                'tab' => 'Shipping'
            ],
            [
                'name' => 'st_addr',
                'label' => 'Ship To Address',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-6',
                ],
                'tab' => 'Shipping'
            ],
            [
                'name' => 'st_addr2',
                'label' => 'Ship To Address 2',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-6',
                ],
                'tab' => 'Shipping'
            ],
            [
                'name' => 'st_city',
                'label' => 'Ship To City',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],
            [
                'name' => 'st_county',
                'label' => 'Ship To county',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],
            [
                'name' => 'st_state',
                'label' => 'Ship To state',
                'type' => 'select2_from_array',
                'options' => $states,
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],
            [
                'name' => 'st_zip',
                'label' => 'Ship To zip',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],
            [
                'name' => 'st_phone',
                'label' => 'Ship To Phone',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],
            [
                'name' => 'st_ctry',
                'label' => 'Ship To Country',
                'type' => 'text',
                'default' => 'US',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],
            [
                'name' => 'st_ctryid',
                'label' => 'Ship To Countr ID',
                'type' => 'text',
                'default' => 'US',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],
            [
                'name' => 'separatorsgfrom',
                'type' =>'custom_html',
                'value' => '<div class="hr-text">Ship From</div>',
                'tab' => 'Shipping',
                'wrapper' => [
                    'class' => 'form-group col-sm-12 mb-0 pb-0',
                ],
            ],
            [
                'name' => 'cc',
                'label' => 'cc',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Payment'
            ],
            [
                'name' => 'paymethod',
                'label' => 'Pay method',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Payment'
            ],
            [
                'name' => 'STRIPECUSTOMERID',
                'label' => 'Stripe Customer ID',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-6',
                ],
                'tab' => 'Payment'
            ],
            [
                'name' => 'cvv2',
                'label' => 'cvv2',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Payment'
            ],
            [
                'name' => 'ccexp',
                'label' => 'ccexp',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Payment'
            ],
            [
                'name' => 'cctype',
                'label' => 'cctype',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Payment'
            ],
            [
                'name' => 'cc',
                'label' => 'cc',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Payment'
            ],
            [
                'name' => 'checkno',
                'label' => 'checkno',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Payment'
            ],
            [
                'name' => 'SALERSREP_MAXORDERSIZE',
                'label' => 'Max order size for sales rep.',
                'type' => 'number',
                'attributes' => ['step' => 'any'],
                'decimals' => 2,
                'prefix' => '$',
                'default' => 0.01,
                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Payment'
            ],
            [
                'name' => 'routingno',
                'label' => 'routingno',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Billing'
            ],
            [
                'name' => 'userid',
                'label' => 'userid',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Account'
            ],
            [
                'name' => 'password',
                'label' => 'password',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Account'
            ],



            [
                'name' => 'custpass',
                'label' => 'custpass',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Account'
            ],

            [
                'name' => 'ACCOUNTINGEMAIL',
                'label' => 'ACCOUNTINGEMAIL',
                'type' => 'text',
                'fake' => true,
                'store_in' => 'settings',
                'tab' => 'Account'
            ],
            [
                'name' => 'APEMAIL',
                'label' => 'Account Payable Email',
                'type' => 'text',
                'fake' => true,
                'store_in' => 'settings',
                'tab' => 'Account'
            ],
            [
                'name' => 'B2BUSERNAME',
                'label' => 'Username of B2B site',
                'type' => 'text',
                'fake' => true,
                'store_in' => 'xml',
                'tab' => 'Account'
            ],
            [
                'name' => 'B2BPASSWORD',
                'label' => 'Password of B2B site',
                'type' => 'text',
                'fake' => true,
                'store_in' => 'xml',
                'tab' => 'Account'
            ],
            [
                'name' => 'NEXPARTCUSTOMERID',
                'label' => 'Nexpart Customer ID',
                'type' => 'text',
                'fake' => true,
                'store_in' => 'xml',
                'tab' => 'Account'
            ],
            [
                'name' => 'NEXPARTUSERNAME',
                'label' => 'Nexpart Customer Username',
                'hint' => 'To be used only if  the customer should use an alternate account to order from suppliers',
                'type' => 'text',
                'fake' => true,
                'store_in' => 'xml',
                'tab' => 'Account'
            ],
            [
                'name' => 'NEXPARTPASSWORD',
                'label' => 'Nexpart Customer Password',
                'hint' => 'To be used only if  the customer should use an alternate account to order from suppliers',
                'type' => 'text',
                'fake' => true,
                'store_in' => 'xml',
                'tab' => 'Account'
            ],
            [
                'name' => 'INFREQBUYER',
                'label' => 'Don\'t check activity',
                'type' => 'select_from_array',
                'hint' => 'If set to FALSE, if we do not receive an order in the last 24 hours, the super-admins are alerted. To disable checking the customer, set to TRUE.',
                'options' => ['TRUE' => 'TRUE', 'FALSE' => 'FALSE'],
                'default' => 'FALSE',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Account'
            ],

            [
                'name' => 'taxable',
                'label' => 'taxable',
                'type' => 'boolean',
                'tab' => 'Taxing'
            ],
            [
                'name' => 'taxid',
                'label' => 'taxid',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-6',
                ],
                'tab' => 'Taxing'
            ],
            [
                'name' => 'taxexpire',
                'label' => 'taxexpire',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-6',
                ],
                'tab' => 'Taxing'
            ],
            [
                'name' => 'MYSQLPRICING',
                'label' => 'MySQL Pricing',
                'hint' => 'Enable Markup pricing support',
                'type' => 'radio',
                'options' => [
                    '' => 'Disabled',
                    'true' => 'Active',
                ],
                'inline' => true,
                'fake' => true,
                'store_in' => 'xml',
                'tab' => 'Pricing'
            ],

            [
                'name' => 'MARKETINGREBATE',
                'label' => 'Marketing Rebate',
                'type' => 'number',
                'attributes' => ["step" => "any"],
                'fake' => true,
                'store_in' => 'xml',
                'tab' => 'Settings'
            ],
            [
                'name' => 'SOPHIOORDERSAPI',
                'label' => 'Sophio Orders API',
                'hint' => 'Enable Order API support',
                'type' => 'radio',
                'options' => [
                    'FALSE' => 'Disabled',
                    'TRUE' => 'Active',
                ],
                'inline' => true,
                'fake' => true,
                'store_in' => 'xml',
                'tab' => 'Order API'
            ],
            [
                'name' => 'EMAILORDERCONFIRMATIONS',
                'label' => 'Email Order Confirmations',
                'type' => 'radio',
                'options' => [
                    '' => 'Disabled',
                    'true' => 'Active',
                ],
                'inline' => true,
                'fake' => true,
                'store_in' => 'xml',
                'tab' => 'Notifications'
            ],
            [
                'name' => 'DISABLENOTIFICATIONS',
                'label' => 'Disable Notifications',
                'type' => 'radio',
                'options' => [
                    'false' => 'Disabled',
                    'true' => 'Active',
                ],
                'inline' => true,
                'fake' => true,
                'store_in' => 'xml',
                'tab' => 'Notifications'
            ],
            [
                'name' => 'EMAILDATAFEEDCONFIRMATIONS',
                'label' => 'Email data feed confirmations',
                'type' => 'text',
                'fake' => true,
                'store_in' => 'xml',
                'tab' => 'Notifications'
            ],
            [
                'name' => 'UPDATECUSTOMERURL',
                'label' => 'Update Customer URL',
                'type' => 'text',
                'fake' => true,
                'store_in' => 'xml',
                'tab' => 'Notifications'
            ],

            [
                'name' => 'RESALECERT',
                'label' => 'Resale Certificate',
                'type' => 'text',
                'limit' => 1000,
                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-6',
                ],
                'tab' => 'Settings'
            ],
            [
                'name' => 'RESALECERTEXPIRATIONDATE',
                'label' => 'Resale Certificate Expiration Date',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-6',
                ],
                'tab' => 'Settings'
            ],
            [
                'name' => 'ACESPARTSDB',
                'label' => 'ACES Parts and Pricing Database',
                'hint' => 'Database where inventory and pricing is stored. For general customers \'fbn_aces\' should be used',
                'type' => 'text',
                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Pricing'
            ],
            [
                'name' => 'AFFILIATEPRICINGDB',
                'label' => 'Affiliate Pricing Database',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Settings'
            ],
            [
                'name' => 'SELLITEMSCOREPRICE',
                'label' => 'SELLITEMSCOREPRICE',
                'type' => 'radio',
                'options' => [
                    '' => 'Disabled',
                    'true' => 'Active',
                ],
                'inline' => true,

                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Settings'
            ],
            [
                'name' => 'STORENOTES',
                'label' => 'Store Notes',
                'type' => 'textarea',
                'hint' => 'A comment that is added to each order note that is viewed in our system (e.g. special shipping notes etc.)',
                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-12',
                ],
                'tab' => 'Order Settings'
            ],
            [
                'name' => 'INVSTATUS',
                'label' => 'Invoice Status',
                'type' => 'text',
                'hint' =>'Default is new. If another status is used, the invoice will not be processed by the system.',
                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Order Settings'
            ],
            [
                'name' => 'AFFILIATEFLATSHIPPINGRATE',
                'label' => 'Affiliate Flat Shipping Rate',
                'hint' => 'If set the flat rate is added to the invoice total.',
                'type' => 'number',
                'attributes' => ["step" => "any"],
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'fake' => true,
                'store_in' => 'xml',
                'tab' => 'Order Settings'
            ],
            [
                'name' => 'USERPK',
                'label' => 'User PK',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Settings'
            ],
            [
                'name' => 'INVNOASPO',
                'label' => 'Invno as PoNumber',
                'type' => 'radio',
                'hint'=>'Use the Invno as PONumber.Used in some special cases.',
                'options' => [
                    'FALSE' => 'Disabled',
                    'TRUE' => 'Active',
                ],

                'fake' => true,
                'store_in' => 'xml',
                'inline' => true,
                'tab' => 'Order Settings'
            ],
            [
                'name' => 'SHIPFROMNAME',
                'label' => 'Ship From name',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],

            [
                'name' => 'SHIPFROMCOMPANY',
                'label' => 'Ship From Company',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',

                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],

            [
                'name' => 'SHIPFROMADDRESS',
                'label' => 'Ship From Address',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],

            [
                'name' => 'SHIPFROMADDRESS2',
                'label' => 'Ship From Address2',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],

            [
                'name' => 'SHIPFROMCITY',
                'label' => 'Ship From City',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],

            [
                'name' => 'SHIPFROMSTATE',
                'label' => 'Ship From State',
                'type' => 'select2_from_array',
                'options' => $states,
                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],

            [
                'name' => 'SHIPFROMPOSTAL',
                'label' => 'Ship From Postal Code',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],

            [
                'name' => 'SHIPFROMCOUNTRY',
                'label' => 'Ship From Country',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],

            [
                'name' => 'SHIPFROMPHONE',
                'label' => 'Ship From Phone',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],



            [
                'name' => 'BILLTOCARRIERCODE',
                'label' => 'BillTo Carrier Code',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Billing'
            ],

            [
                'name' => 'BILLTOSHIPPINGSERVICE',
                'label' => 'BillTo Shipping Service',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Billing'
            ],

            [
                'name' => 'BILLTOSERVICECODE',
                'label' => 'BILLTOSERVICECODE',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Billing'
            ],
/*
            [
                'name' => 'FEDEXTYPE',
                'label' => 'Fedex account type',
                'type' => 'select_from_array',
                'options' => ['PERSUPPLIER' => 'Customer provides one account per supplier', 'OWN' => 'Customer provides account', 'OUR' => 'Use our account'],
                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],
*/
            [
                'name' => 'separatorfedex',
                'type' =>'custom_html',
                'value' => '<div class="hr-text">Fedex</div>',
                'tab' => 'Shipping',
                'wrapper' => [
                    'class' => 'form-group col-sm-12 mb-0 pb-0',
                ],
            ],
            [
                'name' => 'FEDEXACCOUNT',
                'label' => 'Fedex Account (3rd party)',
                'type' => 'text',
                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],
            [
                'name' => 'FEDEXOTHERACCOUNT',
                'label' => 'Fedex Other Account (alternative)',
                'type' => 'text',
                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],
            [
                'name' => 'FEDEXPOSTALCODE',
                'label' => 'Fedex Postal Code',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],
            [
                'name' => 'FEDEXSHIPFROMCOMPANY',
                'label' => 'FEDEXSHIPFROMCOMPANY',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],
            [
                'name' => 'FEDEXAPIKEY',
                'label' => 'FEDEXAPIKEY',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],
            [
                'name' => 'FEDEXAPIPASSWORD',
                'label' => 'FEDEXAPIPASSWORD',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],

            [
                'name' => 'FEDEXMETERNUMBER',
                'label' => 'FEDEXMETERNUMBER',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],

            [
                'name' => 'FEDEXSMARTPOSTHUBID',
                'label' => 'FEDEXSMARTPOSTHUBID',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],
            [
                'name' => 'fedexaccounts',
                'type' => 'carrier_accounts',
                'carrier' => 'FEDEX',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],
            [
                'name' => 'FEDEXPOSTALCODE',
                'label' => 'FEDEXPOSTALCODE',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],
            [
                'name' => 'separatorstamps',
                'type' =>'custom_html',
                'value' => '<div class="hr-text">USPS</div>',
                'tab' => 'Shipping',
                'wrapper' => [
                    'class' => 'form-group col-sm-12 mb-0 pb-0',
                ],
            ],
            [
                'name' => 'STAMPSTYPE',
                'label' => 'USPS account type',
                'type' => 'select_from_array',
                'options' => ['PERSUPPLIER' => 'Customer provides one account per supplier', 'OTHER' => 'Use supplier my other', 'OUR' => 'Use our my other'],
                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],
            [
                'name' => 'STAMPSACCOUNT',
                'label' => 'STAMPSACCOUNT',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],
            [
                'name' => 'STAMPSUSERNAME',
                'label' => 'STAMPSUSERNAME',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],
            [
                'name' => 'STAMPSPASSWORD',
                'label' => 'STAMPSPASSWORD',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],
            [
                'name' => 'stampsaccounts',
                'type' => 'ss_carrier_accounts',
                'carrier' => 'STAMPS',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],
            [
                'name' => 'separatoups',
                'type' =>'custom_html',
                'value' => '<div class="hr-text">UPS</div>',
                'tab' => 'Shipping',
                'wrapper' => [
                    'class' => 'form-group col-sm-12 mb-0 pb-0',
                ],
            ],
            [
                'name' => 'UPSTYPE',
                'label' => 'UPS account type',
                'type' => 'select_from_array',
                'options' => ['PERSUPPLIER' => 'Customer provides one account per supplier', 'OWN' => 'Use customer account', 'OUR' => 'Use our account'],
                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],
            [
                'name' => 'UPSACCOUNT',
                'label' => 'Customer UPS account',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],
            [
                'name' => 'UPSPOSTALCODE',
                'label' => 'Customer UPS postal code',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],
            [
                'name' => 'upsaccounts',
                'type' => 'carrier_accounts',
                'carrier' => 'UPS',
                'wrapper' => [
                    'class' => 'form-group col-md-3  pt-4',
                ],
                'tab' => 'Shipping'
            ],


            [
                'name' => 'separatorsur',
                'type' =>'custom_html',
                'value' => '<div class="hr-text">Surcharges</div>',
                'tab' => 'Shipping',
                'wrapper' => [
                    'class' => 'form-group col-sm-12 mb-0 pb-0',
                ],
            ],
            [
                'name' => 'SURCHARGEENABLE',
                'label' => 'SURCHARGEENABLE',
                'type' => 'select_from_array',
                'options' => [
                    'TRUE' => 'Enabled',
                    'FALSE' => 'Disabled'
                ],
                'fake' => true,
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'store_in' => 'xml',
                'tab' => 'Shipping'
            ],
            [
                'name' => 'SURCHARGELIMIT',
                'label' => 'SURCHARGELIMIT',
                'type' => 'text',
                'fake' => true,
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'store_in' => 'xml',
                'tab' => 'Shipping'
            ],
            [
                'name' => 'separatorss',
                'type' =>'custom_html',
                'value' => '<div class="hr-text">Custom Shipstation</div>',
                'tab' => 'Shipping',
                'wrapper' => [
                    'class' => 'form-group col-sm-12 mb-0 pb-0',
                ],
            ],
            [
                'name' => 'SHIPSTATIONRATEENABLE',
                'label' => 'Enable alternate SS account for rate shop',
                'type' => 'select_from_array',
                'options' => [
                    'TRUE' => 'Enabled',
                    'FALSE' => 'Disabled'
                ],
                'fake' => true,
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'store_in' => 'xml',
                'tab' => 'Shipping'
            ],
            [
                'name' => 'SHIPSTATIONRATEAPIKEY',
                'label' => 'Shipstation API Key (for rate only)',
                'type' => 'text',
                'fake' => true,
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'store_in' => 'xml',
                'tab' => 'Shipping'
            ],
            [
                'name' => 'SHIPSTATIONRATEAPISECRET',
                'label' => 'Shipstation API Secret (for rate only)',
                'type' => 'text',
                'fake' => true,
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'store_in' => 'xml',
                'tab' => 'Shipping'
            ],
            [
                'name' => 'separatorsspre',
                'type' =>'custom_html',
                'value' => '<div class="hr-text">Custom Shipping Prefix PO</div>',
                'tab' => 'Shipping',
                'wrapper' => [
                    'class' => 'form-group col-sm-12 mb-0 pb-0',
                ],
            ],
            [
                'name' => 'SHIPPINGPREFIXFORPO',
                'type' => 'supplier_setting',
                'settingname' => 'SHIPPINGPREFIXFORPO',
                'settinglabel' => 'Shipping prefix for PO',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Shipping'
            ],
            [
                'name' => 'FTPPORT',
                'label' => 'FTPPORT',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'settings',
                'tab' => 'FTP'
            ],
            [
                'name' => 'FTPUSERID',
                'label' => 'FTPUSERID',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'settings',
                'tab' => 'FTP'
            ],
            [
                'name' => 'FTPPASSWORD',
                'label' => 'FTPPASSWORD',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'settings',
                'tab' => 'FTP'
            ],
            [
                'name' => 'FTPLOCATION',
                'label' => 'FTPLOCATION',
                'type' => 'text',
                'fake' => true,
                'store_in' => 'settings',
                'tab' => 'FTP'
            ],
            [
                'name' => 'FTPCERTIFICATE',
                'label' => 'FTPCERTIFICATE',
                'type' => 'text',
                'fake' => true,
                'store_in' => 'settings',
                'tab' => 'FTP'
            ],
            [
                'name' => 'FTPFILENAME',
                'label' => 'FTPFILENAME',
                'type' => 'text',
                'fake' => true,
                'store_in' => 'settings',
                'tab' => 'FTP'
            ],
            [
                'name' => 'DATAFEED_TABLE',
                'label' => 'Data Feed Table',
                'type' => 'text',
                'hint' => 'In general the table name is pricingDatabase.datafeed_custpk, but check with development for custom cases',
                'fake' => true,
                'store_in' => 'settings',
                'tab' => 'Pricing'
            ],
            [
                'name' => 'pricing_fieldset',

                'type' => 'custom_html',
                'value' => '<div class="hr-text">B2B pricing rules</div>',
                'tab' => 'Pricing',
                'wrapper' => [
                    'class' => 'form-group col-sm-12 mb-0 pb-0',
                ],
            ],

            [
                'name' => 'PRIVATEKEY',
                'label' => 'PRIVATEKEY',
                'type' => 'text',
                'limit' => 1000,
                'fake' => true,
                'store_in' => 'settings',

                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Settings'
            ],
            [
                'name' => 'SALESREP',
                'label' => 'Enable Sales Reps',

                'limit' => 1000,
                'fake' => true,
                'store_in' => 'xml',
                'type' => 'select_from_array',
                'options' => [
                    'FALSE' => 'Disabled',
                    'TRUE' => 'Enabled',

                ],
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Settings'
            ],
            [
                'name' => 'NOSHIPTOOVERRIDE',
                'label' => 'Disallow Sales Rep to use custom shipping addresses',

                'limit' => 1000,
                'fake' => true,
                'store_in' => 'xml',
                'type' => 'select_from_array',
                'options' => [
                    'FALSE' => 'Disabled',
                    'TRUE' => 'Enabled',

                ],
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Settings'
            ],
            [
                'name' => 'ADJUSTCOST',
                'label' => 'Adjust price we sell with markup*cost in API stockchecks',
                'type' => 'select_from_array',
                'hint' => 'If enabled, the pricing markup over cost (in customer db) is used instead of the sellprice',
                'options' => [
                    'TRUE' => 'Enabled',
                    'FALSE' => 'Disabled'
                ],
                'fake' => true,
                'store_in' => 'xml',
                'tab' => 'Pricing'
            ],
            [
                'name' => 'USEDATAFEEDCOST',
                'label' => 'Use Datafeed price for sell price in API stockchecks',
                'type' => 'select_from_array',
                'hint' => 'Overrides adjusted cost',
                'options' => [
                    'TRUE' => 'Enabled',
                    'FALSE' => 'Disabled'
                ],
                'fake' => true,
                'store_in' => 'xml',
                'tab' => 'Pricing'
            ],
            [
                'name' => 'USEPARTSHARECOST',
                'label' => 'Sellprice as markup*(itemaltaffiliate partshare) in API stockchecks',
                'hint' => 'Overrides adjusted cost, but does not overrides data feed cost (if enabled)',
                'type' => 'select_from_array',
                'options' => [
                    'TRUE' => 'Enabled',
                    'FALSE' => 'Disabled'
                ],
                'fake' => true,
                'store_in' => 'xml',
                'tab' => 'Pricing'
            ],
            [
                'name' => 'AGREEMENTSIGNATURE',
                'label' => 'Signature',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'tab' => 'Agreement'
            ],
            [
                'name' => 'AGREEMENTSIGNATURE',
                'label' => 'Signature',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'tab' => 'Agreement'
            ],

            [
                'name' => 'AGREEMENTCOMPANYNAME',
                'label' => 'Company Name',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'tab' => 'Agreement'
            ],
            [
                'name' => 'AGREEMENTDATE',
                'label' => 'Date',
                'type' => 'date',

                'fake' => true,
                'store_in' => 'xml',
                'tab' => 'Agreement'
            ],
            [
                'name' => 'AGREEMENTTITLE',
                'label' => 'Title',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'tab' => 'Agreement'
            ],
            [
                'name' => 'AGREEMENTDDESCRIBEDITEMS',
                'label' => 'Described items',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'tab' => 'Agreement'
            ],
            [
                'name' => 'AGREEMENTDDESCRIBEDEXEMPTION',
                'label' => 'Describe Exemption',
                'type' => 'text',

                'fake' => true,
                'store_in' => 'xml',
                'tab' => 'Agreement'
            ],
            [
                'name' => 'AGREEMENTFILE',
                'label' => 'Signature',
                'type' => 'upload',

                'fake' => true,
                'store_in' => 'xml',
                'tab' => 'Agreement'
            ],
            [
                'name' => 'BRANCHLEVELPRICING',
                'label' => 'Branch Level Pricing',
                'type' => 'select_from_array',
                'hint' => 'Enable or disable branching.',
                'options' => ['TRUE' => 'TRUE', 'FALSE' => 'FALSE'],
                'default' => 'FALSE',
                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'SparkLink'
            ],
            [
                'name' => 'RTSC',
                'label' => 'Real Time Stock Check',
                'type' => 'select_from_array',
                'hint' => 'Enables or disabled RTSC. Currently affects only Sparklink. If Disabled local warehouse quantity and costs are used.',
                'options' => ['TRUE' => 'TRUE', 'FALSE' => 'FALSE'],
                'default' => 'TRUE',
                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Order Settings'
            ],
            [
                'name' => 'PRIORITYCONTACTPK',
                'label' => 'Enforce branch/supplier from order payload',
                'type' => 'select_from_array',
                'hint' => 'Enabled for buyers that are aware of our branches/shipnodes. For Sparklink  branch 1 means no enforcement.',
                'options' => ['FALSE' => 'FALSE','TRUE' => 'TRUE'],
                'default' => 'FALSE',
                'fake' => true,
                'store_in' => 'xml',
                'wrapper' => [
                    'class' => 'form-group col-md-3',
                ],
                'tab' => 'Order Settings'
            ],
            [
                'name'=>'webshop',
                'type' => "relationship",
                'label' => 'Webshop',
                'entity' => 'webshop',
                'attribute' => 'name',

                'tab' => 'Webshop',
                'subfields'   => [
                    [
                        'name' => 'name',
                        'label' => 'Name',
                        'type' => 'text'
                    ],
                    [
                        'name' => 'description',
                        'label' => 'Description',
                        'type' => 'textarea'
                    ],
                    [
                        'name' => 'active',
                        'label' => 'Active',
                        'type' => 'checkbox'
                    ],
                    ]

            ]
        ];
    }
    public function activateb2b($id)
    {

        $customer = $this->crud->getCurrentEntry();
        $this->customerTopBar($customer);

        return view('admin.fbs.customer.activateb2b', [
            'customer' => $customer,


        ]);
    }
    public function apikeys()
    {

        $customer = $this->crud->getCurrentEntry();
        $this->customerTopBar($customer);
        $apikeys = ApiKeys::where('custpk', $this->crud->getCurrentEntryId())->get();
        return view('admin.fbs.customer.apikeys', [
            'customer' => $this->crud->getCurrentEntry(),
            'apikeys'=>$apikeys
        ]);
    }
    public function createapikey()
    {
        $customer = $this->crud->getCurrentEntry();
        $uuid = Str::uuid();
        $username = Str::uuid();
        $password = Str::uuid();
        $token = base64_encode($username . ':' . $password);
        APIKeys::create(
            [
                'custpk' => $customer->pk,
                'storepk' => $customer->storepk,
                'custtype' => $customer->custtype,
                'uuid' => $uuid,
                'username' => $username,
                'password' => $password,
                'token' => $token,
                'allow_update_from_invoice' =>1,
                'allow_create_from_invoice' =>1,
                'active' => 1,
            ]
        );
        $customer->xml['SOPHIOORDERSAPI'] = 'TRUE';
        $customer->save();
        return back();
    }
}
