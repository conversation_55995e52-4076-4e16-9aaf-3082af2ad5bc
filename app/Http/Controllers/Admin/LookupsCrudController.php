<?php

namespace App\Http\Controllers\Admin;

use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Sophio\Common\Models\FBS\Lookups;

class LookupsCrudController extends CrudController
{

    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;

    public function setup()
    {

        parent::setup();
        CRUD::setModel(Lookups::class);

        CRUD::setRoute(config('backpack.base.route_prefix') . '/lookups');
        CRUD::setEntityNameStrings('lookups', 'Lookups');

    }

    protected function setupListOperation()
    {
        $types = Lookups::groupBy('type')->select('type')->get();
        $this->crud->addColumns([
            'type',
           ['type'=>'text','name'=> 'cdata',
               'searchLogic'=> function ($query, $column, $searchTerm) {
                   $query->orWhere('cdata', 'like', '%' . $searchTerm . '%');
               }
           ],
            ['type'=>'text','name'=>   'cdata1',
                'searchLogic'=> function ($query, $column, $searchTerm) {
                    $query->orWhere('cdata1', 'like', '%' . $searchTerm . '%');
                }
            ],
            'cdata2',
            'idata',
            'storepk',
            'cdata3',

        ]);
        $this->crud->addFilter([
            'name' => 'type',
            'type' => 'select2',
            'label' => 'Type'
        ], function () use ($types) {
            $t = [];
            foreach ($types as $type) {
                $t[$type->type] = $type->type;
            }
            return $t;
        }, function ($value) { // if the filter is active
            $this->crud->addClause('where', 'type', $value);
        });
    }

    protected function setupCreateOperation()
    {
        $types = Lookups::groupBy('type')->select('type')->get();
        $t = [];
        foreach ($types as $type) {
            $t[$type->type] = $type->type;
        }
        $this->crud->addFields(
            [[   // select_from_array
                'name' => 'type',
                'label' => "Type",
                'type' => 'select_from_array',
                'options' => $t,
                'allows_null' => false,
                'default' => 'one',
                // 'allows_multiple' => true, // OPTIONAL; needs you to cast this to array in your model;
            ],
                ['name' => 'cdata'],
                ['name' => 'cdata1'],
                ['name' => 'cdata2'],
                ['name' => 'idata'],
                ['name' => 'storepk'],
                ['name' => 'cdata3'],
                ]
        );
    }

    protected function setupUpdateOperation()
    {
        $this->setupCreateOperation();
    }
}