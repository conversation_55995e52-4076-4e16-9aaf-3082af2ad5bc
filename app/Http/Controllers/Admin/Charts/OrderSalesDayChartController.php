<?php

namespace App\Http\Controllers\Admin\Charts;

use App\Models\Orders\Order;
use Backpack\CRUD\app\Http\Controllers\ChartController;
use ConsoleTVs\Charts\Classes\Chartjs\Chart;
use Illuminate\Support\Facades\DB;

class OrderSalesDayChartController extends ChartController
{
    public function setup()
    {
        $database = \Route::current()->parameter('database');
        $this->chart = new Chart();
        // MANDATORY. Set the labels for the dataset points
        $labels = [];

        $d = array();
        for($i = 31; $i >= 0; $i--)
            $labels[] = date("l d M", strtotime('-'. $i .' days'));

        $this->chart->labels($labels);

        // RECOMMENDED.
        // Set URL that the ChartJS library should call, to get its data using AJAX.
        $this->chart->load(backpack_url('charts/' . $database . '/ordersalesday'));

        // OPTIONAL.
        $this->chart->minimalist(false);
        $this->chart->displayLegend(true);


    }


    /**
     * Respond to AJAX calls with all the chart data points.
     *
     * @return json
     */
    public function data()
    {
        $d = now('CDT')->toArray();


        $i =0;
        $database = \Route::current()->parameter('database');
        for ($month = 30; $month >=-1;$month--) {
            // Could also be an array_push if using an array rather than a collection.

            $dbs = \DB::select("select sum(qty*lineitems.unitPrice)  as sales from $database.orders join $database.lineitems on ".
                "lineitems.orderId=orders.orderId"
                ." where invstatus='F' AND dateCreated>= '".date("Y-m-d", strtotime('-'. ($month+1) .' days'))."'"
                 ." AND dateCreated <'".date("Y-m-d", strtotime('-'. ($month) .' days'))."'");


            $orders[] = $dbs[0]->sales;
            $i++;
        }
        $this->chart->dataset('Daily', 'line', $orders)
            ->color('rgb(0,0,0)')
            ->backgroundColor('rgba(255, 255, 255, 0.4)');

    }
}