<?php

namespace App\Http\Controllers\Admin\Charts;

use App\Models\Orders\Order;
use Backpack\CRUD\app\Http\Controllers\ChartController;
use ConsoleTVs\Charts\Classes\Chartjs\Chart;
use Illuminate\Support\Facades\DB;

class OrderSalesYearlyDayChartController extends ChartController
{
    public function setup()
    {
        $database = \Route::current()->parameter('database');
        $this->chart = new Chart();
        // MANDATORY. Set the labels for the dataset points
        $labels = [];

        $d = array();
        for($i = 365; $i >= 0; $i--)
            $labels[] = date("l d M", strtotime('-'. $i .' days'));

        $this->chart->labels($labels);

        // RECOMMENDED.
        // Set URL that the ChartJS library should call, to get its data using AJAX.
        $this->chart->load(backpack_url('charts/' . $database . '/ordersalesyear'));

        // OPTIONAL.
        $this->chart->minimalist(false);
        $this->chart->displayLegend(true);


    }


    /**
     * Respond to AJAX calls with all the chart data points.
     *
     * @return json
     */
    public function data()
    {
        $d = now('CDT')->toArray();


        $i =0;
        $database = \Route::current()->parameter('database');
        $days = 366;
        $db = \DB::select("select sum(qty*lineitems.unitPrice)  as sales from $database.orders join $database.lineitems on ".
            "lineitems.orderId=orders.orderId"
            ." where invstatus='F' AND dateCreated>= '".date("Y-m-d", strtotime('-'. ($days) .' days'))."' group by date(dateCreated) order by dateCreated ASC"
             );
        foreach($db as $d) {
            $orders[] = $d->sales;
        }

        $this->chart->dataset('Daily', 'line', $orders)
            ->color('rgb(0,0,0)')
            ->backgroundColor('rgba(255, 255, 255, 0.4)');

    }
}