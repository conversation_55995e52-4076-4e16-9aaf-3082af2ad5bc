<?php

namespace App\Http\Controllers\Admin\Walmart;

use App\Library\Sophio\Jobs\FBS\AutozoneStoreUpdateJob;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Carbon\Carbon;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Storage;
use Sophio\Walmart\Library\RemitService;
use Sophio\Walmart\Models\DSV;
use Sophio\Walmart\Models\Remit;
use Sophio\Walmart\Models\RemitFile;

class RemitCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;

    public function setup()
    {
        CRUD::setModel(Remit::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/walmart/remit');
        CRUD::setEntityNameStrings('Remit', 'Remit');
        $this->crud->query->join(config('tenant_db') . '.wws_invoice', function ($q) {
            $q->on('wws_invoice.ponumber', '=', 'remit.ponumber');
        });
        $this->crud->query->orderBy('shipped_date', 'desc');
    }

    protected function setupListOperation()
    {
        CRUD::enableExportButtons();
        $this->crud->addFilter([
            'type' => 'text',
            'name' => 'ponumber',
            'label' => 'PoNumber'
        ],
            false,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'ponumber', '=', "$value");
            }
        );
        $this->crud->addFilter([
            'type' => 'date_range',
            'name' => 'shipped_date',
            'label' => 'Shipped Date'
        ],
            false,
            function ($value) { // if the filter is active, apply these constraints
                $dates = json_decode($value);
                $this->crud->addClause('where', 'shipped_date', '>=', Carbon::parse($dates->from));
                $this->crud->addClause('where', 'shipped_date', '<=', Carbon::parse($dates->to));
            });
        $this->crud->addFilter(
            [
                'type' => 'select2',
                'name' => 'amount',
                'label' => 'Amount comparison'],
            ['eq' => 'Amount match', 'gr' => 'Their greater', 'ls' => 'Their smaller'],
            function ($value) {
                switch ($value) {
                    case 'eq':
                        $this->crud->query->whereRaw('abs(amount-wws_invoice.invtotal)<0.01');
                        break;
                    case 'gr':
                        $this->crud->query->whereRaw('amount-wws_invoice.invtotal>=0.01');
                        break;
                    case 'ls':
                        $this->crud->query->whereRaw('amount-wws_invoice.invtotal<=-0.01');
                        break;
                }
            }

        );
        CRUD::addColumns([
            [
                'name' => 'ponumber',
                'label' => 'PO Number',
                'type' => 'text',
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $query->orWhere('ponumber', $searchTerm);
                }
            ], 'shipnode', 'shipped_date',
            'amount',
            [
                'name' => 'invoice.invtotal',
                'label' => 'Inv total'
            ],
            [
                'name' => 'invoice.pk',
                'wrapper' => [
                    'href' => function ($crud, $column, $entry, $related_key) {
                        return sophio_route('fbs/fbsorder.main', ['id' => $entry->invoice->pk]);
                    },
                ],
            ]
        ]);
    }

    public function setupOtherRoutes($segment, $routeName, $controller)
    {
        Route::any($segment . '/upload', [
            'as' => $routeName . '.upload',
            'uses' => $controller . '@upload',
            'operation' => 'upload',
        ]);
        Route::any($segment . '/report', [
            'as' => $routeName . '.report',
            'uses' => $controller . '@report',
            'operation' => 'report',
        ]);
    }

    public function upload()
    {
        $files = collect(Storage::disk('fbs')->files('walmart/remit'))->sortByDesc(function ($file) {
            return Storage::disk('fbs')->lastModified($file);
        });
        if (request()->post()) {
            $remote_filename = request()->file('file')->getClientOriginalName();
            Storage::disk('fbs')->putFileAs('walmart/remit/', request()->file('file'), $remote_filename);
            /*request()->get('format_input_type')*/
            $rs = new RemitService();
            $cnt = $rs->upload('walmart/remit/' . $remote_filename);
            $report = $rs->createReportByShippedDate($rs->shipped_date);
            if (RemitFile::where('shipped_date', $rs->shipped_date)->count() > 0) {
                RemitFile::where('shipped_date', $rs->shipped_date)->update([
                    'shipped_date' => $rs->shipped_date,
                    'total_amount' => $report['walmart_paid_us'],
                    'total_invoiced' => $report['sophio_invoiced_walmart'],
                    'total_delta' => $report['delta'],
                    'remit_file' => $remote_filename,
                    'user_id' => backpack_user()->id,
                    'remit_report' => 'walmart-remit-exceptions_' . \Illuminate\Support\Carbon::parse($rs->shipped_date)->toDateString() . '.csv'
                ]);
            } else {
                RemitFile::create([
                    'shipped_date' => $rs->shipped_date,
                    'total_amount' => $report['walmart_paid_us'],
                    'total_invoiced' => $report['sophio_invoiced_walmart'],
                    'total_delta' => $report['delta'],
                    'remit_file' => $remote_filename,
                    'user_id' => backpack_user()->id,
                    'remit_report' => 'walmart-remit-exceptions_' . \Illuminate\Support\Carbon::parse($rs->shipped_date)->toDateString() . '.csv'
                ]);
            }

            return view('admin.walmart.uploadremit', ['files' => $files, 'uploaded' => true, 'cnt' => $cnt, 'report' => $report,'shipped_date'=>$rs->shipped_date]);
        }
        return view('admin.walmart.uploadremit', ['files' => $files, 'uploaded' => []]);
    }

    public function report()
    {
        $files = RemitFile::orderBy('created_at', 'desc')->get();
        return view('admin.walmart.remitreport', ['files' => $files]);

    }
}