<?php

namespace App\Http\Controllers\Admin\Catalog;

use App\Http\Controllers\Admin\BaseCatalogCrudController;
use App\Library\Sophio\Traits\Catalog\ParttypeMoveOperation;
use App\Models\NeedsContent;
use App\Models\Wwsitem;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Str;
use Prologue\Alerts\Facades\Alert;
use Sophio\Common\Models\Catalog\LocalBrands;
use Sophio\Common\Models\Partshare\FlattenedData;
use Sophio\FBSOrder\Library\Rules\Supplier\ActiveSuppliersNetworkList;

class NeedsContentCrudController extends BaseCatalogCrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use ParttypeMoveOperation;

    public function setup()

    {
        ini_set('max_execution_time', '300');
        CRUD::setModel(NeedsContent::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/needscontent');
        CRUD::setEntityNameStrings('Part', 'Parts');
        CRUD::addBaseClause('where', 'resolved', '=', '0');
        $this->crud->query->with('partshare')->with('brand')->with('partTerminology');
        $this->crud->query->where('contactpk', '=', 201366);
        $this->objectClass = "NeedsContent";
        if (!$this->crud->getRequest()->has('order')) {
            $this->crud->orderBy('description', 'asc');
        }
        if (!$this->crud->getRequest()->has('hasaaiabrandid')) {
            CRUD::addClause('where', 'aaiabrandid', '<>', '');
            CRUD::addClause('whereNotNull', 'aaiabrandid');
        }
    }

    public function setupOtherRoutes($segment, $routeName, $controller)
    {
        Route::any($segment . '/rebuild', [
            'as' => $routeName . '.rebuild',
            'uses' => $controller . '@rebuild',
            'operation' => 'rebuild',
        ]);
        Route::any($segment . '/report', [
            'as' => $routeName . '.report',
            'uses' => $controller . '@report',
            'operation' => 'report',
        ]);
        Route::any($segment . '/{id}/resolve', [
            'as' => $routeName . '.resolve',
            'uses' => $controller . '@resolve',
            'operation' => 'resolve',
        ]);

    }

    public function report()
    {
        $when = request()->get('when') ?? 'MTD';
        $userid = request()->get('user_id') ?? '';
        if (backpack_user()->hasRole('super-admin')) {
            $ncs = NeedsContent::with('user')->selectRaw("user_id, DATE_FORMAT(needs_content.updated_at,'%y-%m-%d') AS day ,COUNT(*)  as cnt")->where('user_id','>',0)->groupByRaw("user_id,DATE_FORMAT(needs_content.updated_at,'%y-%m-%d')")->orderByRaw("user_id,DATE_FORMAT(needs_content.updated_at,'%y-%m-%d') desc");
            if($userid>0){
                $ncs->where('user_id',$userid);
            }
            $ncs->filterDate(['updated_at',$when]);
            $ncs = $ncs->get();
        }else{
            $ncs = NeedsContent::with('user')->selectRaw("user_id, DATE_FORMAT(needs_content.updated_at,'%y-%m-%d') AS day ,COUNT(*)  as cnt")->where('user_id',auth()->user()->id)->groupByRaw("user_id,DATE_FORMAT(needs_content.updated_at,'%y-%m-%d')")->orderByRaw("user_id,DATE_FORMAT(needs_content.updated_at,'%y-%m-%d') desc");
            $ncs->filterDate(['updated_at',$when]);
            $ncs = $ncs->get();
        }
        return view('admin.catalog.reports.needscontent',['data'=>$ncs]);
    }

    public function rebuild()
    {
        DB::statement('call needs_content_update() ');
        Alert::add('success', 'Table refreshed!')->flash();
        return back();
    }

    public function resolve()
    {
        $nc = $this->crud->getEntry($this->crud->getCurrentEntryId());
        $nc->resolved = 1;
        $nc->user_id = auth()->id();
        $nc->save();
        Alert::add('success', 'Part marked as solved!')->flash();
        return back();
    }


    protected function setupListOperation()
    {
        ini_set('max_execution_time', '300');
        $mfr = NeedsContent::where('resolved',0)->where('contactpk', '=', 201366)->groupBy('mfr')->pluck('mfr','mfr')->toArray();
        $part_desc = NeedsContent::where('resolved',0)->where('contactpk', '=', 201366)->groupBy('part_desc')->havingRaw('count(*)>=5')->selectRaw("part_desc,concat(part_desc,' (',count(*),')') as pt_desc")->pluck('pt_desc','part_desc')->toArray();
        CRUD::enableExportButtons();
        CRUD::setOperationSetting('lineButtonsAsDropdown', false);
        CRUD::button('rebuild')->stack('top')->view('crud::buttons.quick')->meta([
            'wrapper' => [
                'href' => function ($entry) {
                    return backpack_url("needscontent/rebuild");
                },
            ],
        ]);
        /*
        CRUD::button('resolve')->stack('line')->view('crud::buttons.quick')->meta([
            'wrapper' => [
                'href' => function ($entry) {
                    return sophio_route('needscontent.resolve', ['id' => $entry->id]);
                },
            ],
        ]);
        */
       // CRUD::disableResponsiveTable();
        CRUD::enableDetailsRow();
        CRUD::setDetailsRowView('admin.catalog.needscontent_details_row');
        CRUD::addColumns([
            [
                'name' => 'aaiabrandid','label'=>'Brand',
                'escaped' => false, 'limit' => 10000,

                'value'=>function($entry){
            if($entry->brand) {
                return '<a href="'.sophio_route('catalog/brands.show',['id'=>$entry->brand->id]).'"  data-bs-toggle="tooltip" title="'.$entry->brand->mfg_name.'">'. $entry->aaiabrandid.'</a>';
            }else{
                return $entry->aaiabrandid;
            }

                }
            ],


            [
                'name' => 'part_number','label'=>'Part Number',
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $query->orWhere('part_number_unformatted', 'like', '%' . unformatString($searchTerm) . '%');
                }
            ],
            [
                'name' => 'onsite',
                'escaped' => false, 'limit' => 10000,
                'value' => function ($entry) {
                    if ($entry->BrandID!=="") {
                        return '<a target="_blank" href="' . sophiosettings()->getStore()->VIRTUAL . 'itemdetail/' . $entry->BrandID. '/' . Str::slug($entry->part_number_unformatted) . '">Yes</a>';
                    } else {
                        return 'No';
                    }
                },

            ],
            [

                'name' => 'description_________________',

                'type' => 'text',
                'limit' => 1000,
                'style' => 'white-space: wrap !important;',
                'wrapper' => [
                    'element' => 'span',
                    'style' => 'white-space: wrap !important;max-width:300px',
                ],
                'orderable' => true,
                'escaped'=>false,
                'value'=>function($entry){

                  return html_entity_decode($entry->description);
                },
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $query->orWhere('description', 'like', '%' . $searchTerm . '%');
                }
            ],
            [

                'name' => 'part_desc',
                'limit' => 1000,

                'orderable' => true,
                'escaped'=>false,

                'searchLogic' => function ($query, $column, $searchTerm) {
                    $query->orWhere('part_desc', 'like', '%' . $searchTerm . '%');
                }
            ],
            [
                'name' => 'mfr',
                'limit' => 1000,
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $query->orWhere('mfr', 'like', '%' . $searchTerm . '%');
                }
            ],
            [
                'name' => 'pt_num',
                'limit' => 1000,
                'escaped' => false,
                'style' => 'white-space: wrap !important;', 'escaped' => false,
                'wrapper' => [
                    'element' => 'span',
                    'style' => 'white-space: wrap !important;max-width:50px',


                ],
                'value' => function ($entry) {
                    if ($entry->pt_num == 0) {
                        return 'Not set';
                    }
                    if ($entry->pt_num > 0) {

                        if ($entry->partTerminology) {
                            return $entry->partTerminology->PartTerminologyID . ':' . $entry->partTerminology->PartTerminologyName;
                            if($entry->product && $entry->product->parttypedcf ){
                                if(  $entry->product->parttypedcf->active===1 ){
                                    return $entry->partTerminology->CategoryName . '/<br> ' . $entry->partTerminology->SubCategoryName . ' /<br> ' . $entry->partTerminology->PartTerminologyName;
                                } else{
                                    return $entry->partTerminology->CategoryName . '/<br> ' . $entry->partTerminology->SubCategoryName . ' /<br> ' . $entry->partTerminology->PartTerminologyName.
                                        '<a href="'.sophio_route('catalog/parttypedcf.edit',['id'=>$entry->product->parttypedcf->id]).'">(Not Active)</a>';
                                }
                            }else{
                                return $entry->partTerminology->CategoryName . '/<br> ' . $entry->partTerminology->SubCategoryName . ' /<br> ' . $entry->partTerminology->PartTerminologyName.
                                    '<a href="'.sophio_route('catalog/parttypedcf.create',['mfg_code'=>$entry->aaiabrandid]).'">(Add)</a>';
                            }


                        }
                    }
                    return $entry->pt_num ?? 'Not Set';
                }
            ],
            [
                'name' => 'partshare',
                'type' => 'relationship',

                'model' => FlattenedData::class,
                'value' => function ($entry) {
                    return $entry->partshare->PID ?? null;
                },
                'wrapper' => [
                    'target' => '_blank',
                    'href' => function ($crud, $column, $entry, $related_key) {
                        if ($entry->PID !== null && $entry->partshare) {
                            return sophio_route('flattenpartshare.show', ['id' => $entry->partshare->PID]);
                        }

                    },
                ]
            ],
            [
                'name' => 'product',
                'attribute' => 'id',
                'value' => function ($entry) {
                    if ($entry->product)
                        return $entry->product->id ?? null;
                    return null;
                },
                'entity' => 'product',
                'wrapper' => [
                    'target' => '_blank',
                    'href' => function ($crud, $column, $entry, $related_key) {
                        if ($entry->product && $entry->product->id !== null) {
                            return sophio_route('pim/product.edit', ['id' => $entry->product->id]);
                        }

                    },
                ]
            ],

            [
                'name' => 'images',
                'type' => 'text', 'escaped' => false, 'limit' => 10000,
                'entity' => 'images',
                'value' => function ($entry) {
                    $r = "";
                    foreach ($entry->images as $image) {
                        if ($image->width > 0) {
                            $r .= '<a href="' . getImagePath($image->original_path) . '" data-lightbox="' . $entry->part_number_unformatted . '"><img src="' . getImagePath($image->original_path) . '" width="50" height="50"></a>';
                            break;
                        }

                    }
                    return $r;
                }

            ],
            [
                'name' => 'bullets', 'limit' => 1000,
                'style' => 'white-space: wrap !important;',
                'value' => function ($entry) {
                    return $entry->bullets !== "" && $entry->bullets != null && $entry->bullets !== "[]" ? "yes" : 'no';
                },
                'wrapper' => [
                    'element' => 'span',
                    'style' => 'white-space: wrap !important;max-width:100px',
                ],
            ],
            [
                'name' => 'identifier',
            ],
            [
                'name' => 'linecode',
            ],
            /*
            [
                'name' => 'sales',
            ],
            [
                'name' => 'stock',
            ],
*/
            [
                'name' => 'user.id','label'=>'user'
            ],
            [
                'name' => 'updated_at',
                'type' => 'text',
                'value'=>function($entry) {
                return $entry->updated_at->format('m-d-Y');
                }
            ],
        ]);
        CRUD::filter('hasaaiabrandid')
            ->type('dropdown')
            ->values(['0' => 'No', '1' => 'Yes'])
            ->label('HAS Brand ID')
            ->whenActive(function ($value) {
                if ($value == "1") {
                    CRUD::addClause('Where', 'aaiabrandid', '<>', '');
                }else{
                    CRUD::addClause('whereNull', 'aaiabrandid');
                    CRUD::addClause('orWhere', 'aaiabrandid', '=', '');

                }
            });
        CRUD::filter('aaiabrandid')
            ->type('text')
            ->label('Brand ID')
            ->whenActive(function ($value) {

                CRUD::addClause('where', 'aaiabrandid', '=', $value);
            });
        CRUD::filter('mfr')
            ->type('select2')
            ->options($mfr)
            ->label('Brand Name')
            ->whenActive(function ($value) {
                CRUD::addClause('where', 'mfr', 'LIKE', '%' . $value . '%');
            });
        CRUD::filter('part_desc')
            ->type('select2')
            ->options($part_desc)
            ->label('Part Desc')
            ->whenActive(function ($value) {
                CRUD::addClause('where', 'part_desc', 'LIKE', '%' . $value . '%');
            });
        CRUD::filter('linecode')
            ->type('text')
            ->label('Linecode
            ')
            ->whenActive(function ($value) {
                CRUD::addClause('where', 'linecode', '=', $value);
            });
        CRUD::filter('part_number_unformatted')
            ->type('text')
            ->label('Part Number')
            ->whenActive(function ($value) {
                CRUD::addClause('where', 'part_number_unformatted', unformatString($value));
            });
        CRUD::filter('partshare')
            ->type('dropdown')
            ->values(['0' => 'No', '1' => 'Yes'])
            ->label('IN Partshare')
            ->whenActive(function ($value) {
                if ($value == "1") {
                    CRUD::addClause('whereNotNull', 'PID');
                    $this->crud->query->whereNotNull('PID')->where('PID', '<',100000000);
                } elseif($value == "0") {
                    $this->crud->query->where(
                        function ($query) {
                            $query->whereNull('PID')->orWhere('PID', '>=',100000000);
                        }
                    );
                }
            });


        CRUD::filter('has_image')
            ->type('dropdown')
            ->values(['0' => 'No', '1' => 'Yes'])
            ->label('Has Image')
            ->whenActive(function ($value) {
                CRUD::addClause('where', 'has_image', '=', $value);
            });
        CRUD::filter('identifier')
            ->type('dropdown')
            ->values(['O' => 'No', '1' => 'Yes'])
            ->label('Has UPC/GTIN')
            ->whenActive(function ($value) {
                if ($value == "1") {
                    CRUD::addClause('whereNotNull', 'identifier');
                    CRUD::addClause('where', 'identifier', '<>', '');
                } else {
                    $this->crud->query->whereNull('identifier')->orWhere('identifier', '=', '');
                }

            });
        CRUD::filter('pt_num')
            ->type('dropdown')
            ->values(['0' => 'No', '1' => 'Yes'])
            ->label('With Part Type')
            ->whenActive(function ($value) {
                if ($value == "1") {
                    CRUD::addClause('where', 'pt_num', '>', 0);
                } else {
                    CRUD::addClause('where', 'pt_num', '=', 0);
                }

            });
        CRUD::filter('stock')
            ->type('dropdown')
            ->values(['0' => 'No', '1' => 'Yes'])
            ->label('Has Stock')
            ->whenActive(function ($value) {
                if ($value == "1") {
                    CRUD::addClause('where', 'stock', '>', 0);
                } else {
                    CRUD::addClause('where', 'stock', '=', 0);
                }
            });
        CRUD::filter('sales')
            ->type('dropdown')
            ->values(['0' => 'No', '1' => 'Yes'])
            ->label('Has Sales')
            ->whenActive(function ($value) {
                if ($value == "1") {
                    CRUD::addClause('where', 'sales', '>', 0);
                } else {
                    CRUD::addClause('where', 'sales', '=', 0);
                }
            });

        $this->crud->addFilter([
            'name' => 'contactpk',
            'type' => 'dropdown',
            'label' => 'Supplier',
        ],
            function () {
                $invs = (new ActiveSuppliersNetworkList)()->get();
                $return = [];
                foreach ($invs as $in) {
                    $return[$in->contactpk] = $in->getSupplierDisplayName() ?? '';
                }
                return $return;
            }
            , function ($value) {

                CRUD::addClause('where', 'contactpk', '=', $value);
            });
        CRUD::filter('user_id')
            ->type('dropdown')
            ->values(['0' => 'No', '1' => 'Yes'])
            ->label('Edited')
            ->whenActive(function ($value) {
                if($value == "1"){
                    CRUD::addClause('where','user_id','>',0);
                }else{
                    CRUD::addClause('where','user_id','=',0);
                }

            });
    }
}