<?php

namespace App\Http\Controllers\Admin\Catalog;

use App\Http\Controllers\Admin\BaseCatalogCrudController;
use App\Library\Sophio\Catalog\ProductsIndex;
use App\Library\Sophio\Jobs\Catalog\ADNBrandUpdate;
use App\Library\Sophio\PIM\ProductsUtils;
use App\Models\NeedsContent;
use App\Models\Wwsitem;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;
use Sophio\Common\Models\AAIA\BrandMix;
use Sophio\Common\Models\Catalog\AdnExtract;
use Sophio\Common\Models\Catalog\AdnExtractDcf;
use Sophio\Common\Models\Catalog\LocalBrands;
use Sophio\Common\Models\Catalog\VisionDCF;
use Sophio\Common\Models\Partshare\FlattenedData;
use Sophio\Common\Models\PIM\Image;
use Sophio\Common\Models\PIM\Product;

class AdnExtractDcfCrudController
    extends BaseCatalogCrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\Pro\Http\Controllers\Operations\FetchOperation;

    public function setup()
    {
        CRUD::enablePersistentTable();

        CRUD::setModel(AdnExtractDcf::class);
        $this->crud->query->withCount('wwsitem');
        $this->crud->query->withCount('adnextract');
        $this->crud->query->orderBy('wwsitem_count', 'desc');
        CRUD::setRoute(config('backpack.base.route_prefix') . '/adnextractdcf');;

        CRUD::setEntityNameStrings('Adn Extract line', 'AdnExtract DCF');
        AdnExtractDcf::saving(function (AdnExtractDcf $entry) {
            $entry->user_id = auth()->user()->id;
            if ($entry->getOriginal('brandid') !== "") {
                $pp = Product::with('images')->whereHas('wwsitem')->where('aaiabrandid', $entry->getOriginal('brandid'))->get();
                foreach ($pp as $p) {
                    $p->images()->update(['aaiabrandid' => $entry->brandid]);
                }
                Product::whereHas('wwsitem')->where('aaiabrandid', $entry->getOriginal('brandid'))->update(['aaiabrandid' => $entry->brandid, 'mfg_code' => $entry->brandid]);

                AdnExtract::where('aaiabrandid', $entry->getOriginal('brandid'))->update(['aaiabrandid' => $entry->brandid]);
                NeedsContent::whereHas('wwsitem')->where('aaiabrandid', $entry->getOriginal('brandid'))->update(['aaiabrandid' => $entry->brandid]);
            }

        });
        AdnExtractDcf::saved(function (AdnExtractDcf $entry) {

            AdnExtract::where('mfr', $entry->mfr)->update(['aaiabrandid' => $entry->brandid]);

            Wwsitem::where('contactpk', 201366)->where('mfr', $entry->mfr)->update(['BrandID' => $entry->brandid]);
            $bm = BrandMix::where('brand_id', $entry->brandid)->first();
            $bx = LocalBrands::where('BrandID', $entry->brandid)->first();
            if ($bx) {
            } else {
                LocalBrands::create(['BrandID' => $entry->brandid, 'mfg_code' => $entry->brandid, 'mfg_name' => $bm->BrandName, 'active' => 1, 'department' => 2]);
            }
            ADNBrandUpdate::dispatch($entry->brandid, $entry->mfr, config('tenant_db'));

        });

    }

    public function setupOtherRoutes($segment, $routeName, $controller)
    {
        Route::any($segment . '/importtowws', [
            'as' => $routeName . '.importtowws',
            'uses' => $controller . '@importtowws',
            'operation' => 'importtowws',
        ]);

    }

    public function importtowws()
    {
        DB::statement("    INSERT IGNORE INTO wws_items( sku, description, product_name,  mfr,   uom,
                           list,  cost, 
                         notes, category_level1, category_level2, category_level3, 
                            contactpk,image, image2, image3,
                          image4, image5,  BrandID,part_number_unformatted)
    SELECT  sku, descript, descript,  mfr,  uom,
                         list, LIST,
                       smartpage, LEVEL1, LEVEL2, LEVEL3,
                           201366,image1, image2, image3,
                          image4, image5,  aaiabrandid,part_number_unformatted
    FROM nonapps.adnextract where aaiabrandid='" . request()->get('brandid') . "'");
        $bx = LocalBrands::where('BrandID',  request()->get('brandid'))->first();
        $bm = BrandMix::where('brand_id',request()->get('brandid'))->first();
        if ($bx) {
        } else {
            LocalBrands::create(['BrandID' =>  request()->get('brandid'), 'mfg_code' => request()->get('brandid'), 'mfg_name' => $bm->BrandName, 'active' => 1, 'department' => 2]);
        }
        ADNBrandUpdate::dispatch(request()->get('brandid'), "", config('tenant_db'));
        return redirect()->back();
    }

    protected function setupListOperation()
    {
        CRUD::enablePersistentTable();
        CRUD::column([
            'name' => 'mfr',
            'label' => "Mfr",
            'limit' => 120,
            'type' => 'text',
            'wrapper' => [

                'href' => function ($crud, $column, $entry, $related_key) {
                    return sophio_route('adnextractdcf.edit', ['id' => $entry->id]);
                },
            ],
            'searchLogic' => function ($query, $column, $searchTerm) {
                $query->where('mfr', 'like', "%$searchTerm%");
            }
        ]);
        CRUD::column([
            'name' => 'brandid',
            'label' => "Brand ID",
            'limit' => 120,
            'type' => 'text',

        ]);
        CRUD::column([
            'name' => 'brand.BrandName',
            'label' => "BrandName",
            'limit' => 120,
            'type' => 'text',
        ]);

        CRUD::column([
            'name' => 'adnextract_count',
            'label' => "Count in origin",

            'suffix' => ' items',
            'type' => 'text',
            'wrapper' => [

                'href' => function ($crud, $column, $entry, $related_key) {
                    return sophio_route('adnextract.index', ['mfr' => $entry->mfr]);
                },
            ],
        ]);
        CRUD::column([
            'name' => 'wwsitem_count',
            'label' => "Count in working",

            'suffix' => ' items',
            'type' => 'text',
            'wrapper' => [

                'href' => function ($crud, $column, $entry, $related_key) {
                    return sophio_route('wwsitem.index', ['mfr' => $entry->mfr]);
                },
            ],
        ]);

        CRUD::filter('brandid')
            ->type('text')
            ->label('Brand ID')
            ->whenActive(function ($value) {
                CRUD::addClause('where', 'brandid', '=', "$value");
            });
        CRUD::filter('BrandName')
            ->type('text')
            ->label('Brand Name')
            ->whenActive(function ($value) {
                CRUD::addClause('whereHas', 'brand', function ($x) use ($value) {
                    $x->where('BrandName', '=', "$value");
                });
            });
        CRUD::filter('mfr')
            ->type('text')
            ->label('MFR')
            ->whenActive(function ($value) {
                CRUD::addClause('where', 'mfr', 'like', "%$value%");
            });
    }

    public function destroy($id)
    {
        $a = $this->crud->getEntry($id);
        if ($a->brandid !== "" && $a->brandid !== null) {
            try {
                FlattenedData::where('AAIABrandID', $a->brandid)->where('PID', '>', 100000000)->delete();
                DB::statement("DELETE FROM partshare.FlattenedSales WHERE AAIABrandID =" . $a->brandid . " AND PID >100000000");
            } catch (\Throwable $e) {
                \Log::error('error when delete from adnextract:' . $e->getMessage());
            }
        }
        Wwsitem::where('mfr', $this->crud->getEntry($id)->mfr)->delete();
        return true;

    }

    protected function setupUpdateOperation()
    {

        app('translator')->addLines(['crud.delete_confirm' => 'Please note this will remove all items for this manufacturer in the working table only. '], 'en', 'backpack');
        $this->crud->setOperationSetting('showDeleteButton', true);
        $entry = $this->crud->getEntry($this->crud->getCurrentEntryId());
        $this->setupCreateOperation();
        /*
        CRUD::field([   // CustomHTML
            'name' => 'html14',
            'tab'=>'WWS Items with part type ',
            'type' => 'custom_html_callback',
            "value" => function () use ($entry) {
                if($entry->brandid!=="") {

                    $adn =
                        Wwsitem::join('pnci_pim.products', function($q){
                            $q->on('products.part_number_unformatted','=','wws_items.part_number_unformatted');
                            $q->on('products.aaiabrandid','=','wws_items.BrandID');
                            $q->on('products.aaia_parttype_id','=','wws_items.pt_num');
                        })->where('aaia_parttype_id','>',0)->whereNotNull('pt_num')->where('BrandID',$entry->brandid)
                            ->groupBy('aaia_parttype_id')

                            ->join('aces_meta.aces_taxonomy', function($q){
                                $q->on('aaia_parttype_id','=','PartTerminologyID');
                            })
                     ->count();

                    return  $adn.' wws items found with part type: ';
                }else{
                    return 'This manufacturer doesn\'t have a Brand ID assigned. This means we cannot  match yet with Partshare or PIM to discover if the items have  taxonomy information.';
                }
            }]);
        */
        CRUD::field([   // CustomHTML
            'name' => 'html15',
            'tab' => 'WWS Items with part type ',
            'type' => 'custom_html_callback',
            "value" => function () use ($entry) {
                if ($entry->brandid !== "") {
                    $links = [];
                    $adn =
                        Wwsitem::join('pnci_pim.products', function ($q) {
                            $q->on('products.part_number_unformatted', '=', 'wws_items.part_number_unformatted');
                            $q->on('products.aaiabrandid', '=', 'wws_items.BrandID');;
                        })->where('aaia_parttype_id', '>', 0)->where('BrandID', $entry->brandid)
                            ->groupBy('aaia_parttype_id')
                            ->selectRaw('CategoryID,SubCategoryID,pt_num,count(*) as cnt')
                            ->join('aces_meta.aces_taxonomy', function ($q) {
                                $q->on('aaia_parttype_id', '=', 'PartTerminologyID');
                            })
                            ->orderByRaw('CategoryID asc, SubCategoryID asc')->get();

                    foreach ($adn as $a) {
                        if ($a->taxonomy) {

                            $links[] =
                                '<a target="_blank" href="' . sophio_route('wwsitem.index', ['BrandID' => $entry->brandid, 'pt_num' => $a->taxonomy->PartTerminologyID]) . '">' .
                                $a->taxonomy->CategoryID . ' - ' . $a->taxonomy->CategoryName . ' \ ' . $a->taxonomy->SubCategoryID . ' - ' . $a->taxonomy->SubCategoryName . ' \ ' . $a->taxonomy->PartTerminologyID . ' - ' . $a->taxonomy->PartTerminologyName . ' (' . $a->cnt . ')' . '</a>' .
                                '&nbsp;&nbsp;&nbsp;<a href="#">Remove</a>';
                        }

                    }
                    return implode('<br>', $links);;;
                } else {
                    return 'This manufacturer doesn\'t have a Brand ID assigned. This means we cannot  match yet with Partshare or PIM to discover if the items have  taxonomy information.';
                }
            }]);
        CRUD::field([   // CustomHTML
            'name' => 'html0',
            'tab' => 'Edit',
            'type' => 'custom_html_callback',
            "value" => function () use ($entry) {
                $adn = AdnExtract::where('mfr', $entry->mfr)->count();
                return '<a target="_blank" href="' . sophio_route('adnextract.index', ['mfr' => $entry->mfr, 'BrandID' => $entry->brandid]) . '">' . $adn . ' items found with this manufacturer name in origin ADN table</a> '.
                    ' - <a target="_blank" href="' . sophio_route('adnextractdcf.importtowws', [  'brandid' => $entry->brandid]) . '"> import them to wws</a> ';
            }]);
        CRUD::field([   // CustomHTML
            'name' => 'html1',
            'tab' => 'Edit',
            'type' => 'custom_html_callback',
            "value" => function () use ($entry) {
                $adn = Wwsitem::where('mfr', $entry->mfr)->count();
                return '<a target="_blank" href="' . sophio_route('wwsitem.index', ['mfr' => $entry->mfr, 'BrandID' => $entry->brandid]) . '">' . $adn . ' items found with this manufacturer name in WWS (working table)</a> ';;
            }]);
        CRUD::field([   // CustomHTML
            'name' => 'hint1',
            'tab' => 'Edit',
            'type' => 'custom_html ',
            "value" => "After saving, changes on website may appear delayed. This is because system has to discovery and update new items that are not already in PIM."
        ]);
        CRUD::field([   // CustomHTML
            'name' => 'html2',
            'tab' => 'Edit',
            'type' => 'custom_html_callback',
            "value" => function () use ($entry) {
                if ($entry->brandid !== "") {
                    $adn = FlattenedData::where('AAIABrandID', $entry->brandid)->count();
                    return '<a target="_blank" href="' . sophio_route('flattenpartshare.index', ['AAIABrandID' => $entry->brandid]) . '">' . $adn . ' items found in Partshare</a> ';;
                } else {
                    return '';
                }
            }]);
        CRUD::field([   // CustomHTML
            'name' => 'html3',
            'type' => 'custom_html_callback',
            'tab' => 'Partshare Not in Wwws Items',
            "value" => function () use ($entry) {
                if ($entry->brandid !== "") {
                    $adn = FlattenedData::whereDoesntHave('wwsitem')->where('AAIABrandID', $entry->brandid)->count();
                    return '<a target="_blank" href="' . sophio_route('flattenpartshare.index', ['AAIABrandID' => $entry->brandid, 'withWssItem' => 0]) . '">' . $adn . ' items found in Partshare but not in wws items</a> ';
                } else {
                    return '';
                }
            }]);
        /*
        CRUD::field([   // CustomHTML
            'name' => 'html5',
            'tab'=>'Partshare ',
            'type' => 'custom_html_callback',
            "value" => function () use ($entry) {
                if($entry->brandid!=="") {
                    $links=['Partshare Parttypes:'];
                    $adn = FlattenedData::where('AAIABrandID', $entry->brandid)->where('PartTypeID','>',0)->groupBy('PartTypeID')
                        ->selectRaw('CategoryID,SubCategoryID,PartTypeID,count(*) as cnt')
                        ->join('aces_meta.aces_taxonomy', function($q){
                            $q->on('PartTypeID','=','PartTerminologyID');

                        })
                        ->orderByRaw('CategoryID asc, SubCategoryID asc')->get();

                    foreach ($adn as $a){
                        $links[] =
                            '<a target="_blank" href="'.sophio_route('flattenpartshare.index',['AAIABrandID'=>$entry->brandid,'parttype'=>$a->taxonomy->PartTerminologyID]).'">'.
                            $a->taxonomy->CategoryID.' - '.$a->taxonomy->CategoryName.' \ '.$a->taxonomy->SubCategoryID.' - '.$a->taxonomy->SubCategoryName.' \ '. $a->taxonomy->PartTerminologyID.' - '.$a->taxonomy->PartTerminologyName.' ('.$a->cnt.')'.'</a>'
                        ;
                    }
                    return implode('<br>',$links) ;;;
                }else{
                    return '';
                }
            }]);
        */
        CRUD::field([   // CustomHTML
            'name' => 'html4',
            'tab' => 'Partshare Not in Wwws Items',
            'type' => 'custom_html_callback',
            "value" => function () use ($entry) {
                if ($entry->brandid !== "") {
                    $links = ['Partshare Parttypes:'];
                    $adn = FlattenedData::where('AAIABrandID', $entry->brandid)->whereDoesntHave('wwsitem')->where('PartTypeID', '>', 0)->groupBy('PartTypeID')
                        ->selectRaw('CategoryID,SubCategoryID,PartTypeID,count(*) as cnt')
                        ->join('aces_meta.aces_taxonomy', function ($q) {
                            $q->on('PartTypeID', '=', 'PartTerminologyID');

                        })
                        ->orderByRaw('CategoryID asc, SubCategoryID asc')->get();

                    foreach ($adn as $a) {
                        $links[] =
                            '<a target="_blank" href="' . sophio_route('flattenpartshare.index', ['AAIABrandID' => $entry->brandid, 'PartTypeID' => $a->taxonomy->PartTerminologyID]) . '">' .
                            $a->taxonomy->CategoryID . ' - ' . $a->taxonomy->CategoryName . ' \ ' . $a->taxonomy->SubCategoryID . ' - ' . $a->taxonomy->SubCategoryName . ' \ ' . $a->taxonomy->PartTerminologyID . ' - ' . $a->taxonomy->PartTerminologyName . ' (' . $a->cnt . ')' . '</a>'
                            . '&nbsp;&nbsp;&nbsp;<a href="#">Add to Wwws</a>';
                    }
                    return implode('<br>', $links);;;
                } else {
                    return 'No Brand ID assigned. Please map a brand ID to perform identification with Partshare.';
                }
            }]);
    }

    public function fetchBrand()
    {
        return BrandMix::where('BrandName', 'LIKE', '%' . request()->get('q') . '%')->selectRaw('brand_id,path')->get();
    }

    protected function setupCreateOperation()
    {

        CRUD::field([
            'name' => 'mfr',
            'label' => "Mfr",
            'tab' => 'Edit'

        ]);    /*
        CRUD::field([
            'name' => 'brandid',
            'label' => 'Brand ID',
            'tab'=>'Edit',
            'type' => 'partsharelines_modal',


        ]);
    */
        CRUD::field([
            'name' => 'brandid',
            'label' => "Brand",
            'default' => request()->get('BrandID'),
            'method' => 'POST',
            'tab' => 'Edit',
            'entity' => false,
            'type' => 'ourselect2_from_ajax',
            'foreignkey' => 'brand_id',
            'model' => BrandMix::class,
            'attribute' => "path",
            'data_source' => backpack_url('adnextractdcf/fetch/brand'),
        ]);


    }
}