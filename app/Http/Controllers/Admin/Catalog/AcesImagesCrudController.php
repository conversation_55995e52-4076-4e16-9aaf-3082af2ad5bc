<?php

namespace App\Http\Controllers\Admin\Catalog;

use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Sophio\Common\Models\AAIA\AcesImages;
use Sophio\Common\Models\AAIA\Category;
use Sophio\Common\Models\AAIA\Parttypes;
use Sophio\Common\Models\AAIA\Subcategory;
use Sophio\Common\Models\FBS\Lookups;

class AcesImagesCrudController
    extends CrudController
{

    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    protected $taxonomies=['PartTerminologyID'=>'PartType','SubCategoryID'=>'SubCategory'];
    public function setup()
    {

        parent::setup();
        CRUD::setModel(AcesImages::class);

        CRUD::setRoute(config('backpack.base.route_prefix') . '/aaia/images');
        CRUD::setEntityNameStrings('image', 'images');
        AcesImages::saving(function($image) {
            $image->image_path = str_replace('Image US1','',$image->image_path);
        });

    }
    protected function setupListOperation()
    {
        $this->crud->addColumns([
            'id','taxonomy_type',
            [
                'name'=>'taxonomy_id',
                'type'=>'text',
                'value'=>function($entry) {
                    switch ($entry->taxonomy_type) {
                        case 'PartTerminologyID':
                            return Parttypes::find($entry->taxonomy_id)->PartTerminologyName;
                            break;
                        case 'SubCategoryID':
                            return Subcategory::find($entry->taxonomy_id)->SubCategoryName;
                            break;
                        case 'CategoryID':
                            return Category::find($entry->taxonomy_id)->CategoryName;
                            break;
                    }
                }
            ],
            [
                'name'=>'image_path',
                'type'=>'image',
                'disk'=>'imagespool'
            ]

        ]);
        $this->crud->addFilter([
            'name' => 'taxonomy_type',
            'type' => 'dropdown',
            'label' => 'Type'
        ], $this->taxonomies, function ($value) { // if the filter is active
            $this->crud->addClause('where', 'taxonomy_type', $value);
        });
    }
    protected function setupUpdateOperation()
    {
        $this->crud->addFields(
            [
                [   // select_from_array
                    'name' => 'taxonomy_type',
                    'label' => "Type",
                    'type' => 'select_from_array',
                    'options' => $this->taxonomies,
                    'allows_null' => false,

                ],
                'taxonomy_id',
                ['name'=>'image_path','type'=>'browse'],
                ['name'=>'image_path_preview','type'=>'custom_html','value'=>'<img style="max-width:400px" src="https://images-us1.sophio.com'.$this->crud->getCurrentEntry()->image_path.'">']
            ]
        );
    }

    protected function setupCreateOperation()
    {

        $this->crud->addFields(
            [
                [   // select_from_array
                'name' => 'taxonomy_type',
                'label' => "Type",
                'type' => 'select_from_array',
                'options' => $this->taxonomies,
                'allows_null' => false,

            ],
          'taxonomy_id',
            ['name'=>'image_path','type'=>'browse'],
                ['name'=>'image_path_preview','type'=>'custom_html','value'=>' ']
            ]
        );
    }

}