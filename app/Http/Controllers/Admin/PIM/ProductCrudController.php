<?php

namespace App\Http\Controllers\Admin\PIM;

use App\Library\Sophio\Products;
use App\Library\Sophio\Catalog\CatalogUtils;
use App\Library\Sophio\ImageCrawlResizer;
use App\Library\Sophio\ImageFileName;
use App\Library\Sophio\Jobs\Products\AppsWarehouse;
use App\Library\Sophio\SingleFile;
use App\Library\Sophio\Traits\AuditOperation;
use App\Library\Sophio\Traits\Catalog\ParttypeMove;
use App\Models\NeedsContent;
use App\Models\Wwsitem;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Backpack\CRUD\app\Library\Uploaders\Support\FileNameGenerator;
use Backpack\CRUD\app\Library\Widget;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Sophio\Common\Models\AAIA\Brand;
use Sophio\Common\Models\AAIA\Taxonomy;
use Sophio\Common\Models\B2CCentral\AmazonCompetittivePriceHistory;
use Sophio\Common\Models\PIM\Asin;
use Sophio\Common\Models\PIM\Attribute;
use Sophio\Common\Models\PIM\Image;
use Sophio\Common\Models\PIM\Product;
use Sophio\Common\Models\PIM\Variant;
use Sophio\Common\Models\WHIACES\ProductFeed;
use Intervention\Image\Laravel\Facades\Image as ImageIntervention;

class ProductCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\Pro\Http\Controllers\Operations\FetchOperation;
    use AuditOperation;

    public function setupOtherRoutes($segment, $routeName, $controller)
    {
        Route::any($segment . '/regenerateImage', [
            'as' => $routeName . '.regenerateImage',
            'uses' => $controller . '@regenerateImage',
            'operation' => 'regenerateImage',
        ]);
        Route::any($segment . '/{id}/amazon', [
            'as' => $routeName . '.amazon',
            'uses' => $controller . '@amazon',
            'operation' => 'amazon',
        ]);
        Route::any($segment . '/amazon-exceptions', [
            'as' => $routeName . '.amazon-exceptions',
            'uses' => $controller . '@amazon_exceptions',
            'operation' => 'amazon_exceptions',
        ]);
    }

    public function setup()
    {
        parent::setup();
        CRUD::setModel(\Sophio\Common\Models\PIM\Product::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/pim/product');
        CRUD::setEntityNameStrings('Product', 'Products');
        $this->crud->enableExportButtons();
        Product::saving(function (Product $product) {
            if ($product->width > 0 && $product->height > 0 && $product->length > 0 && $product->weight > 0) {
                $product->has_dimensions = 1;
            } else {
                $product->has_dimensions = 0;
            }
            $product->long_description = json_decode(request()->get('long_description') ?? [], true)['long_description'] ?? null;
            $product->long_description_source = json_decode(request()->get('long_description') ?? [], true)['id'] ?? null;
            if ($product->isDirty('upc')) {
                $product->upc_source = 'OWN';
            }
            if ($product->isDirty('gtin')) {
                $product->gtin_source = 'OWN';
            }
            $bullets = [];
            if (is_array($product->bullets)) {
                foreach ($product->bullets as $bullet) {
                    $bullets[] = $bullet['value'];
                }
                $product->bullets = $bullets;
            }
            $cu  = new CatalogUtils(null);
            if ($product->isDirty('aaia_parttype_id')) {
                $product->aaia_parttype_id_source = 'OWN';
                $ptm = new ParttypeMove();
                $ptm->setPtNum($product->aaia_parttype_id);
                $ptm->moveByPIM($product);

                $cu->clear_web_cache();
            }else{

            }

            $product->product_name = json_decode(request()->get('product_name') ?? [], true)['product_name'] ?? null;
            $product->product_name_source = json_decode(request()->get('product_name') ?? [], true)['id'] ?? null;


        });
        Attribute::saving(function(Attribute $attribute){
            $a = json_decode( $attribute->attributeID,true);
            if(json_last_error()===JSON_ERROR_NONE) {
                $attribute->attributeID = $a['id'];
            }


        });
        Variant::saving(function(Variant $variant){
            $a = json_decode( $variant->attributeID,true);
            if(json_last_error()===JSON_ERROR_NONE) {
                $variant->attributeID = $a['id'];
            }
        });
        Variant::retrieved(function(Variant $variant){
            if( preg_match('/^[0-9]+$/', $variant->attributeID)===1) {

                $a = DB::select("select * from  aaia_pcadb.PartAttributes where PAID=".$variant->attributeID ." limit 1");
                $variant->attributeID =json_encode(['id'=>$variant->attributeID ,'display'=>$a[0]->PAName]);

            }else{
                $variant->attributeID =json_encode(['id'=>$variant->attributeID,'display'=>$variant->attributeID ]);
            }

        });
        Attribute::retrieved(function($entry){
            if($entry->source=="PADB") {

                $a = DB::select("select * from  aaia_pcadb.PartAttributes where PAID=".$entry->attributeID ." limit 1");
                $entry->attributeID =json_encode(['id'=>$entry->attributeID ,'display'=>$a[0]->PAName]);

            }else{
           $entry->attributeID =json_encode(['id'=>$entry->attributeID,'display'=>$entry->attributeID ]);
            }

        });
        Product::retrieved(function ($entry) {
            $entry->product_name = json_encode(['id' => $entry->product_name_source, 'product_name' => $entry->product_name, 'display' => "[$entry->product_name_source] " . $entry->product_name]);
            $entry->long_description = json_encode(['id' => $entry->long_description_source, 'long_description' => $entry->long_description, 'display' => "[$entry->long_description_source] " . $entry->long_description]);
            $r = [];
            if (is_array($entry->bullets))
                if ($entry->bullets)
                    foreach ($entry->bullets as $bullet) {
                        $r[] = ['value' => $bullet];

                    }
            $entry->bullets = $r;
        });


    }

    public function regenerateImage()
    {
        if (request()->get('image_id')) {

            $image = Image::find(request()->get('image_id'));
            $i = new ImageCrawlResizer();
            $i->regen($image);
            return back();
        }
    }

    public function fetchBrand()
    {
        if (request()->get('q')) {
            return Brand::where('BrandName', 'LIKE', '%' . request()->get('q') . '%')->pluck('BrandName', 'BrandID')->toArray();
        }
        return [];
    }

    public function fetchParttype()
    {
        $form = request()->all();
        foreach ($form['form'] as $field => $value) {

            if ($value['name'] === 'id') {
                $product = Product::find($value['value']);
                break;
            }
        }
        if (request()->get('q')) {
            $taxs = Taxonomy::where('PartTerminologyName', 'like', '%' . request()->get('q') . '%')->get();
            $r = [];
            foreach ($taxs as $tax) {
                $r[] = ['PartTerminologyID' => $tax->PartTerminologyID, 'PartTerminologyName' => $tax->CategoryName . ' \ ' . $tax->SubCategoryName . ' \ ' . $tax->PartTerminologyName];
            }
            return $r;
        } else {
            $taxs = Taxonomy::where('PartTerminologyID', $product->aaia_parttype_id)->get();
            $r = [];
            foreach ($taxs as $tax) {
                $r[] = ['PartTerminologyID' => $tax->PartTerminologyID, 'PartTerminologyName' => $tax->CategoryName . ' \ ' . $tax->SubCategoryName . ' \ ' . $tax->PartTerminologyName];
            }
            return $r;

        }
    }
    public function fetchAttribute()
    {
        $return = [];
        $form = request()->all();
        foreach ($form['form'] as $field => $value) {

            if ($value['name'] === 'id') {
                $product = Product::find($value['value']);
                break;
            }
        }
        if($product->aaia_parttype_id) {
            if (request()->get('q')) {
                $pddbs = DB::select("   SELECT  PA.PAID, PA.PAName  FROM aaia_pcadb.PartAttributeAssignment PAA join  aaia_pcadb.Parts p ON p.PartTerminologyID = PAA.PartTerminologyID 
 join aaia_pcadb.PartAttributes PA ON PA.PAID=PAA.PAID WHERE p.PartTerminologyID=".$product->aaia_parttype_id."  and PA.PAName LIKE '%".request()->get('q')."%' GROUP BY  PA.PAID ORDER BY p.PartTerminologyID asc,PA.PAName asc");
            }else{
                $pddbs = DB::select("   SELECT  PA.PAID, PA.PAName  FROM aaia_pcadb.PartAttributeAssignment PAA join  aaia_pcadb.Parts p ON p.PartTerminologyID = PAA.PartTerminologyID 
 join aaia_pcadb.PartAttributes PA ON PA.PAID=PAA.PAID WHERE p.PartTerminologyID=".$product->aaia_parttype_id." GROUP BY  PA.PAID ORDER BY p.PartTerminologyID asc,PA.PAName asc");
            }
            if(count($pddbs)>0) {
                foreach($pddbs as $pddb) {
                    $return[]  = ['id'=>$pddb->PAID,'display'=>$pddb->PAName];
                }

            }else{
                $return[] = ['id'=>request()->get('q'),'display'=>request()->get('q')];
            }
        }
        return collect($return);
    }
    public function fetchProductname()
    {
        $return = [];
        $form = request()->all();
        foreach ($form['form'] as $field => $value) {

            if ($value['name'] === 'id') {
                $product = Product::find($value['value']);
                break;
            }
        }
        if (request()->get('q')) {
            $return['OWN'] = ['id' => 'OWN', 'product_name' => request()->get('q'), 'display' => '[OWN] ' . request()->get('q')];
        } else {
            if ($product->product_name_source == 'OWN') {

                $return['OWN'] = ['id' => 'OWN', 'product_name' => $product->product_name, 'display' => '[OWN] ' . $product->product_name];
            }

        }
        if ($product->partshareflatten) {
            if ($product->partshareflatten->ShortDescription !== "") {
                $return['PSF'] = ['id' => 'PSF', 'product_name' => $product->partshareflatten->ShortDescription, 'display' => '[PartShare] ' . $product->partshareflatten->ShortDescription];
            }

        }
        if ($product->productfeed->first()) {
            $return['WHI'] = ['id' => 'WHI', 'product_name' => $product->productfeed->first()->part_label, 'display' => '[WHI] ' . $product->productfeed->first()->part_label];
        }

        if ($product->partshareflatten && $product->partshareflatten->wwsitem) {
            $return['WWS'] = ['id' => 'WWS', 'product_name' => $product->partshareflatten->wwsitem->description, 'display' => '[WWS] ' . $product->partshareflatten->wwsitem->description];
        }

        if ($product->asin) {
            if ($product->asin->attribute) {
                if ($product->asin->attribute->where('
                AttributeID', 'item_name')->count() > 0) {
                    $return['AMZ'] = ['id' => 'AMZ', 'product_name' => $product->asin->attribute->where('AttributeID', 'item_name')->first()->value, 'display' => '[AMZ] ' . $product->asin->attribute->where('AttributeID', 'item_name')->first()->value];
                }
            }

        }
        return collect($return);
    }

    public function fetchLongdescription()
    {
        $return = [];
        $form = request()->all();
        foreach ($form['form'] as $field => $value) {

            if ($value['name'] === 'id') {
                $product = Product::find($value['value']);
                break;
            }
        }
        if (request()->get('q')) {
            $return[] = ['id' => 'OWN', 'long_description' => request()->get('q'), 'display' => '[OWN] ' . request()->get('q')];
        } else {
            if ($product->product_name_source == 'OWN') {

                $return[] = ['id' => 'OWN', 'long_description' => $product->product_name, 'display' => '[OWN] ' . $product->product_name];
            }

        }
        if ($product->partshareflatten) {
            if ($product->partshareflatten->MartketingDescription !== "") {
                $return[] = ['id' => 'PSF', 'long_description' => $product->partshareflatten->MartketingDescription, 'display' => '[PartShare - Marketing] ' . $product->partshareflatten->MartketingDescription];
            }
            if ($product->partshareflatten->LongDescription !== "") {
                $return[] = ['id' => 'PSF', 'long_description' => $product->partshareflatten->LongDescription, 'display' => '[PartShare - Long] ' . $product->partshareflatten->LongDescription];
            }

        }
        if ($product->productfeed->first()) {
            $return[] = ['id' => 'WHI', 'long_description' => $product->productfeed->first()->pt_desc, 'display' => '[WHI] ' . $product->productfeed->first()->pt_desc];
        }
        if ($product->partshareflatten && $product->partshareflatten->wwsitem) {
            $return[] = ['id' => 'WWS', 'long_description' => $product->partshareflatten->wwsitem->description, 'display' => '[WWS] ' . $product->partshareflatten->wwsitem->description];
        }
        if ($product->asin) {
            if ($product->asin->attribute) {
                if ($product->asin->attribute->where('AttributeID', 'product_description')->count() > 0) {
                    $return[] = ['id' => 'AMZ', 'long_description' => $product->asin->attribute->where('AttributeID', 'product_description')->first()->value, 'display' => '[AMZ] ' . $product->asin->attribute->where('AttributeID', 'product_description')->first()->value];
                }
            }

        }
        return collect($return);
    }

    protected function productTopBar($product)
    {
        $content = [
            [
                'type' => 'text',

                'label' => $product->aaiabrandid . ' ' . $product->part_number,
            ],
        ];

        if ($product->partshareflatten) {
            $content[] =
                [
                    'type' => 'link',
                    'href' => sophio_route('flattenpartshare.show', ['id' => $this->crud->getCurrentEntry()->partshareflatten->PID]),
                    'label' => 'Partshare',

                ];
        }

        $asin = Asin::where('mfg_code', $product->aaiabrandid)->where('part_number_unformatted', $product->part_number_unformatted)->first();

            $content [] = [
                'type' => 'link',
                'href' => sophio_route('pim/product.amazon', ['id' => $product->id]),
                'label' => 'Amazon',
            ];


        $wwsitem = Wwsitem::where('BrandID', $product->aaiabrandid)->where('part_number_unformatted', $product->part_number_unformatted)->get();
        if ($wwsitem) {
            $content [] = [
                'type' => 'link',
                'href' => sophio_route('wwsitem.index', ['BrandID' => $product->aaiabrandid, 'sku' => $product->part_number]),
                'label' => 'Promo',
            ];
        }
        $wwsitem = NeedsContent::where('aaiabrandid', $product->aaiabrandid)->where('part_number_unformatted', $product->part_number_unformatted)->where('resolved', '<>', 0)->get();
        if ($wwsitem) {
            $content [] = [
                'type' => 'link',
                'href' => sophio_route('needscontent.index', ['aaiabrandid' => $product->aaiabrandid, 'part_number_unformatted' => $product->part_number]),
                'label' => 'Needs Content',
            ];
        }
        Widget::add([
            'type' => 'topbar',
            'content' => $content
        ])->to('before_content');
    }
    public function amazon_exceptions()
    {
        $d = Wwsitem::with('product')->leftJoin('pnci_pim.asins', function($join) {
            $join->on('wws_items.BrandId','=','asins.mfg_code')->on('wws_items.part_number_unformatted','=','asins.part_number_unformatted') ;
        })->where(function($where){
            $where->whereNull('asins.id')->orWhere('asins.asin','');
        })


            ->groupBy('wws_items.id')->get();
        return view('admin.catalog.reports.amazonexceptions', ['data' => $d]);
    }
    public function amazon()
    {
        $this->productTopBar($this->crud->getCurrentEntry());
        $product = $this->crud->getCurrentEntry();
        $asin = Asin::where('mfg_code',$product->aaiabrandid)->where('part_number_unformatted',$product->part_number_unformatted)->first();
        if($asin  ) {
            if($asin->data!="") {
                $amazon =json_decode($asin->data,true);
                $prices = AmazonCompetittivePriceHistory::where('mfg_code',$product->aaiabrandid)->where('part_number_unformatted',$product->part_number_unformatted)->orderByDesc('created_at')->get();
                return view('admin.catalog.amazon',compact( 'product','asin','amazon','prices'));
            }
            return view('admin.catalog.amazon',['error'=>'No ASIN found in Amazon to match the product identifier ('.$asin->identifier_type.":".$asin->identifier.') . Last check made on '.$asin->updated_at, 'product'=>$product]);
        }
        return view('admin.catalog.amazon',['error'=>'No crawled yet', 'product'=>$product]);

    }
    protected function setupUpdateOperation()
    {
        CRUD::addSaveAction([
            'name' => 'save_action_one',
            'redirect' => function($crud, $request, $itemId) {

                return sophiosettings()->getStore()->VIRTUAL.'itemdetail/'.$crud->entry->aaiabrandid.'/'.$crud->entry->part_number_unformatted ;
            }, // what's the redirect URL, where the user will be taken after saving?

            // OPTIONAL:
            'button_text' => 'Save and view on website', // override text appearing on the button
            // You can also provide translatable texts, for example:
            // 'button_text' => trans('backpack::crud.save_action_one'),
            'visible' => function($crud) {
                return true;
            }, // customize when this save action is visible for the current operation
            'referrer_url' => function($crud, $request, $itemId) {
                return $crud->route;
            }, // override http_referrer_url
            'order' => 1, // change the order save actions are in
        ]);
        CRUD::setOperationSetting('strippedRequest', function ($request) {
            $input = $request->only(CRUD::getAllFieldNames());
            if ($input['images'] === "") {
                $this->crud->getCurrentEntry()->images()->delete();
                unset($input['images']);
            }
            if ($input['bullets'] === "") {

                unset($input['bullets']);
            }
            if ($input['attribute'] === "") {

                unset($input['attribute']);
            }
            if ($input['variant'] === "") {

                unset($input['variant']);
            }
            return $input;
        });
        $this->productTopBar($this->crud->getCurrentEntry());
        CRUD::field([
            'name' => 'mfg_code',
            'label' => 'Manufacturer code',
            'tab' => 'Content',
            'type' => 'text',
            'wrapper' => [
                'class' => 'form-group col-md-3'
            ],
        ]);
        CRUD::field([
            'name' => 'aaiabrandid',
            'label' => 'AAIA Brand ID',
            'type' => 'text',
            'tab' => 'Content',
            'wrapper' => [
                'class' => 'form-group col-md-3'
            ],
        ]);
        CRUD::field([
            'name' => 'part_number',
            'label' => 'Part Number',
            'type' => 'text',
            'tab' => 'Content',
            'wrapper' => [
                'class' => 'form-group col-md-3'
            ],
        ]);
        CRUD::field([
            'name' => 'part_number_unformatted',
            'label' => 'Part Number Unformatted',
            'type' => 'slug',
            'tab' => 'Content',
            'remove' => '/[*+~.()!:@]/g',

            'trim' => true,
            'target' => 'part_number',
            'separator' => '',
            'wrapper' => [
                'class' => 'form-group col-md-3'
            ],
        ]);
        CRUD::field([
            'name' => 'model_number',
            'label' => 'Model Number',
            'type' => 'text',
            'tab' => 'Content',
            'wrapper' => [
                'class' => 'form-group col-md-3'
            ],
        ]);
        CRUD::field([
            'name' => 'model_number_unformatted',
            'label' => 'Model Number Unformatted',
            'type' => 'slug',
            'tab' => 'Content',
            'remove' => '/[*+~.()!:@]/g',

            'trim' => true,
            'target' => 'model_number',
            'separator' => '',
            'wrapper' => [
                'class' => 'form-group col-md-3'
            ],
        ]);
        CRUD::field([
            'name' => 'upc',
            'label' => 'UPC',
            'type' => 'text',
            'tab' => 'Content',
            'wrapper' => [
                'class' => 'form-group col-md-3'
            ],

        ]);

        CRUD::field([
            'name' => 'gtin',
            'label' => 'GTIN',
            'type' => 'text',
            'tab' => 'Content',
            'wrapper' => [
                'class' => 'form-group col-md-3'
            ],
        ]);

        CRUD::field([
            'name' => 'product_name',
            'label' => 'Product Name',
            'tab' => 'Content',
            'type' => 'select2_json_from_api',
            'method' => 'POST',
            'delay' => 0,
            'minimum_input_length' => 0,
            'data_source' => backpack_url('pim/product/fetch/productname'),
            'include_all_form_fields' => true,
            'attribute' => 'display',
            'multiple' => false,
            'attributes_to_store' => ['id', 'product_name', 'display'],
            'wrapper' => [
                'class' => 'form-group col-md-9'
            ],

        ]);
        CRUD::field([
            'name' => 'long_description',
            'label' => 'Long Description',
            'tab' => 'Content',
            'type' => 'select2_json_from_api',
            'method' => 'POST',
            'delay' => 0,
            'minimum_input_length' => 0,
            'data_source' => backpack_url('pim/product/fetch/longdescription'),
            'include_all_form_fields' => true,
            'attribute' => 'display',
            'multiple' => false,
            'attributes_to_store' => ['id', 'long_description', 'display'],
            'wrapper' => [
                'class' => 'form-group col-md-9'
            ],

        ]);

        /*
        CRUD::field([
            'name' => 'aaia_parttype_id',
            'tab' => 'Content',
            'label' => 'Part Type',
            'method' => 'POST',
            'delay' => 0,
            'minimum_input_length' => 0,
            'type' => 'select2_json_from_api',
            'data_source' => backpack_url('pim/product/fetch/parttype'),
            'include_all_form_fields' => true,
            'attributes_to_store' => ['PartTerminologyID', 'PartTerminologyName'],
            'attribute' => 'PartTerminologyName',
            'multiple' => false,
            'wrapper' => [
                'class' => 'form-group col-md-12'
            ],
            'events' => [
                'saving' => function ($entry) {

                    $entry->aaia_parttype_id = json_decode(request()->get('aaia_parttype_id') ?? [], true)['PartTerminologyID'] ?? null;

                },
                'retrieved' => function ($entry) {

                    $tax = Taxonomy::where('PartTerminologyID', $entry->aaia_parttype_id)->first();
                    if($tax){
                        $entry->aaia_parttype_id = json_encode(['PartTerminologyID' => $entry->aaia_parttype_id, 'PartTerminologyName' => $tax->CategoryName . ' \ ' . $tax->SubCategoryName . ' \ ' . $tax->PartTerminologyName]);
                    }

                }
            ]
        ]);*/
        CRUD::field([
            'name' => 'aaia_parttype_id',
            'tab' => 'Content',
            'label' => 'PCDB Taxonomy',
            'type' => 'parttypes_modal',
            'wrapper' => ['class' => 'form-group col-md-4'],

        ]);
        CRUD::field([
            'name' => 'bullets',
            'label' => 'Bullets',
            'type' => 'repeatable',
            'tab' => 'Bullets',
            'force_delete' => true,
            'subfields' => [
                [
                    'force_delete' => true,
                    'name' => 'value',
                    'type' => 'text',
                    'label' => 'Feature',
                    'wrapper' => ['class' => 'form-group col-md-4'],
                ],
            ],
            'wrapper' => [
                'class' => 'form-group col-md-12'
            ],

        ]);
    CRUD::field([
       'name' => 'variant',
       'tab' => 'Variants',
        'label' => 'Variants',
        'type'          => "relationship",
        'subfields' =>[
            [
                'name' =>'attributeID',
                'type' => 'select2_json_from_api',
                'method' => 'POST',
                'delay' => 0,
                'minimum_input_length' => 0,
                'data_source' => backpack_url('pim/product/fetch/attribute'),
                'include_all_form_fields' => true,
                'attribute' => 'display',
                'multiple' => false,
                'attributes_to_store' => ['id',  'display'],

            ],
            [
                'name' => 'value',
            ]
        ]
    ]);
        CRUD::field([
            'name' => 'images',
            'label' => 'Images',
            'tab' => 'Multimedia',

            'force_delete' => true,
            'subfields' => [ // also works as: "fields"

                [
                    'force_delete' => true,
                    'name' => 'original_path',
                    'type' => 'myimageupload',
                    'label' => '',
                    'upload' => true,
                    'disk' => 'imagespool',
                    'withFiles' => [
                        'uploader' => SingleFile::class,
                        'disk' => 'imagespool',
                        'path' => '', // . CRUD::getCurrentEntry()->aaiabrandid . '/',
                        'fileNamer' => ImageFileName::class
                    ],
                    'accept' => "image/*,audio/*,video/*",
                    'wrapper' => ['class' => 'form-group col-md-3',],

                ],

                [
                    'name' => 'primary',
                    'type' => 'checkbox',
                    'label' => 'Primary',
                    'wrapper' => ['class' => 'form-group col-md-4'],

                ],
                [
                    'name'=>'sep1','type'=>'custom_html','value'=>'<hr>'
                ],

                [
                    'name' => 'image_url',
                    'type' => 'text',
                    'label' => 'Source',
                    'attributes' => ['readonly' => 'readonly',],
                    'wrapper' => ['class' => 'form-group col-md-4'],

                ],
                [
                    'name' => 'thumb_path',
                    'type' => 'text',
                    'label' => 'Thumb',
                    'attributes' => ['readonly' => 'readonly',],
                    'wrapper' => ['class' => 'form-group col-md-4'],

                ],
                [          'wrapper' => ['class' => 'form-group col-md-4'],
                    'name'=>'sep1','type'=>'custom_html_callback_entry','value'=>function($entry) {
                    return '<br><a class="btn btn-primary" href="'.sophio_route('pim/product.regenerateImage',['image_id'=>$entry['id']]).'">regenerate</a>';
                }
                ],

            ],
        ]);
        CRUD::field([
            'name' => 'width',
            'tab' => 'Dimensions',
            'label' => 'Width',
            'type' => 'text',
            'wrapper' => ['class' => 'form-group col-md-4'],

        ]);
        CRUD::field([
            'name' => 'height',
            'tab' => 'Dimensions',
            'label' => 'Height',
            'type' => 'text',
            'wrapper' => ['class' => 'form-group col-md-4'],

        ]);
        CRUD::field([
            'name' => 'length',
            'tab' => 'Dimensions',
            'label' => 'Length',
            'type' => 'text',
            'wrapper' => ['class' => 'form-group col-md-4'],

        ]);
        CRUD::field([
            'name' => 'weight',
            'tab' => 'Dimensions',
            'label' => 'Weight',
            'type' => 'text',
            'wrapper' => ['class' => 'form-group col-md-4'],

        ]);
        CRUD::field([
            'name' => 'attribute',
            'label' => 'Attribute',
            'force_delete' => true,
            'tab' => 'Attributes',
            'subfields' => [

                [
                    'name' =>'attributeID',
                    'type' => 'select2_json_from_api',
                    'method' => 'POST',
                    'delay' => 0,
                    'minimum_input_length' => 0,
                    'data_source' => backpack_url('pim/product/fetch/attribute'),
                    'include_all_form_fields' => true,
                    'attribute' => 'display',
                    'multiple' => false,
                    'attributes_to_store' => ['id',  'display'],
                ],
                [ 'name' =>'value'],
                [ 'name' =>'OUM'],
                [ 'name' =>'source','type'=>'select_from_array',   'options'     => ['WWS' => 'Promo', 'AMZ' => 'Amazon','OWN'=>'Custom','PADB'=>'PADB Attribute'],]

            ]
        ]);
    }

    protected function setupListOperation()
    {
        CRUD::setOperationSetting('showEntryCount', false);
        $this->crud->addColumns([
            'mfg_code',            'aaiabrandid',

            [ 'name'=>'aaiabrand.BrandName'],
            'part_number_unformatted',
            [
                'name' => 'product_name',
                'value' => function ($entry) {
                    return json_decode($entry->product_name)->product_name;

                }
            ],
            [
                'name' => 'image.thumb', // The db column name
                'label' => 'Image', // Table column heading
                'type' => 'ourimage',
                'disk' => 'imagespool'
            ],
        ]);
        $this->crud->addFilter(
            [ // text filter
                'type' => 'text',
                'name' => 'part_number_unformatted',
                'label' => 'Part Number',
            ],
            false,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'part_number_unformatted', unformatString($value));
            }
        );
        $this->crud->addFilter(
            [ // text filter
                'type' => 'text',
                'name' => 'model_number_unformatted',
                'label' => 'Model Number',
            ],
            false,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'part_number_unformatted', unformatString($value));
            }
        );
        $this->crud->addFilter(
            [ // text filter
                'type' => 'text',
                'name' => 'mfg_code',
                'label' => 'Mfg Code',
            ],
            false,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'mfg_code', '=', $value);
            }
        );
        $this->crud->addFilter(
            [ // text filter
                'type' => 'text',
                'name' => 'aaia_parttype_id',
                'label' => 'Part Type ID',
            ],
            false,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'aaia_parttype_id', '=', $value);
            }
        );
        CRUD::filter('parttype')
            ->type('select2_ajax')
            ->select_attribute('PartTerminologyName')
            ->select_key('PartTerminologyID')
            ->values(sophio_route('flattenpartshare.fetchParttype'))
            ->method('POST')
            ->whenActive(function ($value) {
                $this->crud->query->where('aaia_parttype_id', $value);
                /*$this->crud->query->where(function ($q) use ($value) {
                    $q->where('PartTypeID', $value)->orWhereHas('product', function ($query) use ($value) {
                        $query->where('aaia_parttype_id', $value);
                    });
                });*/
            });
        CRUD::filter('aaiabrandid')
            ->type('select2_ajax')
            ->values(backpack_url('pim/product/fetch/brand'))
            ->method('POST')
            ->label('AAIA Brand')
            ->whenActive(function ($value) {
                CRUD::addClause('where', 'aaiabrandid', '=', $value);
            });
        $this->crud->addFilter(
            [ // add a "simple" filter called Draft
                'type' => 'simple',
                'name' => 'has_image',
                'label' => 'Has Image',
            ],
            false, // the simple filter has no values, just the "Draft" label specified above
            function () { // if the filter is active (the GET parameter "draft" exits)
                $this->crud->addClause('where', 'has_image', '=', 1);
            }
        );
        $this->crud->addFilter(
            [ // add a "simple" filter called Draft
                'type' => 'simple',
                'name' => 'has_dimensions',
                'label' => 'Has Dimensions',
            ],
            false, // the simple filter has no values, just the "Draft" label specified above
            function () { // if the filter is active (the GET parameter "draft" exits)
                $this->crud->addClause('where', 'has_dimensions', '=', 1);
            }
        );
        $this->crud->addButtonFromModelFunction('top', 'contactpk', 'openRefresh', 'beginning');
    }

    protected function setupShowOperation()
    {
        CRUD::setOperationSetting('tabsEnabled', true);
        $this->crud->setOperationSetting('tabsType', 'vertical');

        $this->crud->setShowContentClass('col-md-12');
        $this->crud->addColumns([
            ['name' => 'id', 'tab' => 'Identification',],
            [
                'name' => 'partmaster_id',
                'tab' => 'Identification',
                'wrapper' => [

                    'href' => function ($crud, $column, $entry, $related_key) {
                        return backpack_url('productfeed?partmaster_id=' . $entry->partmaster_id);
                    },
                    'target' => '_blank',

                ],
            ],
            ['name' => 'mfg_code', 'tab' => 'Identification',],
            ['name' => 'part_number_unformatted', 'tab' => 'Identification',],
            ['name' => 'part_number', 'tab' => 'Identification',],
            ['name' => 'aaiabrandid', 'tab' => 'Identification',],
            ['name' => 'upc', 'tab' => 'Identification',],
            [
                'name' => 'upc_source',
                'tab' => 'Identification',
                'label' => 'UPC Source',
                'type' => 'text',
                'wrapper' => [
                    'element' => 'span',
                    'class' => function ($crud, $column, $entry, $related_key) {
                        return 'badge badge-default';
                    },
                ],
            ],
            [
                'name' => 'gtin', 'tab' => 'Identification',],
            [
                'name' => 'gtin_source',
                'tab' => 'Identification',
                'label' => 'GTIN Source',
                'type' => 'text',
                'wrapper' => [
                    'element' => 'span',
                    'class' => function ($crud, $column, $entry, $related_key) {
                        return 'badge badge-default';
                    },
                ],
            ],
            [
                'name' => 'has_image',
                'tab' => 'Media',
                'label' => 'Has Image',
                'type' => 'boolean',
                'options' => [0 => 'No', 1 => 'Yes'],
                'wrapper' => [
                    'element' => 'span',

                    'class' => function ($crud, $column, $entry, $related_key) {
                        if ($column['text'] == 'Yes') {
                            return 'badge badge-success';
                        }

                        return 'badge badge-error';
                    },
                ],
            ],
            [
                'name' => 'image_url', // The db column name
                'tab' => 'Media',
                'label' => 'Image', // Table column heading
                'type' => 'image',
                'height' => '100px',
                'width' => '100px',
            ],
            [
                'name' => 'image_source', 'tab' => 'Media',],
            [
                'name' => 'image_source',
                'tab' => 'Media',
                'label' => 'images source',
                'type' => 'text',
                'wrapper' => [
                    'element' => 'span',
                    'class' => function ($crud, $column, $entry, $related_key) {
                        return 'badge badge-default';
                    },
                ],
            ],
            [
                'name' => 'qty_uom', 'tab' => 'Dimensions',],
            [
                'name' => 'min_order_qty', 'tab' => 'Dimensions',],
            [
                'name' => 'part_status', 'tab' => 'Other',],
            [
                'name' => 'has_dimensions',
                'label' => 'Has Dimensions',
                'tab' => 'Dimensions',
                'type' => 'boolean',
                'options' => [0 => 'No', 1 => 'Yes'],
                'wrapper' => [
                    'element' => 'span',

                    'class' => function ($crud, $column, $entry, $related_key) {
                        if ($column['text'] == 'Yes') {
                            return 'badge badge-success';
                        }

                        return 'badge badge-error';
                    },
                ],
            ],
            [
                'name' => 'oversized', 'tab' => 'Dimensions',],
            [
                'name' => 'overweight', 'tab' => 'Dimensions',],
            [
                'name' => 'dimensions_source',
                'tab' => 'Dimensions',
                'label' => 'dimensions source',
                'type' => 'text',
                'wrapper' => [
                    'element' => 'span',
                    'class' => function ($crud, $column, $entry, $related_key) {
                        return 'badge badge-default';
                    },
                ],
            ],
            [
                'name' => 'excluded_mfg', 'tab' => 'Other',],
            [
                'name' => 'has_seo_fitment', 'tab' => 'Marketing',],
            [
                'name' => 'countryoforigin', 'tab' => 'Other',],
            [
                'name' => 'countryoforigin_source',
                'label' => 'Country source',
                'tab' => 'Other',
                'type' => 'text',
                'wrapper' => [
                    'element' => 'span',
                    'class' => function ($crud, $column, $entry, $related_key) {
                        return 'badge badge-default';
                    },
                ],
            ],
            ['name' => 'long_description', 'tab' => 'Marketing', 'limit' => 20000],
            [
                'name' => 'long_description_source',
                'label' => 'Long description source',
                'tab' => 'Marketing',
                'type' => 'text',
                'wrapper' => [
                    'element' => 'span',
                    'class' => function ($crud, $column, $entry, $related_key) {
                        return 'badge badge-default';
                    },
                ],
            ],
            ['name' => 'description', 'tab' => 'Marketing', 'limit' => 20000],
            [
                'name' => 'description_source',
                'tab' => 'Marketing',
                'label' => 'Description source',
                'type' => 'text',
                'wrapper' => [
                    'element' => 'span',
                    'class' => function ($crud, $column, $entry, $related_key) {
                        return 'badge badge-default';
                    },
                ],
            ],
            ['name' => 'bullets', 'tab' => 'Marketing', 'limit' => 20000, 'type' => 'json'],
            [
                'name' => 'bullets_source', 'tab' => 'Marketing',
                'label' => 'Bullets source',
                'type' => 'text',
                'wrapper' => [
                    'element' => 'span',
                    'class' => function ($crud, $column, $entry, $related_key) {
                        return 'badge badge-default';
                    },
                ],
            ],
            ['name' => 'materials', 'tab' => 'Hazard', 'limit' => 20000],

            ['name' => 'product_name', 'tab' => 'Identification', 'limit' => 20000],
            [
                'name' => 'product_name_source',
                'label' => 'Product Name source', 'tab' => 'Identification',
                'type' => 'text', 'limit' => 20000

            ],

            ['name' => 'part_name', 'tab' => 'Identification', 'limit' => 20000],
            [
                'name' => 'part_name_source',
                'label' => 'Part Name source', 'tab' => 'Identification',
                'type' => 'text', 'limit' => 20000,

            ],
            ['name' => 'prop65require', 'tab' => 'Hazard'],
            ['name' => 'prop65text', 'tab' => 'Hazard', 'limit' => 20000],
            [
                'name' => 'prop65_source',
                'tab' => 'Hazard',
                'label' => 'Prop 65 source',
                'type' => 'text',
                'wrapper' => [
                    'element' => 'span',
                    'class' => function ($crud, $column, $entry, $related_key) {
                        return 'badge badge-default';
                    },
                ],
            ],
            ['name' => 'warranty', 'tab' => 'Other', 'limit' => 1000],
            ['name' => 'width', 'tab' => 'Dimensions'],
            ['name' => 'height', 'tab' => 'Dimensions'],
            ['name' => 'length', 'tab' => 'Dimensions'],
            ['name' => 'weight', 'tab' => 'Dimensions'],
            ['name' => 'created_at', 'tab' => 'Identification',],
            ['name' => 'updated_at', 'tab' => 'Identification',],
            [
                'key' => 'walmartid',
                'name' => 'productfeed',
                'label' => 'walmartid',
                'type' => 'relationship',
                'entity' => 'productfeed',
                'attribute' => 'walmartid',
                'tab' => 'External IDs',
                'model' => ProductFeed::class
            ],
            [
                'key' => 'tractorid',
                'name' => 'productfeed',
                'label' => 'tractorid',
                'type' => 'relationship',
                'entity' => 'productfeed',
                'attribute' => 'tractorid',
                'tab' => 'External IDs',
                'model' => ProductFeed::class
            ],
            [
                'name' => 'package',
                'label' => 'Packages',
                'type' => 'ctable',
                'link' => 'package',
                'tab' => 'Packages',
                'columns' => ['gtin', 'uom', 'qty', 'weight', 'weight_uom', 'dimensional_weight', 'width', 'length', 'height', 'dimension_uom', 'source']
            ]
        ]);
    }

    public function contactpk()
    {
        $parts = [];
        $limit = 10;
        $contactpk = "";
        $part_number_unformatted = "";

        $validator = Validator::make(request()->all(), [
            'contactpk' => 'required',


        ]);
        if (!$validator->fails()) {
            $contactpk = $validator->safe()->only(['contactpk'])['contactpk'];
            $limit = request()->limit;
            $zoro = new Products();
            $zoro->refreshByContactpk($contactpk);
        }

        return view('admin.zoroexport.contactpk', ['contactpk' => $contactpk, 'parts' => $parts, 'limit' => $limit]);
    }

    public function tractor()
    {
        set_time_limit(3000);
        $products = new Products();
        $products->existingTractors();
    }

    public function doAppsWarehouse()
    {
        AppsWarehouse::dispatch();
    }
}