<?php

namespace App\Http\Controllers\Admin\PIM;

use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Sophio\Common\Models\PIM\Image;

class ImageCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    public function setup()
    {

        parent::setup();
        CRUD::setModel(Image::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/pim/image');
        CRUD::setEntityNameStrings('image', 'Images');

    }
protected function setupUpdateOperation()
{

}
    protected function setupListOperation()
    {
        CRUD::addColumns([
            'aaiabrandid',
            'part_number',
            ['name' => 'thumb', 'type' => 'image', 'width' => 'auto', 'height' => '100px',
                'wrapper' => [
                    'href' => function ($crud, $column, $entry, $related_key) {
                        return $entry->original;
                    }
                ]],
            'primary'

        ]);
        $this->crud->addFilter(
            [ // text filter
                'type' => 'text',
                'name' => 'aaiabrandid',
                'label' => 'AAIA BrandID',
            ],
            false,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'aaiabrandid', $value);
            }
        );
        $this->crud->addFilter(
            [ // text filter
                'type' => 'text',
                'name' => 'part_number_unformatted',
                'label' => 'Part Number',
            ],
            false,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'part_number_unformatted', $value);
            }
        );
        $this->crud->addFilter(
            [ // text filter
                'type' => 'text',
                'name' => 'filename',
                'label' => 'Filename',
            ],
            false,
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'filename', $value);
            }
        );
    }

    protected function setupShowOperation()
    {
        CRUD::addColumns([
            'aaiabrandid',
            'part_number',
            ['name' => 'thumb', 'type' => 'image', 'width' => 'auto', 'height' => '250px'],
            ['name' => 'original', 'type' => 'image', 'width' => 'auto', 'height' => '400px'],
            ['name' => 'resized', 'type' => 'image', 'width' => 'auto', 'height' => '400px'],
            ['name' => 'image_url', 'limit' => 1000],
            'primary'

        ]);
    }
}