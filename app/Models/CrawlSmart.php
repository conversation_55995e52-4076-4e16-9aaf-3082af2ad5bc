<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\CrawlSmart
 *
 * @property int $id
 * @property string $mfgcode
 * @property string $part_number
 * @property int $pt_num
 * @property string $interchange_mfgcode
 * @property string $interchange_brand
 * @property string $interchange_part_number
 * @property string|null $cost
 * @property string|null $qty
 * @property string|null $interchange_part_number_unformatted
 * @method static \Illuminate\Database\Eloquent\Builder|CrawlSmart newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CrawlSmart newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CrawlSmart query()
 * @mixin \Eloquent
 */
class CrawlSmart extends Model
{
    protected $table = 'sophio_fbs.crawl_smart';
    protected $primaryKey = 'id';
    public $timestamps = false;
    protected $fillable = ['mfgcode','part_number','pt_num','interchange_mfgcode','interchange_brand','interchange_part_number'];
}