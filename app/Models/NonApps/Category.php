<?php

namespace App\Models\NonApps;


use App\Models\NonApps\Scopes\CatalogScope;
use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Sophio\Common\Models\Traits\ImageTrait;

/**
 * App\Models\NonApps\Category
 *
 * @property int $CategoryID
 * @property int $clientId
 * @property string $CategoryName
 * @property string $image_url
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\NonApps\Part[] $parts
 * @property-read int|null $parts_count
 * @property-write mixed $image
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\NonApps\SubCategory[] $subcategories
 * @property-read int|null $subcategories_count
 * @method static \Illuminate\Database\Eloquent\Builder|Category newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Category newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Category query()
 * @mixin \Eloquent
 */
class Category extends Model
{
    use CrudTrait;
  //  use ImageTrait;
    protected  $table='nonapps.Categories';
    protected $primaryKey = 'CategoryID';
    protected $fillable =['clientId','CategoryName','image_url'];
    public $timestamps = false;
    protected static function booted()
    {

        static::addGlobalScope(New CatalogScope);
        static::saved(function ($invoice) {
            Http::withoutVerifying()->get('https://'.config('sophio.cronicle.domain').'/api/app/run_event/v1?id='.config('sophio.cronicle.reindex_manticore').'&api_key='.config('sophio.cronicle.api_key'));
        });

    }
    public function subcategories()
    {
        return $this->belongsToMany(SubCategory::class, 'nonapps.CodeMaster','CategoryID','SubCategoryID');
    }
    public function parts()
    {
        return $this->belongsToMany(Part::class,  'nonapps.CodeMaster','CategoryID','PartTerminologyID');
    }

}