<?php

namespace App\Models;
use Backpack\CRUD\app\Models\Traits\CrudTrait;
use <PERSON><PERSON><PERSON><PERSON><PERSON>z\ImpersonateUser\app\Interfaces\ImpersonateInterface;

/**
 * App\Models\SophioUser
 *
 * @property int $id
 * @property int|null $role_id
 * @property string $name
 * @property string $email
 * @property string|null $avatar
 * @property string $password
 * @property string|null $remember_token
 * @property string|null $fullname
 * @property string|null $description
 * @property int|null $contactpk
 * @property int|null $user_key
 * @property string|null $ip
 * @property string|null $phone
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Activitylog\Models\Activity> $activities
 * @property-read int|null $activities_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Sophio\Common\Models\FBS\Customer> $customers
 * @property-read int|null $customers_count
 * @property-read mixed $related_models
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Permission> $permissions
 * @property-read int|null $permissions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Sophio\Common\UserProfile> $profilables
 * @property-read int|null $profilables_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Role> $roles
 * @property-read int|null $roles_count
 * @property-read \Sophio\Common\Models\FBS\SalesRep|null $salesrep
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Sophio\Common\Models\FBS\Supplier> $suppliers
 * @property-read int|null $suppliers_count
 * @method static \Illuminate\Database\Eloquent\Builder|SophioUser newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SophioUser newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|User permission($permissions)
 * @method static \Illuminate\Database\Eloquent\Builder|SophioUser query()
 * @method static \Illuminate\Database\Eloquent\Builder|User role($roles, $guard = null)
 * @mixin \Eloquent
 */
class SophioUser extends \Sophio\Common\SophioUser implements ImpersonateInterface
{
    use CrudTrait;
}