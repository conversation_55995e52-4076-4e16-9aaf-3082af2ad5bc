<?php

namespace App\Library\Sophio\Actions;

use Illuminate\Support\Facades\DB;

class SyncCentralToFBS
{
    public function __invoke()
    {
        DB::statement('CREATE TABLE IF NOT EXISTS sophio_fbs.wws_sendprofiles LIKE b2ccentral_aces.wws_sendprofiles');
        DB::statement('TRUNCATE TABLE sophio_fbs.wws_sendprofiles');
        DB::statement('INSERT INTO sophio_fbs.wws_sendprofiles SELECT * FROM b2ccentral_aces.wws_sendprofiles');

        DB::statement('CREATE TABLE IF NOT EXISTS sophio_fbs.wws_items_pricing LIKE b2ccentral_aces.wws_items_pricing');
        DB::statement('TRUNCATE TABLE sophio_fbs.wws_items_pricing');
        DB::statement('INSERT INTO sophio_fbs.wws_items_pricing SELECT * FROM b2ccentral_aces.wws_items_pricing');

        DB::statement('CREATE TABLE IF NOT EXISTS sophio_fbs.exclude_brands LIKE b2ccentral_aces.exclude_brands');
        DB::statement('TRUNCATE TABLE sophio_fbs.exclude_brands');
        DB::statement('INSERT INTO sophio_fbs.exclude_brands SELECT * FROM b2ccentral_aces.exclude_brands');

        DB::statement('CREATE TABLE IF NOT EXISTS sophio_fbs.exclude_items LIKE b2ccentral_aces.exclude_items');
        DB::statement('TRUNCATE TABLE sophio_fbs.exclude_items');
        DB::statement('INSERT INTO sophio_fbs.exclude_items SELECT * FROM b2ccentral_aces.exclude_items');

        DB::statement('CREATE TABLE IF NOT EXISTS sophio_fbs.deletesurcharges LIKE b2ccentral_aces.deletesurcharges');
        DB::statement('TRUNCATE TABLE sophio_fbs.deletesurcharges');
        DB::statement('INSERT INTO sophio_fbs.deletesurcharges SELECT * FROM b2ccentral_aces.deletesurcharges');

        DB::statement('CREATE TABLE IF NOT EXISTS sophio_fbs.adjustsurcharges LIKE b2ccentral_aces.adjustsurcharges');
        DB::statement('TRUNCATE TABLE sophio_fbs.adjustsurcharges');
        DB::statement('INSERT INTO sophio_fbs.adjustsurcharges SELECT * FROM b2ccentral_aces.adjustsurcharges');
    }
}