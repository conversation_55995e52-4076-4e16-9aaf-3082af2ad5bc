<?php

namespace App\Library\Sophio\Exporters;

use App\Models\Wwsitem;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class AWDAExportWWSExpections
{
    protected $settings = [];
    protected $export;
    protected $time;

    public function __construct($settings = [])
    {

        $this->settings = $settings;
        $this->time = time();
    }

    public function export()
    {
        $this->export = new AWDATemplate('awda_partshare_' . $this->settings['database'] . '_' . ($this->settings['aaiabrandid']??'') . date("Ymd") . '_' . $this->time . '.xlsx','fbs');
        $this->export->open();
        $this->populate();
        $this->export->close();
    }
    public function getExport()
    {
        return $this->export;
    }

    public function populate()
    {
        $d = Wwsitem::leftJoin('partshare.FlattenedData', function ($on) {
            $on->on('wws_items.BrandID', '=', 'FlattenedData.AAIABrandID');
            $on->on('wws_items.part_number_unformatted', '=', 'FlattenedData.CompressedPartNumber');
            $on->where('PID', '<', 100000000);
        }
        )->whereNull('PID')->where('contactpk',201366)
            ->leftJoin('tool_and_equipment.vision_dcf', 'wws_items.BrandID', '=', 'vision_dcf.aaiabrandid');
        if(isset($this->settings['brandid']) && $this->settings['brandid']!=="") {
            $d = $d->where('wws_items.BrandID', $this->settings['brandid']);
        }else{
            DB::table('partshare.awda')->truncate();
        }
        $cols =['Price Sheet Number','Superseded Price Sheet','Price Sheet Effective Date','Price Sheet Expiration Date','Hazardous Material Code YN','Item Level GTIN','Item Level GTIN Qualifier','Part Number','AAIA Brand ID','ACES Applications','Item Qty Size','Item Quantity Size UOM','Container Type','Quantity per Application','Minimum Order Quantity','Product Group Code','Product SubGroup Code','Product Category Code','Part Terminology ID','Application Summary','Product Description Long 80','Product Description Short 20','Product Description Invoice 40','WD Price','Item WD Core Price','Jobber Price','Dealer','NonStock Dealer','User','List Price','Jobber Core','Price Break Quantity','Country of Origin Primary','Harmonized Tariff Code Schedule B','Life Cycle Status','MSDS FLAG','National Popularity Code','Part Number Old','Part Number Superseded To','Taxable YN','MSDS SHEET ORDER NUMBER','Each Package Level GTIN','Each Package Bar Code Characters','Each Package Inner Quantity','Each Package Inner Quantity UOM','Each Height','Each Width','Each Length','Each Weight','Inner Pack Level GTIN','Inner Package Bar Code Characters','Inner Pack Quantity of Eaches In Package','Inner Pack Height','Inner Pack Width','Inner Pack Length','Inner Pack Weight','Case Package Level GTIN','Case Package Bar Code Characters','Quantity of Eaches in Case','Case Height','Case Width','Case Length','Case Weight','Pallet Package Level GTIN','Pallet Package Bar Code Characters','Pallet Quantity of Eaches in Package','Pallet Height','Pallet Width','Pallet Length','Pallet Weight','UOM for Dimensions','UOM for Weight','Hazardous Material Description','Hazardous Materials Class Code','Link to Supplier Web Page','Item Retail MAP Price','Silver Line Item Invoice Price','Gold Line Item Invoice Price','Silver Net Invoice Price','Gold Net Invoice Price','Silver Program Core Invoice Price','Gold Program Core Invoice Price','MSDS link','HazMat Shipping Name','Marketing Description Long','Primary Image Link','Image 2 Link','Image 3 Link','Image 4 Link','California Proposition 65 Required','California Proposition 65 Text','California Proposition 65 Image','Linecode'];
        $d = $d->selectRaw('wws_items.*, vision_dcf.pslinecode')->groupBy(['wws_items.BrandID','wws_items.part_number_unformatted'])->get();

        foreach ($d as $key => $wwsitem) {

            $array = [
                $this->time,
                '',
                Carbon::now()->format('m/d/y'),
                Carbon::now()->addYear()->format('m/d/y'),
                'N',
                ((int)$wwsitem->gtin>0)?$wwsitem->gtin:'',
                'EN',
                $wwsitem->sku,
                $wwsitem->BrandID,
                'N', //aces application
                1,
                'EA',
                'BX',
                $wwsitem->qty_per_application>0?$wwsitem->qty_per_application:1,
                $wwsitem->minqty>0?$wwsitem->minqty:1,
                '',
                '',
                '',

                $wwsitem->pt_num>0?$wwsitem->pt_num:($wwsitem->product?$wwsitem->product->aaia_parttype_id:''),
                'Non Apps',
                substr($wwsitem->description,0,80),
                substr($wwsitem->description,0,20),
                substr($wwsitem->description,0,40),
                number_format( $wwsitem->cost, 2, '.', ''),
                $wwsitem->coreprice,
                '0.0',
                '',
                '',
                number_format($wwsitem->list, 2, '.', ''),
                number_format($wwsitem->list, 2, '.', ''),
                '',
                '',
                'US', //COO
                '',
                '2', //life cycle status
                'N',
                '',
                '',
                '',
                'N',
                '',
                ((int)$wwsitem->gtin>0)?$wwsitem->gtin:'', // each level gtin
                ((int)$wwsitem->upc>0)?$wwsitem->upc:'',
                $wwsitem->minqty>0?$wwsitem->minqty:1,
                'EA',
                $wwsitem->height>0?$wwsitem->height:'',
                $wwsitem->width>0?$wwsitem->width:'',
                $wwsitem->length>0?$wwsitem->length:'',
                $wwsitem->weight>0?$wwsitem->weight:'',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                'IN',
                'LB',
                '',
                '',
                '',
                '',
                '',
                number_format( $wwsitem->cost, 2, '.', ''),
                '',
                number_format( $wwsitem->cost, 2, '.', ''),
                '',
                '',
                '',
                '',
                $wwsitem->description,
                $wwsitem->image,
                $wwsitem->image2,
                $wwsitem->image3,
                $wwsitem->image4,
                '',
                '',
                '',
                $wwsitem->pslinecode


            ];
            if($this->settings['brandid']=="") {

                DB::table('partshare.awda')->insert(array_combine($cols,$array));
            }
            $this->export->addRow($array);
        }
    }
}