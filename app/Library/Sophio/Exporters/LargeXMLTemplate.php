<?php

namespace App\Library\Sophio\Exporters;


class LargeXMLTemplate extends ExportTemplate
{
    protected $writer;
    protected $i = 0;
    protected $flush_threshold = 1000;

    public function open()
    {
        $this->writer = new \XMLWriter();
        $this->writer->openMemory();
        $this->writer->startDocument('1.0', 'UTF-8');
        $this->writer->setIndent(2);
    }

    public function setHeaders($array = [])
    {

    }

    public function startElement($element)
    {
        $this->writer->startElement($element);
    }

    public function addRow($outputArr)
    {
        foreach ($outputArr as $root => $node) {

            if (is_array($node)) {
                $this->writer->startElement($root);
                foreach ($node as $key => $value) {
                    $this->writer->writeElement($key, $value);

                }
                $this->writer->endElement();
                $this->i++;
            }else {
                $this->writer->writeElement($root, $node);
                $this->i++;
            }
            if ($this->i % $this->flush_threshold == 0) {
                file_put_contents($this->newfile, $this->writer->flush(true), FILE_APPEND);
            }
        }
    }

    public
    function endElement()
    {
        $this->writer->endElement();
    }

    public
    function close()
    {
        file_put_contents($this->newfile, $this->writer->flush(true), FILE_APPEND);
    }
}