<?php

namespace App\Library\Sophio;

use Sophio\Common\Models\WHIACES\ProductFeed;
use Sophio\Common\Models\PIM\Product;
use Sophio\Common\Models\PIM\Package;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Sophio\Common\Services\IsoCodesService;

class ProductUpdater
{
    protected Product $c;
    protected ProductFeed $p;
    protected $settings;
    protected $pies_attrs;
    protected $product_attributes;
    protected $has_dimensions;
    protected $has_pies;
    protected $cal65_found;
    protected $expis;
    protected $materials_paids = [3, 10, 59, 73, 95, 219, 222, 226, 243, 287, 289, 292, 294, 302, 305, 346, 357, 362, 368, 393, 396, 398, 414, 421,
        463, 477, 525, 529, 547, 548, 584, 586, 603, 604, 621, 630, 634, 647, 657, 658, 689, 696, 709, 732, 734, 738, 743, 751, 755, 761, 776, 827, 828,
        834, 840, 842, 849, 853, 911, 915, 946, 959, 1006, 1018, 1033, 1045, 1051, 1079, 1088, 1092, 1100, 1104, 1105, 1106, 1111, 1130, 1137, 1159,
        1164, 1179, 1261, 1294, 1307, 1311, 1342, 1357, 1360, 1367, 1380, 1391, 1393, 1403, 1471, 1556, 1586, 1595, 1609, 1661, 1675, 1679, 1751,
        1850, 1869, 1919, 1988, 1997, 2019, 2058, 2060, 2064, 2078, 2101, 2142, 2175, 2182, 2186, 2193, 2218, 2237, 2238, 2246, 2247, 2257, 2285,
        2288, 2290, 2306, 2307, 2329, 2389, 2412, 2418, 2438, 2455, 2475, 2480, 2538, 2539, 2563, 2672, 2673, 2731, 2741, 2743, 2760, 2782, 2848,
        2913, 2921, 2924, 2931, 2939, 2960, 2978, 2988, 3019, 3028, 3039, 3055, 3075, 3089, 3090, 3112, 3117, 3119, 3154, 3195, 3226, 3321, 3345,
        3348, 3374, 3403, 3405, 3414, 3415, 3416, 3426, 3427, 3443, 3458, 3459, 3482, 3489, 3492, 3493, 3499, 3506, 3512, 3528, 3530, 3549, 3552,
        3553, 3583, 3585, 3596, 3609, 3614, 3643, 3668, 3704, 3709, 3711, 3713, 3753, 3784, 3806, 3807, 3818, 3825, 3834, 3836, 3845, 3848, 3849,
        3863, 3889, 3890, 3908, 3931, 3940, 3963, 3987, 3999, 4005, 4019, 4026, 4034, 4035, 4078, 4084, 4090, 4150, 4194, 4203, 4216, 4249, 4265,
        4289, 4316, 4320, 4332, 4345, 4372, 4392, 4418, 4428, 4436, 4442, 4528, 4572, 4582, 4600, 4643, 4657, 4704, 4706, 4737, 4746, 4747, 4755,
        4768, 4781, 4810, 4817, 4822, 4846, 4876, 4908, 4917, 4930, 4978, 5004, 5012, 5023, 5024, 5030, 5031, 5078, 5086, 5096, 5111, 5128, 5129,
        5132, 5163, 5164, 5167, 5175, 5184, 5194, 5205, 5224, 5232, 5281, 5300, 5310, 5311, 5318, 5320, 5339, 5344, 5369, 5388, 5406, 5409, 5425,
        5438, 5441, 5447, 5454, 5468, 5501, 5530, 5579, 5580, 5582, 5591, 5619, 5671, 5679, 5686, 5691, 5694, 5710, 5712, 5720, 5726, 5728, 5746,
        5755, 5756, 5766, 5772, 5807, 5830, 5836, 5840, 5866, 5867, 5877, 5881, 5888, 5903, 5915, 5917, 5922, 5950, 5964, 5979, 5990, 5998, 5999,
        6003, 6009, 6017, 6033, 6060, 6080, 6081, 6110, 6141, 6145, 6146, 6155, 6180, 6219, 6242, 6249, 6255, 6296, 6306, 6312, 6316, 6345, 6371,
        6387, 6402, 6413, 6416, 6456, 6457, 6476, 6494, 6544, 6563, 6585, 6602, 6619, 6636, 6647, 6649, 6650, 6673, 6674, 6688, 6732, 6736, 6738,
        6767, 6771, 6797, 6798, 6801, 6829, 6846, 6873, 6876, 6895, 6901, 6906, 6909, 6925, 6931, 6940, 6958, 6979, 7031, 7032, 7089, 7102, 7224,
        7243, 7270, 7281, 7295, 7303, 7307, 7352, 7355, 7427, 7430, 7431, 7440, 7460, 7468, 7495, 7519, 7525, 7662, 7666, 7674, 7682, 7709, 7719
        , 7720, 7747, 7748, 7754, 7764, 7784, 7788, 7790, 7800, 7822, 7828, 7857, 7865, 7879, 7886, 7929, 7940, 7952, 7960, 8019, 8022, 8026, 8035,
        8050, 8057, 8074, 8079, 8168, 8173, 8184, 8240, 8313, 8333, 8351, 8354, 8397, 8401, 8408, 8419, 8422, 8441, 8448, 8449, 8472, 8489, 8503,
        8504, 8537, 8541, 8547, 8568, 8585, 8589, 8607, 8616, 8667, 8695, 8701, 8716, 8720, 8730, 8744, 8803, 8818, 8840, 8849, 8854, 8864, 8865,
        8871, 8878, 8879, 8885, 8930, 8976, 9090, 9091, 9127, 9141, 9142, 9143, 9144, 9145, 9146, 9147, 9152, 9220, 9287, 9288, 9306, 9307, 9308,
        9309, 9310, 9354, 9383, 9384, 9388, 9398, 9399, 9400, 9401, 9417, 95188];
    private $banned_words = [
        'evaporator',
        'refrigerant',
        'liquid',
        'chemical',
        'chemicals'
    ];

    public function __construct(\Sophio\Common\Models\PIM\Product $c, $settings = [])
    {
        $this->c = $c;
        $this->p = new ProductFeed();
        $this->settings = $settings;
        $this->has_dimensions = false;
        $this->has_pies = false;
        $this->cal65_found = false;

    }

    public function initProductFeedSource(ProductFeed $p = null)
    {
        if ($p == null) {
            if ($this->c->productfeed && count($this->c->productfeed) > 0) {
                $this->p = $this->c->productfeed[0];
            } else {
                $this->p = new ProductFeed();
            }

        } else {
            $this->p = $p;
        }

        if ($this->p->aaiabrandid != null && $this->p->aaiabrandid != "") {
            $this->has_pies = isset($this->p->pies->id);

        }

    }

    public function getProduct()
    {
        return $this->c;
    }

    public function refresh($exclude = [])
    {

        $methods = preg_grep('/^set/', get_class_methods($this));
        $this->setBrand();
        $this->setUpcGtin();
        $this->setPartMasterId();
        $this->setPartNumber();
        foreach ($methods as $method) {
            if (in_array($method, $exclude)) {
            } else {
                $this->$method();
            }
        }
    }

    public function setBrand()
    {
        if ($this->p && $this->p->aaiabrandid!==null) {
            $this->c->aaiabrandid = $this->p->aaiabrandid;
        }

    }

    public function setPartNumber()
    {
        if ($this->p)
            if ($this->has_pies) {
                $this->c->part_number = $this->p->pies->PartNumber;
            } else {
                $this->c->part_number = $this->p->part_number;
            }
    }

    public function setPartTypeID()
    {
        if ($this->c->aaia_parttype_id_source === "OWN") {
            return;
        }
        if ($this->p && $this->p->pt_num != 0) {
            $this->c->aaia_parttype_id = $this->p->pt_num;
            $this->c->aaia_parttype_id_source = 'WHI';
        }
        if ($this->has_pies &&  $this->p->pies->PartTerminologyID>0) {
            $this->c->aaia_parttype_id = $this->p->pies->PartTerminologyID;
            $this->c->aaia_parttype_id_source = 'PIES';
        }
    }

    public function setPartMasterId()
    {
        $this->c->partmaster_id = $this->p->partmaster_id;
    }

    public function setUpcGtin()
    {
        $new = false;
        if ($this->p->partshareprice) {
            list($gtin, $upc) = IsoCodesService::extractUpcGtin([$this->p->partshareprice->gtin, $this->p->partshareprice->upc]);
            if ($gtin != "") {
                $this->c->gtin = $gtin;
                $this->c->gtin_source = 'PSP';
                $new = true;
            }
            if ($upc != "") {
                $this->c->upc = $upc;
                $this->c->upc_source = 'PSP';
            }
        }
        if ((!isset($this->c->gtin) || $this->c->gtin == "") && $this->p->pies) {
            list('single' => $single, 'packages' => $packages) = $this->extractPackagesAndGtinsfromPies();
            if (count($single) > 0) {
                if (isset($single['Package']['PackageLevelGTIN'])) {
                    $this->c->gtin = IsoCodesService::extractGtin($single['Package']['PackageLevelGTIN']);
                    $this->c->gtin_source = 'PIES';
                    $new = true;
                    $this->c->upc = IsoCodesService::extractUpc($single['Package']['PackageLevelGTIN']);
                    $this->c->upc_source = 'PIES';
                }
            }

        }

        if (!isset($this->c->gtin) || $this->c->gtin == "") if (IsoCodesService::extractGtin([$this->p->itemlevel_gtin, $this->p->upc])) {
            $this->c->gtin = IsoCodesService::extractGtin([$this->p->itemlevel_gtin, $this->p->upc]);
            $this->c->gtin_source = 'WHI';
            $new = true;
        }
        if (!isset($this->c->upc) || $this->c->upc == "") if (IsoCodesService::extractUpc([$this->p->itemlevel_gtin, $this->p->upc])) {
            $this->c->upc = IsoCodesService::extractUpc([$this->p->itemlevel_gtin, $this->p->upc]);
            $this->c->upc_source = 'WHI';
        }
        if ($new === true && $this->c->gtin !== null && $this->c->gtin !== "") {
            if (Product::where('gtin', $this->c->gtin)->where('part_number_unformatted', '<>', $this->c->part_number_unformatted)->count()) {
                $this->c->gtin = "";
                $this->c->upc = "";
                $this->c->gtin_source .= 'DUP';
                $this->c->upc_source .= 'DUP';
            }

        }

    }

    public function getExpis()
    {


        if (!isset($this->expis)) {
            if ($this->has_pies) {
                foreach ($this->p->pies->expis as $x) {
                    $this->expis[$x->EXPICode] = $x->value;
                }
            } else {
                $this->expis = [];
            }
        }
    }

    public function setPartStatus()
    {
        $this->c->part_status = $this->p->part_status;
    }

    public function setQtyUOM()
    {

        if ($this->has_pies && $this->p->pies && isset($this->p->pies->UOM) && $this->p->pies->UOM !== "") {
            $this->c->qty_uom = $this->p->pies->UOM;
        } elseif ($this->p->partshareprice && isset($this->p->partshareprice->ItemUOM) && $this->p->partshareprice->ItemUOM != "") {
            $this->c->qty_uom = $this->p->partshareprice->ItemUOM;
        } else {
            $this->c->qty_uom = $this->p->qty_uom;
        }

    }

    public function updateImage()
    {
        if ($this->c->image_source == 'FBS' && $this->c->image_url != "") {
            return true;
        } else {
            $this->setImage();
        }
    }

    public function setImage($force = false)
    {
        if (isset($this->settings['skip_image']) && $this->settings['skip_image'] == true) {
            return;
        }
        if ($force) {
            $this->c->image_url = '';
            $this->c->has_image = 0;
            $this->c->image_source = '';

        }
        if ($this->c->aaiabrandid == "" || $this->c->aaiabrandid == null) {
            return false;
        }
        if (isset($this->c->image_url) && $this->c->image_url != "" && $this->c->image_source == "FBS") {
            $this->c->has_image = 1;
            if (!$force) {
                return true;
            }
        }
        if (!$force && (isset($this->p->image->filename) && $this->p->image->filename != "")) {
            $this->c->image_url = "https://images-us1.sophio.com/resized/" . $this->p->image->aaiabrandid . '/' . $this->p->image->filename;
            $this->c->image_source = 'FBS';
            $this->c->has_image = 1;
            return true;
        }
        $took_from_pies = false;
        $is_primary = 0;
        if ($this->has_pies) {
            if ($force || ((!isset($this->c->image_url) || $this->c->image_url == ""))) {
                $assets = $this->p->pies->digitalassets;

                foreach ($assets as $a) {
                    if ($a->FileType == 'JPG' && $a->Representation == 'A' && $a->AssetType == 'P04' && $a->URI != "" && $a->FileName != "") {
                        $this->c->image_url = $a->URI;
                        $this->c->has_image = 1;
                        $this->c->image_source = 'PIES';
                        $is_primary = 1;
                        $took_from_pies = true;
                        break;
                    } elseif ($a->FileType == 'JPG' && $a->Representation == 'A' && $a->URI != "" && $a->FileName != "" && in_array($a->AssetType, ['P01', 'P02', 'P03', 'P05', 'P06', 'P07',])) {

                        $this->c->image_url = $a->URI;
                        $this->c->has_image = 1;
                        $this->c->image_source = 'PIES';
                        $took_from_pies = true;
                    }
                }
            }
        }
        if ($this->c->image_url && !$force) {
        } elseif (($force || (!isset($this->c->image_url) || $this->c->image_url == '')) && isset($this->p->imageprimary->image_url) && $this->p->imageprimary->image_url != "") {
            $this->c->image_url = $this->p->imageprimary->image_url;
            $this->c->image_source = 'WHI';
            $imgparts = array_values(array_filter(explode('/', $this->p->imageprimary->image_url)));

            $this->c->has_image = 1;
            try {
                $sizer = new ImageCrawlResizer();
                $resized = $sizer->addImage([
                    'image_url' => $this->p->imageprimary->image_url,
                    'aaiabrandid' => $this->c->aaiabrandid,
                    'part_number' => $this->c->part_number,
                    'part_number_unformatted' => $this->c->part_number_unformatted,
                    'upc_int' => ((int)$this->c->upc != 0) ? (int)$this->c->upc : (int)$this->c->gtin,
                    'filename' => $imgparts[count($imgparts) - 1],
                    'original_path' => "original/" . $this->c->aaiabrandid . "/" . $imgparts[count($imgparts) - 1],
                    'primary' => 1,
                    'source' =>  $this->c->image_source ,
                    'product_id'=>$this->c->id
                ]);
            } catch (\Throwable $e) {
            }
            if ($resized['resized'] == 1) {
                $this->c->image_url = "https://images-us1.sophio.com/" . $resized['original_path'];
                $this->c->image_source = "FBS";
                $this->c->has_image = 1;
            }
        }
        if (isset($this->c->image_url) && $this->c->image_url != "") {
            $imgparts = array_filter(explode('/', $this->c->image_url));
            $this->c->has_image = 1;
            try {
                $sizer = new ImageCrawlResizer();
                $resized = $sizer->addImage([
                    'image_url' => $this->c->image_url,
                    'aaiabrandid' => $this->c->aaiabrandid,
                    'part_number' => $this->c->part_number,
                    'part_number_unformatted' => $this->c->part_number_unformatted,
                    'upc_int' => ((int)$this->c->upc != 0) ? (int)$this->c->upc : (int)$this->c->gtin,
                    'filename' => $imgparts[count($imgparts) - 1],
                    'original_path' => "original/" . $this->c->aaiabrandid . "/" . $imgparts[count($imgparts) - 1],
                    'primary' => $is_primary,
                    'source' =>  $this->c->image_source ,
                    'product_id'=>$this->c->id
                ]);
            } catch (\Throwable $e) {
            }
            if ($resized['resized'] == 1) {
                $this->c->image_url = "https://images-us1.sophio.com/" . $resized['original_path'];
                $this->c->image_source = "FBS";
                $this->c->has_image = 1;
            }
        } else {
            $this->c->has_image = 0;
        }
    }

    public function setFitments()
    {
        if (isset($this->p->seofitment->fitment_1)) {
            $this->c->has_seo_fitment = 1;
        }
    }

    public function setMinOrderQty()
    {
        if (isset($this->p->partshareprice->part_number)) {
            $this->c->min_order_qty = $this->p->partshareprice->minorderqty;
        } else {
            $this->c->min_order_qty = $this->p->min_order_qty;
        }

    }

    public function getPiesAttribute()
    {
        if (!isset($this->pies_attrs)) {
            if ($this->has_pies) {
                $this->pies_attrs = $this->p->pies->attributes;
            }
        }

    }

    /**
     * @param $label
     * @param $all whenever to return all values or only first (default)
     * @return array|mixed|null
     */
    public function getWhiAttribute($label, $all = false)
    {
        if (!isset($this->product_attributes)) {
            if ($this->p->productattributes) {
                foreach ($this->p->productattributes as $a) {
                    if (!isset($this->product_attributes[$a->label])) {
                        $this->product_attributes[$a->label] = [];
                    }
                    $this->product_attributes[$a->label][] = $a->value;
                }
            }
        }
        $labels = [];
        if (is_array($label)) {
            $labels = $label;
        } else {
            $labels = [$label];
        }
        if (is_array($this->product_attributes)) {
            $int = array_intersect(array_keys($this->product_attributes), $labels);
            if (count($int) > 0) {
                $ret = [];
                foreach ($int as $in) {
                    foreach ($this->product_attributes[$in] as $a) {
                        $ret[] = $a;
                    }

                }
                if ($all == true) {
                    return $ret;
                } else {
                    return reset($ret);
                }
            }
        }


        return null;
    }

    public function setProp65()
    {
        $cal65_found = false;
        if ($this->has_pies) {
            $this->getPiesAttribute();
            foreach ($this->pies_attrs as $a) {
                if (stripos($a->AttributeID, 'Proposition 65') !== false || stripos($a->AttributeID, 'Prop 65') !== false) {
                    if ($a->value != "") {

                        $cal65_found = true;
                        $this->c->prop65require = 'Y';
                        $this->c->prop65text = $a->value;
                        $this->c->prop65_source = 'PIES';
                    }
                }
            }
        }
        if ($cal65_found == false) {
            if (isset($this->p->partshareprice->part_number)) {
                if ($this->p->partshareprice->CalifProp65Required == "Y") {

                    $cal65_found = true;
                    $this->c->prop65require = 'Y';
                    $this->c->prop65text = $this->p->partshareprice->CalifProp65Text;
                    $this->c->prop65_source = 'PSP';
                }
            }
        }
        if ($cal65_found == false) {
            /*
            if ($this->p->aaiabrandid != null && $this->p->aaiabrandid != "" && isset($this->p->flattendata->PID)) {
                if ($this->p->flattendata->CalifProp65Required == "Y") {

                    $cal65_found = true;
                    $this->c->prop65require = 'Y';
                    $this->c->prop65text = $this->p->flattendata->CalifProp65;
                    $this->c->prop65_source = 'HQ';
                }
            }
            */
        }
        if ($cal65_found == false) {
            $wh_cal = $this->getWhiAttribute([
                'California Proposition 65',
                'Prop 65 Flag',
                'California_Prop 65',
                'CA Prop 65',
                'Prop 65 (Yes/No)',
                'Prop 65 Applicable',
                'Prop 65 Flag',
                'Prop 65 Warning Label Req',
                'Prop 65 Warning Required',
                'Prop 65 Yes/No',
                'Proposition 65 Compliant',
                'Subject to Prop 65 (Yes/N',
                'WARNING CA Proposition 65'
            ]);

            if ($wh_cal != null && $wh_cal != "") {
                $cal65_found = true;

                if (strtolower($wh_cal) == "y" || strtolower($wh_cal) == "n" || strtolower($wh_cal) == "yes" || strtolower($wh_cal) == "no") {
                    $this->c->prop65require = $wh_cal;
                } else {
                    $this->c->prop65require = 'Y';

                }
                $this->c->prop65_source = 'WHIA';
            }

        }
        $wh_cal = $this->getWhiAttribute([
            'CA Prop 65 Type',
            'Prop 65 Flag',
            'California Prop 65 Warnin',
            'California Proposition 65',
            'California_Prop 65 Messag',
            'CA_Prop65_Lead',
            'CA_Prop65_Nickel',
            'Prop 65',
            'Prop 65 - Long Label',
            'Prop 65 - Short Label',
            'Prop 65 Potential Effects',
            'Prop 65 Warning',
            'Prop 65 Warning Cancer',
            'Prop 65 Warning Text',
            'PROP 65 WARNING:',
            'Prop65',
            'PROP65 WARNING',
            'Proposition 65 Warning'
        ]);
        if ($wh_cal != null && $wh_cal != "") {
            $cal65_found = true;
            $this->c->prop65text = $wh_cal;
            $this->c->prop65require = 'Y';
            $this->c->prop65_source = 'WHIA';
        }
        if ($cal65_found == false) {
            $this->c->prop65require = 'N';
            $this->c->prop65text = null;
            $this->c->prop65_source = null;

        }
    }

    public function setMaterials()
    {
        $ar = [];
        if ($this->has_pies) {
            $this->getPiesAttribute();

            foreach ($this->pies_attrs as $a) {

                if (in_array((int)$a->AttributeID, $this->materials_paids)) {
                    $ar[] = $a->value;
                }
            }
            if (count($ar) > 0) {
                $this->c->materials = implode(', ', $ar);
            }
        }
        if (count($ar) == 0) {
            $wh_mats = $this->getWhiAttribute('Materials', true);
            if ($wh_mats != null) {
                $this->c->materials = implode(', ', $wh_mats);
            }
        }
    }

    public function setBullets()
    {
        if( $this->c->bullets_source !=='FAKE'){
            return;
        }
        $bullets = [];
        if ($this->has_pies) {
            if ($this->p->pies->custommarketcontent) {
                if ($this->p->pies->custommarketcontent->bullet01 != "") {
                    if (Str::contains(mb_strtolower($this->p->pies->custommarketcontent->bullet01), $this->banned_words) === false) {
                        $bullets[] = $this->p->pies->custommarketcontent->bullet01;
                    }
                }
                if ($this->p->pies->custommarketcontent->bullet02 != "") {
                    if (Str::contains(mb_strtolower($this->p->pies->custommarketcontent->bullet02), $this->banned_words) === false) {
                        $bullets[] = $this->p->pies->custommarketcontent->bullet02;
                    }
                }
                if ($this->p->pies->custommarketcontent->bullet03 != "") {
                    if (Str::contains(mb_strtolower($this->p->pies->custommarketcontent->bullet03), $this->banned_words) === false) {
                        $bullets[] = $this->p->pies->custommarketcontent->bullet03;
                    }
                }
                if ($this->p->pies->custommarketcontent->bullet04 != "") {
                    if (Str::contains(mb_strtolower($this->p->pies->custommarketcontent->bullet04), $this->banned_words) === false) {
                        $bullets[] = $this->p->pies->custommarketcontent->bullet04;
                    }
                }
                if (count($bullets) > 0) {

                    $this->c->bullets_source = 'PCM';
                }
            }

            if (count($bullets) == 0 && isset($this->p->pies->marketcontent) && isset($this->p->pies->marketingextract->bullets)) {
                $bullets = json_decode($this->p->pies->marketingextract->bullets, true);
                if (count($bullets) > 0) {

                    $this->c->bullets_source = 'PIES';
                }
            }

            if (count($bullets) == 0) {
                $this->getPiesAttribute();
                foreach ($this->pies_attrs as $a) {
                    if (stripos($a->AttributeID, 'Features and Benefits') !== false) {
                        if ($a->value != "") {
                            $bullets[] = $a->value;
                            $this->c->bullets_source = 'PIES';
                        }
                    }
                }
            }
        }
        if (count($bullets) == 0) {
            $buls = $this->getWhiAttribute('FAB', true);
            if ($buls != null) {
                $bullets = array_values($buls);
            } else {
                $buls = $this->getWhiAttribute([
                    'Features &amp; Benefits',
                    'Features and Benefits',
                    'Features And Benefits 1',
                    'Features And Benefits 2',
                    'Features And Benefits 3',
                ], true);
                if ($buls != null) {
                    $bullets = array_values($buls);
                }
            }
            if (count($bullets) > 0) {
                $this->c->bullets_source = 'WHIA';
            }
        }
        if (count($bullets) == 0) {
            if($this->p &&  $this->p->aaiabrand) {
                for ($i = 1; $i <= 4; $i++) {
                    $enrich = \DB::select("select enrichment.clean_bullet(enrichment.fake_bullet(?,?,?,?)) as bullet", [
                        $i,
                        isset($this->p->aaiabrand->BrandName) ? $this->p->aaiabrand->BrandName : $this->p->mfg_code,
                        isset($this->p->parttype->PartTerminologyName) ? $this->p->parttype->PartTerminologyName : $this->p->part_label,
                        1
                    ]);

                    $bullets[] = $enrich[0]->bullet;
                }
                $this->c->bullets_source = 'FAKE';
            }

        }
        if (count($bullets) > 0) {
            $this->c->bullets = $bullets;
        }
    }

    public function setDescription()
    {
        if ($this->c->long_description_source !== "FAKE") {
            return;
        }
        $description = "";
        $part_name = "";
        if ($this->has_pies) {
            if ($this->p->pies->custommarketcontent) {
                if (stripos($this->p->pies->custommarketcontent->marketingcopy, 'liquid') || stripos($this->p->pies->custommarketcontent->marketingcopy, 'chemical') || stripos($this->p->pies->custommarketcontent->marketingcopy, 'refrigerant')) {
                    $description = $this->p->part_label;
                } else {

                }
                $description = $this->p->pies->custommarketcontent->marketingcopy;
                $part_name = $this->p->pies->custommarketcontent->name;

                $productName = $this->p->pies->custommarketcontent->aaiabrandname . " " . $this->p->part_number . " " . $part_name;
                $this->c->long_description = $description;
                $this->c->long_description_source = 'PCM';
                $this->c->description = $description;
                $this->c->description_source = 'PCM';
                $this->c->part_name = $part_name;
                $this->c->part_name_source = 'PCM';
                $this->c->product_name = $productName;
                $this->c->product_name_source = 'PCM';
            } elseif ($this->p->pies->marketingextract) {
                if (stripos($this->p->pies->marketingextract->marketingcopy, 'liquid') || stripos($this->p->pies->marketingextract->marketingcopy, 'chemical') || stripos($this->p->pies->marketingextract->marketingcopy, 'refrigerant')) {
                    $description = $this->p->part_label;
                } else {

                }
                $description = $this->p->pies->marketingextract->marketingcopy;

                if ($this->p->pies->descriptions) {
                    foreach ($this->p->pies->descriptions as $a) {
                        if ($a->DescriptionCode == "LAB" && ($a->LanguageCode == "" || $a->LanguageCode == "EN")) {
                            $part_name = $a->value;
                        }
                    }
                }
                if ($part_name == "") {
                    $part_name = $this->p->part_label;
                }


                if (isset($this->p->aaiabrand->BrandName)) {
                    $productName = explode(" ", $this->p->aaiabrand->BrandName)[0] . " " . $this->p->part_number . " " . $part_name;
                } else {
                    $productName = $this->p->mfg_code . " " . $this->p->part_number . " " . $part_name;
                }
                $this->c->long_description = $description;
                $this->c->long_description_source = 'PIES';
                $this->c->description = $description;
                $this->c->description_source = 'PIES';
                $this->c->part_name = $part_name;
                $this->c->part_name_source = 'PIES';
                $this->c->product_name = $productName;
                $this->c->product_name_source = 'PIES';
            }
        } else {
            $desc_found = false;
            $label_found = false;
            $wh_desc = $this->getWhiAttribute('MKT');
            if ($wh_desc != null && $wh_desc != "") {
                $this->c->long_description = $wh_desc;
                $this->c->long_description_source = 'WHIA';
                $this->c->description = $wh_desc;
                $this->c->description_source = 'WHIA';
                $description = $wh_desc;
                $desc_found = true;
            }
            /*
            $wh_label = $this->getWhiAttribute('LAB');
            if($wh_label!=null && $wh_label!='') {
                $this->c->part_name = $wh_label;
                $this->c->part_name_source = 'WHIA';
                $label_found = true;
            }
            */

            if ($desc_found == false) {

                $this->c->long_description = $this->p->part_label;
                $this->c->long_description_source = 'WHI';
                $this->c->description = $this->p->part_label;
                $this->c->description_source = 'WHI';
            }
            if ($label_found == false) {
                $part_name = $this->p->part_label;
                $this->c->part_name = $part_name;
                $this->c->part_name_source = 'WHI';
            }


            if (isset($this->p->aaiabrand->BrandName)) {
                $productName = explode(" ", $this->p->aaiabrand->BrandName)[0] . " " . $this->p->part_number . " " . $part_name;
            } else {
                $productName = $this->p->mfg_code . " " . $this->p->part_number . " " . $part_name;
            }


            $this->c->product_name = $productName;
            $this->c->product_name_source = $this->c->part_name_source;
        }
        if (strlen($description) == 0 || $description == "") {

            $enrich = \DB::select("select enrichment.clean_mc(enrichment.pickamarketing(
            ?,?,?,?,enrichment.fake_mc(?,?,1 ),?,?)) as description", [
                isset($this->p->pies->brand->use_custom) ? 1 : 0,
                isset($this->p->pies->brand->use_pies) ? 1 : 0,
                isset($pies_custommarket[0]->marketingcopy) ? $pies_custommarket[0]->marketingcopy : '',
                isset($pies_market[0]->marketingcopy) ? $pies_market[0]->marketingcopy : '',
                isset($this->p->aaiabrand->BrandName) ? $this->p->aaiabrand->BrandName : $this->p->mfg_code,
                isset($this->p->parttype->PartTerminologyName) ? $this->p->parttype->PartTerminologyName : $this->p->part_label,
                isset($this->p->aaiabrand->BrandName) ? $this->p->aaiabrand->BrandName : $this->p->mfg_code,
                $this->p->part_label,
            ]);
            $description = $enrich[0]->description;
            $this->c->description = $description;
            $this->c->description_source = 'FAKE';
            $this->c->long_description = $description;
            $this->c->long_description_source = 'FAKE';
        }
    }

    public function setWarranty()
    {
        $warranty = [];
        $this->getExpis();
        if (isset($this->expis['WT1']) && isset($this->expis['WT2'])) {
            $warranty[$this->expis['WT2']] = $this->expis['WT1'];
        }
        if (isset($this->expis['WD1']) && isset($this->expis['WD2'])) {
            $warranty[$this->expis['WD2']] = $this->expis['WD1'];
        }
        $wh_wt1 = $this->getWhiAttribute('WT1');
        $wh_wt2 = $this->getWhiAttribute('WT2');
        $wh_wd1 = $this->getWhiAttribute('WD1');
        $wh_wd2 = $this->getWhiAttribute('WD2');
        $wh_ws1 = $this->getWhiAttribute('WS1');
        $wh_ws2 = $this->getWhiAttribute('WS2');
        if ($wh_wt1 != null && $wh_wt2 != null) {

            $warranty[$wh_wt2] = $wh_wt1;
        }
        if ($wh_wd1 != null && $wh_wd2 != null) {
            $warranty[$wh_wd2] = $wh_wd1;
        }
        if ($wh_ws1 != null && $wh_ws2 != null) {
            $warranty[$wh_ws2] = $wh_ws1;
        }
        $this->c->warranty = json_encode($warranty);
        return $warranty;
    }

    public function setCountry()
    {
        if ($this->has_pies) {
            if (!isset($this->c->countryoforigin)) {
                if (isset($this->p->pies->custommarketcontent->id)) {
                    if ($this->p->pies->custommarketcontent->country != "") {
                        $this->c->countryoforigin = $this->p->pies->custommarketcontent->country;
                        $this->c->countryoforigin_source = 'CMC';
                    }
                }
            }
            if (!isset($this->c->countryoforigin)) {
                $this->getExpis();
                if (isset($this->expis['CTO'])) {
                    $this->c->countryoforigin = $this->expis['CTO'];
                    $this->c->countryoforigin_source = 'PIES';
                }
            }
        }
        if (!isset($this->c->countryoforigin)) {
            /*
            if ($this->p->aaiabrandid != null && $this->p->aaiabrandid != "" && isset($this->p->flattendata->PID)) {
                if ($this->p->flattendata->CountryOfOrigin != "") {
                    $this->c->countryoforigin = $this->p->flattendata->CountryOfOrigin;
                    $this->c->countryoforigin_source = 'FHQ';
                }
            }
            */
        }
    }

    public function extractPackagesAndGtinsfromPies()
    {
        $packages = [];
        $single = [];
        if ($this->has_pies) {
            if (isset($this->p->pies->packages[0])) {
                foreach ($this->p->pies->packages as $p) {
                    if (in_array($p->extra['Package']['PackageUOM']['value'], ['EA', 'BX'])) {
                        $single = $p->extra;
                    } else {
                        $packages[] = $p->extra;
                    }
                }
            }
        }

        return [
            'single' => $single,
            'packages' => $packages
        ];
    }

    public function setDimensions()
    {
        /*
        $this->c->dimensions_source = '';
        $this->c->width = null;
        $this->c->height = null;
        $this->c->length = null;
        $this->c->weight = null;
        $this->c->oversized = 0;
        $this->c->overweight = 0;
        */
        $has_dimensions = false;
        $dim = [];
        if (isset($this->p->partshareprice)) {
            if ($this->p->partshareprice->Height > 0 && $this->p->partshareprice->Width > 0 && $this->p->partshareprice->Depth > 0 && $this->p->partshareprice->Weight > 0) {
                $dims = [
                    'height' => $this->p->partshareprice->Height,
                    'width' => $this->p->partshareprice->Width,
                    'length' => $this->p->partshareprice->Depth,
                    'weight' => $this->p->partshareprice->Weight
                ];
                if ($this->p->partshareprice->DimUOMCode === "CM") {
                    $dims['height'] = $dims['height'] * 0.393701;
                    $dims['width'] = $dims['width'] * 0.393701;
                    $dims['length'] = $dims['length'] * 0.393701;
                }
                if ($this->p->partshareprice->WeightUOMCode === "GT") {
                    $dims['weight'] = $dims['weight'] * 2.20462;
                }
                $this->c->has_dimensions = 1;
                $has_dimensions = true;
                $this->c->dimensions_source = 'PSP';
                $this->c->width = (float)$dims['width'];
                $this->c->height = (float)$dims['height'];
                $this->c->length = (float)$dims['length'];
                $this->c->weight = (float)$dims['weight'];
            }
        }
        if ($this->has_pies && $has_dimensions == false) {

            if (isset($this->p->pies->packages[0])) {

                try {

                    list('single' => $dim, 'packages' => $packages) = $this->extractPackagesAndGtinsfromPies();

                    if (count($dim) > 0) {
                        if (
                            isset($dim['Package']['Dimensions']['Length']['value']) && (float)$dim['Package']['Dimensions']['Length']['value'] > 0 &&
                            isset($dim['Package']['Dimensions']['Width']['value']) && (float)$dim['Package']['Dimensions']['Width']['value'] > 0 &&
                            isset($dim['Package']['Dimensions']['Height']['value']) && (float)$dim['Package']['Dimensions']['Height']['value'] > 0 &&
                            isset($dim['Package']['Weights']['Weight']['value']) && (float)$dim['Package']['Weights']['Weight']['value'] > 0
                        ) {
                            $this->c->has_dimensions = 1;
                            $has_dimensions = true;

                            $dims = [
                                'height' => $dim['Package']['Dimensions']['Height']['value'],
                                'width' => $dim['Package']['Dimensions']['Width']['value'],
                                'length' => $dim['Package']['Dimensions']['Length']['value'],
                                'weight' => $dim['Package']['Weights']['Weight']['value']
                            ];
                            if (isset($dim['Package']['Dimensions']['attributes']['UOM']) && $dim['Package']['Dimensions']['attributes']['UOM'] === "CM") {
                                $dims['height'] = $dims['height'] * 0.393701;
                                $dims['width'] = $dims['width'] * 0.393701;
                                $dims['length'] = $dims['length'] * 0.393701;
                            }
                            if (isset($dim['Package']['Weights']['attributes']['UOM']) && $dim['Package']['Weights']['attributes']['UOM'] == "GT") {
                                $dims['weight'] = $dims['weight'] * 2.20462;
                            }

                            $this->c->dimensions_source = 'PIES';
                            $this->c->width = (float)$dims['width'];
                            $this->c->height = (float)$dims['height'];
                            $this->c->length = (float)$dims['length'];
                            $this->c->weight = (float)$dims['weight'];
                        }
                        if (
                            isset($dim['Package']['Dimensions']['MerchandisingLength']['value']) && (float)$dim['Package']['Dimensions']['MerchandisingLength']['value'] > 0 &&
                            isset($dim['Package']['Dimensions']['MerchandisingWidth']['value']) && (float)$dim['Package']['Dimensions']['MerchandisingWidth']['value'] > 0 &&
                            isset($dim['Package']['Dimensions']['MerchandisingHeight']['value']) && (float)$dim['Package']['Dimensions']['MerchandisingHeight']['value'] > 0 &&
                            isset($dim['Package']['Weights']['Weight']['value']) && (float)$dim['Package']['Weights']['Weight']['value'] > 0
                        ) {
                            $this->c->has_dimensions = 1;
                            $has_dimensions = true;
                            $dims = [
                                'height' => $dim['Package']['Dimensions']['MerchandisingHeight']['value'],
                                'width' => $dim['Package']['Dimensions']['MerchandisingWidth']['value'],
                                'length' => $dim['Package']['Dimensions']['MerchandisingLength']['value'],
                                'weight' => $dim['Package']['Weights']['Weight']['value']
                            ];
                            if (isset($dim['Package']['Dimensions']['attributes']['UOM']) && $dim['Package']['Dimensions']['attributes']['UOM'] === "CM") {
                                $dims['height'] = $dims['height'] * 0.393701;
                                $dims['width'] = $dims['width'] * 0.393701;
                                $dims['length'] = $dims['length'] * 0.393701;
                            }
                            if (isset($dim['Package']['Weights']['attributes']['UOM']) && $dim['Package']['Weights']['attributes']['UOM'] === "GT") {
                                $dims['weight'] = $dims['weight'] * 2.20462;
                            }
                            $this->c->dimensions_source = 'PIES';
                            $this->c->width = (float)$dims['width'];
                            $this->c->height = (float)$dims['height'];
                            $this->c->length = (float)$dims['length'];
                            $this->c->weight = (float)$dims['weight'];

                        }
                    }

                } catch (\Exception $e) {
                    Log::channel('walmart')->error($e->getMessage());
                }
            }
        }
        if ($has_dimensions == false && $this->p->height > 0 && $this->p->width > 0 && $this->p->length && $this->p->weight > 0) {

            $this->c->has_dimensions = 1;
            $dims = [
                'height' => $this->p->height,
                'width' => $this->p->width,
                'length' => $this->p->length,
                'weight' => $this->p->weight
            ];

            $this->c->dimensions_source = 'WHI';
            $this->c->width = (float)$this->p->width;
            $this->c->height = (float)$this->p->height;
            $this->c->length = (float)$this->p->length;
            $this->c->weight = (float)$this->p->weight;

        }

        if (isset($dims)) {
            if ($dims['weight'] > 150) {
                $this->c->overweight = 1;
            } else {
                $this->c->overweight = 0;
            }
            unset($dims['weight']);
            rsort($dims);
            $this->c->oversized = 0;
            if ($dims[0] > 108) {
                $this->c->oversized = 1;
            }
            if (($dims[0] + 2 * $dims[1] + 2 * $dims[2]) > 165) {
                $this->c->oversized = 1;
            }

        }
        return $this->c->isDirty();
    }

    public function setPackage()
    {
        if ($this->has_pies) {
            if (!$this->c->id) {
                $this->c->save();
            }
            if (!$this->c->package) {
                if ($this->p->pies->packages) {
                    list('single' => $dim, 'packages' => $packages) = $this->extractPackagesAndGtinsfromPies();
                    foreach ($packages as $dim) {
                        $cp = Package::create(['product_id' => $this->c->id]);
                        $cp->save();

                        //$cp->gtin = $package->PackageLevelGTIN;
                        $cp->gtin = $dim['Package']['PackageLevelGTIN'];

                        // $cp->uom = $upackage->Package->PackageUOM->value;
                        $cp->uom = $dim['Package']['PackageUOM']['value'];
                        //$cp->qty = $upackage->Package->QuantityofEaches->value;
                        $cp->qty = $dim['Package']['QuantityofEaches']['value'];
                        //$cp->weight = $upackage->Package->Weights->Weight->value;
                        $cp->weight = $dim['Package']['Weights']['Weight']['value'];
                        //$cp->weight_uom = $upackage->Package->Weights->attributes->UOM;
                        $cp->weight_uom = $dim['Package']['Weights']['attributes']['UOM'];
                        if (isset($dim['Package']['Weights']['DimensionalWeight'])) {
                            //$cp->dimensional_weight = $upackage->Package->Weights->DimensionalWeight->value;
                            $cp->weight_uom = $dim['Package']['Weights']['DimensionalWeight']['value'];
                        }

                        if (
                            isset($dim['Package']['Dimensions']['Length']['value']) && (float)$dim['Package']['Dimensions']['Length']['value'] > 0 &&
                            isset($dim['Package']['Dimensions']['Width']['value']) && (float)$dim['Package']['Dimensions']['Width']['value'] > 0 &&
                            isset($dim['Package']['Dimensions']['Height']['value']) && (float)$dim['Package']['Dimensions']['Height']['value'] > 0 &&
                            isset($dim['Package']['Weights']['Weight']['value']) && (float)$dim['Package']['Weights']['Weight']['value'] > 0
                        ) {
                            $cp->width = $dim['Package']['Dimensions']['Width']['value'];
                            $cp->height = $dim['Package']['Dimensions']['Height']['value'];
                            $cp->length = $dim['Package']['Dimensions']['Length']['value'];
                            $cp->dimension_uom = $dim['Package']['Dimensions']['attributes']['UOM'];
                        }
                        if (
                            isset($dim['Package']['Dimensions']['MerchandisingLength']['value']) && (float)$dim['Package']['Dimensions']['MerchandisingLength']['value'] > 0 &&
                            isset($dim['Package']['Dimensions']['MerchandisingWidth']['value']) && (float)$dim['Package']['Dimensions']['MerchandisingWidth']['value'] > 0 &&
                            isset($dim['Package']['Dimensions']['MerchandisingHeight']['value']) && (float)$dim['Package']['Dimensions']['MerchandisingHeight']['value'] > 0 &&
                            isset($dim['Package']['Weights']['Weight']['value']) && (float)$dim['Package']['Weights']['Weight']['value'] > 0
                        ) {
                            $cp->width = $dim['Package']['Dimensions']['MerchandisingLength']['value'];
                            $cp->height = $dim['Package']['Dimensions']['MerchandisingLength']['value'];
                            $cp->length = $dim['Package']['Dimensions']['MerchandisingLength']['value'];
                            $cp->dimension_uom = $dim['Package']['Dimensions']['attributes']['UOM'];

                        }
                        $cp->source = 'PIES';
                        $cp->save();

                    }
                }
            }
        }
    }

    public function save()
    {

        $this->c->save();

    }
}