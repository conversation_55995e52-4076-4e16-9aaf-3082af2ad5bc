<?php

namespace App\Library\Sophio\Catalog\Manticore;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ManticoreReindexClientIdJob
    implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    public $stmntid;
    public $timeout = 0;
    public $tries = 1;
    protected $database;
    protected $clientId;
    protected $email;
    public function __construct($clientid,$database,$email)
    {
        $this->clientId = $clientid;
        $this->database = $database;
        $this->email = $email;
        $this->onQueue('longtasks');
    }
    public function handle()
    {
        Log::error($this->database);
        Log::error("call ".$this->database.".update_partmasterclientid()");
        DB::unprepared( 'call '.$this->database.'.update_partmasterclientid()');
        Http::post('https://'.config('sophio.cronicle.domain').'/api/app/run_event/v1?id='.config('sophio.cronicle.reindex_manticore').'&api_key='.config('sophio.cronicle.api_key'),
            ["id"=>config('sophio.cronicle.reindex_manticore'),"notify_success"=>$this->email]);
    }
}