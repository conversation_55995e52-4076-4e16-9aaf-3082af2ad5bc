<?php

namespace App\Library\Sophio\Catalog;

use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use OpenSpout\Reader\Common\Creator\ReaderEntityFactory;
use Sophio\Common\Models\Catalog\VisionDCF;
use Sophio\Common\Models\FBS\Customer;
use Sophio\Common\Models\FBS\Invoice;
use Sophio\Common\Models\FBS\LineItem;
use Sophio\Common\Models\FBS\Supplier;
use Sophio\Common\Models\PIM\Product;
use Sophio\Common\Repository\Settings;
use Sophio\FBSOrder\Library\Actions\SaveTracking;

class TrackingManager
{
    protected $sup;
    protected $settings;
    protected $file;

    public function __construct($settings)
    {
        $this->settings = $settings;
        $this->sup = Supplier::find(199);
    }

    public function getFileFromFTP($date, $filename = 'te-tracking-_',$force_filename=null)
    {
        $sup = Supplier::find(199);
        $connect = [
            'host' => $sup->SETTINGS['FTPLOCATION'],
            'username' => $sup->SETTINGS['FTPUSERID'],
            'password' => $sup->SETTINGS['FTPPASSWORD'],
            'port' => 21,
            'timeout' => 60
        ];
        $storage = Storage::createFtpDriver($connect);
        if($force_filename!==null) {
            $remote_filename = $force_filename;
        }else{
            $remote_filename = $filename . $date->format('m-d-y') . '.csv';
        }


        try {
            $exists = $storage->exists($remote_filename);
        } catch (\ErrorException $e) {
            echo "Did not find " . $remote_filename . "\n";
            $exists = false;
        } catch (\Exception $e) {
            echo "Did not find " . $remote_filename . "\n";
            $exists = false;
        }
        if ($exists) {
            echo "Found " . $remote_filename . "\n";
            $file = $storage->get($remote_filename);
            Storage::disk('fbs')->put('te/' . $remote_filename, $file);
            $this->file = 'te/' . $remote_filename;
            return true;
        } else {

            echo "Did not find " . $remote_filename . "\n";
            return false;
        }
    }

    public function processTrackRow($track)
    {
        $customer = Customer::where('accountnum', $track->custpk)->first();
        if (!$customer) {
            $customer = Customer::create([
                'pk' => $track->custpk,
                'storepk' => 300001,
                'accountno' => $track->custpk,
                'accountnum' => $track->custpk,
                'company' => $track->st_name,
                'st_company' => $track->st_name,
                'st_addr' => $track->st_addr,
                'st_addr2' => $track->st_addr2,
                'st_city' => $track->st_city,
                'st_state' => $track->st_state,
                'st_zip' => $track->st_zip,
                'st_phone' => $track->st_phone,

                'custtype' => 'MEM',
                'paymethod' => 'OA',
                'XML' => [
                    'NOSHIPTOOVERRIDE' => "",
                    'ADJUSTCOST' => '',
                    'USEDATAFEEDCOST' => '',
                    'SALERSREP_MAXORDERSIZE' => 100000,
                    'USEPARTSHARECOST' => ''
                ],
                'active' => 'T',
                'created' => Carbon::now()
            ]);

        }
        $line = LineItem::where('custpk', $track->custpk)->whereHas('invoice', function ($q) use ($track) {
            $q->where('ponumber', $track->ponumber);
        })
            ->where('sku', $track->sku)
            ->first();
        if ($line) {
            echo "Found lineitem for PO ".$track->ponumber." sku ".$track->sku."\n";
            $this->doShipment($track, $line);
        } else {
            echo "Could not found lineitem for PO ".$track->ponumber." sku ".$track->sku."\n";
            $invoices = Invoice::where('ponumber', $track->ponumber)->where('custpk', $track->custpk)->orderBy('invdate', 'desc')->get();

            if ($invoices->count()==0) {
                echo "Create new invoice for  PO ".$track->ponumber."\n";

                $invoice = Invoice::create([
                    'custpk' => $customer->pk,
                    'custtype' => $customer->custtype,
                    'storepk' => 300001,
                    'invno' => $track->ponumber,
                    'ponumber' => $track->ponumber,
                    'invdate' => $track->order_time,
                    'ccresult' => 'APPROVED',
                    'paymethod' => $customer->paymethod,
                    'SHIPPING' => '',
                    'service' => '',
                    'carrier' > '',
                    'handling' => 0,
                    'st_name' => $track->st_name,

                    'st_addr' => $track->st_addr,
                    'st_addr2' => $track->st_addr2,
                    'st_city' => $track->st_city,
                    'st_state' => $track->st_state,
                    'st_zip' => $track->st_zip,
                    'st_phone' => $track->st_phone,

                    'STORENOTES' => 'Imported from sales',
                    'salesrep' => '',
                    'accountnum' => $customer->accountnum,
                    'invtotal' => 0,
                    'invstatus' => 'F',
                    'completed' => $track->order_time,
                    'company' => $customer->company,
                    'heardfrom' => 'IMPORT'

                ]);

            } else {
                $invoice = $invoices->first();
            }
            $v = VisionDCF::where('linecode', $track->linecode)->pluck('aaiabrandid');
            $product = Product::where('part_number_unformatted', unformatString($track->sku))->whereIn('aaiabrandid', $v->all())->first();
            echo "Create new line for  PO ".$track->ponumber." sku ".$track->sku."\n";
            $lineitem = LineItem::create([
                'storepk' => 300001,
                'invpk' => $invoice->pk,
                'custpk' => $customer->pk,
                'sup_ord_id' => $track->sup_ord_id,
                'lineStatus' => 'F',
                'mfr' => $product->aaiabrand->BrandName ?? '',
                'sku' => $track->sku,
                'descript' => $product->short_description ?? '',
                'qty' => $track->qty,
                'price' => (float)$track->price,
                'cost' => $track->price,
                'scost' => $track->price,
                'itemtotal' => $track->price * $track->qty,
                'timein' => $track->order_time,
                'skuform' => $track->sku,
                'sup' => 'FED',
                'itemform' => $track->sku,
                'linecode' => $v[0]??'',
                'wdlinecode' => $track->linecode,
                'image' => '',
                'profilepk' => 199,
                'contactpk' => 201366,
                'upc' => $track->upc??'',
                'qty_ord' => (int)$track->qty,
                'salesrep' => '',
                'timestamp' => $track->order_time,
                'timeout' => $track->order_time
            ]);
            $this->doShipment($track, $lineitem);
        }
    }

    public function doShipment($track, $lineitem)
    {
        $shipment = new \stdClass();
        $shipment->orderId = "";
        $shipment->carrierCode = $track->carrier;
        $shipment->serviceCode = "";
        $shipment->trackingNumber = $track->track_num;
        $shipment->voided = false;
        $shipment->createDate = Carbon::now();
        $shipment->shipmentItems = [];
        $shipmentItem = new \stdClass();
        $shipmentItem->lineItemKey = $lineitem->pk;
        $shipment->shipmentItems[] = $shipmentItem;
        (new SaveTracking($this->settings))($lineitem->invoice, $this->sup->PK, $shipment);
    }

    public function loadFileInTable($file = null)
    {
        if ($file !== null) {
            $this->file = $file;
        }
        ini_set('max_execution_time', 0);
        DB::connection()->getpdo()->exec(sprintf(" LOAD DATA LOCAL INFILE '%s' IGNORE  INTO TABLE ".config('tenant_db').".npw_tracking FIELDS TERMINATED BY ',' OPTIONALLY  ENCLOSED BY '\"' LINES TERMINATED BY '\r\n' IGNORE 1 LINES set price=replace(price,'$','.')", Storage::disk('fbs')->path($this->file)));
        DB::connection()->getpdo()->exec("CALL ".config('tenant_db').".npw_tracking_update()");
    }

    public function processTrackings()
    {


        $tracks = DB::table('npw_tracking')->whereNull('processed')->cursor();
        $i = 0;
        foreach ($tracks as $track) {
            $this->processTrackRow($track);
            DB::table('npw_tracking')->where('id', $track->id)->update(['processed' => Carbon::now()]);


        }
        echo "updated " . $i . " lineitems\n";

    }
}