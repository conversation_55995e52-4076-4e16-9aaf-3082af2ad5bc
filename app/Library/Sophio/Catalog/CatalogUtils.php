<?php

namespace App\Library\Sophio\Catalog;

use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class CatalogUtils
{
    protected $email;
    protected $api_key;

    public function __construct($email)
    {
        $this->api_key = config('sophio.cronicle.api_key');
        $this->email = $email;
    }

    public function  run_cronicle($cron_id)
    {
        $options = ["id"=>$cron_id];
        if(isset($this->email)){
            $options['notify_success'] = $this->email;
        }
        Http::post('https://'.config('sophio.cronicle.domain').'/api/app/run_event/v1?id='.$cron_id.'&api_key='.$this->api_key,$options);
    }
    public function update_partshare()
    {

        $this->run_cronicle(config('sophio.cronicle.update_partshare'));
    }
    public function import_whse()
    {
        $this->run_cronicle(config('sophio.cronicle.importfeeds'));
    }
    public function reindex()
    {
        //actually calls the command that first update partmasterclientid and then chains reindex"
        $this->run_cronicle(config('sophio.cronicle.reindex_manticore'));
    }
    public function clear_web_cache()
    {
        $this->run_cronicle(config('sophio.cronicle.clearwebsitescache'));
    }
    public function clear_api_cache()
    {
        $this->run_cronicle(config('sophio.cronicle.clearapicache'));
    }
    public function awda()
    {
        $exporter = new \App\Library\Sophio\Exporters\AWDASupplierExport(['tenant_db'=>'toolsequipments_aces','website_id'=>2]);
        $exporter->export();
    }
    public function update_from_supplier($supplier)
    {
        DB::statement('CALL ' . config('tenant_db') . '.update_from_supplier(?,?,?,?)', [
            'inventory_' . $supplier->contactpk,
            $supplier->contactpk,
            $supplier->PK,
            $supplier->contactpk,
        ]);
    }
    public function nonapps_discover()
    {
        Artisan::queue($command = 'sophio:nonappsdiscover');
    }
    public function nonappsupdatecontent()
    {
        Artisan::queue($command = 'sophio:nonappsupdatecontent');
    }
}