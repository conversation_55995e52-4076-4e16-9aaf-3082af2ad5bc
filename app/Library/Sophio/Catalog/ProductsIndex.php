<?php

namespace App\Library\Sophio\Catalog;

use App\Library\Manticore\ManticoreSearch;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Manticoresearch\Search;

class ProductsIndex
{
    public function refreshPartMasterClientId($clientId, $parttype = null, $mfgkey = null)
    {

        if ($parttype !== null) {
            $parttypeCond = " and parttype=" . $parttype;
            $parttypeCond2 = " and  p.aaia_parttype_id=" . $parttype;

        } else {
            $parttypeCond = "";
            $parttypeCond2 = "";
        }
        if ($mfgkey !== null) {
            $mfgcodeCond = " and manufacturerkey = " . $mfgkey;
            $mfgcodeCond2 = " and aces_brands_mix.id = " . $mfgkey;
        } else {
            $mfgcodeCond = "";
            $mfgcodeCond2 = "";
        }
        if ($parttypeCond !== "" || $mfgcodeCond !== "") {
            DB::statement("DELETE FROM nonapps.partmaster_clientid WHERE clientid = " . $clientId . $parttypeCond . $mfgcodeCond);
        }
        $this->createPartmasterClientId(" $parttypeCond2  $mfgcodeCond2", $clientId);

    }

    public function createPartmasterClientId($condition, $clientId)
    {
        DB::statement("insert ignore into nonapps.partmaster_clientid (clientId,mfg_code, sku, product_sku, categorykey, subcategorykey, parttype,
                                                    manufacturerkey, part_label, mfg_name, sku_slug, sku_unformatted,
                                                    qty_avail, props)
 
    select $clientId, aces_brands_mix.BrandID,
           FD.PartNumber,
           FD.PID,
 coalesce(dcf.CategoryID, 0),
           coalesce(dcf.SubCategoryID, 0),
           coalesce( p.aaia_parttype_id,0),
         coalesce(aces_brands_mix.id, 0) as manufacturer,
           coalesce(wi.description, shortDESCRIPTION),
             aces_brands_mix.BrandName,
           FD.PartNumber as sku_slug,
          FD.CompressedPartNumber,
           product_inventory.qty_avail,

           concat('{\"', $clientId, '\":{\"p\":', coalesce(product_pricing.sellprice, 0),
					   ',\"t\":',coalesce(zipcodes.latitude, 0),
						',\"g\":', coalesce(zipcodes.longitude, 0),
						',\"s\":',coalesce(a.sale, 0), 
						',\"su\":',coalesce(a.sale_units, 0), 
						',\"r12\":',coalesce(a.ranking12, 0), 
						',\"r3\":',coalesce(a.ranking3, 0), 
						',\"a\":',coalesce(    UNIX_TIMESTAMP(FD.ItemAffiliateEffectiveDate), 0), 
						',\"q\":',if(coalesce(max(wi.id), 0)>0,1,0), 
						',\"r1\":',coalesce(a.ranking1, 0), 
						',\"d\":',COALESCE( dcf.`active`,0),
						
						',\"c\":',COALESCE(product_inventory.pk,0),
						',\"n\":',COALESCE( NetworkItemDemand,0), ',\"i\":',
               if( (SELECT COUNT(*) FROM pnci_pim.images pi WHERE pi.aaiabrandid =FD.AAIABrandID AND pi.part_number_unformatted = FD.CompressedPartNumber AND pi.width>0 AND pi.height>0) >0, 1, 0), '}}')

      FROM 
              partshare.FlattenedData FD
             JOIN pnci_pim.products p ON p.aaiabrandid = FD.AAIABrandID AND p.part_number_unformatted = FD.CompressedPartNumber
                         join aces_meta.aces_brands_mix ON aces_brands_mix.brand_id =  FD.AAIABrandID 
             LEFT  JOIN wws_items wi
                       ON wi.BrandID =  FD.AAIABrandID  AND wi.part_number_unformatted =FD.CompressedPartNumber
             left join toolsequipments_aces.product_inventory
                        on FD.CompressedPartNumber = product_inventory.part_number_unformatted AND
                           FD.AAIABrandID = product_inventory.mfgcode
             left join toolsequipments_aces.product_pricing
                        on FD.CompressedPartNumber = product_pricing.part_number_unformatted AND
                          FD.AAIABrandID = product_pricing.mfgcode
             left JOIN parttype_dcf as dcf
                       ON dcf.mfg_code = FD.AAIABrandID AND dcf.PartTerminologyID = p.aaia_parttype_id AND
                        p.aaia_parttype_id IS NOT NULL 
             LEFT JOIN faceted_meta_helper.zipcodes ON wi.zipcode = zipcodes.zip AND wi.zipcode <> ''
             LEFT JOIN(SELECT left(linecodesku, 3) as linecode,
                              d.aaiabrandid,
                              d.pslinecode,
                              product_number,  SUM(if(invdate>CURDATE()-INTERVAL 1 YEAR,1,0)) AS ranking12,SUM(if(invdate>CURDATE()-INTERVAL 1 MONTH,1,0)) AS ranking1,
                              SUM(if(invdate>CURDATE()-INTERVAL 3 MONTH,1,0)) AS ranking3,
      
                             
                              COUNT(*)             AS sale,SUM(qty) AS sale_units
                       FROM tool_and_equipment.sales s
                                LEFT JOIN tool_and_equipment.vision_dcf d ON d.linecode = left(s.linecodesku, 3)
                       where d.pslinecode <> ''
                       GROUP BY d.aaiabrandid, product_number) a
                      ON a.pslinecode = FD.LineCode AND a.product_number = FD.PartNumber WHERE 1=1 $condition
     GROUP BY FD.AAIABrandID , FD.CompressedPartNumber");
    }

    public function refreshProduct($row, $clientId)
    {
        $this->deleteProduct($row);
        $index = ManticoreSearch::index('nonapps_products');
        $index->addDocument([
            'partnumbersearch' => $row->sku_unformatted,
            'incrementalkey' => $row->sku_unformatted,
            'categorykey' => 'catid_' . $row->categorykey,
            'subcategorykey' => 'subcatid_' . $row->subcategorykey,
            'manufacturerkey' => 'manufacturer_' . $row->manufacturerkey,
            'parttypekey' => 'parttype_' . $row->parttype,
            'mfg_name' => $row->mfg_name,
            'part_number' => $row->sku,
            'part_label' => $row->part_label,
            'part_number_slug' => $row->sku_slug,
            'part_number_unformatted' => $row->sku_unformatted,
            'clientids' => "" . $clientId,
            'type' => 'nonapps',
            'manufacturer' => $row->manufacturerkey,
            'category' => $row->categorykey,
            'subcategory' => $row->subcategorykey,
            'parttype' => $row->parttype,
            'clientid' => [$clientId],
            'partnumbercrc' => $row->product_sku,
            'props' => $row->props,], $row->id);
        $this->clearCache();
    }

    public function deleteProduct($row)
    {
        ManticoreSearch::sql([
            'mode' => 'raw',
            'body' => ['query' => "DELETE from nonapps_products where id=" . $row->id]]);
    }
    public function countByClientId($clientId)
    {
        $search = new Search(ManticoreSearch::client());
        $search->setIndex('nonapps_products');
        $result =$search->filter('clientId', $clientId)->get();
        return   ManticoreSearch::sql(

         "SELECT count(*) as cnt from  nonapps_products where clientId=" . $clientId );

    }
    public function refreshIndex($clientId, $parttype = null, $mfgkey = null)
    {
        if ($parttype !== null) {
            $parttypeCond = " and parttype=" . $parttype;
        } else {
            $parttypeCond = "";
        }
        if ($mfgkey !== null) {
            $mfgcodeCond = " and manufacturer = " . $mfgkey;
            $mfgcodeCond2 = " and manufacturerkey = " . $mfgkey;
        } else {
            $mfgcodeCond = "";
            $mfgcodeCond2 = "";
        }
        if ($parttypeCond !== "" || $mfgcodeCond !== "") {
            ManticoreSearch::sql([
                'mode' => 'raw',
                'body' => ['query' => "DELETE from nonapps_products where clientId=" . $clientId . $parttypeCond . $mfgcodeCond]]);

        }
        $rows = DB::select("select * from nonapps.partmaster_clientid where clientId=" . $clientId . $parttypeCond . $mfgcodeCond2);
        $index = ManticoreSearch::index('nonapps_products');
        $i = 0;
        $docs = [];
        foreach ($rows as $row) {
            $docs[] = [

                'partnumbersearch' => $row->sku_unformatted,
                'incrementalkey' => $row->sku_unformatted,
                'categorykey' => 'catid_' . $row->categorykey,
                'subcategorykey' => 'subcatid_' . $row->subcategorykey,
                'manufacturerkey' => 'manufacturer_' . $row->manufacturerkey,
                'parttypekey' => 'parttype_' . $row->parttype,
                'mfg_name' => $row->mfg_name,
                'part_number' => $row->sku,
                'part_label' => $row->part_label,
                'part_number_slug' => $row->sku_slug,
                'part_number_unformatted' => $row->sku_unformatted,
                'clientids' => "" . $clientId,
                'type' => 'nonapps',
                'manufacturer' => $row->manufacturerkey,
                'category' => $row->categorykey,
                'subcategory' => $row->subcategorykey,
                'parttype' => $row->parttype,
                'clientid' => [$clientId],
                'partnumbercrc' => $row->product_sku,
                'props' => $row->props,
                'id' => $row->id
            ];
            $i++;
            if ($i == 10) {
                $index->addDocuments($docs);
                $docs = [];
            }


        }
        if ($i > 0) {
            $index->addDocuments($docs);
        }
        $this->clearCache();
    }

    public function clearCache()
    {
        Http::withoutVerifying()->get('https://'.config('sophio.cronicle.domain').'/api/app/run_event/v1?id='.config('sophio.cronicle.clearwebsitescache').'&api_key='.config('sophio.cronicle.api_key'));
    }
}