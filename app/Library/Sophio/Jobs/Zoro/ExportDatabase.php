<?php

namespace App\Library\Sophio\Jobs\Zoro;

use App\Events\AdminQueueMailEvent;
use App\Library\Sophio\Products;
use App\Library\Sophio\Exporters\ZoroExporter;
use App\Mail\QueueJobGeneral;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class ExportDatabase implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $zoroexporter;
    public $database;
    public $contactpk;
    public $settings;
    public $timeout = 0;

    public function __construct($settings)
    {
        $this->settings = $settings;

        $this->onQueue('longtasks');
    }

    public function handle()
    {
        config(['logging.default' => 'zoro']);
        $zoroExporter = new ZoroExporter($this->settings);

        $npi = $zoroExporter->export();
        event(new AdminQueueMailEvent('QueueJobGeneral', config('sophio.admin.mail_senders.exporters'), [
            'subject' => 'Zoro Export Job on ' .$this->database . ' finished!',
            'job_name' => '',
            'tenant_db' => '',
            'description' => 'The job exporting to Zoro for database ' . $this->database . ' has finished!<br>' .
                'The file is available at ' . $npi->getUrl()
        ]));
    }

    public function tags()
    {
        return ['zoroexportdatabase_' . $this->database . '_' . $this->contactpk, 'zoroexportdatabase'];
    }


}