<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Sophio\Common\Enums\SystemLogStatus;
use Sophio\Common\Models\FBS\Invoice;
use Sophio\Common\Models\FBS\SupplierLog;
use Sophio\Common\Models\SystemLog;
use Sophio\Common\Repository\Settings;
use Sophio\Walmart\Library\WalmartOrdersManager;

class WalmartMassSendConfirm extends Command
{
    protected $signature = 'sophio:walmartsendconfirm {when?}';

    protected $description = 'Discover new Walmart orders';

    public function handle()
    {
        $supplierlogs = SupplierLog::where('apitype', 'Walmart')
            ->where('action', 'SendFulfillmentConfirmation')
            ->where('response', 'LIKE', '%We could not find this purchase order%')->get();
        if ($this->argument('when')) {
            $when = $this->argument('when');
        } else {
            $when = 'LASTSEVEN';
        }
        $invoices = Invoice::filterDate(['invdate', $when])
            ->where('custtype', 'WAL')
            ->whereNotIn('invstatus',['X', '4', '5','R','8','9'])->orderBy('invdate')
            ->get();
        $failed = 0;
        $success = 0;
        $sys = SystemLog::create(['task' => 'wal_sendfulconfirm', 'status' => 'NEW', 'user_id' => 0]);
        [$start,$end] = getDatesFromWhen($when);
        $sys->message = "For ".$start.' - '.$end."\n";
        foreach ($invoices as $invoice) {
            $send = true;
            foreach ($invoice->lineitem as $lineitem) {
                if ($lineitem->qty > 0 && $lineitem->track_num !== "" && $lineitem->sup_ord_id !== "" && !isset(  $lineitem->list1['WALMARTCANCELREASON'])) {

                } else {
                    $send = false;
                }
            }
            if ($send && !isset($invoice->SHIPTRAKID['WALMARTSHIPUPDATE'])) {
                $walmartManager = new WalmartOrdersManager(new Settings(), $invoice->seller);
                $responses = $walmartManager->shipAllLines($invoice);
                foreach ($responses as $r) {
                    if ($r == false) {
                        $sys->message .= $invoice->pk ." failed to send\n";
                        echo  $invoice->pk ." failed to send\n";
                    } else {
                        $sys->message .= $invoice->pk . " send\n";
                        echo  $invoice->pk ."  send\n";
                    }
                    $sys->save();
                }
            }
        }
        $sys->status = SystemLogStatus::SUCCESS;
        $sys->save();
    }
}