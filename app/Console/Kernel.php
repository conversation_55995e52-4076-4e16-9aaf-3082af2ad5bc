<?php

namespace App\Console;

use App\Console\Commands\ProductsRefresh;
use App\Console\Commands\CatalogTablesChecker;
use App\Console\Commands\FBSImport;
use App\Console\Commands\WalmartOnboard;

use App\Library\Sophio\Commands\MkSnap;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;


class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        Commands\RefreshDb::class,
        MkSnap::class
    ];

    protected function commands()
    {
        $this->load(__DIR__.'/Commands');


        // ...
    }
    /**
     * Define the application's command schedule.
     *
     * @param \Illuminate\Console\Scheduling\Schedule $schedule
     *
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
    }
}
