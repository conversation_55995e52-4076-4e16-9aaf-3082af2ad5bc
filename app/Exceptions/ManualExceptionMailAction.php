<?php

namespace App\Exceptions;

use App\Mail\ExceptionOccured;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Redis;
use Throwable;

/**
 * limiting mails send is inspired by abrigham1/laravel-email-exceptions
 */
class ManualExceptionMailAction
{
    protected $globalThrottleCacheKey = "email_exception_global";
    protected $throttleCacheKey = null;

    public function __invoke(\Throwable $exception,$data)
    {
        if( $this->throttle($exception) ||  config('sophio.admin.disable_exception_mails')===true) {
            return false;
        }

        try {
            $content= [];
            $content['message'] = $exception->getMessage();
            $content['file'] = $exception->getFile();
            $content['line'] = $exception->getLine();
            $content['trace'] = $exception->getTrace();
            $content['url'] = request()->url();
            $content['headers'] = request()->header();
            $content['body'] = request()->all();
            $content['ip'] = request()->ip();
            $content['referer'] = request()->headers->get('referer');
            $content['data'] = $data;
            Mail::to(config('sophio.admin.mail_senders.developers'))->send(new ExceptionOccured($content));

        } catch (\Throwable $exception) {
            Log::error($exception);
        }
    }
    protected function getThrottleCacheKey(Throwable $exception)
    {

        // if we haven't already set the cache key lets set it
        if ($this->throttleCacheKey == null) {
            // make up the cache key from a prefix, exception class, exception message, and exception code
            // with all special characters removed
            $this->throttleCacheKey = preg_replace(
                "/[^A-Za-z0-9]/",
                '',
                'laravelEmailException'.get_class($exception).$exception->getMessage().$exception->getCode()
            );
        }

        // return the cache key
        return $this->throttleCacheKey;
    }
    protected function throttle(Throwable $exception)
    {
        if(cache()->has($this->getThrottleCacheKey($exception))) {
            return true;
        }else{
            cache()->put($this->getThrottleCacheKey($exception),true,$this->getDateTimeMinutesFromNow( 5));
            return false;
        }
    }
    protected function getDateTimeMinutesFromNow($minutesToAdd = 0)
    {
        $now = new \DateTime();

        return $now->modify("+{$minutesToAdd} minutes");
    }
}