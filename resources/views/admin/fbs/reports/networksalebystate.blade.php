@extends(backpack_view('blank'))
@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form>
                        <div class="row form-row align-items-center">

                            <div class="col-sm-3">
                                <select name="when" class="form-select">
                                    @foreach(getWhens() as $when)
                                        <option value="{{$when}}"
                                                @if((request()->get('when')??'LASTMONTH')==$when) selected @endif>{{$when}}</option>

                                    @endforeach
                                </select>
                            </div>
                            <div class="col-sm-3">
                                <label class="form-check form-switch">
                                    <input type="checkbox" name="send_ftp" class="form-check-input"><span
                                            class="form-check-label">Send to FTP</span>
                                </label>
                            </div>
                            <div class="col-sm-3">
                                <input type="submit" name="send" class="btn btn-primary">
                            </div>
                        </div>
                    </form>

                </div>
            </div>
        </div>
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="card-title"> Sales by State
                        - {{implode(' : ',getDatesFromWhen(request()->get('when')??'LASTMONTH','m/d/y'))}}</div>
                </div>
                <div class="card-body">
                    <table class="table datatabletable">
                        <thead>
                        <tr>

                            <th>Month</th>
                            <th>Total</th>
                            <th>Total Taxes Collected</th>
                            <th>State</th>

                        </tr>
                        </thead>
                        <tbody>
                        @foreach($data as $row)
                            <tr>

                                <td>{{ $row->month }}</td>

                                <td>{{number_format($row->total,2,'.')}}</td>
                                <td>{{number_format($row->total_taxes,2,'.')}}</td>
                                <td>{{$row->st_state}}</td>

                            </tr>
                        @endforeach
                        <tr>

                            <td></td>

                            <td><b>{{number_format($data->sum('total'),2,'.')}}</b></td>
                            <td><b>{{number_format($data->sum('total_taxes'),2,'.')}}</b></td>
                            <td></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('after_styles')
    @include('admin.inc.css')
@endsection

@push('after_scripts')
    @include('admin.inc.js')
    <script type="text/javascript"
            src="/assets/js/sophio/utils.js"></script>
    <script>
        $(document).ready(function () {

            $('.datatabletable').dataTable({
                order: [], pageLength: 1000, dom: 'Bfrtip',
                buttons: [
                    {extend: 'csv', filename: '{{$filename}}', title: null}, {
                        extend: 'excel',
                        filename: '{{$filename}}',
                        title: null
                    }, {extend: 'pdf', filename: '{{$filename}}', title: null}, {
                        extend: 'print',
                        filename: '{{$filename}}',
                        title: null
                    }
                ],
            });
        });
    </script>
@endpush