@extends(backpack_view('blank'))
@section('content')

    <div class="row">
        <div class="col-md-12 bold-labels">
            <div class="card">
                <div class="card-header">

                    <div class="card-title"><h2>API Keys</h2>

                    </div>

                </div>
                <div class="card-body">
                    <div class="row">
                        @if(count($apikeys)==0)
                            <h>No API Found. <a href="{{sophio_route('fbs/customer.createapikey',['id'=>$customer->pk])}}">Create API Key</a></h>
                            @endif
                        @foreach($apikeys as $apikey)

                            <div class="col-md-6 bold-labels">
                                <div class="table-responsive">
                                    <table class="table datatabletable">
                                        <tbody>

                                        <tr>
                                            <td>CustomerID</td>
                                            <td>{{$apikey->custpk}}</td>
                                        </tr>
                                        <tr>
                                            <td>StoreID</td>
                                            <td>{{$apikey->storepk}}</td>
                                        </tr>
                                        <tr>
                                            <td>URL UUID</td>
                                            <td>{{$apikey->uuid}}</td>
                                        </tr>
                                        <tr>
                                            <td>Username</td>
                                            <td>{{$apikey->username}}</td>
                                        </tr>
                                        <tr>
                                            <td>Password</td>
                                            <td>{{$apikey->password}}</td>
                                        </tr>
                                        <tr>
                                            <td>Token</td>
                                            <td>{{$apikey->token}}</td>
                                        </tr>
                                        <tr>
                                            <td>Active</td>
                                            <td>{{$apikey->active==1?'Yes':'No'}}</td>
                                        </tr>

                                        </tbody>
                                    </table>
                                </div>

                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('after_styles')
    @include('admin.inc.css')
@endsection

@push('after_scripts')
    @include('admin.inc.js')
    <script>
        $(document).ready(function () {
            $('.select2').select2();

        });

    </script>
@endpush