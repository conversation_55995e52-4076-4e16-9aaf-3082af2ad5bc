@extends(backpack_view('blank'))
@section('content')

    <div class="row">
        <div class="col-md-12 bold-labels">
            <div class="card">
                <div class="card-header">

                    <div class="card-title"><h2>Activate B2B for {{$customer->company}}</h2>

                    </div>

                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12 bold-labels">
                            <div class="table-responsive">
                                <table class="table datatabletable">
                                    <thead>
                                    <tr>
                                        <th></th>
                                        <th></th>
                                        <th></th>

                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td>Customer Type</td>
                                        <td>{{$customer->custtype}}</td>
                                        <td></td>

                                    </tr>
                                    <tr>
                                        <td>Account Number</td>
                                        <td>{{$customer->accountnum}}</td>
                                        <td>
                                            @if($customer->accountnum==="")
                                                Please set an account number!!!
                                                @endif
                                        </td>

                                    </tr>
                                    <tr>
                                        <td>Active</td>
                                        <td>{{$customer->active=="T"?'Yes':'No'}}</td>
                                        <td></td>

                                    </tr>

                                    <tr>
                                        <td>API Key</td>

                                        <td>@if($customer->apikey)
                                                <a href="{{sophio_route('fbs/apikey.edit',['id'=>$customer->apikey->id])}}">Yes
                                                    - Edit</a>
                                            @else
                                                <a href="{{sophio_route('fbs/apikey.create')}}">No - Create</a>
                                            @endif</td>
                                        <td>
                                            @if(!$customer->apikey)
                                                Setup an API Key to allow customer to use the Order API.
                                            @endif
                                        </td>

                                    </tr>
                                    <tr>
                                        <td>FTP</td>

                                        <td>@if(isset($customer->settings['FTPLOCATION']) && $customer->settings['FTPLOCATION']!="" )
                                                Yes
                                            @else
                                                No
                                            @endif</td>

                                        <td>@if(isset($customer->settings['FTPLOCATION']) && $customer->settings['FTPLOCATION']!="" )

                                            @else
                                                If we are sending a datafeed file with stock and prices to customer we
                                                need to setup FTP credentials. Either ask support to create a FTP
                                                account on our servers or ask customer to provide a FTP account.
                                            @endif</td>

                                    </tr>
                                    <tr>
                                        <td>Pricing Database</td>

                                        <td>@if(isset($customer->xml['ACESPARTSDB']) && $customer->xml['ACESPARTSDB']!="" )
                                                {{$customer->xml['ACESPARTSDB']}}
                                            @else
                                                No
                                            @endif</td>

                                        <td>@if(isset($customer->xml['ACESPARTSDB']) && $customer->xml['ACESPARTSDB']!="" )

                                            @else
                                                Go to Pricing tab and set the "ACES Parts and Pricing Database". Use
                                                'fbn_aces' for general customers.
                                            @endif</td>

                                    </tr>
                                    <tr>
                                        <td>Pricing Markups</td>

                                        <td>@if(isset($customer->xml['ACESPARTSDB']) && $customer->xml['ACESPARTSDB']!="" )

                                            @else
                                                Set pricing database first
                                            @endif</td>

                                        <td>@if(isset($customer->xml['ACESPARTSDB']) && $customer->xml['ACESPARTSDB']!="" )

                                            @else

                                            @endif</td>

                                    </tr>
                                    <tr>
                                        <td>Nexpart</td>

                                        <td>@if(isset($customer->xml['NEXPARTUSERNAME']) && $customer->xml['NEXPARTUSERNAME']!="" )
                                                Yes
                                            @else
                                                No
                                            @endif</td>

                                        <td>@if(isset($customer->xml['NEXPARTUSERNAME']) && $customer->xml['NEXPARTUSERNAME']!="" )

                                            @else
                                              (optional)
                                                If customer is supposed to send orders via Nexpart SparkLink service, we
                                                need their Nexpart buyer credentials.
                                            @endif</td>

                                    </tr>
                                    <tr>
                                        <td>Rate Shop</td>

                                        <td>@if(isset($customer->xml['RATESHOP']) && $customer->xml['RATESHOP']!=="BESTWAY" )
                                                Disabled
                                            @else
                                                Enabled
                                            @endif</td>

                                        <td>
                                            @if(isset($customer->xml['RATESHOP']) && $customer->xml['RATESHOP']!=="BESTWAY" )
                                                When rate-shopping is disabled we use instructions sent by customer in
                                                the order for which carrier/service to use.
                                            @endif
                                        </td>

                                    </tr>
                                    <tr>
                                        <td>Payment method</td>

                                        <td>@if($customer->paymethod==='ST' )
                                                Stripe
                                            @else
                                                Open Account
                                            @endif</td>

                                        <td>
                                            @if($customer->paymethod==='ST' )
                                                This customer is setup to pay via Stripe.
                                                @if(!isset($customer->xml['STRIPECUSTOMERID']) && $customer->xml['STRIPECUSTOMERID']!=="")
                                                    But no Stripe customer ID is setup. Please create one.
                                                @endif
                                            @endif
                                        </td>

                                    </tr>

                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('after_styles')
    @include('admin.inc.css')
@endsection

@push('after_scripts')
    @include('admin.inc.js')
    <script>
        $(document).ready(function () {
            $('.select2').select2();

        });

    </script>
@endpush