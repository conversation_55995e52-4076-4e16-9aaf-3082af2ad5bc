@extends(backpack_view('blank'))
@section('content')

    <div class="row">
        <div class="col-md-12 bold-labels">
            <div class="card">
                <div class="card-header">

                    <div class="card-title"> Import from Nexpart list with Autozone stores</div>

                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12 bold-labels">
                            @if(request()->post())
                                <p>{{$missingCount}} stores were not found in Nexpart.</p>
                                <p> <a href="{{$url}}" class="btn btn-primary">Download </a>latest missing stores in Nexpart.</p>
                            @else
                                <form method="POST" id="myform" enctype="multipart/form-data"
                                      action=""
                                      id="myform">
                                    @csrf
                                    <!--
                                    <div class="form-group row">
                                        <label class="col-sm-2 col-form-label">Format:</label>
                                        <div class="col-sm-10">
                                         <select name="format_input_type" class="form-select">
                                             <option value="simple">Simple </option>
                                             <option value="complete">Complete</option>
                                         </select>
                                        </div>
                                    </div>
                    //-->
                                    <input type="hidden" name="format_input_type" value="complete">
                                    <div class="form-group row">
                                        <label class="col-sm-2 col-form-label">Upload file:</label>
                                        <div class="col-sm-10">
                                            <input type="file" class="form-control" name="file">
                                        </div>
                                    </div>

                                    <div class="card-footer">

                                        <input type="submit" class="btn btn-primary"
                                               value="Upload">&nbsp;
                                    </div>
                                </form>

                            @endif
                        </div>


                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-header">

                    <div class="card-title"> Existing exports with missing Autozone stores <a href="{{sophio_route('fbs/customer.missingfromnexpart')}}" class="btn btn-primary">Recalc missing</a></div>


                </div>
                <div class="card-body">
                    <div class="row">
                        <table class="table">
                            <tr>
                                <th>Filename</th>
                                <th>Date</th>
                            </tr>
                            @foreach($files as $file)
                                <tr>
                                    <td>
                                        <a href="{{\Illuminate\Support\Facades\Storage::disk('fbs')->url($file)}}">{{basename($file)}}</a>
                                    </td>
                                    <td>
                                        {{\Illuminate\Support\Carbon::parse(\Illuminate\Support\Facades\Storage::disk('fbs')->lastModified($file))}}
                                    </td>
                                </tr>

                            @endforeach
                        </table>

                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('after_styles')
    @include('admin.inc.css')
@endsection

@push('after_scripts')
    @include('admin.inc.js')
    <script>
        $(document).ready(function () {
            $('.select2').select2();

        });
        $('.datatabletable').dataTable({
            order: [], pageLength: 1000, dom: 'Bfrtip',

        });
    </script>
@endpush