<html>
<head>

    <link href="tabler.min.css" rel="stylesheet"
          type="text/css"/>
    <link href="style.css" rel="stylesheet"
          type="text/css"/>
    <link href="animate.compat.css" rel="stylesheet" type="text/css"/>
    <link href="noty.css" rel="stylesheet" type="text/css"/>

    <link href="common.css" rel="stylesheet"
          type="text/css"/>

    <link href="color-adjustments.css"
          rel="stylesheet" type="text/css"/>
    <link href="colors.css" rel="stylesheet"
          type="text/css"/>

    <link href="admin.css" rel="stylesheet" type="text/css"/>


    <style>

        .pagebreak {
            clear: both;
            page-break-after: always;
        }

    </style>
</head>
<body class="theme-light">
<div class="page">
    <div class="page-wrapper">
        <div class="page-body">
            <main class="container-xl">
                <div class="container-fluid animated fadeIn">
                    <div class="row ">
                        <div class="col-12">
                            <div class="card">

                                <div class="card-body">
                                    @include('admin.fbs.customer.statement_data')
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>
</body>
</html>
