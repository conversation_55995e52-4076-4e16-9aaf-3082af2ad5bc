
<div class="row ">
    <div class="col-12">
        <div class="card">

            <div class="card-body">
                <p class="text-secondary">If set, it will override the setting defined in the supplier settings.</p>


                <form method="POST" action="{{sophio_route('fbs/customer.suppliersetting',['id'=>$customer->pk])}}?setting={{$suppliersetting}}" id="{{$suppliersetting}}_accounts_form">
                    @csrf
                    <table class="table">
                        <tr>
                            <th>Supplier Name</th>
                            <th>Value</th>
                            <th>Supplier Value</th>
                        </tr>
                        @foreach($suppliers as $supplier)

                            <tr >

                                <td>{{$supplier->PK}} {{$supplier->NAME}}</td>
                                <td>
                                    @if(count($lookups)>0)
                                        <select class="form-select"          name="{{$suppliersetting}}[{{$supplier->PK}}]">
                                            <option value="" >None</option>
                                            @foreach($lookups as $l)
                                                @if($l->cdata==($customer->xml[$suppliersetting.'-'.$supplier->PK]??''))
                                                    <option value="{{$l->cdata}}" selected>{{$l->cdata1}}</option>
                                                @else
                                                    <option value="{{$l->cdata}}" >{{$l->cdata1}}</option>
                                                    @endif
                                            @endforeach
                                        </select>
                                        @else
                                    <input type="text" class="form-control"
                                           name="{{$suppliersetting}}[{{$supplier->PK}}]"
                                           value="{{$customer->xml[$suppliersetting.'-'.$supplier->PK]??''}}">
                                        @endif
                                </td>
                                <td>{{$supplier->SETTINGS[$suppliersetting]??''}}</td>
                            </tr>

                        @endforeach
                    </table>
                    <div class="col">

                        <button name="submit" type="submit" class="btn btn-success" value="true" id="{{$suppliersetting}}_accounts_submit">Submit</button>

                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
