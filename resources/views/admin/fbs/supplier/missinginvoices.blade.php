@extends(backpack_view('blank'))

@section('content')
    <h2>
        <span class="text-capitalize">FBS  \ Missing Supplier Invoices</span>
    </h2>


    <div class="row">
        <div class="col-md-12">
            @switch(request()->get('Action'))
                @case('Summary')
                    <div class="card">
                        <div class="card-header">
                            Summary of missing supplier invoices between {{$start->toDateString()}}
                            - {{$end->toDateString()}}
                            @if(request()->get('with_sup_ord_id')=="1")
                                - With Sup Ord Id
                            @endif
                            @if(request()->get('with_track')=="1")
                                - With Tracking Number
                            @endif
                            @if(request()->get('without_supinvpk')=="1")
                                - Without Statement id
                            @endif
                        </div>
                        <div class="card-body">
                            <table class="table">
                                <thead>
                                <tr>
                                    <th>Supplier</th>
                                    <th>Supplier ID</th>
                                    <th>Missing invoices</th>
                                </tr>
                                </thead>
                                <tbody>

                                @foreach($rows['count'] as $row)

                                    <tr>
                                        <td>
                                            <a href="{{sophio_route('fbs/supplier.missingsupplierinvoices',array_merge(request()->all(),['profilepk'=>$row->supplier->PK,'Action'=>'Search']))}}">{{$row->supplier->NAME}}</a>
                                        </td>
                                        <td>{{$row->profilepk}}</td>
                                        <td>{{$row->aggregate}}</td>

                                    </tr>
                                @endforeach
                                </tbody>
                                <tfoot>
                                <tr>
                                    <td></td>
                                    <td></td>
                                    <td>{{$rows['count']->sum('aggregate')}}</td>
                                </tr>
                                </tfoot>
                            </table>

                        </div>
                    </div>
                    @break
                @case('Search')
                    <div class="card">
                        <div class="card-header">
                            <div>
                            <h3 class="card-title">
                            Summary of missing supplier invoices for {{$supplier->NAME}} between {{$start->toDateString()}}
                            - {{$end->toDateString()}}
                                -  Expected Cost:  {{our_format($rows['rows']->sum(function($lineitem) { return ($lineitem->cost>0?$lineitem->cost:$lineitem->scost)*$lineitem->qty;}))}}
                                - System Cost: {{our_format($rows['rows']->sum(function($lineitem) { return ($lineitem->coreprice+($lineitem->scost>0?$lineitem->scost:$lineitem->cost))*$lineitem->qty;}))}};
                                - Variance :  {{our_format($rows['rows']->sum(function($lineitem) { return ($lineitem->coreprice+($lineitem->scost>0?$lineitem->scost:$lineitem->cost))*$lineitem->qty;})-$rows['rows']->sum(function($lineitem) { return ($lineitem->cost>0?$lineitem->cost:$lineitem->scost)*$lineitem->qty;}))}}

                            </h3>

                            <p class="card-subtitle">
                                Note: {{$rows['rows']->sum(function($item){return $item->scost==0.0?1:0;})}}  items had no system cost, so we used the po cost instead.
                                <br>
                                @if(request()->get('with_sup_ord_id')=="1")
                                    - With Sup Ord Id
                                @endif
                                @if(request()->get('with_track')=="1")
                                    - With Tracking Number
                                @endif
                                @if(request()->get('without_supinvpk')=="1")
                                    - Without Statement id
                                @endif
                            </p>
                            </div>
                                   <div class="card-actions">
                                       <a href="{{$file->getUrl()}}" class="btn">Download all lines as NetIntel file</a>
                                   </div>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{sophio_route('fbs/supplier.paysupplierinvoices')}}">
                                @csrf
                                <input type="hidden" name="when" value="{{request()->get('when')}}">
                                <input type="hidden" name="profilepk" value="{{$supplier->PK}}">
                            <table class="table datatabletable">
                                <thead>
                                <tr>
                                    <th>Select</th>
                                    <th>Line Item</th>
                                    <th>Po Number</th>
                                    <th>Sup ord id</th>
                                    <th>Time in</th>
                                    <th>Time out</th>
                                    <th>Completed</th>
                                    <th>Mfr</th>
                                    <th>Linecode</th>
                                    <th>Wd linecode</th>
                                    <th>Qty ordered</th>
                                    <th>SKU</th>
                                    <th>Unit Cost</th>
                                    <th>Nwtosophio</th>
                                    <th>Unit Scost</th>
                                    <th>Core price</th>
                                    <th>PO cost</th>
                                    <th>System cost</th>
                                    <th>Handlingc</th>
                                    <th>Invtotalc</th>
                                    <th>Supplier</th>
                                </tr>
                                </thead>
                                <tbody>

                                @foreach($rows['rows'] as $lineitem)

                                    <tr>
                                        <td><input type="checkbox" checked name="lineitempk[]" value="{{$lineitem->pk}}" class="form-check"></td>
                                        <td>{{$lineitem->pk}}</td>
                                        <td>{{$lineitem->invpk}}</td>
                                        <td>{{$lineitem->sup_ord_id}}</td>
                                        <td>{{$lineitem->timein}}</td>
                                        <td>{{$lineitem->timeout}}</td>
                                        <td>{{$lineitem->invoice->completed}}</td>
                                        <td>{{$lineitem->mfr}}</td>
                                        <td>{{$lineitem->linecode}}</td>
                                        <td>{{$lineitem->wdlinecode}}</td>
                                        <td>{{$lineitem->qty}}</td>
                                        <td>{{$lineitem->sku}}</td>
                                        <td>{{($lineitem->cost>0?$lineitem->cost:$lineitem->scost)}}</td>
                                        <td>{{$lineitem->nwtosophio}}</td>
                                        <td>{{$lineitem->scost}}</td>
                                        <td>{{$lineitem->coreprice}}</td>
                                        <td>{{($lineitem->cost>0?$lineitem->cost:$lineitem->scost)*$lineitem->qty}}</td>
                                        <td>{{($lineitem->coreprice+($lineitem->cost>0?$lineitem->cost:$lineitem->scost))*$lineitem->qty}}</td>
                                        <td>{{$lineitem->handlingc}}</td>
                                        <td>{{$lineitem->invtotalc}}</td>
                                        <td>{{$lineitem->profilepk}}</td>
                                    </tr>
                                @endforeach
                                </tbody>

                            </table>
                                <input type="submit" class="btn btn-success" value="Pay Invoices - ${{$rows['rows']->sum(function($lineitem) { return ($lineitem->coreprice+($lineitem->cost>0?$lineitem->cost:$lineitem->scost))*$lineitem->qty;})}}">
                            </form>
                        </div>
                    </div>
                    @break
                @default
                    <div class="card">
                        <div class="card-header">

                        </div>
                        <div class="card-body">
                            @if(isset($error) &&$error!=="")
                                <div class="alert alert-danger">
                                    {{$error}}
                                </div>
                            @endif

                            <form method="GET" action="">


                                <div class="form-group row">
                                    <label class="col-sm-4 col-form-label text-right">Supplier:</label>
                                    <div class="col-sm-8">
                                        <select class="form-control" id="shipper" name="profilepk">
                                            <option value="">Select</option>
                                            @foreach($suppliers as $supplier)
                                                <option value="{{$supplier->PK}}">{{$supplier->getSupplierDisplayName()}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-4 col-form-label text-right">Choose Period:</label>
                                    <div class="col-sm-8">
                                        <select class="form-control" id="when" name="when">
                                            <option value="">Select</option>
                                            @foreach(getWhens() as $when)
                                                <option value="{{$when}}"
                                                        @if(request()->get('when')==$when) selected @endif>{{$when}}</option>

                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-4 col-form-label text-right">Show Items with Tracking Numbers
                                        Only:</label>
                                    <div class="col-sm-8">
                                        <input type="checkbox" class="form-check" name="with_track" value="1" checked>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-4 col-form-label text-right">Show Items with Supplier Invoice
                                        Id's:</label>
                                    <div class="col-sm-8">
                                        <input type="checkbox" class="form-check" name="with_supinvpk" value="1"
                                                >
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-4 col-form-label text-right">Ignore Orders with Supplier Statement Id?:</label>
                                    <div class="col-sm-8">
                                        <input type="checkbox" class="form-check" name="without_supinvpk" value="1" checked
                                        >
                                    </div>
                                </div>
                                <div class="form-group row text-center center">

                                    <div class="col-auto"><input type="submit" class="btn btn-primary" value="Search"
                                                                 name="Action"/></div>
                                    <div class="col-auto"><input type="submit" class="btn btn-primary" value="Summary"
                                                                 name="Action"/></div>

                                </div>
                            </form>
                        </div>
                    </div>
            @endswitch
        </div>
    </div>

@endsection
@section('after_styles')
    @include('admin.inc.css')
@endsection
@push('after_scripts')
    @include('admin.inc.js')
    <script>
        $(document).ready(function () {
            @if(request()->get('Action')==="Search")
            $("form").submit(function() {
                $('input[type=submit]', this).attr('disabled', 'disabled');
            });
            @endif
            $('.datatabletable').dataTable({
                order: [], pageLength: 100000,  dom: 'Bfrtip',

                buttons: [
                    'copy', 'csv', 'excel', 'pdf', 'print'
                ],
            });
        });
    </script>
@endpush