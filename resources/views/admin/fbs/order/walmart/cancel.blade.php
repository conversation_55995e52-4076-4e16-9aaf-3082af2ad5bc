@extends(backpack_view('blank'))
@section('content')

    <div class="row">
        <div class="col-md-12 bold-labels">
            @if(request()->post())
                <div class="card">
                    <div class="card-body">
                        @if($responses!==false)

                            @foreach($responses as $response)
                                <pre><code>{{$response}}</code></pre>
                            @endforeach
                        @else
                            <div class="alert alert-success">Failed!</div>
                            <br>
                            <div class="alert alert-success">{{$walmartManager->getError()}}</div>
                        @endif
                    </div>
                </div>
            @else
                <form method="POST">
                    @csrf
                    <div class="card">
                        <div class="card-header">
                            Send a Cancellation Request
                        </div>

                        <div class="card-body">
                            <p>Start off by selecting the item you wish to cancel by confirming the quantity to cancel
                                in
                                the box below.<br>
                                Next, select an appropriate action(cancel) for the item.<br>
                            </p>
                            <p> Choose a cancel reason:</p>
                            <ul>
                                <li>Supplier Cancel unrecognized - Supplier Cancel unrecognized</li>
                                <li>Supplier cancel at customer request - Supplier cancel at customer request</li>
                                <li>Supplier Cancel due to a discontinued item - Supplier Cancel due to a discontinued
                                    item
                                </li>
                                <li>Supplier Cancel Backorder - Supplier Cancel Backorder</li>
                            </ul>
                            <label class="form-label">Order line items:</label>
                            <table class="table">
                                <tr>
                                    <th>Action</th>
                                    <th>Cancel Reason</th>
                                    <th>Qty</th>
                                    <th>SKU</th>
                                    <th>Tracking number</th>
                                    <th>Market line number id</th>
                                    <th>Price</th>
                                </tr>
                                @foreach($invoice->lineitem as $lineitem)
                                    <tr>
                                        <td><select class="form-control" name="action[{{$lineitem->pk}}]" required
                                                    class="form-control">
                                                <option value="">-- choose --</option>
                                                <option value="v_nocancel">No Action</option>
                                                <option value="v_cancel">Cancel line item</option>
                                            </select>
                                        </td>
                                        <td>
                                            <select class="form-control" name="reason[{{$lineitem->pk}}]" required
                                                    class="form-control">
                                                <option value="SUPPLIER_CANCEL_UNRECOGNIZED">Supplier Cancel
                                                    unrecognized
                                                </option>
                                                <option value="SUPPLIER_CANCEL_CUSTOMER_REQUEST">Supplier cancel at
                                                    customer
                                                    request
                                                </option>
                                                <option value="SUPPLIER_CANCEL_DISCONTINUE">Supplier Cancel due to a
                                                    discontinued item
                                                </option>
                                                <option value="SUPPLIER_CANCEL_BACKORDER">Supplier Cancel Backorder
                                                </option>
                                            </select>
                                        </td>
                                        <td><input type="number" name="qty[{{$lineitem->pk}}]"
                                                   value="{{$lineitem->qty}}"
                                                   class="form-control"
                                            ></td>
                                        <td>{{$lineitem->sku}}</td>
                                        <td>{{$lineitem->track_num}}</td>
                                        <td>{{$lineitem->mrktplcid}}</td>
                                        <td>{{$lineitem->price}}</td>
                                    </tr>
                                @endforeach
                            </table>
                            <label class="form-label">Order level message:</label>
                            <textarea name="orderlevelmessage" cols="50" rows="3" id="notes"
                                      class="form-control"></textarea>
                        </div>
                        <div class="card-footer">

                            <input type="submit" class="btn btn-primary"
                                   value="Submit">&nbsp;
                        </div>
                    </div>
                </form>
            @endif
        </div>
    </div>

@endsection
