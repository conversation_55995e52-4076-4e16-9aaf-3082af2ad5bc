@extends(backpack_view('blank'))


@section('content')
    <div class="row">
        <div class="col-md-6 bold-labels">
            @if($cancelled==true)
                <div class="card">
                    <div class="card-body">
                        <h1> Line was cancelled with success!</h1>
                        <p class="text-secondary">
                            @foreach($messages as $message)
                                {{$message}} <br>
                            @endforeach
                        </p>
                    </div>
                </div>
            @elseif($error!==null && request()->post())
                <div class="card">
                    <div class="card-body">
                        <div class="alert alert-danger">Failed to cancel:</div>
                        {{$error}}
                    </div>
                </div>
            @else
                <form method="POST" class="card">
                    @csrf
                    <div class="card-header"> Cancel Lineitem {{$lineitem->pk}}
                        - {{\Sophio\Common\Models\FBS\Lookups::where('type', 'INVSTATUS')->where('cdata',$lineitem->lineStatus)->first()->cdata1}}
                    </div>
                    <div class="card-body">
                        <table class="table">
                            <tr>
                                <td>Linecode:</td>
                                <td>{{$lineitem->linecode}}</td>
                            </tr>
                            <tr>
                                <td>SKU:</td>
                                <td>{{$lineitem->sku}}</td>
                            </tr>
                            @if($lineitem->sup_ord_id!=="")
                                <tr>
                                    <td>
                                        <div class="text-red bg-transparent">Supplier order id:</div>
                                    </td>
                                    <td>
                                        <div class="text-red bg-transparent">{{$lineitem->sup_ord_id}}</div>
                                    </td>
                                </tr>
                            @endif
                            @if($lineitem->supplier)
                                <tr>
                                    <td>
                                        <div class="text-red bg-transparent">Supplier:</div>
                                    </td>
                                    <td>
                                        <div class="text-red bg-transparent">{{$lineitem->supplier->getSupplierDisplayName()}}</div>
                                    </td>
                                </tr>
                            @endif
                            @if($lineitem->track_num!=="")
                                <tr>
                                    <td>
                                        <div class="text-red bg-transparent">Tracking number:</div>
                                    </td>
                                    <td>
                                        <div class="text-red bg-transparent">{{$lineitem->track_num}}</div>
                                    </td>
                                </tr>
                            @endif
                            @if(isset($lineitem->list2['SHIPSTATIONORDERID']))
                                <tr>
                                    <td>
                                        <div class="text-red bg-transparent">Shipstation ID:</div>
                                    </td>
                                    <td>
                                        <div class="text-red bg-transparent">{{$lineitem->list2['SHIPSTATIONORDERID']}}</div>
                                    </td>
                                </tr>
                            @endif
                        </table>

                        <select name="status" class="form-select">
                            <option value="4">Cancel by Seller</option>
                            <option value="5">Cancel by Supplier</option>
                            <option value="X">Cancel by Buyer</option>
                        </select>
                        <br>
                        <div class="text-secondary">
                            <ul class="list-unstyled">
                                <li>
                                    4 - Cancelled by supplier - <b>use it for re-routing</b>
                                </li>
                                <li>
                                    X - Cancelled by customer
                                </li>

                                <li>
                                    5 - Cancelled by seller - we have no choice but to cancel
                                </li>
                            </ul>

                        </div>
                        <p class="text-secondary">Cancel by customer or seller trigger actions to customer like refunding or informing them of the cancellation.</p>
                        <br>
                        @if(isset($lineitem->list2['SHIPSTATIONORDERID']))

                                <div class="alert alert-danger">
                                    This lineitem  has a Shipstation order associated. If this is the only line associated  with the shipping order, PLEASE cancel first the shipping order from Shipping tab.
                                    If there's more than one line item associated with the shipping order, you should:
                                    <ol>
                                        <li>Cancel the shipstation order from the Shipping tab</li>
                                        <li>Cancel this line</li>
                                        <li>Create again a shipping order for the lines that are not cancelled</li>
                                    </ol>

                                </div>

                        @endif
                        @if($lineitem->track_num!=="")
                            <div class="alert alert-danger">
                                WARNING! This lineitem has a tracking number associated. You need to cancel first the shipping order.
                            </div>
                            @endif
                    </div>

                    <div class="card-footer   text-end">
                        <a href="{{sophio_route('fbs/fbsorder.main',['id'=>$lineitem->invpk])}}" class="btn btn-info">Back
                            to order</a>
                        <button type="submit" class="btn btn-success">Confirm</button>
                    </div>

                </form>
            @endif
        </div>
    </div>
@endsection

