@extends(backpack_view('blank'))
@section('content')

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    @if($shipment==null)
                        The warehouse has yet to print this label!
                    @else

                        Order {{$shipment->orderNumber}} was shipped on {{$shipment->shipDate}}  with <span class="btn  btn-sm  btn-primary">{{$shipment->carrierCode}}</span> and tracking number <span class="btn btn-sm btn-primary">{{$shipment->trackingNumber}}</span> at a cost of {{$shipment->shipmentCost}}
                        <p><a class="btn  btn-sm  btn-danger"   href="{{sophio_route('fbs/fbsorder.voidshipstation',[ 'id'=>$invoice->pk,'shipstationid='=>$shipment->orderId])}}">Void Shipment and get a  {{$shipment->shipmentCost}} refund</a></p>
                    @endif

                </div>
            </div>
        </div>
    </div>
@endsection

