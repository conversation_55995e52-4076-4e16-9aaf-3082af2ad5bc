<div class="col-md-12 bold-labels">

    <form method="POST">
        @csrf
        <div class="card">
            <div class="card-header  bg-info-lt text-dark py-1">Line items</div>

            <div class="card-body py-1">
                @if(isset($valid))
                    @if($valid && isset($result['resolved']) && $result['resolved']===true && count($result['fulfilable'])>0  )

                        @if (!(new \Sophio\FBSOrder\Library\Rules\Invoice\autoStatus)($invoice))
                            <div class="alert alert-info">This invoice can be automatically fulfilled ,but it
                                will
                                not be picked up by system because of it's status: {{$invoice->statusText()}}
                                ({{$invoice->invstatus}})
                            </div>
                        @else
                            <div class="alert alert-success">This invoice can be automatically fulfilled</div>
                        @endif

                    @else
                        <div class="alert alert-warning">This invoice cannot be automatically fulfilled
                            {{!$valid && count($errors)>0?'('.implode('|',$errors).')':''}}
                            @if(request()->get('ManagerApproval')=='')
                                <a href="{{url()->full()}}&ManagerApproval=2">Override restrictions</a>
                            @endif
                        </div>
                    @endif
                @endif
                <table class="table table-sm">
                    <tr>
                        <th></th>

                        <th></th>
                        <th>Action</th>
                        <th>Qty <br>ord</th>
                        <th>Qty <br>ship</th>
                        <th>Description</th>
                        <th>Supplier/Sup Ord Id</th>
                        <th>Tracking</th>
                        <th>SKU</th>
                        <th>Manufacturer</th>
                        <th>Mfg/linecode</th>

                        <th>Dimensions(W/H/L)</th>

                        <th>Weight</th>

                        <th>Price</th>
                        <th>Item total</th>


                    </tr>
                    @foreach($invoice->lineitem as $lineitem)
                        <tr>
                            <td>

                                <a data-bs-toggle="collapse" href="#table{{$lineitem->pk}}" role="button"
                                   aria-expanded="true" aria-controls="table{{$lineitem->pk}}"><i
                                            class="las la-plus-square"></i></a>

                            </td>

                            <td>
                                @if(isset($lineitem->product->image) && $lineitem->product->image->getOriginalAttribute()!="")
                                    <img src="{{getImagePath($lineitem->product->image->getOriginalAttribute())}}"
                                         style="max-height: 100px;max-width: 100px;width: auto;border-radius: 3px;">
                                @elseif($lineitem->image!=="")
                                    <img src="{{getImagePath($lineitem->image)}}"
                                         style="max-height: 100px;max-width: 100px;width: auto;border-radius: 3px;">
                                @endif
                            </td>
                            <td>
                                <nav class="navbar navbar-expand-lg bg-light" style="min-height: 1em">
                                    <ul class="nav navbar-nav me-auto mb-0 mb-lg-0">
                                        <li class="nav-item">
                                            <a class="btn btn-square btn-primary  btn-sm  " type="button"
                                               href="{{backpack_url('dbs/'.config('tenant_db').'/fbs/fbslineitem/'.$lineitem->pk.'/edit')}}">Edit</a>
                                        </li>
                                        @if(in_array($lineitem->lineStatus,['4','X']))
                                            <span class="btn btn-square btn-dark  btn-sm  " type="button"
                                                  style="cursor: default">Cancelled</span>
                                        @elseif($lineitem->track_num=="")
                                            <li class="nav-item">
                                                <a class="btn btn-square btn-danger  btn-sm  " type="button"
                                                   href="{{sophio_route('fbs/fbsorder.cancellineitem',[ 'id'=>$invoice->pk,'lineitempk'=>$lineitem->pk])}}"

                                                >Cancel</a>
                                                @else
                                                    @if(in_array($lineitem->lineStatus,['R','8']))
                                                        <span class="btn btn-square btn-dark  btn-sm  " type="button"
                                                              style="cursor: default">Returned</span>
                                                        @else
                                                    <span class="btn btn-square btn-success  btn-sm  " type="button"
                                                          style="cursor: default">Shipped</span>
                                                        @endif
                                                @endif
                                            </li>
                                    </ul>
                                </nav>
                            </td>
                            <td>{{$lineitem->qty_ord}}
                            @if($invoice->custtype=='WAL' && $lineitem->qty_ord>1 && $lineitem->marketplaceitem->pack_qty>1)
                                (Pack)
                                @endif
                            </td>
                            <td>{{$lineitem->qty}}</td>


                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-secondary dropdown-toggle" type="button"
                                            id="descript_{{$lineitem->pk}}" data-bs-toggle="dropdown"
                                            aria-haspopup="true" aria-expanded="false" title="{{$lineitem->descript}}">
                                        {{str()->limit($lineitem->descript,20)}}
                                    </button>
                                    <div class="dropdown-menu" aria-labelledby="descript_{{$lineitem->pk}}">
                                        <span class="dropdown-item"> {{str()->limit($lineitem->descript,200)}}</span>
                                        <a class="dropdown-item"
                                           href="{{backpack_url('dbs/'.config('tenant_db').'/productwarehouse?mfgcode='.$lineitem->linecode.'&part_number_unformatted='.unformatString($lineitem->sku))}}">Inventory</a>
                                        <a class="dropdown-item"
                                           href="{{backpack_url('/productfeed?mfg_code='.$lineitem->linecode.'&part_number='.$lineitem->sku)}}">Product
                                            Feed</a>
                                        @hasrole('super-admin')
                                        <a class="dropdown-item"
                                           href="{{backpack_url('/productfeed/partstatus?mfg_code='.$lineitem->linecode.'&sku='.$lineitem->sku)}}">Disable Item</a>

                                        @endhasrole
                                        <a class="dropdown-item"
                                           href="{{backpack_url('/pim/product?mfg_code='.$lineitem->linecode.'&part_number_unformatted='.unformatString($lineitem->sku))}}">Product
                                            Information</a>
                                        @if($invoice->custtype=='WAL')
                                            @if($lineitem->marketplaceitem->itemresponse)
                                                <a class="dropdown-item"
                                                   href="https://www.walmart.com/ip/{{Str::slug($lineitem->marketplaceitem->itemresponse->product_name)}}/{{$lineitem->mrktplcsku}}">View
                                                    on Walmart</a>
                                            @else
                                                <a class="dropdown-item"
                                                   href="https://www.walmart.com/search/?query={{$lineitem->mrktplcsku}}">View
                                                    on Walmart</a>
                                            @endif

                                            <a class="dropdown-item"
                                               href="{{backpack_url('/walmart/marketplaceitem/'.$lineitem->marketplaceitem->pk.'/show')}}">Price Feed History</a>

                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td>
                                @if($lineitem->supplier)
                                    @hasrole('super-admin')
                                    <a href="{{backpack_url('dbs/'.config('tenant_db').'/fbs/supplier/'.$lineitem->supplier->PK.'/edit')}}">
                                        {{$lineitem->supplier->NAME}}</a>
                                @else
                                    {{$lineitem->supplier->NAME}}
                                    @endhasrole
                                @endif / {{$lineitem->sup_ord_id}}

                            </td>
                            @if(sophiosettings()->getStore()['STORETYPE']==='TE')
                                <td id="track_td_{{$lineitem->pk}}">@if($lineitem->track_num!=="") <span id="tracknum_{{$lineitem->pk}}">{!! trackNumExternalLinks($lineitem->track_num) !!}</span>
                                <a href="#"  id="tracknum_link_{{$lineitem->pk}}" data-bs-sku="{{$lineitem->mfr.' '.$lineitem->sku}}"  data-bs-handlingc="{{$lineitem->handlingc}}"
                                   data-bs-toggle="modal" data-bs-target="#tracknum_modal" data-bs-lineitempk="{{$lineitem->pk}}" data-bs-tracknum="{{$lineitem->track_num}}"><button  class="btn btn-square btn-danger  btn-sm">Edit</button></a>
                                @else
                                    <a href="#"  id="tracknum_link_{{$lineitem->pk}}"    data-bs-handlingc="{{$lineitem->handlingc}}" data-bs-sku="{{$lineitem->mfr.' '.$lineitem->sku}}" data-bs-toggle="modal" data-bs-target="#tracknum_modal" data-bs-lineitempk="{{$lineitem->pk}}" data-bs-tracknum="{{$lineitem->track_num}}"><button  class="btn btn-square btn-danger  btn-sm">Add</button></a> @endif</td>
                            @else
                                <td id="track_td_{{$lineitem->pk}}">{!! trackNumExternalLinks($lineitem->track_num) !!}</td>
                                @endif
                            <td>{{$lineitem->sku}}</small></td>
                            <td>{{$lineitem->mfr}}</td>
                            <td>{{$lineitem->linecode}} / {{$lineitem->wdlinecode}}</td>

                            <td>{{$lineitem->skuwidth}}/{{$lineitem->skuheight}}/{{$lineitem->skulength}}</td>

                            <td>{{$lineitem->weight}}</td>


                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-secondary dropdown-toggle" type="button"
                                            id="price_{{$lineitem->pk}}" data-bs-toggle="dropdown" aria-haspopup="true"
                                            aria-expanded="false">
                                        $ {{$lineitem->price}}
                                    </button>
                                    <div class="dropdown-menu" aria-labelledby="price_{{$lineitem->pk}}">
                                        <button class="dropdown-item" type="button">Sold price:
                                            $ {{$lineitem->price}}</button>
                                        <button class="dropdown-item" type="button">Core price:
                                            $ {{$lineitem->coreprice}}</button>
                                        <button class="dropdown-item" type="button">Supplier feed price:
                                            $ {{$lineitem->cost}}</button>
                                        <button class="dropdown-item" type="button">Paid supplier cost:
                                            $ {{$lineitem->scost}}</button>
                                        <button class="dropdown-item" type="button">Wholesale price:
                                            $ {{our_format($lineitem->nwtosophio)}}</button>
                                        <button class="dropdown-item" type="button">Contract price:
                                            $ {{$lineitem->cprice}}</button>
                                        <button class="dropdown-item" type="button">Amazon Price:
                                            $ {{$lineitem->amazonprc}}</button>
                                        @if($lineitem->supplier!=null)
                                        <button class="dropdown-item" type="button">
                                            <a href="{{sophio_route('historyfeed.index',['linecode'=>$lineitem->wdlinecode,'part_number_unformatted'=>$lineitem->sku,'contactpk'=>$lineitem->supplier->contactpk])}}">Price History</a>
                                        </button>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td>$ {{$lineitem->itemtotal}}</td>

                            <td>
                                @if(isset($valid))
                                    @if($valid && $result['resolved'] && isset($result['fulfilable']) && in_array($lineitem->pk,$result['fulfilable']))
                                        <button type="button" class="btn btn-sm btn-success">Auto
                                            @php
                                                $sups = 0;
                                                if(isset($itemList[$lineitem->pk]))
                                                foreach($itemList[$lineitem->pk]->sources as $w) {
                                                            if($w->qty_found>=$itemList[$lineitem->pk]->qty_req && $w->cost>0) {
                                                                $sups++;
                                                            }
                                                }
                                            @endphp <span class="badge badge-light">{{($sups)}}     </span>
                                        </button>
                                    @else
                                        @php
                                            $buy = 0;
                                             if(isset($itemList[$lineitem->pk]))
                                            foreach($itemList[$lineitem->pk]->sources as $w) {
                                                        if($w->qty_found>=$itemList[$lineitem->pk]->qty_req && $w->cost>0) {
                                                            $buy++;
                                                        }
                                            }
                                        @endphp
                                        @if($buy>0)
                                            <button type="button" class="btn btn-sm btn-warning"> Manual <span
                                                        class="badge badge-light">{{($buy)}}     </span>
                                            </button>
                                        @else
                                            <button type="button" class="btn btn-sm btn-danger"> Unfillable
                                            </button>
                                        @endif

                                    @endif
                                @endif
                            </td>

                        </tr>

                        <tr class="multi-collapse collapse hide" id="table{{$lineitem->pk}}">
                            <td colspan="14">
                                <div class="row justify-content-md-center">
                                    <div class="col-md-9">
                                        <table class="table  table-sm">
                                            <tr>
                                                <th>PK</th>

                                                <th>WHSE</th>
                                                <th>Sup. order id</th>
                                                <th>Sup. invoice pk</th>

                                                <th>Marketplace SKU</th>
                                                <th>Marketplace ID</th>
                                                <th>Ordered price</th>
                                                <th>Core Price</th>
                                                <th>Data feed price</th>
                                                <th>Handling Cost</th>
                                                <th>Supplier cost</th>
                                                <th>Part Share wholesale</th>
                                                <th>Contract price</th>

                                                <th>Backorder qty</th>
                                                @if($invoice->custtype=="WAL")
                                                    <th>ShipNode Supplier</th>

                                                @endif
                                            </tr>
                                            <tr>
                                                <td>{{$lineitem->pk}}</td>

                                                <td>{{$lineitem->whse}}</td>
                                                <td>{{$lineitem->sup_ord_id}}</td>
                                                <td>{{$lineitem->supinvpk}}</td>
                                                <td>{{$lineitem->mrktplcsku}}</td>
                                                <td>{{$lineitem->mrktplcid}}</td>
                                                <td>$ {{$lineitem->price}}</td>
                                                <td>$ {{$lineitem->coreprice}}</td>
                                                <td>$ {{$lineitem->cost}}</td>
                                                <td>$ {{$lineitem->handlingc}}</td>
                                                <td>$ {{$lineitem->scost}}</td>
                                                <td>$ {{$lineitem->nwtosophio}}</td>
                                                <td>$ {{$lineitem->cprice}}</td>

                                                <td>{{$lineitem->qty_bo}}</td>
                                                @if($invoice->custtype=="WAL")
                                                    <td> {{(new \Sophio\Walmart\Actions\GetOriginalSupplier())($invoice)->getSupplierDisplayName()}}</td>

                                                @endif

                                            </tr>
                                        </table>
                                        @if($lineitem->getRawOriginal('list1')!=='' || $lineitem->getRawOriginal('list2')!=='')
                                            <div class="row">
                                                @if($lineitem->getRawOriginal('list1')!=='')
                                                    <div class="col-md-6 bold-labels">

                                                        <div class="language-xml" style="max-width: 1000px">
                                                            <pre><code> {{ str_replace('> <',">\n<",$lineitem->getRawOriginal('list1')) }}</code></pre>

                                                        </div>

                                                    </div>
                                                @endif
                                                @if($lineitem->getRawOriginal('list2')!=='')
                                                    <div class="col-md-6 bold-labels">

                                                        <div class="language-xml" style="max-width: 1000px">
                                                            <pre><code> {{ str_replace('> <',">\n<",$lineitem->getRawOriginal('list2')) }}</code></pre>
                                                        </div>

                                                    </div>
                                                @endif
                                            </div>
                                        @endif</div>
                                </div>
                            </td>
                        </tr>

                    @endforeach
                </table>

            </div>

        </div>
    </form>


</div>
