@extends(backpack_view('blank'))

@section('header')
    <div class="container-fluid">
        <h2>
            <span class="text-capitalize">Import Statistics</span>
            <small>Overview of CSV import activity</small>
        </h2>
    </div>
@endsection

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fa fa-chart-line"></i> 
                    Overall Statistics
                </h3>
                <div class="card-tools">
                    <a href="{{ route('general-csv-import.index') }}" class="btn btn-primary">
                        <i class="fa fa-upload"></i> New Import
                    </a>
                    <a href="{{ route('general-csv-import.history') }}" class="btn btn-info">
                        <i class="fa fa-history"></i> View History
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h2>{{ number_format($stats['total_imports']) }}</h2>
                                <p class="mb-0">Total Imports</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h2>{{ number_format($stats['successful_imports']) }}</h2>
                                <p class="mb-0">Successful</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <h2>{{ number_format($stats['failed_imports']) }}</h2>
                                <p class="mb-0">Failed</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h2>{{ number_format($stats['total_records_imported']) }}</h2>
                                <p class="mb-0">Records Imported</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h2>{{ number_format($stats['recent_imports']) }}</h2>
                                <p class="mb-0">Imports This Week</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-secondary text-white">
                            <div class="card-body text-center">
                                @php
                                    $successRate = $stats['total_imports'] > 0 ? ($stats['successful_imports'] / $stats['total_imports']) * 100 : 0;
                                @endphp
                                <h2>{{ number_format($successRate, 1) }}%</h2>
                                <p class="mb-0">Overall Success Rate</p>
                            </div>
                        </div>
                    </div>
                </div>

                @if($stats['total_imports'] > 0)
                <div class="mt-4">
                    <h5>Success Rate Breakdown</h5>
                    <div class="progress" style="height: 30px;">
                        @php
                            $successPercentage = ($stats['successful_imports'] / $stats['total_imports']) * 100;
                            $failedPercentage = ($stats['failed_imports'] / $stats['total_imports']) * 100;
                        @endphp
                        <div class="progress-bar bg-success" 
                             role="progressbar" 
                             style="width: {{ $successPercentage }}%"
                             title="Successful: {{ number_format($successPercentage, 1) }}%">
                            {{ number_format($successPercentage, 1) }}% Success
                        </div>
                        <div class="progress-bar bg-danger" 
                             role="progressbar" 
                             style="width: {{ $failedPercentage }}%"
                             title="Failed: {{ number_format($failedPercentage, 1) }}%">
                            {{ number_format($failedPercentage, 1) }}% Failed
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>

        @if($topTables->count() > 0)
        <div class="card mt-4">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fa fa-table"></i> 
                    Most Imported Tables
                </h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Table Name</th>
                                <th>Import Count</th>
                                <th>Total Records</th>
                                <th>Average per Import</th>
                                <th>Usage</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($topTables as $table)
                            <tr>
                                <td>
                                    <code>{{ $table->target_table }}</code>
                                </td>
                                <td>
                                    <span class="badge badge-primary">
                                        {{ number_format($table->import_count) }}
                                    </span>
                                </td>
                                <td>
                                    {{ number_format($table->total_records) }}
                                </td>
                                <td>
                                    {{ number_format($table->total_records / $table->import_count) }}
                                </td>
                                <td>
                                    @php
                                        $usagePercentage = ($table->import_count / $stats['total_imports']) * 100;
                                    @endphp
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar bg-info" 
                                             role="progressbar" 
                                             style="width: {{ $usagePercentage }}%">
                                            {{ number_format($usagePercentage, 1) }}%
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        @endif

        <div class="card mt-4">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fa fa-lightbulb"></i> 
                    Tips & Best Practices
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>📊 Import Performance</h5>
                        <ul>
                            <li>Use batch sizes between 100-500 for optimal performance</li>
                            <li>Validate data before importing to reduce errors</li>
                            <li>Use unique field detection to avoid duplicates</li>
                            <li>Test with small samples first</li>
                        </ul>

                        <h5 class="mt-4">🔧 Data Preparation</h5>
                        <ul>
                            <li>Ensure CSV files are properly formatted</li>
                            <li>Use UTF-8 encoding for special characters</li>
                            <li>Remove empty rows and columns</li>
                            <li>Validate data types match database requirements</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>✅ Validation Rules</h5>
                        <ul>
                            <li><code>required</code> - Field must have a value</li>
                            <li><code>email</code> - Must be valid email format</li>
                            <li><code>numeric</code> - Must be a number</li>
                            <li><code>max:255</code> - Maximum length/value</li>
                            <li><code>unique:table,column</code> - Must be unique</li>
                        </ul>

                        <h5 class="mt-4">🚀 Advanced Features</h5>
                        <ul>
                            <li>Use column mapping for flexible data import</li>
                            <li>Set up validation rules for data quality</li>
                            <li>Monitor import logs for troubleshooting</li>
                            <li>Export results for record keeping</li>
                        </ul>
                    </div>
                </div>

                @if($stats['failed_imports'] > 0)
                <div class="alert alert-warning mt-4">
                    <h5><i class="fa fa-exclamation-triangle"></i> Improvement Opportunities</h5>
                    <p>You have {{ number_format($stats['failed_imports']) }} failed imports. Common causes include:</p>
                    <ul class="mb-0">
                        <li>Invalid data format or missing required fields</li>
                        <li>Data too long for database columns</li>
                        <li>Foreign key constraint violations</li>
                        <li>Duplicate values in unique fields</li>
                    </ul>
                </div>
                @endif

                @if($stats['successful_imports'] > 0 && $successRate >= 90)
                <div class="alert alert-success mt-4">
                    <h5><i class="fa fa-check-circle"></i> Excellent Performance!</h5>
                    <p>Your import success rate of {{ number_format($successRate, 1) }}% is excellent. Keep up the good work!</p>
                </div>
                @endif
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fa fa-download"></i> 
                    Export Statistics
                </h3>
            </div>
            <div class="card-body">
                <p class="text-muted">Download detailed statistics for reporting and analysis.</p>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary" onclick="exportStats('csv')">
                        <i class="fa fa-file-csv"></i> Export as CSV
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="exportStats('json')">
                        <i class="fa fa-file-code"></i> Export as JSON
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="window.print()">
                        <i class="fa fa-print"></i> Print Report
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('after_scripts')
<script>
function exportStats(format) {
    var stats = @json($stats);
    var topTables = @json($topTables);
    var filename = 'import_statistics_' + new Date().toISOString().slice(0,10) + '.' + format;
    
    if (format === 'csv') {
        var csv = 'Metric,Value\n';
        csv += 'Total Imports,' + stats.total_imports + '\n';
        csv += 'Successful Imports,' + stats.successful_imports + '\n';
        csv += 'Failed Imports,' + stats.failed_imports + '\n';
        csv += 'Total Records Imported,' + stats.total_records_imported + '\n';
        csv += 'Recent Imports (7 days),' + stats.recent_imports + '\n';
        
        csv += '\nTop Tables\n';
        csv += 'Table Name,Import Count,Total Records\n';
        topTables.forEach(function(table) {
            csv += table.target_table + ',' + table.import_count + ',' + table.total_records + '\n';
        });
        
        downloadFile(csv, filename, 'text/csv');
    } else if (format === 'json') {
        var data = {
            statistics: stats,
            top_tables: topTables,
            generated_at: new Date().toISOString()
        };
        var json = JSON.stringify(data, null, 2);
        downloadFile(json, filename, 'application/json');
    }
}

function downloadFile(content, filename, contentType) {
    var blob = new Blob([content], { type: contentType });
    var url = window.URL.createObjectURL(blob);
    var a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}
</script>

<style>
@media print {
    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
        page-break-inside: avoid;
    }
    
    .btn, .alert {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }
    
    .card-tools {
        display: none;
    }
}

.progress {
    background-color: #e9ecef;
}

.table th {
    border-top: none;
}

.badge {
    font-size: 0.875em;
}

.card h2 {
    font-size: 2.5rem;
    font-weight: bold;
}

.card h5 {
    color: #495057;
    margin-bottom: 1rem;
}

.alert h5 {
    color: inherit;
}
</style>
@endsection
