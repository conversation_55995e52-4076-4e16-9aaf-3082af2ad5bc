<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta http-equiv="Content-Language" content="en"/>
    <meta name="viewarea" content="STANDALONE"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <meta name="robots" content="index,follow"/>
    <title>{{config('website.name')}}</title>

    <link rel="stylesheet" type="text/css" href="/assets/css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css"
          href="/assets/css/font-awesome.min.css">
    <link rel="stylesheet" type="text/css"
          href="/assets/css/sweet-alert.css">
    <link rel="stylesheet" type="text/css"
          href="/assets/css/bootstrap-multiselect.css">
    <link rel="stylesheet" href="/assets/css/select2.min.css">
    <link rel="canonical" href="/">
    <script src="/assets/js/jquery.min.js"></script>

    @stack('before-styles')
    <link rel="stylesheet" type="text/css" href="/assets/css/sophio-faceted-catalog-bootstrap-3.css">
    <link rel="stylesheet" href="/assets/design/{{config('website.design')}}/assets/css/style.css">
    <link rel="stylesheet" href="/assets/design/{{config('website.design')}}/assets/css/tractor.supply.css">

    @stack('after-styles')

</head>


<body>
<header>
    <div class="container-fluid">
        <div class="d-flex align-items-center mb-3">
            <a href="/"><img src="/assets/design/{{config('website.design')}}/assets/images/{{config('website.logo_filename')}}" width="149"
                             class="mr-5"/></a>
            <div class="smartsearch flex-1 ml-5 mr-3 d-none d-sm-block">
                <div class="catalogsmartsearch">
                    <form method="get" name="frm-header-search-vin" id="frm-header-search-vin"
                          class="index-frm-header-search-vind-flex" action="/catalog-2/search/">
                        <div class="flex-1 d-flex">
                            <input class="typeahead form-control flex-1 brr-0" type="text" name="keyword"
                                   id="hd-input-keyword-search"
                                   placeholder="Enter year, make, model, part number or VIN" value=""/>
                            <input type="hidden" id="hd-input-keyword-search-increment" name="incrementkeyword"
                                   value=""/>
                            <button class="searchbutton btn btn-primary blr-0" type="submit">GO</button>
                        </div>
                    </form>
                </div>
            </div>

            <ul class="nav navbar-nav navbar-right ml-3">
                <li class="myacc"><a
                            href="https://www.tractorsupply.com/LogonForm?myAcctMain=1&catalogId=10051&langId=-1&storeId=10151&cm_sp=Header_Top--Account--SignIn&URL=/"><i
                                class="fa fa-user"></i> &nbsp;My
                        Account</a></li>
                <li class="mycrt hidden-xs">
                    <a id="showcart1"
                       href="https://www.tractorsupply.com/TSCShoppingCartView?catalogId=10051&cm_sp=Header_Nav-_-Links-_-Cart&langId=-1&storeId=10151"><i
                                class="fa fa-shopping-cart"></i> &nbsp;<span class="cart-text">Cart</span> <span
                                class="cart-info">0</span></a>
                    <div id="myscartdetail" style="display:none;"><i class="fa fa-caret-up fa-2x"></i>
                        <div class="col-sm-6 left-all"></div>
                        <div class="col-sm-6 right-all"></div>
                        <div class="col-sm-12 lastline store-listing-geolocation"><a
                                    href="https://www.tractorsupply.com/TSCShoppingCartView?catalogId=10051&cm_sp=Header_Nav-_-Links-_-Cart&langId=-1&storeId=10151">view
                                cart</a></div>
                    </div>
                </li>
            </ul>
        </div>

    </div>
</header>

<main>
    @yield('content')
</main>
<footer class="footer-10">
    <div class="container">

    </div>
</footer>


<div id="cookies" class="alert alert-info alert-dismissible" role="alert"
     style="position:fixed;bottom:0;border-radius:0;width:100%;display:none;z-index:100;">
    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span>
    </button>
    This site uses cookies to provide you with a great user experience. By using this website, you accept our <a
            href="/assets/design/{{config('website.design')}}/cookies-policy.wws" style="color:#23527c;">use of
        cookies</a>.
</div>
@if($can_have_vehicle==true)
@include('catalog-2.components.ymmmodal')
@endif

@stack('before-scripts')
@include('theme::partials.js')
<script src="/assets/js/sophio/year.js"></script>

@stack('after-scripts')
</body>
</html>
