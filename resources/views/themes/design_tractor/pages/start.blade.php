@extends('theme::layouts.app')
@push('before-scripts')
<script>
function initFitWidget(){

	 window.SearchVM = new SearchViewModel();

	 window.SearchVM.showParts = function() {
		  this.addonFilter = jQuery.extend({
				"checkfit": "y",
				"make": this.selectedMake(),
				"year": this.selectedYear(),
				"model": this.selectedModel(),
				"engine": this.selectedEngine(),
				"submodel": this.selectedSubModel()
		  }, this.getAddOnFilters());
		  checkForFitment(this.baseSubmitURL + this.getPartsAddOnFilters());
	 }



	 window.SearchVM.baseAjaxURL = sophio.api_base_url+"vehicle?clientId="+sophio.api_parameters.clientId+"&nomfgcode="+sophio.api_parameters.nomfgcode+"regionid="+sophio.api_parameters.regionId;
	 window.SearchVM.baseSubmitURL = baseSubmitURL;
	 window.SearchVM.hasPartTypes = false;
	 window.SearchVM.hasSubmodels = false;
	 window.SearchVM.profile("checkFit")
    window.SearchVM.loadMakes({'smbeng':'engines','usestatic':false,'onlymake':sophio.web_parameters.onlymakes});
	ko.cleanNode(document.getElementById("frm-vehicle-locator-afmkt"));
	 ko.applyBindings(window.SearchVM, document.getElementById("frm-vehicle-locator-afmkt"));

}
function checkFit(cDesc, cSku, cMfg, cprodlink) {
	baseSubmitURL = cprodlink;
	initFitWidget();
	 $('#widget-product-fit-locator .part-title').html(cDesc);
	 $('#widget-product-fit-locator .part-number-title strong').html(cSku);
	 $('#widget-product-fit-locator .manufacturer-title  strong').html(cMfg);
	 $('#widget-product-fit-locator').modal({
		  show: true
	 });
	 if (typeof window.SearchVM === "object") {
		  window.SearchVM.baseSubmitURL = cprodlink;
	 }
}
</script>
@endpush

@section('content')
    <div class="row mt-5">
        <div class="col-md-12">
            <div id="catalog-start">
                <div class="modal fade" tabindex="-1" role="dialog" id="smart-search-modal">
                    <div class="modal-dialog" role="document">
                        <div class="modal-content">
                            <div class="modal-body">
                                <p>Our Smart Search accepts the following:</p>
                                <ul>
                                    <li> Year, Make, Model followed by the part type<br> Example: 2010 Ford F-150 Air
                                        Filter
                                    </li>
                                    <li>Part number <br> Example: 9004</li>
                                    <li> VIN of your car</li>
                                </ul>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
 
                <div class="row">
                    <div class="col-sm-6">
                        @include('catalog-2.components.ymmwidget')
                    </div>

                    <div class="col-sm-6">
                        <div class="mb-3">@include('catalog-2.components.searchbypart')</div>
                        <div class="mb-3">@include('catalog-2.components.searchbylicence')</div>
                        <div class="">@include('catalog-2.components.garage')</div>
                    </div>
                </div>

            </div>

            <div class="alert alert-info">
                <p><i class="icon icon-2x icon-exclamation"></i> If you don't know the part number, we suggest using the
                    make, year, model selector first. If you know the part number and it fails, try an alternative
                    lookup, we might have the part you are looking for but we might reference it via the manufacturer or
                    distributors number.</p>
            </div>

            <!-- Alternate search options Start -->
            <div class="alert alert-block alert-info fade in notification-alternate-lookup">
                <h6 class="alert-heading"><i class="icon-info-sign"></i> Can't find what you are looking for? Try
                    searching by one of these methods below.</h6>
                <ul class="nav nav-pills">
                    <li><a href="/catalog-2/vehicle"><i class="icon-caret-right"></i> By Vehicle</a></li>
                    <li><a href="/catalog-2/category"><i class="icon-caret-right"></i> By Category</a></li>
                    <li><a href="/catalog-2/parttype"><i class="icon-caret-right"></i> By Part Type</a></li>
                    <li><a href="/catalog-2/manufacturer"><i class="icon-caret-right"></i> By Manufacturer</a></li>
                    <li><a href="/catalog-2/search"><i class="icon-caret-right"></i> By Keyword/Part Number</a></li>
                </ul>
            </div>


            <!-- catalog supplier logo -->


        </div>
    </div>
@endsection
@push('after-scripts')

    <script type="text/javascript">
        @if(is_array(session('vehicle')))
            var selectedVehicle = "{{session('vehicle')['name']}}";
            var defaultPartTypesURL = "/catalog-2/vehicle/{{session('vehicle')['ids']['make']}}/{{session('vehicle')['ids']['year']}}/{{session('vehicle')['ids']['model']}}";
        @else
        var selectedVehicle = "";
        var defaultPartTypesURL = "/catalog-2/vehicle/";
        @endif

        var typeaheadLoaded = false;
        var select2Loaded = false;
        var defaultSearchUImode = "searchmenu";



        function loadSearch() {
            $('#input-keyword-search').show();
            $('#filterParttypes').hide();
            $('#keyword-catalog-search-submit').html('<button type="submit" class="btn btn-primary">Search</button>');
            $('#keyword-catalog-search-submit').click(function () {
                $('#frm-catalog-search').trigger('submit');
            });
            if (select2Loaded == true) {
                $('#filterParttypes').select2('destroy');
            }
            $('#input-keyword-search').typeahead(
                {
                    minLength: 2,
                    highlight: true,
                    hint: false
                },
                {
                    name: 'oincrdata',
                    displayKey: function (title) {
                        return title.value;
                    },
                    templates: {
                        suggestion: function (data) {
                            return '<div><p>' + data.value + '</p></div>';
                        }
                    },
                    source: oincrementdata2.ttAdapter()
                }).on('typeahead:selected', function ($e, data) {
                console.log(data);
                $('#input-keyword-search-increment').val(data.key);
                $('#input-keyword-search').val(data.value);
                $('#input-keyword-search').attr('searchtype', data.type);
                if (data.type == "incremental") {
                    $('#input-keyword-search').attr('year', data.data.year);
                    $('#input-keyword-search').attr('make', data.data.make);
                    $('#input-keyword-search').attr('model', data.data.model);
                    $('#input-keyword-search').attr('parttype', data.data.parttype);

                } else if (data.type == "partnumber" && 'data' in data && 'manufacturer' in data.data) {
                    $('#input-keyword-search').attr('manufacturer', data.data.manufacturer);
                }
            });
            $('#input-keyword-search').focus();
            typeaheadLoaded = true;

        }

        function loadPartsWidget(year, make, model) {
            defaultPartTypesURL = "/catalog-2/vehicle/" + make + "/" + year + "/" + model;
            $('#input-keyword-search').typeahead('destroy');
            $('#input-keyword-search').hide();
            $('#keyword-catalog-search-submit').html('<button  type="button" class="btn btn-primary"><i class="icon-filter"></i></button>');
            $('#keyword-catalog-search-submit').click(function () {
                if ($('#filterParttypes').val() && "" != $('#filterParttypes').val()) {
                    location.href = defaultPartTypesURL + "/" + $('#filterParttypes').val();
                } else {
                    window.alert("Please select a part type.");
                }
            });

            $('#filterParttypes').show();
            $('#filterParttypes').html('');
            var ur = "{{request()->api_base_url}}parttypes/" + year + "/" + make + "/" + model + "?clientId={{request()->api_parameters['clientId']}}&nomfgcode={{request()->api_parameters['nomfgcode']}}";
            $.getJSON(ur, function (data) {
                var categories = [];
                var catnames = [];
                $.each(data.parttypes, function (k, parttype) {
                    var opton = $("<option></option>");
                    opton.val(parttype.id);
                    opton.text(parttype.name);
                    if (parttype.id == "disc-brake-caliper") {
                        opton.prop('selected', true);
                    }
                    $.each(parttype.categories, function (i, cat) {
                        catnames[cat.category_id] = cat.category_name;
                        if (categories[cat.category_id] === undefined) {
                            categories[cat.category_id] = [];
                        }
                        categories[cat.category_id].push(opton);
                    });
                });
                for (var id in catnames) {
                    var optgroup = $('<optgroup></optgroup>');
                    optgroup.attr('label', catnames[id]);

                    $.each(categories[id], function (i, opt) {
                        optgroup.append(opt);
                    });

                    $('#filterParttypes').append(optgroup);
                }
                ;
                $('#filterParttypes').select2({
                    placeholder: "Example: filter or brake or gasket", dropdownParent: $('#filter-form-group'),
                    allowClear: true
                });
                $('#filterParttypes').on("select2:select", function (e) {
                    location.href = defaultPartTypesURL + "/" + $('#filterParttypes').val();
                });
            });
            select2Loaded = true;

        }

        BloodhoundData = {}
        oincrementdata2 = new Bloodhound({
            limit: 10,
            datumTokenizer: function (d) {
                return Bloodhound.tokenizers.whitespace(d.value);
            },
            queryTokenizer: Bloodhound.tokenizers.whitespace,
            sorter: function (a, b) {
                return 0;
            },
            dupDetector: function (remoteMatch, localMatch) {
                return (remoteMatch.value === localMatch.value);
            },
            prefetch: {
                url: '{{request()->api_base_url}}search/prefetchdata?clientId={{request()->api_parameters['clientId']}}&nomfgcode={{request()->api_parameters['nomfgcode']}}',
                filter: function (list) {
                    return $.map(list, function (title) {
                        return {value: title,};
                    });
                }
            },
            remote: {
                url: '{{request()->api_base_url}}search/incrementalSearch?returntype=json&clientId={{request()->api_parameters['clientId']}}&nomfgcode={{request()->api_parameters['nomfgcode']}}&q=%QUERY',
                wildcard: '%QUERY',
                transform: function (response) {
                    BloodhoundData = response;
                    return response;
                }
            }
        });
        oincrementdata2.initialize();


        $(document).ready(function () {

            if (selectedVehicle == "" && defaultSearchUImode != "vehiclemenu") {
                if ($('#input-keyword-search').val() != "") {
                    $('#input-keyword-search').attr('searchtype', 'partnumber');
                }
                loadSearch();
            } else {
                if (defaultSearchUImode == "vehiclemenu") {
                    $('.search_menu_vehicle[data-vehicle="' + "".toUpperCase() + '"]').trigger("click");
                    loadPartsWidget('2017', 'bentley', 'continental');
                } else {
                    loadSearch();
                }
            }
            var $inputkwsearch = $("input-keyword-search");
            //$inputkwsearch.width($("#frm-catalog-search").innerWidth()-(10+$("#frm-catalog-search.add-on").outerWidth()+$("#frm-catalog-search.btn-catalog-search").outerWidth()+($inputkwsearch.outerWidth()-$inputkwsearch.innerWidth())));
            $inputkwsearch.popover({content: "Enter a part number (e.g:9004), or a keyword (e.g Oil), or multiple keywords (e.g Oil Filter). You can also include any information you might have about your vehicle (e.g Year Model Engine Size etc)."});
            var $inputkwsearch = $("#frm-catalog-search .keyword-catalog-search");
            $('#frm-catalog-search').submit(function () {
                var keyword = $('#frm-catalog-search').find("input[name='keyword']").val();
                if (keyword === "") {
                    alert("Please enter a keyword or part number first");
                    return false;
                }
                var incremental = $('#input-keyword-search-increment').val();
                var url = this.action;
                if ($('#input-keyword-search').attr('searchtype') == 'incremental') {
                    url = url + $('#frm-catalog-search').find("input[name='keyword']").val() + "?searchtype=incremental";
                    if ($('#input-keyword-search').attr('parttype')) {
                        url = '/catalog-2/vehicle/' + $('#input-keyword-search').attr('make') + '/' + $('#input-keyword-search').attr('year') + '/' + $('#input-keyword-search').attr('model') + '/' + $('#input-keyword-search').attr('parttype');
                    }
                    $(location).attr('href', url);
                } else if ($('#input-keyword-search').attr('searchtype') == 'partnumber') {
                    url = url + $('#frm-catalog-search').find("input[name='incrementkeyword']").val() + "?searchtype=partnumber";
                    if ($('#input-keyword-search').attr('manufacturer')) {
                        url = url + "&manufacturer=" + $('#input-keyword-search').attr('manufacturer');
                    }
                    $(location).attr('href', url);
                } else {
                    var vin = $('#frm-catalog-search').find("input[name='keyword']").val();
                    if (!validateVin(vin)) {
                        if (BloodhoundData.length > 0) {
                            var first = BloodhoundData[0];
                            if (first.type == "incremental") {
                                url = url + first.key + "?searchtype=incremental";
                                if (first.hasOwnProperty('data')) {
                                    url = '/catalog-2/vehicle/' + first.data.make + '/' + first.data.year + '/' + first.data.model + '/' + first.data.parttype;
                                }
                                $(location).attr('href', url);
                            } else {
                                url = url + first.key + "?searchtype=partnumber";
                                $(location).attr('href', url);
                            }
                        } else {

                            $('#smart-search-modal').modal();
                        }
                    } else {
                        url = url + $('#frm-catalog-search').find("input[name='keyword']").val() + "?searchtype=vin";
                        $(location).attr('href', url);
                    }
                }

                return false;
            });
        });
    </script>
    <script>
        function getURLParameters(url) {

            var result = {};
            var searchIndex = url.indexOf("?");
            if (searchIndex == -1) return result;
            var sPageURL = url.substring(searchIndex + 1);
            var sURLVariables = sPageURL.split('&');
            for (var i = 0; i < sURLVariables.length; i++) {
                var sParameterName = sURLVariables[i].split('=');
                result[sParameterName[0]] = sParameterName[1];
            }
            return result;
        };
        $(document).ready(function () {

            if (typeof fetchnexpartpage !== 'function') {
                var fetchnexpartpage = function (curl) {
                    params = '';
                    if ($('input[name=searchin]').val() == 0) {
                        baset = curl.split('?')[0];
                        base = baset.substr(0, baset.lastIndexOf('/')) + '/';
                        params = getURLParameters(curl);
                        curl = base;
                    }
                    chain_of_prices = [];
                    $('a.pagenum').unbind("click");
                    $(".part-page")
                        .block({
                            message: '<div class="alert alert-info" style="margin:0"><h3><i class="icon-spinner icon-spin icon-large"></i>Please wait Loading next page... </h3></div>',
                            centerY: false,
                            css: {top: 0, width: 'auto', border: '3px solid #000'}
                        })
                        .load(curl + '&ajaxpage=true', params, function (response, status, xhr) {
                            if (status == "error") {
                                // navigate to url
                                document.location.href = curl;
                                return false;
                            } else {
                                $('a.pagenum').click(function (event) {
                                    fetchnexpartpage(this.href);
                                    return false;
                                });
                                applyPageSelects();
                                $("link[rel='canonical']").attr("href", curl);

                                // Check to see if History.js is enabled for our Browser
                                if (window.History.enabled) {
                                    var title = $('title,Title').html();
                                    window.History.pushState(null, title, curl);
                                    var
                                        rootUrl = window.History.getRootUrl(),
                                        State = window.History.getState(),
                                        url = State.url,
                                        relativeUrl = url.replace(rootUrl, '');

                                    // Inform Google Analytics of the change
                                    if (typeof _gaq !== 'undefined') {
                                        _gaq.push(['_trackPageview', relativeUrl]);
                                    }

                                }

                                // Inform ReInvigorate of a state change
                                if (typeof window.reinvigorate !== 'undefined' && typeof window.reinvigorate.ajax_track !== 'undefined') {
                                    reinvigorate.ajax_track(curl);
                                    // ^ we use the full url here as that is what reinvigorate supports
                                }


                            }
                            $(".part-page").unblock();
                            sp.ChainPrices(chain_of_prices);
                            event.preventDefault();
                            return false;
                        });


                }

                $('a.pagenum').unbind("click").click(function () {
                    fetchnexpartpage(this.href);
                    return false;
                });
            }

            function applyPageSelects() {
                $('.sortby').change(function () {
                    var newaction = this.value;
                    var oldaction = document.getElementById('store').action;
                    if (oldaction.indexOf('?') != -1) {
                        if (oldaction.indexOf('sortby') != -1) {
                            var regex = /sortby=(\w+)/;
                            document.getElementById('store').action = oldaction.replace(regex, 'sortby=' + newaction);
                        } else {
                            document.getElementById('store').action += '&sortby=' + newaction;
                        }

                    } else {
                        document.getElementById('store').action += '?sortby=' + newaction;
                    }
                    $('#store').submit();
                });

                $('.filterby').change(function () {
                    var newaction = this.value;
                    var oldaction = document.getElementById('store').action;
                    if (oldaction.indexOf('?') != -1) {
                        if (oldaction.indexOf('filterby') != -1) {
                            var regex = /filterby=(\w+)/;
                            document.getElementById('store').action = oldaction.replace(regex, 'filterby=' + newaction);
                        } else {
                            document.getElementById('store').action += '&filterby=' + newaction;
                        }

                    } else {
                        document.getElementById('store').action += '?filterby=' + newaction;
                    }
                    $('#store').submit();
                });
            }

            applyPageSelects();

            function getUrlVars() {
                var vars = [], hash;
                var hashes = window.location.href.slice(window.location.href.indexOf('?') + 1).split('&');
                for (var i = 0; i < hashes.length; i++) {
                    hash = hashes[i].split('=');
                    vars.push(hash[0]);
                    vars[hash[0]] = hash[1];
                }
                return vars;
            }

            var sortu = getUrlVars()["sortby"];
            if (sortu) {
                $('.sortby').val(sortu);
            }
        });

        function replaceUrlParam(url, paramName, paramValue) {
            if (paramValue == null) {
                paramValue = '';
            }
            var pattern = new RegExp('\\b(' + paramName + '=).*?(&|$)');
            if (url.search(pattern) >= 0) {
                return url.replace(pattern, '$1' + paramValue + '$2');
            }
            url = url.replace(/\?$/, '');
            return url + (url.indexOf('?') > 0 ? '&' : '&') + paramName + '=' + paramValue;
        }
    </script>

    <script type="text/javascript">




        $(document).ready(function () {
            var $inputskusearch = $("#frm-search-partnumber input[name='partnumber']");
            $inputskusearch.popover({content: "Enter a part number (e.g:9004) to search the catalog."});

            var $inputvinsearch = $("#frm-search-vin input[name='vin']");

            $inputvinsearch.popover({content: "Every car or truck has a serial number, or sort of fingerprint, called a Vehicle Identification Number, or VIN. Sometimes, the easiest method of looking up your vehicle is by your VIN number. Your VIN can be found in a number of locations and is made up of 17 digits and characters."});

            var $inputkwsearch = $("#frm-search-keyword input[name='keyword']");

            $inputkwsearch.popover({content: "Enter  a keyword (e.g Oil), or multiple keywords (e.g Oil Filter). You can also include any information you might have about your vehicle (e.g Year Model Engine Size etc"});

            $('#frm-search-partnumber').submit(function () {
                var $input = $(this).find("input:first");
                if ($input.val() === "") {
                    alert("Please enter a part number first!");
                    $input.focus();
                    return false;
                }

                var url = this.action + $input.val().replace(/\s/g, '') + "?searchtype=partnumber";
                $(location).attr('href', url);
                return false;
            });

            $('#frm-search-keyword').submit(function () {

                var $input = $(this).find("input:first");
                if ($input.val() === "") {
                    alert("Please enter a keyword first!");
                    $input.focus();
                    return false;
                }

                var url = this.action + $input.val();
                $(location).attr('href', url);
                return false;
            });

            $('#frm-search-vin').submit(function () {
                var $input = $(this).find("input:first");
                if ($input.val() === "") {
                    alert("Please enter a VIN first!");
                    $input.focus();
                    return false;
                }

                if (valvin($input.val()) === false) {
                    alert("The VIN number entered appears invalid. Please double check!");
                    $input.focus().select();

                    return false;
                }

                var url = this.action + $input.val() + "?searchtype=vin";
                $(location).attr('href', url);
                return false;
            });

        });

    </script>

    <script>
        $("#stateplate option[value='']").prop('selected', true);
    </script>

    

@endpush
