@if($part['pricing']['sellprice']>0 )
<button class="btn btn-primary  btn-block  buy-button"
        type="submit">
    add to cart&nbsp;&nbsp;<i  class="icon icon-caret-right icon-large"></i></button>
<input type="hidden" name="cartdata_p__{{$part['product_sku']}}"
       value="{{base64_encode(json_encode($part))}}">
<input type="hidden" name="partuniqueid" value="p__{{$part['product_sku']}}">
@else
    <button class="btn btn-info  btn-block  request-button"
            type="button">
        request quote&nbsp;&nbsp;<i  class="icon icon-caret-right icon-large"></i></button>
    <input type="hidden" name="cartdata_p__{{$part['product_sku']}}"
           value="{{base64_encode(json_encode($part))}}">
    <input type="hidden" name="partuniqueid" value="p__{{$part['product_sku']}}">
@endif


@once
    @push('after-scripts')
        <script type="text/javascript">
            var chain_of_prices = [];
            @if(isset($question_facets) && count($question_facets)>0)
            $(document).ready(function () {

                $('.buy-button').on('click', function () {
                    window.questioner.showPopup();
                    return false;
                });
            });
            @endif
        </script>
    @endpush
@endonce