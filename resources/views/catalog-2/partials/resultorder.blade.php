<div class="row toolbar" style="margin-bottom: 0">
    <!-- layout selectors -->
    <div class="col-sm-2 text-center-left">
        <div class="paging-layout-selector btn-group" role="group" aria-label="grid-layout">
            <!-- show part list layout selector only if we have more then 2 items -->

            <a href="javascript:void(0)" title="Currently displaying parts in grid"
               class="btn btn-default"><i class="icon-th text-success"></i></a>
            <a href="javascript:void(0)"
               onclick="location.href=UpdateQueryString('showas','list','{{url()->full()}}')"
               title="Show Parts as Listing" class="btn btn-default"><i
                        class="icon-th-list text-danger"></i></a>

        </div>
    </div>
    <form class="form-inline" name="store" id="store" method="post"
          action="{{url()->full()}}">
        <!-- sort by -->
        <div class="col-sm-8">
            <div class="sort-by">
                <select class="sortby" name="facetedcatalogorderby">
                    <option name="sortby" value="distance_asc">Sort by: Distance</option>
                    <option name="sortby" value="price_asc">Sort by: Price low</option>
                    <option name="sortby" value="price_desc">Sort by: Price high</option>
                    <option name="sortby" value="weight_asc">Sort by: Relevancy</option>
                </select>
            </div>
        </div>
    </form>
    @if($count/$per_page>1)
        <ul class="pagination pagination-sm">
            <li @if($current_page<1)class="disabled" @endif>
                @if($current_page<1)
                    <a href="javascript:void(0)" title="Previous Page"><span aria-hidden="true">«</span></a>
                @else
                    <a href="{{request()->fullURlWithQuery(['currentpage'=>$current_page-1,'_url'=>null])}}"
                       title="Previous Page"><span aria-hidden="true">«</span></a>
                @endif
            </li>
            @for($page=0;$page<floor($count/$per_page);$page++)

                @if($page == $current_page)
                    <li class="active hidden-phone"><a href="javascript:void(0);">{{$page}}</a></li>
                @else
                    <li class="hidden-phone"><a class="pagenum"
                                                href="{{request()->fullURlWithQuery(['currentpage'=>$page,'_url'=>null])}}">{{$page}}</a>
                    </li>
                @endif


            @endfor
            <li @if($current_page>=floor($count/$per_page))class="disabled" @endif>
                @if($current_page>=floor($count/$per_page))
                    <a href="javascript:void(0)" title="Next Page"><span aria-hidden="true">»</span></a>
                @else
                    <a href="{{request()->fullURlWithQuery(['currentpage'=>$current_page+1,'_url'=>null])}}"
                       title="Next Page"><span aria-hidden="true">»</span></a>
                @endif
            </li>
        </ul>
@endif
<!-- paging as numbering -->
    <div class="col-sm-12 text-center">
        <p style="font-size:12px;" class="pagination-text text-center text-muted">
            <strong><span class="Hidden-phone">Showing: </span> 1
                - @if( ($current_page+1)*$per_page<$count) {{($current_page+1)*$per_page}}@else {{$count}} @endif
                of {{$count}} Items</strong>

        </p></div>

</div>

