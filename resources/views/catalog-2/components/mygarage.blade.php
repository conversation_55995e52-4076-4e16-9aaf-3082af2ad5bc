<!-- catalog-2.components.mygarage -->
<section id="widget-garage-dropdown">
    <div class="dropdown">
        <a class="btn btn-mini btn-primary dropdown-toggle" id="garage-dropdown"
           data-toggle="dropdown"
           title="You have - vehicle(s) in your garage.">
            Your Garage (0) <b class="caret"></b>
        </a>
        <ul class="dropdown-menu dropdown-menu-right" role="menu"
            aria-labelledby="garage-dropdown" id="garage-list">

            <li class="divider"></li>
            <li>
                <a href="/catalog-2/garage">
                    <i class="icon-wrench"></i> Manage your garage
                </a>
            </li>
        </ul>
    </div>
</section>
@once
    @push('after-scripts')
        <script>

            $(document).ready(function () {
                mygarage = new MyGarage();
                if (mygarage.hasVehicles()) {
                    garage = mygarage.getVehicles()
                    for (v in garage) {
                        vehicle = garage[v];
                        link = new URL('https://'+window.location.hostname+"/catalog-2/vehicle/" + vehicle.make + "/" + vehicle.year + "/" + vehicle.model);
                        params = new URLSearchParams(link);
                        if(vehicle.engine!="" && vehicle.engine!="ALL") {
                            params.append('engine',vehicle.engine);
                        }
                        if(vehicle.submodel!="" && vehicle.submodel!="ALL") {
                            params.append('submodel',vehicle.submodel);
                        }
                        if(params.toString()!=="") {
                            link+='?'+params.toString();
                        }
                        image_url = "https://images.sophio.com/vehicle/general/" + vehicle.make + "/" + vehicle.make + "_" + vehicle.model + ".png";
                        let li = $('<li style="border-bottom: 1px solid #eee; text-align: center;">' +
                            '<a href="' + link + '" title="Manage your garage">' +
                            '<img src="' + image_url + '"  height="100" alt="' + vehicle.year + ' ' + vehicle.make + ' ' + vehicle.model + '" onerror="this.onerror=null; this.src=\'https://images.sophio.com/vehicle/coming-soon.jpg\'">' +
                            '<div style="text-align: center;"><strong>' + vehicle.year + ' ' + vehicle.make.toUpperCase() + ' ' + vehicle.model.toUpperCase() + '</strong>' +
                            '<br><small>Eng: <strong>' + vehicle.engine.toUpperCase() + '</strong><br> Trim: <strong>' + vehicle.submodel.toUpperCase() + '</strong>' +
                            '</small>   </div>              </a>                </li>');
                        $('#garage-list').prepend(li);
                    }

                    $('#garage-dropdown').html('Your garage ( ' + Object.keys(garage).length + ' )  <b class="caret"></b>');

                } else {
                    $('#widget-garage-dropdown').hide();
                }
            });
        </script>
    @endpush
@endonce