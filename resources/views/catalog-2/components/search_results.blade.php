@if($count>0)

<section class="part-page">
@if(request()->website_parameters['pagination']==1)
    @include('catalog-2.partials.pagination')
@endif
    <div class="thumbnail"
         style="overflow:visible;border-width:0; border-top-width:1px; border-radius:0">

        <div class="view-desktop">
            <div class="text-center-left">
                <div class="filter-breadcrumbs" role="group" aria-label="filter">
                    @foreach($breadcrumbs as $name =>$breadcrumb)
                        <a href="{{request()->fullURlWithQuery([$name=>null,'_url'=>null])}}"
                           onclick="refreshWithPost('{{request()->fullURlWithQuery([$name=>null,'_url'=>null])}}',event)"
                           title="remove filter on &nbsp; {{$breadcrumb}}" type="button"
                           class="btn btn-danger btn-xs">{{$breadcrumb}} <i
                                    class="icon-remove-sign"></i></a>

                    @endforeach
                </div>
            </div>
        </div>
        <div class="clearfix"></div>
    </div>

    <section class="clearfix parts-listing">

        <div class="view-desktop">

            @if($options['showas'] ==="list")
                @include('catalog-2.components.results_list')
            @else
                @include('catalog-2.components.results_grid')
            @endif

        </div>

    </section>

@if(request()->website_parameters['pagination']==1)
    @include('catalog-2.partials.pagination')
@endif
</section>
@include('catalog-2.components.productfitlocator')
@include('catalog-2.components.questioner')
@include('catalog-2.components.bottom_search_links')
@else
    <section class="part-page">
        @include('catalog-2.components.noproductsfound')
    </section>
@endif