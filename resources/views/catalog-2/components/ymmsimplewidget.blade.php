<div class="vehicle-selector-wrapper">
                <div class="container pl-0">
                    <div class="index-vehicle-selector">
                        <h3 class="col-12">Parts by Vehicle</h3>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="homecatalogwidget col-md-12">

                                    <div class="col-12">
                                        <div class="bs-component" id="home-ymm-selector">
                                            <form method="get" id="frm-vehicle-locator-afmkt"
                                                  class="vehicle-locator-afmkt-vertical" action="/catalog-2/vehicle/">


                                                <fieldset>
                                                    <div class="form-group col-12">
                                                        <select name="year" class="form-control" id="afmkt-year"
                                                                data-bind="options: years, optionsCaption: yearSelectCaption(),
					 optionsValue: function(item) { return item.id; },
					 optionsText: function(item) { return item.name; }, value: selectedYear,
				valueUpdate: 'change', enable: ( years().length)" required="">
                                                            <option value="">Choose A Year...</option>


                                                        </select>
                                                    </div>
                                                    <div class="form-group col-12">
                                                        <select name="make" class="form-control" id="afmkt-make"
                                                                data-bind="options: makes, optionsCaption: makeSelectCaption(),
					 optionsValue: function(item) { return item.id; },
					 optionsText: function(item) { return item.name; }, value: selectedMake,
					 valueUpdate: 'change',  enable: (selectedYear &amp;&amp; makes().length)" required="" disabled="">
                                                            <option value="">Choose A Make...</option>

                                                        </select>
                                                    </div>
                                                    <div class="form-group col-12">
                                                        <select name="model" class="form-control" id="afmkt-model"
                                                                data-bind="options: models, optionsCaption: modelSelectCaption(),
					 optionsValue: function(item) { return item.id; },
					 optionsText: function(item) { return item.name; }, value: selectedModel,
				valueUpdate: 'change', enable:(selectedMake &amp;&amp; models().length)" required="" disabled="">
                                                            <option value="">Choose A Model...</option>

                                                        </select>
                                                    </div>
                                                    <div class="form-group col-12">
                                                        <button type="submit" id="afmkt-submit"
                                                                class="btn btn-primary btn-lg"
                                                                data-bind="enable: selectedModel, click:showParts,text:submitButtonTitle"
                                                                disabled="">Show Parts
                                                        </button>
                                                    </div>
                                                </fieldset>

                                            </form>
                                        </div>


                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <style>
                .vehicle-selector-wrapper {
                    z-index: 10;
		    position:relative;
                }
            </style>
@push('after-scripts')
<script type="text/javascript">
    $(document).ready(function () {
        window.SearchVM = new SearchViewModel();
        window.SearchVM.baseAjaxURL = sophio.api_base_url+"vehicle?clientId="+sophio.api_parameters.clientId+"&nomfgcode="+sophio.api_parameters.nomfgcode;
        window.SearchVM.baseSubmitURL = "/catalog-2/vehicle";
        window.SearchVM.loadYears({
            'smbeng': 'engines',
            'usestatic': true,
            'onlymake': sophio.website_parameters.onlymakes
        });
        ko.applyBindings(window.SearchVM, document.getElementById("home-ymm-selector"));
    });

</script>
    @endpush