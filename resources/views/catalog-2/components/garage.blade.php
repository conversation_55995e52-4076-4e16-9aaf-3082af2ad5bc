<div class="panel panel-primary panel-garage">
    <div class="panel-heading">
        <h3 class="panel-title">Garage</h3>
    </div>
    <div class="panel-body">
	<p class="pl-3">Click on your vehicle below to show all parts.</p>
        <ul class="nav nav-pills nav-stacked" id="garage-list-home">
        </ul>
        
    </div>
</div>
@once
    @push('after-scripts')
        <script>
            mygarage = new MyGarage();
            $(document).ready(function () {

                if (mygarage.hasVehicles()) {
                    garage = mygarage.getVehicles()
                    for (v in garage) {
                        vehicle = garage[v];
                        link = "/catalog-2/vehicle/" + vehicle.make + "/" + vehicle.year + "/" + vehicle.model;
                        image_url = "https://images.sophio.com/vehicle/general/" + vehicle.make + "/" + vehicle.make + "_" + vehicle.model + ".png";
                        let li = $('<li> <a href="' + link + '" title="Click to show part for	' + vehicle.year + ' ' + vehicle.make.toUpperCase() + ' ' + vehicle.model.toUpperCase() + '!">' + vehicle.year + ' ' + vehicle.make.toUpperCase() + ' ' + vehicle.model.toUpperCase() + '</a> </li>');
                        $('#garage-list-home').append(li);
                    }
                } else {

                }
            });
        </script>
    @endpush
@endonce
