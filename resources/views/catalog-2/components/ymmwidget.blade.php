<div id="quick-parts-selector" class="bg-primary">
    <h2>Search by vehicle</h2>
    <form method="get" id="frm-vehicle-locator-af" class="vehicle-locator-af-vertical" action="/catalog-2/vehicle">
        <fieldset>
            <div class="form-group">
                <select class="form-control" data-bind="options: years, optionsCaption: yearSelectCaption(),
                optionsValue: function(item) { return item.id; },
                optionsText: function(item) { return item.name; }, value: selectedYear,
            valueUpdate: 'change', enable: ( years().length)" id="af-year" name="year"></select>
            </div>
            <div class="form-group">
                <select class="form-control" data-bind="options: makes, optionsCaption: makeSelectCaption(),
                optionsValue: function(item) { return item.id; },
                optionsText: function(item) { return item.name; }, value: selectedMake,
                valueUpdate: 'change',  enable: (selectedYear && makes().length)" id="af-make" name="make">
                </select>
            </div>
            <div class="form-group">
                <select class="form-control" data-bind="options: models, optionsCaption: modelSelectCaption(),
                optionsValue: function(item) { return item.id; },
                optionsText: function(item) { return item.name; }, value: selectedModel,
            valueUpdate: 'change', enable:(selectedMake && models().length)" id="af-model" name="model"></select>
            </div>
            <div class="form-group" data-bind="visible:hasSubmodels">
                <select class="form-control" data-bind="options: submodels, optionsCaption: submodelSelectCaption(),
                optionsValue: function(item) { return item.id; },
                optionsText: function(item) { return item.name; }, value: selectedSubModel,
            valueUpdate: 'change', enable:(selectedEngine && submodels().length)" id="af-submodel"
                        name="submodel"></select>
            </div>
            <div class="form-group" data-bind="visible:hasEngines">
                <select class="form-control" data-bind="options: engines, optionsCaption: engineSelectCaption(),
                optionsValue: function(item) { return item.id; },
                optionsText: function(item) { return item.name; }, value: selectedEngine,
            valueUpdate: 'change', enable:(selectedModel && engines().length)" id="af-engine" name="engine"></select>
            </div>

            <div class="form-group" data-bind="visible:hasPartTypes">
                <select class="form-control" data-bind="options: parttypes, optionsCaption: parttypeSelectCaption(),
                optionsValue: function(item) { return item.id; },
                optionsText: function(item) { return item.name; }, value: selectedPartType,
            valueUpdate: 'change', enable: (selectedSubModel && parttypes().length)" id="af-parttype"
                        name="parttype"></select>
            </div>

            <div class="form-group">
                <button type="button" id="af-submit" class="btn btn-default" disabled="disabled"
                        data-bind="enable: selectedModel, click:showParts,text:submitButtonTitle">Show Parts
                </button>
            </div>
        </fieldset>
    </form>

</div>

@push('after-scripts')

    <script type="text/javascript">

        $(document).ready(function () {
            $("#af-parttype").select2({});

            window.SearchVM = new SearchViewModel();
            window.SearchVM.baseAjaxURL = "{{request()->api_base_url}}vehicle?clientId={{request()->api_parameters['clientId']}}&nomfgcode={{request()->api_parameters['nomfgcode']}}&regionid={{request()->api_parameters['regionId']}}";
            window.SearchVM.baseSubmitURL = "/catalog-2/vehicle";
            window.SearchVM.loadYears({'smbeng': 'submodels', 'usestatic': true, 'onlymake': sophio.website_parameters.onlymakes});

            ko.applyBindings(window.SearchVM, document.getElementById("quick-parts-selector"));
        });

    </script>
@endpush