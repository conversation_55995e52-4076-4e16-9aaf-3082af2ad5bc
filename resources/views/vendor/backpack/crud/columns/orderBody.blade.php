<div class="m-t-10 m-b-10 p-l-10 p-r-10 p-t-10 p-b-10">
    <div class="row">
        <div class="col-md-12">

            <?php $body = json_decode($entry->orderBody, true);?>

            <div class="card">
                <div class="card-body">
                    <ul>
                        @foreach($body as $prop=>$value)
                            @if($prop!="shipTo" && $prop!="items")
                                <li>{{$prop}}: {{print_r($value,true)}}</li>
                            @endif
                        @endforeach
                    </ul>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Shipping info</h5>
                    <ul>
                        @foreach($body['shipTo'] as $n=>$s)
                            <li>{{$n}}: {{$s}}</li>
                        @endforeach
                    </ul>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Lineitems</h5>
                    <?php
                    $log_create = [];
                    foreach ($body['items'] as $k => $item) {
                        $io = new \App\Models\Orders\RequestLineItem($item);
                        $io->supplierlog_id = $entry->supplierlog_id;
                        $log_create[$k] = $io->logorder();

                    }

                    ?>
                    @if(isset($entry->lineitem))

                    <table class="table table-sm table-responsive">
                        <tr>

                            <?php $columns = array_keys($entry->lineitem[0]->toArray()); ?>
                            @foreach($columns as $c)
                                <th>{{$c}}</th>
                            @endforeach
                                <th>RTSC</th>
                            <th></th>
                        </tr>

                        @foreach($entry->lineitem  as $k=>$item)
                            <tr>
                                @foreach($item->toArray() as $j=>$i)
                                    @switch($j)
                                        @case('contactpk')
                                        <td>{{getContactpkName($i)['name']??$i}}</td>
                                    @break
                                        @default
                                    <td>{{$i}}</td>
                                    @endswitch
                                @endforeach
                                @if(isset($log_create[$k]))

                                <td class="table-warning">{{$log_create[$k]['rtsc']?"Yes":"No"}}</td>

                                <td><a target="_blank"
                                       href="{{ backpack_url('dbs/'.session('database').'/logcreate/'.$log_create[$k]['id'].'/show') }}">View
                                        Log</a></td>
                                    @endif
                            </tr>
                        @endforeach
                    </table>
                    @else
                        <table class="table table-sm table-responsive">
                            <tr>

                                    <?php $columns = array_keys($body['items'][0]); ?>
                                @foreach($columns as $c)
                                    <th>{{$c}}</th>
                                @endforeach
                                <th>RTSC</th>
                                <th></th>
                            </tr>

                            @foreach($body['items']  as $k=>$item)
                                <tr>
                                    @foreach($item as $j=>$i)
                                        @switch($j)
                                            @case('contactpk')
                                                <td>{{getContactpkName($i)['name']??$i}}</td>
                                                @break
                                            @default
                                                <td>{{print_r($i,true)}}</td>
                                        @endswitch
                                    @endforeach
                                    @if(isset($log_create[$k]))

                                        <td class="table-warning">{{$log_create[$k]['rtsc']?"Yes":"No"}}</td>

                                        <td><a target="_blank"
                                               href="{{ backpack_url('dbs/'.session('database').'/logcreate/'.$log_create[$k]['id'].'/show') }}">View
                                                Log</a></td>
                                    @endif
                                </tr>
                            @endforeach
                        </table>
                    @endif
                </div>
            </div>
            @include('vendor.backpack.crud.columns.order_links')
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Checks having same po_number. Please note the order was registered
                        at {{$entry->dateCreated}}.</h5>
                    @php
                    $sups_ids = [];
                    foreach($entry->stockcheck() as $stockcheck)
                        $sups_ids[] = $stockcheck->supplierlog_id;

                    $logchecks = \App\Models\Orders\LogCheck::whereIn('supplierlog_id',$sups_ids)->get();
                    @endphp
                    <table class="table table-sm table-responsive">
                        <tr>
                            <td>Resolution</td>
                            <td>Date</td>
                            <td></td>
                            <td>Number of Suppliers</td>
                            <td>more</td>
                        </tr>
                        @foreach($logchecks as $logcheck)
                            <tr>
                                <td>{{$logcheck->resolution}}</td>
                                <td>{{$logcheck->created_at}}</td>
                                <td>{{$logcheck->multiline==0?'Single Line':'Multi Line'}}</td>
                                <td>{{$logcheck->no_suppliers==0?'None':($logcheck->no_suppliers==1?'Single':'Multiple')}}</td>

                                <td><a target="_blank"
                                       href="{{ backpack_url('dbs/'.session('database').'/logcheck/'.$logcheck->id.'/show') }}">more</a>
                                </td>
                            </tr>
                        @endforeach
                    </table>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Stockchecks having same po_number. Please note the order was registered
                        at {{$entry->dateCreated}}.</h5>
                    <table class="table table-sm table-responsive">
                        <tr>
                            <td>mfgcode</td>
                            <td>part_number_unformatted</td>
                            <td>Is supersed</td>
                            <td>qty_avail</td>
                            <td>contactpk</td>
                            <td>qty_inventory</td>
                            <td>qty_warehouse</td>
                            <td>qty_rtsc</td>
                            <td>reason</td>
                            <td>RTSC</td>
                            <td>Executeddate</td>
                            <td>more</td>
                        </tr>
                        @foreach($entry->stockcheck()->toArray() as $stockcheck)
                            <tr>
                                <td>{{$stockcheck['mfgcode']}}</td>
                                <td>{{$stockcheck['part_number_unformatted']}}</td>
                                <td>{{$stockcheck['super']==0?'No':'Yes'}}</td>
                                <td>{{$stockcheck['qty_avail']}}</td>
                                <td>{{getContactpkName($stockcheck['contactpk'])['name']}}</td>
                                <td>{{$stockcheck['qty_inventory']}}</td>
                                <td>{{$stockcheck['qty_warehouse']}}</td>
                                <td>{{$stockcheck['qty_rtsc']}}</td>
                                <td>{{$stockcheck['reason']}}</td>
                                <td>{{$stockcheck['rtsc']==1?'Yes':'No'}}</td>
                                <td>{{$stockcheck['created_at']}}</td>
                                <td><a target="_blank"
                                       href="{{ backpack_url('dbs/'.session('database').'/logstock/'.$stockcheck['id'].'/show') }}">more</a>
                                </td>
                            </tr>
                        @endforeach
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>
<div class="clearfix"></div>
