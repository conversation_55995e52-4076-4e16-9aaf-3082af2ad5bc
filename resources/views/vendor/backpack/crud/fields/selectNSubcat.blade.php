
@php
    $field['allows_null'] = $field['allows_null'] ?? $crud->model::isColumnNullable($field['name']);
$field['options'] = [];
@endphp
<!-- select from array -->
@include('crud::fields.inc.wrapper_start')
<label>{!! $field['label'] !!}</label>
@include('crud::fields.inc.translatable_icon')
<select
        data-data-source="{{ $field['data_source'] }}"
        data-parent-attribute="{{$field['parent_attribute']}}"
        data-parent-key="{{$field['parent_key']}}"
        name="{{ $field['name'] }}@if (isset($field['allows_multiple']) && $field['allows_multiple']==true)[]@endif"
        @include('crud::fields.inc.attributes')
        @if (isset($field['allows_multiple']) && $field['allows_multiple']==true)multiple @endif
>

    @if ($field['allows_null'])
        <option value="">-</option>
    @endif

    @if (count($field['options']))
        @foreach ($field['options'] as $key => $value)
            @if((old(square_brackets_to_dots($field['name'])) !== null && (
                    $key == old(square_brackets_to_dots($field['name'])) ||
                    (is_array(old(square_brackets_to_dots($field['name']))) &&
                    in_array($key, old(square_brackets_to_dots($field['name'])))))) ||
                    (null === old(square_brackets_to_dots($field['name'])) &&
                        ((isset($field['value']) && (
                                    $key == $field['value'] || (
                                            is_array($field['value']) &&
                                            in_array($key, $field['value'])
                                            )
                                    )) ||
                            (!isset($field['value']) && isset($field['default']) &&
                            ($key == $field['default'] || (
                                            is_array($field['default']) &&
                                            in_array($key, $field['default'])
                                        )
                                    )
                            ))
                    ))
                <option value="{{ $key }}" selected>{{ $value }}</option>
            @else
                <option value="{{ $key }}">{{ $value }}</option>
            @endif
        @endforeach
    @endif
</select>

{{-- HINT --}}
@if (isset($field['hint']))
    <p class="help-block">{!! $field['hint'] !!}</p>
@endif
@include('crud::fields.inc.wrapper_end')
<!-- include field specific select2 js-->
@push('crud_fields_scripts')
    <script>
        var element = $("select[name={{ $field['name'] }}]");
        var $dataSource = element.attr('data-data-source');
        var parentAttribute = element.attr('data-parent-attribute');
        var parentKey = element.attr('data-parent-key');
        $(document).ready(function(){

            $('select[name={{ $field['parent_attribute'] }}]').on("change",function(){
                $.ajax({
                    url: '{{ $field['data_source'] }}',
                    data: {
                        {{ $field['parent_key'] }}: $('select[name={{ $field['parent_attribute'] }}]').val()
                    },
                    type: 'GET',
                    success: function (result) {
                        var element = $("select[name={{ $field['name'] }}]");
                        element.empty();
                        $.each(result,function(key,entry){
                            @if(isset($field['value']))
                            if(entry.{{$field['attribute']}}=='{{$field['value']}}') {
                                element.append($('<option></option>').val(entry.{{$field['attribute']}}).html(entry.{{$field['attribute']}}).attr('selected','selected'));
                            }else{
                                element.append($('<option></option>').val(entry.{{$field['attribute']}}).html(entry.{{$field['attribute']}}));
                            }

                            @else
                            element.append($('<option></option>').val(entry.{{$field['attribute']}}).html(entry.{{$field['attribute']}}));
                            @endif
                        });
                        element.trigger("change");

                    }
                });
            });


            $.ajax({
                url: '{{ $field['data_source'] }}',
                data: {
                    {{ $field['parent_key'] }}: $('select[name={{ $field['parent_attribute'] }}]').val()
                },
                type: 'GET',
                success: function (result) {
                    var element = $("select[name={{ $field['name'] }}]");
                    element.empty();
                    $.each(result,function(key,entry){
                        @if(isset($field['value']))
                            if(entry.{{$field['attribute']}}=='{{$field['value']}}') {
                            element.append($('<option></option>').val(entry.{{$field['attribute']}}).html(entry.{{$field['attribute']}}).attr('selected','selected'));
                        }else{
                            element.append($('<option></option>').val(entry.{{$field['attribute']}}).html(entry.{{$field['attribute']}}));
                        }

                        @else
                        element.append($('<option></option>').val(entry.{{$field['attribute']}}).html(entry.{{$field['attribute']}}));
                        @endif
                    });


                }
            });
        });

    </script>
@endpush
{{-- End of Extra CSS and JS --}}
{{-- ########################################## --}}
