<a href="{{ url(config('backpack.base.route_prefix') . '/dbs/' . \Route::current()->parameter('database') . '/fbs/imports') }}?supplier_pk={{$entry->PK}} " class="btn btn-sm btn-link"><i class="la la-archive"></i> Import logs</a>

<a href="{{ url(config('backpack.base.route_prefix') . '/dbs/' . \Route::current()->parameter('database') . '/fbs/'.$entry->PK.'/importedinventory') }}" class="btn btn-sm btn-link"><i class="la la-folder"></i>Imported Inventory</a>

<a href="{{ url(config('backpack.base.route_prefix') . '/dbs/' . \Route::current()->parameter('database') . '/fbs/supplier/'.$entry->PK.'/downloaddcf') }}" class="btn btn-sm btn-link"><i class="la la-folder"></i> Download DCF Catlink</a>