<?php

namespace CommerceHub\HubXML\ShippingBreakout;

/**
 * Class representing ShippingBreakoutAType
 */
class ShippingBreakoutAType
{
    /**
     * Not currently used
     *
     * @var string $shippingType
     */
    private $shippingType = null;

    /**
     * Not currently used
     *
     * @var string $currencyUnit
     */
    private $currencyUnit = null;

    /**
     * Gets as shippingType
     *
     * Not currently used
     *
     * @return string
     */
    public function getShippingType()
    {
        return $this->shippingType;
    }

    /**
     * Sets a new shippingType
     *
     * Not currently used
     *
     * @param string $shippingType
     * @return self
     */
    public function setShippingType($shippingType)
    {
        $this->shippingType = $shippingType;
        return $this;
    }

    /**
     * Gets as currencyUnit
     *
     * Not currently used
     *
     * @return string
     */
    public function getCurrencyUnit()
    {
        return $this->currencyUnit;
    }

    /**
     * Sets a new currencyUnit
     *
     * Not currently used
     *
     * @param string $currencyUnit
     * @return self
     */
    public function setCurrencyUnit($currencyUnit)
    {
        $this->currencyUnit = $currencyUnit;
        return $this;
    }
}

