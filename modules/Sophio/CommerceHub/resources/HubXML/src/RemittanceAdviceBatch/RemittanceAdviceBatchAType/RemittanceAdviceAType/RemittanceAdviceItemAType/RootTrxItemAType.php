<?php

namespace CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType\RemittanceAdviceAType\RemittanceAdviceItemAType;

/**
 * Class representing RootTrxItemAType
 */
class RootTrxItemAType
{
    /**
     * @var string $transactionItemID
     */
    private $transactionItemID = null;

    /**
     * @var string $action
     */
    private $action = null;

    /**
     * @var string $trxQty
     */
    private $trxQty = null;

    /**
     * Amount this ‘detail’ is contributing to the total net payment amount
     *
     * @var string $trxLineBalanceDue
     */
    private $trxLineBalanceDue = null;

    /**
     * Unique number for this message line within the message
     *
     * @var string $transactionLineNumber
     */
    private $transactionLineNumber = null;

    /**
     * Gets as transactionItemID
     *
     * @return string
     */
    public function getTransactionItemID()
    {
        return $this->transactionItemID;
    }

    /**
     * Sets a new transactionItemID
     *
     * @param string $transactionItemID
     * @return self
     */
    public function setTransactionItemID($transactionItemID)
    {
        $this->transactionItemID = $transactionItemID;
        return $this;
    }

    /**
     * Gets as action
     *
     * @return string
     */
    public function getAction()
    {
        return $this->action;
    }

    /**
     * Sets a new action
     *
     * @param string $action
     * @return self
     */
    public function setAction($action)
    {
        $this->action = $action;
        return $this;
    }

    /**
     * Gets as trxQty
     *
     * @return string
     */
    public function getTrxQty()
    {
        return $this->trxQty;
    }

    /**
     * Sets a new trxQty
     *
     * @param string $trxQty
     * @return self
     */
    public function setTrxQty($trxQty)
    {
        $this->trxQty = $trxQty;
        return $this;
    }

    /**
     * Gets as trxLineBalanceDue
     *
     * Amount this ‘detail’ is contributing to the total net payment amount
     *
     * @return string
     */
    public function getTrxLineBalanceDue()
    {
        return $this->trxLineBalanceDue;
    }

    /**
     * Sets a new trxLineBalanceDue
     *
     * Amount this ‘detail’ is contributing to the total net payment amount
     *
     * @param string $trxLineBalanceDue
     * @return self
     */
    public function setTrxLineBalanceDue($trxLineBalanceDue)
    {
        $this->trxLineBalanceDue = $trxLineBalanceDue;
        return $this;
    }

    /**
     * Gets as transactionLineNumber
     *
     * Unique number for this message line within the message
     *
     * @return string
     */
    public function getTransactionLineNumber()
    {
        return $this->transactionLineNumber;
    }

    /**
     * Sets a new transactionLineNumber
     *
     * Unique number for this message line within the message
     *
     * @param string $transactionLineNumber
     * @return self
     */
    public function setTransactionLineNumber($transactionLineNumber)
    {
        $this->transactionLineNumber = $transactionLineNumber;
        return $this;
    }
}

