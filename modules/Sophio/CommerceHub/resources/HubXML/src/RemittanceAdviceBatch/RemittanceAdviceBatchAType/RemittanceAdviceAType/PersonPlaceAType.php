<?php

namespace CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType\RemittanceAdviceAType;

/**
 * Class representing PersonPlaceAType
 */
class PersonPlaceAType
{
    /**
     * Will be referenced by the remitTo element in a remitAdviceData and/or the refInvoiceTo element in a remitAdviceItemData element and establish a link to the person/place particulars
     *
     * @var string $personPlaceID
     */
    private $personPlaceID = null;

    /**
     * @var string $name1
     */
    private $name1 = null;

    /**
     * @var string $partnerPersonPlaceId
     */
    private $partnerPersonPlaceId = null;

    /**
     * Gets as personPlaceID
     *
     * Will be referenced by the remitTo element in a remitAdviceData and/or the refInvoiceTo element in a remitAdviceItemData element and establish a link to the person/place particulars
     *
     * @return string
     */
    public function getPersonPlaceID()
    {
        return $this->personPlaceID;
    }

    /**
     * Sets a new personPlaceID
     *
     * Will be referenced by the remitTo element in a remitAdviceData and/or the refInvoiceTo element in a remitAdviceItemData element and establish a link to the person/place particulars
     *
     * @param string $personPlaceID
     * @return self
     */
    public function setPersonPlaceID($personPlaceID)
    {
        $this->personPlaceID = $personPlaceID;
        return $this;
    }

    /**
     * Gets as name1
     *
     * @return string
     */
    public function getName1()
    {
        return $this->name1;
    }

    /**
     * Sets a new name1
     *
     * @param string $name1
     * @return self
     */
    public function setName1($name1)
    {
        $this->name1 = $name1;
        return $this;
    }

    /**
     * Gets as partnerPersonPlaceId
     *
     * @return string
     */
    public function getPartnerPersonPlaceId()
    {
        return $this->partnerPersonPlaceId;
    }

    /**
     * Sets a new partnerPersonPlaceId
     *
     * @param string $partnerPersonPlaceId
     * @return self
     */
    public function setPartnerPersonPlaceId($partnerPersonPlaceId)
    {
        $this->partnerPersonPlaceId = $partnerPersonPlaceId;
        return $this;
    }
}

