<?php

namespace CommerceHub\HubXML\MiscChargeBreakout;

/**
 * Class representing MiscChargeBreakoutAType
 */
class MiscChargeBreakoutAType
{
    /**
     * @var string $__value
     */
    private $__value = null;

    /**
     * Explanation for charge or allowance
     *
     * @var string $description
     */
    private $description = null;

    /**
     * "A", "C"
     *
     * @var string $alwChgIndicator
     */
    private $alwChgIndicator = null;

    /**
     * Code representing charge or allowance
     *
     * @var string $chargeType
     */
    private $chargeType = null;

    /**
     * Exception handling code.
     * 01 = Not included in invoice total – bill back
     * 06 = Not included in invoice total – Customer will pay
     *
     * @var string $methodOfHandling
     */
    private $methodOfHandling = null;

    /**
     * @var string $currencyUnit
     */
    private $currencyUnit = null;

    /**
     * Construct
     *
     * @param string $value
     */
    public function __construct($value)
    {
        $this->value($value);
    }

    /**
     * Gets or sets the inner value
     *
     * @param string $value
     * @return string
     */
    public function value()
    {
        if ($args = func_get_args()) {
            $this->__value = $args[0];
        }
        return $this->__value;
    }

    /**
     * Gets a string value
     *
     * @return string
     */
    public function __toString()
    {
        return strval($this->__value);
    }

    /**
     * Gets as description
     *
     * Explanation for charge or allowance
     *
     * @return string
     */
    public function getDescription()
    {
        return $this->description;
    }

    /**
     * Sets a new description
     *
     * Explanation for charge or allowance
     *
     * @param string $description
     * @return self
     */
    public function setDescription($description)
    {
        $this->description = $description;
        return $this;
    }

    /**
     * Gets as alwChgIndicator
     *
     * "A", "C"
     *
     * @return string
     */
    public function getAlwChgIndicator()
    {
        return $this->alwChgIndicator;
    }

    /**
     * Sets a new alwChgIndicator
     *
     * "A", "C"
     *
     * @param string $alwChgIndicator
     * @return self
     */
    public function setAlwChgIndicator($alwChgIndicator)
    {
        $this->alwChgIndicator = $alwChgIndicator;
        return $this;
    }

    /**
     * Gets as chargeType
     *
     * Code representing charge or allowance
     *
     * @return string
     */
    public function getChargeType()
    {
        return $this->chargeType;
    }

    /**
     * Sets a new chargeType
     *
     * Code representing charge or allowance
     *
     * @param string $chargeType
     * @return self
     */
    public function setChargeType($chargeType)
    {
        $this->chargeType = $chargeType;
        return $this;
    }

    /**
     * Gets as methodOfHandling
     *
     * Exception handling code.
     * 01 = Not included in invoice total – bill back
     * 06 = Not included in invoice total – Customer will pay
     *
     * @return string
     */
    public function getMethodOfHandling()
    {
        return $this->methodOfHandling;
    }

    /**
     * Sets a new methodOfHandling
     *
     * Exception handling code.
     * 01 = Not included in invoice total – bill back
     * 06 = Not included in invoice total – Customer will pay
     *
     * @param string $methodOfHandling
     * @return self
     */
    public function setMethodOfHandling($methodOfHandling)
    {
        $this->methodOfHandling = $methodOfHandling;
        return $this;
    }

    /**
     * Gets as currencyUnit
     *
     * @return string
     */
    public function getCurrencyUnit()
    {
        return $this->currencyUnit;
    }

    /**
     * Sets a new currencyUnit
     *
     * @param string $currencyUnit
     * @return self
     */
    public function setCurrencyUnit($currencyUnit)
    {
        $this->currencyUnit = $currencyUnit;
        return $this;
    }
}

