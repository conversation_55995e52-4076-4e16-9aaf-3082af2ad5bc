CommerceHub\HubXML\ConfirmMessageBatch\ConfirmMessageBatchAType\HubConfirmAType:
    properties:
        transactionID:
            expose: true
            access_type: public_method
            serialized_name: transactionID
            accessor:
                getter: getTransactionID
                setter: setTransactionID
            xml_attribute: true
            type: string
        participatingParty:
            expose: true
            access_type: public_method
            serialized_name: participatingParty
            xml_element:
                cdata: false
            accessor:
                getter: getParticipatingParty
                setter: setParticipatingParty
            xml_list:
                inline: true
                entry_name: participatingParty
            type: array<CommerceHub\HubXML\ConfirmMessageBatch\ConfirmMessageBatchAType\HubConfirmAType\ParticipatingPartyAType>
        partnerTrxID:
            expose: true
            access_type: public_method
            serialized_name: partnerTrxID
            xml_element:
                cdata: false
            accessor:
                getter: getPartnerTrxID
                setter: setPartnerTrxID
            type: string
        partnerTrxDate:
            expose: true
            access_type: public_method
            serialized_name: partnerTrxDate
            xml_element:
                cdata: false
            accessor:
                getter: getPartnerTrxDate
                setter: setPartnerTrxDate
            type: string
        poNumber:
            expose: true
            access_type: public_method
            serialized_name: poNumber
            xml_element:
                cdata: false
            accessor:
                getter: getPoNumber
                setter: setPoNumber
            type: string
        shipmentDetail:
            expose: true
            access_type: public_method
            serialized_name: shipmentDetail
            xml_element:
                cdata: false
            accessor:
                getter: getShipmentDetail
                setter: setShipmentDetail
            type: CommerceHub\HubXML\ConfirmMessageBatch\ConfirmMessageBatchAType\HubConfirmAType\ShipmentDetailAType
        trxData:
            expose: true
            access_type: public_method
            serialized_name: trxData
            xml_element:
                cdata: false
            accessor:
                getter: getTrxData
                setter: setTrxData
            type: CommerceHub\HubXML\ConfirmMessageBatch\ConfirmMessageBatchAType\HubConfirmAType\TrxDataAType
        hubAction:
            expose: true
            access_type: public_method
            serialized_name: hubAction
            xml_element:
                cdata: false
            accessor:
                getter: getHubAction
                setter: setHubAction
            xml_list:
                inline: true
                entry_name: hubAction
            type: array<CommerceHub\HubXML\ConfirmMessageBatch\ConfirmMessageBatchAType\HubConfirmAType\HubActionAType>
        packageDetail:
            expose: true
            access_type: public_method
            serialized_name: packageDetail
            xml_element:
                cdata: false
            accessor:
                getter: getPackageDetail
                setter: setPackageDetail
            xml_list:
                inline: true
                entry_name: packageDetail
            type: array<CommerceHub\HubXML\ConfirmMessageBatch\ConfirmMessageBatchAType\HubConfirmAType\PackageDetailAType>
        vendorShipInfo:
            expose: true
            access_type: public_method
            serialized_name: vendorShipInfo
            xml_element:
                cdata: false
            accessor:
                getter: getVendorShipInfo
                setter: setVendorShipInfo
            xml_list:
                inline: true
                entry_name: vendorShipInfo
            type: array<CommerceHub\HubXML\ConfirmMessageBatch\ConfirmMessageBatchAType\HubConfirmAType\VendorShipInfoAType>
