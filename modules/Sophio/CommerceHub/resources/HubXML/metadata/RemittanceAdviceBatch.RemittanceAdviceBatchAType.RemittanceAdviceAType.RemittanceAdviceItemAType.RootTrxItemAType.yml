CommerceHub\HubXML\RemittanceAdviceBatch\RemittanceAdviceBatchAType\RemittanceAdviceAType\RemittanceAdviceItemAType\RootTrxItemAType:
    properties:
        transactionItemID:
            expose: true
            access_type: public_method
            serialized_name: transactionItemID
            accessor:
                getter: getTransactionItemID
                setter: setTransactionItemID
            xml_attribute: true
            type: string
        action:
            expose: true
            access_type: public_method
            serialized_name: action
            xml_element:
                cdata: false
            accessor:
                getter: getAction
                setter: setAction
            type: string
        trxQty:
            expose: true
            access_type: public_method
            serialized_name: trxQty
            xml_element:
                cdata: false
            accessor:
                getter: getTrxQty
                setter: setTrxQty
            type: string
        trxLineBalanceDue:
            expose: true
            access_type: public_method
            serialized_name: trxLineBalanceDue
            xml_element:
                cdata: false
            accessor:
                getter: getTrxLineBalanceDue
                setter: setTrxLineBalanceDue
            type: string
        transactionLineNumber:
            expose: true
            access_type: public_method
            serialized_name: transactionLineNumber
            xml_element:
                cdata: false
            accessor:
                getter: getTransactionLineNumber
                setter: setTransactionLineNumber
            type: string
