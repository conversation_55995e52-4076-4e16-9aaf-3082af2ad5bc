<?php

namespace WalmartDSV;

/**
 * Class representing ShortsStyleType
 *
 * Styles specific to shorts.
 * XSD Type: ShortsStyle
 */
class ShortsStyleType
{

    /**
     * @var string $shortsStyleValue
     */
    private $shortsStyleValue = null;

    /**
     * Gets as shortsStyleValue
     *
     * @return string
     */
    public function getShortsStyleValue()
    {
        return $this->shortsStyleValue;
    }

    /**
     * Sets a new shortsStyleValue
     *
     * @param string $shortsStyleValue
     * @return self
     */
    public function setShortsStyleValue($shortsStyleValue)
    {
        $this->shortsStyleValue = $shortsStyleValue;
        return $this;
    }


}

