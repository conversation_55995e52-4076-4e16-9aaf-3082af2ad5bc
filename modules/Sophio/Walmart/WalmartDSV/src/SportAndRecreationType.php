<?php

namespace WalmartDSV;

/**
 * Class representing SportAndRecreationType
 *
 *
 * XSD Type: SportAndRecreation
 */
class SportAndRecreationType
{

    /**
     * @var \WalmartDSV\CyclingType $cycling
     */
    private $cycling = null;

    /**
     * @var \WalmartDSV\WeaponsType $weapons
     */
    private $weapons = null;

    /**
     * @var \WalmartDSV\OpticsType $optics
     */
    private $optics = null;

    /**
     * @var \WalmartDSV\SportAndRecreationOtherType $sportAndRecreationOther
     */
    private $sportAndRecreationOther = null;

    /**
     * Gets as cycling
     *
     * @return \WalmartDSV\CyclingType
     */
    public function getCycling()
    {
        return $this->cycling;
    }

    /**
     * Sets a new cycling
     *
     * @param \WalmartDSV\CyclingType $cycling
     * @return self
     */
    public function setCycling(\WalmartDSV\CyclingType $cycling)
    {
        $this->cycling = $cycling;
        return $this;
    }

    /**
     * Gets as weapons
     *
     * @return \WalmartDSV\WeaponsType
     */
    public function getWeapons()
    {
        return $this->weapons;
    }

    /**
     * Sets a new weapons
     *
     * @param \WalmartDSV\WeaponsType $weapons
     * @return self
     */
    public function setWeapons(\WalmartDSV\WeaponsType $weapons)
    {
        $this->weapons = $weapons;
        return $this;
    }

    /**
     * Gets as optics
     *
     * @return \WalmartDSV\OpticsType
     */
    public function getOptics()
    {
        return $this->optics;
    }

    /**
     * Sets a new optics
     *
     * @param \WalmartDSV\OpticsType $optics
     * @return self
     */
    public function setOptics(\WalmartDSV\OpticsType $optics)
    {
        $this->optics = $optics;
        return $this;
    }

    /**
     * Gets as sportAndRecreationOther
     *
     * @return \WalmartDSV\SportAndRecreationOtherType
     */
    public function getSportAndRecreationOther()
    {
        return $this->sportAndRecreationOther;
    }

    /**
     * Sets a new sportAndRecreationOther
     *
     * @param \WalmartDSV\SportAndRecreationOtherType $sportAndRecreationOther
     * @return self
     */
    public function setSportAndRecreationOther(\WalmartDSV\SportAndRecreationOtherType $sportAndRecreationOther)
    {
        $this->sportAndRecreationOther = $sportAndRecreationOther;
        return $this;
    }


}

