<?php

namespace WalmartDSV;

/**
 * Class representing VehicleType
 *
 *
 * XSD Type: Vehicle
 */
class VehicleType
{

    /**
     * @var \WalmartDSV\TiresType $tires
     */
    private $tires = null;

    /**
     * @var \WalmartDSV\LandVehiclesType $landVehicles
     */
    private $landVehicles = null;

    /**
     * @var \WalmartDSV\VehiclePartsAndAccessoriesType $vehiclePartsAndAccessories
     */
    private $vehiclePartsAndAccessories = null;

    /**
     * @var \WalmartDSV\WheelsAndWheelComponentsType $wheelsAndWheelComponents
     */
    private $wheelsAndWheelComponents = null;

    /**
     * @var \WalmartDSV\VehicleOtherType $vehicleOther
     */
    private $vehicleOther = null;

    /**
     * @var \WalmartDSV\WatercraftType $watercraft
     */
    private $watercraft = null;

    /**
     * Gets as tires
     *
     * @return \WalmartDSV\TiresType
     */
    public function getTires()
    {
        return $this->tires;
    }

    /**
     * Sets a new tires
     *
     * @param \WalmartDSV\TiresType $tires
     * @return self
     */
    public function setTires(\WalmartDSV\TiresType $tires)
    {
        $this->tires = $tires;
        return $this;
    }

    /**
     * Gets as landVehicles
     *
     * @return \WalmartDSV\LandVehiclesType
     */
    public function getLandVehicles()
    {
        return $this->landVehicles;
    }

    /**
     * Sets a new landVehicles
     *
     * @param \WalmartDSV\LandVehiclesType $landVehicles
     * @return self
     */
    public function setLandVehicles(\WalmartDSV\LandVehiclesType $landVehicles)
    {
        $this->landVehicles = $landVehicles;
        return $this;
    }

    /**
     * Gets as vehiclePartsAndAccessories
     *
     * @return \WalmartDSV\VehiclePartsAndAccessoriesType
     */
    public function getVehiclePartsAndAccessories()
    {
        return $this->vehiclePartsAndAccessories;
    }

    /**
     * Sets a new vehiclePartsAndAccessories
     *
     * @param \WalmartDSV\VehiclePartsAndAccessoriesType $vehiclePartsAndAccessories
     * @return self
     */
    public function setVehiclePartsAndAccessories(\WalmartDSV\VehiclePartsAndAccessoriesType $vehiclePartsAndAccessories)
    {
        $this->vehiclePartsAndAccessories = $vehiclePartsAndAccessories;
        return $this;
    }

    /**
     * Gets as wheelsAndWheelComponents
     *
     * @return \WalmartDSV\WheelsAndWheelComponentsType
     */
    public function getWheelsAndWheelComponents()
    {
        return $this->wheelsAndWheelComponents;
    }

    /**
     * Sets a new wheelsAndWheelComponents
     *
     * @param \WalmartDSV\WheelsAndWheelComponentsType $wheelsAndWheelComponents
     * @return self
     */
    public function setWheelsAndWheelComponents(\WalmartDSV\WheelsAndWheelComponentsType $wheelsAndWheelComponents)
    {
        $this->wheelsAndWheelComponents = $wheelsAndWheelComponents;
        return $this;
    }

    /**
     * Gets as vehicleOther
     *
     * @return \WalmartDSV\VehicleOtherType
     */
    public function getVehicleOther()
    {
        return $this->vehicleOther;
    }

    /**
     * Sets a new vehicleOther
     *
     * @param \WalmartDSV\VehicleOtherType $vehicleOther
     * @return self
     */
    public function setVehicleOther(\WalmartDSV\VehicleOtherType $vehicleOther)
    {
        $this->vehicleOther = $vehicleOther;
        return $this;
    }

    /**
     * Gets as watercraft
     *
     * @return \WalmartDSV\WatercraftType
     */
    public function getWatercraft()
    {
        return $this->watercraft;
    }

    /**
     * Sets a new watercraft
     *
     * @param \WalmartDSV\WatercraftType $watercraft
     * @return self
     */
    public function setWatercraft(\WalmartDSV\WatercraftType $watercraft)
    {
        $this->watercraft = $watercraft;
        return $this;
    }


}

