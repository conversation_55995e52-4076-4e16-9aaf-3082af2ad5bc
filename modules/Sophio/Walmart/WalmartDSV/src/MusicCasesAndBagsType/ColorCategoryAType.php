<?php

namespace WalmartDSV\MusicCasesAndBagsType;

/**
 * Class representing ColorCategoryAType
 */
class ColorCategoryAType
{

    /**
     * @var string[] $colorCategoryValue
     */
    private $colorCategoryValue = [
        
    ];

    /**
     * Adds as colorCategoryValue
     *
     * @return self
     * @param string $colorCategoryValue
     */
    public function addToColorCategoryValue($colorCategoryValue)
    {
        $this->colorCategoryValue[] = $colorCategoryValue;
        return $this;
    }

    /**
     * isset colorCategoryValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetColorCategoryValue($index)
    {
        return isset($this->colorCategoryValue[$index]);
    }

    /**
     * unset colorCategoryValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetColorCategoryValue($index)
    {
        unset($this->colorCategoryValue[$index]);
    }

    /**
     * Gets as colorCategoryValue
     *
     * @return string[]
     */
    public function getColorCategoryValue()
    {
        return $this->colorCategoryValue;
    }

    /**
     * Sets a new colorCategoryValue
     *
     * @param string $colorCategoryValue
     * @return self
     */
    public function setColorCategoryValue(array $colorCategoryValue)
    {
        $this->colorCategoryValue = $colorCategoryValue;
        return $this;
    }


}

