<?php

namespace WalmartDSV;

/**
 * Class representing PaperSizeType
 *
 * The dimensions of the paper, using ISO 216, ANSI, or standard North American sizes.
 * XSD Type: PaperSize
 */
class PaperSizeType
{

    /**
     * @var string $paperSizeValue
     */
    private $paperSizeValue = null;

    /**
     * Gets as paperSizeValue
     *
     * @return string
     */
    public function getPaperSizeValue()
    {
        return $this->paperSizeValue;
    }

    /**
     * Sets a new paperSizeValue
     *
     * @param string $paperSizeValue
     * @return self
     */
    public function setPaperSizeValue($paperSizeValue)
    {
        $this->paperSizeValue = $paperSizeValue;
        return $this;
    }


}

