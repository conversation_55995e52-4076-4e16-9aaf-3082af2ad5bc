<?php

namespace WalmartDSV;

/**
 * Class representing MaterialType
 *
 * The main material(s) that a product is made of. This does not need to be an exhaustive list, but should contain the predominant or functionally important material/materials. Fabric material specifics should be entered using the "Fabric Content" attribute.
 * XSD Type: Material
 */
class MaterialType
{

    /**
     * @var string $materialValue
     */
    private $materialValue = null;

    /**
     * Gets as materialValue
     *
     * @return string
     */
    public function getMaterialValue()
    {
        return $this->materialValue;
    }

    /**
     * Sets a new materialValue
     *
     * @param string $materialValue
     * @return self
     */
    public function setMaterialValue($materialValue)
    {
        $this->materialValue = $materialValue;
        return $this;
    }


}

