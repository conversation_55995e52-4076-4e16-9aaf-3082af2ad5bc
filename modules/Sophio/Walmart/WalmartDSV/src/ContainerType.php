<?php

namespace WalmartDSV;

/**
 * Class representing ContainerType
 *
 * The kind of physical package or receptacle that contains the product as presented to the consumer. Also used to describe storage items. Consumers may select different products based on their container preferences. For example, a parent may select juice packaged in boxes rather than bottles for use in childrens' lunch boxes.
 * XSD Type: ContainerType
 */
class ContainerType
{

    /**
     * @var string[] $containerTypeValue
     */
    private $containerTypeValue = [
        
    ];

    /**
     * Adds as containerTypeValue
     *
     * @return self
     * @param string $containerTypeValue
     */
    public function addToContainerTypeValue($containerTypeValue)
    {
        $this->containerTypeValue[] = $containerTypeValue;
        return $this;
    }

    /**
     * isset containerTypeValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetContainerTypeValue($index)
    {
        return isset($this->containerTypeValue[$index]);
    }

    /**
     * unset containerTypeValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetContainerTypeValue($index)
    {
        unset($this->containerTypeValue[$index]);
    }

    /**
     * Gets as containerTypeValue
     *
     * @return string[]
     */
    public function getContainerTypeValue()
    {
        return $this->containerTypeValue;
    }

    /**
     * Sets a new containerTypeValue
     *
     * @param string $containerTypeValue
     * @return self
     */
    public function setContainerTypeValue(array $containerTypeValue)
    {
        $this->containerTypeValue = $containerTypeValue;
        return $this;
    }


}

