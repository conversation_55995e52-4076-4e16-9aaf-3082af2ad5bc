<?php

namespace WalmartDSV\FootwearType;

/**
 * Class representing SmallPartsWarningsAType
 */
class SmallPartsWarningsAType
{

    /**
     * @var int[] $smallPartsWarning
     */
    private $smallPartsWarning = [
        
    ];

    /**
     * Adds as smallPartsWarning
     *
     * @return self
     * @param int $smallPartsWarning
     */
    public function addToSmallPartsWarning($smallPartsWarning)
    {
        $this->smallPartsWarning[] = $smallPartsWarning;
        return $this;
    }

    /**
     * isset smallPartsWarning
     *
     * @param int|string $index
     * @return bool
     */
    public function issetSmallPartsWarning($index)
    {
        return isset($this->smallPartsWarning[$index]);
    }

    /**
     * unset smallPartsWarning
     *
     * @param int|string $index
     * @return void
     */
    public function unsetSmallPartsWarning($index)
    {
        unset($this->smallPartsWarning[$index]);
    }

    /**
     * Gets as smallPartsWarning
     *
     * @return int[]
     */
    public function getSmallPartsWarning()
    {
        return $this->smallPartsWarning;
    }

    /**
     * Sets a new smallPartsWarning
     *
     * @param int $smallPartsWarning
     * @return self
     */
    public function setSmallPartsWarning(array $smallPartsWarning)
    {
        $this->smallPartsWarning = $smallPartsWarning;
        return $this;
    }


}

