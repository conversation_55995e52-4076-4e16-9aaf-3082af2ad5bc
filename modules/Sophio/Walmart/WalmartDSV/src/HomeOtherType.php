<?php

namespace WalmartDSV;

/**
 * Class representing HomeOtherType
 *
 *
 * XSD Type: HomeOther
 */
class HomeOtherType
{

    /**
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @var string[] $additionalVariantAttributeNames
     */
    private $additionalVariantAttributeNames = null;

    /**
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @var string $shortDescription
     */
    private $shortDescription = null;

    /**
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @var string[] $keyFeatures
     */
    private $keyFeatures = null;

    /**
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @var string $brand
     */
    private $brand = null;

    /**
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @var string $manufacturer
     */
    private $manufacturer = null;

    /**
     * Model numbers allow manufacturers to keep track of each hardware device and identify or replace the proper part when needed. Model numbers are often found on the bottom, back, or side of a product. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @var string $modelNumber
     */
    private $modelNumber = null;

    /**
     * MPN uniquely identifies the product to its manufacturer. For many products this will be identical to the model number. Some manufacturers distinguish part number from model number. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @var string $manufacturerPartNumber
     */
    private $manufacturerPartNumber = null;

    /**
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @var int $multipackQuantity
     */
    private $multipackQuantity = null;

    /**
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @var int $countPerPack
     */
    private $countPerPack = null;

    /**
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @var string $count
     */
    private $count = null;

    /**
     * The number of small pieces, slices, or different items within the product. Piece Count applies to things such as puzzles, building block sets, and products that contain multiple different items (such as tool sets, dinnerware sets, gift baskets, art sets, makeup kits, or shaving kits). EXAMPLE: (1) A 500-piece puzzle has a "Piece Count" of 500. (2) A 105-Piece Socket Wrench set has a piece count of "105." (3) A gift basket of 5 different items has a "Piece Count" of 5.
     *
     * @var int $pieceCount
     */
    private $pieceCount = null;

    /**
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @var string $mainImageUrl
     */
    private $mainImageUrl = null;

    /**
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @var string[] $productSecondaryImageURL
     */
    private $productSecondaryImageURL = null;

    /**
     * Color as described by the manufacturer.
     *
     * @var \WalmartDSV\ColorType $color
     */
    private $color = null;

    /**
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @var string[] $colorCategory
     */
    private $colorCategory = null;

    /**
     * Decorative design or visual ornamentation, often with a thematic, recurring motif.
     *
     * @var \WalmartDSV\PatternType $pattern
     */
    private $pattern = null;

    /**
     * The main material(s) that a product is made of. This does not need to be an exhaustive list, but should contain the predominant or functionally important material/materials. Fabric material specifics should be entered using the "Fabric Content" attribute.
     *
     * @var \WalmartDSV\MaterialType $material
     */
    private $material = null;

    /**
     * Terms describing the overall external treatment applied to the item. Typically finishes give a distinct appearance, texture or additional performance to the item. This attribute is used in a wide variety products and materials including wood, metal and fabric.
     *
     * @var string $finish
     */
    private $finish = null;

    /**
     * General grouping of ages into commonly used demographic labels.
     *
     * @var string[] $ageGroup
     */
    private $ageGroup = null;

    /**
     * Indicate whether this item is meant for a particular gender or meant to be gender-agnostic (unisex).
     *
     * @var string $gender
     */
    private $gender = null;

    /**
     * Overall dimensions of an item. Used only for products that do not already have a more specific 'x size' attribute, such as ring size or clothing size.
     *
     * @var string $size
     */
    private $size = null;

    /**
     * The number of horizontal and vertical threads per square inch of fabric. This is considered an important indicator of item quality. For example, when selecting bed sheets, consumers correlate higher thread counts (over 300) with better quality and comfort.
     *
     * @var int $threadCount
     */
    private $threadCount = null;

    /**
     * The approximate size of rugs and rug runners, measured in feet, and and expressed as Length x Width (for example: a ten-foot-long by five-foot-wide rug's measurements should be entered as 10 x 5).
     *
     * @var string $rugSize
     */
    private $rugSize = null;

    /**
     * Style of rug expressed in fashion or form. Use the example values; if your style does not appear, you may enter it.
     *
     * @var string[] $rugStyle
     */
    private $rugStyle = null;

    /**
     * Type of design for panel curtains typically based on method of attachment. Attribute is used on the front end to display panel types to customers. For example, some customers prefer a grommet style panel because it slides easily onto a rod or pole.
     *
     * @var string $curtainPanelStyle
     */
    private $curtainPanelStyle = null;

    /**
     * Descriptive term for fragrance labeled on the product, if any. "Unscented" is a scent, if labeled. If no scent is specifically labeled, leave blank.
     *
     * @var string $scent
     */
    private $scent = null;

    /**
     * Y indicates the item has special features that are contusive to pet well-being, or living with a pet. Used for home products that are a good choice if you have pets. For example, if a chair cushion is made of fabric that's designed to resistant to stains, smells, bacteria and muddy paws.
     *
     * @var string $isPetFriendly
     */
    private $isPetFriendly = null;

    /**
     * The material used to stuff the item (in a cushion or plush toy, for example).
     *
     * @var string[] $fillMaterial
     */
    private $fillMaterial = null;

    /**
     * The type of numbers on a clock or watch. Some consumers find Arabic numbers easier to read than Roman numerals. Others prefer the clean look of a clock with no numbers.
     *
     * @var string $clockNumberType
     */
    private $clockNumberType = null;

    /**
     * Describes home furnishings and decorations according to various themes, styles and tastes.
     *
     * @var string $homeDecorStyle
     */
    private $homeDecorStyle = null;

    /**
     * The measurement from one side of a circle to the other, through the middle.
     *
     * @var \WalmartDSV\HomeOtherType\DiameterAType $diameter
     */
    private $diameter = null;

    /**
     * A dominant idea, meaning, or setting applied to an item. Used in a wide range of products including decorative objects, clothing, toys, and furniture. Can be an important selection criteria for consumers who want to achieve a particular ambiance for room décor or for a special occasion.
     *
     * @var \WalmartDSV\ThemeType $theme
     */
    private $theme = null;

    /**
     * A person or entity portrayed in print or visual media. A character might be a fictional personality or an actual living person.
     *
     * @var \WalmartDSV\CharacterType $character
     */
    private $character = null;

    /**
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @var string[] $globalBrandLicense
     */
    private $globalBrandLicense = null;

    /**
     * The length of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @var \WalmartDSV\HomeOtherType\AssembledProductLengthAType $assembledProductLength
     */
    private $assembledProductLength = null;

    /**
     * The width of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @var \WalmartDSV\HomeOtherType\AssembledProductWidthAType $assembledProductWidth
     */
    private $assembledProductWidth = null;

    /**
     * The height of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @var \WalmartDSV\HomeOtherType\AssembledProductHeightAType $assembledProductHeight
     */
    private $assembledProductHeight = null;

    /**
     * The weight of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @var \WalmartDSV\HomeOtherType\AssembledProductWeightAType $assembledProductWeight
     */
    private $assembledProductWeight = null;

    /**
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @var string $variantGroupId
     */
    private $variantGroupId = null;

    /**
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @var string[] $variantAttributeNames
     */
    private $variantAttributeNames = null;

    /**
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @var string $isPrimaryVariant
     */
    private $isPrimaryVariant = null;

    /**
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @var string $isPrivateLabelOrUnbranded
     */
    private $isPrivateLabelOrUnbranded = null;

    /**
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @var string $isProp65WarningRequired
     */
    private $isProp65WarningRequired = null;

    /**
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @var string $prop65WarningText
     */
    private $prop65WarningText = null;

    /**
     * To determine if any choking warnings are applicable, check current product packaging for choking warning message(s). Please indicate the warning number (0-6). 0 - No warning applicable; 1 - Choking hazard is a small ball; 2 - Choking hazard contains small ball; 3 - Choking hazard contains small parts; 4 - Choking hazard balloon; 5 - Choking hazard is a marble; 6 - Choking hazard contains a marble.
     *
     * @var int[] $smallPartsWarnings
     */
    private $smallPartsWarnings = null;

    /**
     * Select "Y" if your item contains wool or is one of the following: clothing (except for hats and shoes), handkerchiefs, scarves, bedding (including sheets, covers, blankets, comforters, pillows, pillowcases, quilts, bedspreads and pads (but not outer coverings for mattresses or box springs)), curtains and casements, draperies, tablecloths, napkins, doilies, floor coverings (rugs, carpets and mats), towels, washcloths, dishcloths, ironing board covers and pads, umbrellas, parasols, bats or batting, flags with heading or that are bigger than 216 square inches, cushions, all fibers, yarns and fabrics (but not packaging ribbons), furniture slip covers and other furniture covers, afghans and throws, sleeping bags, antimacassars (doilies), hammocks, dresser and other furniture scarves. For further information on these requirements, refer to the labeling requirements of the Textile Act.
     *
     * @var string $requiresTextileActLabeling
     */
    private $requiresTextileActLabeling = null;

    /**
     * Use “Made in U.S.A. and Imported” to indicate manufacture in the U.S. from imported materials, or part processing in the U.S. and part in a foreign country. Use “Made in U.S.A. or Imported” to reflect that some units of an item originate from a domestic source and others from a foreign source. Use “Made in U.S.A.” only if all units were made completely in the U.S. using materials also made in the U.S. Use "Imported" if units are completely imported.
     *
     * @var string $countryOfOriginTextiles
     */
    private $countryOfOriginTextiles = null;

    /**
     * "Battery or battery containing product" is defined by Company to include any item of Merchandise that is a battery or any component of Merchandise, including reusable packaging intended to stay in use with the item, containing a battery of any chemistry/ type. Mark Y if this definition applies to your product.
     *
     * @var string $hasBatteries
     */
    private $hasBatteries = null;

    /**
     * Please select the Battery Technology Type from the list provided. NOTE: If battery type is lead acid, lead acid (nonspillable), lithium ion, or lithium metal, please ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @var string $batteryTechnologyType
     */
    private $batteryTechnologyType = null;

    /**
     * Y indicates the item comes with a warranty. If an item has a warranty, then enter EITHER the warranty URL or the warranty text in the appropriate field.
     *
     * @var string $hasWarranty
     */
    private $hasWarranty = null;

    /**
     * If you indicated that your item has a warranty, provide either the Warranty URL or Warranty Text. The Warranty URL is the web location of the image, PDF, or link to the manufacturer's warranty page, showing the warranty and its terms, including the duration of the warranty. URLs must begin with http:// or https:// NOTE: Please remember to update the link and/or text of the warranty as the warranty changes. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @var string $warrantyURL
     */
    private $warrantyURL = null;

    /**
     * If you marked Y for "Has Warranty" provide the Warranty URL or Warranty Text (the full text of the warranty terms, including what is covered by the warranty and the duration of the warranty). NOTE: please remember to update the text of your warranty as your warranty changes.
     *
     * @var string $warrantyText
     */
    private $warrantyText = null;

    /**
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @var string $hasStateRestrictions
     */
    private $hasStateRestrictions = null;

    /**
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @var \WalmartDSV\StateRestrictionType[] $stateRestrictions
     */
    private $stateRestrictions = null;

    /**
     * Denotes any item with an empty container that may be filled with fluids, such as fuel, CO2, propane, etc.
     *
     * @var string $hasFuelContainer
     */
    private $hasFuelContainer = null;

    /**
     * Does your item require a Lighting Facts label? A Lighting Facts label must appear on packaging for most general service "lamps" with medium screw bases. That includes most incandescent, compact fluorescent (CFL), and light emitting diode (LED) light bulbs. For more information on what items are covered, see 16 CFR § 305.2 and § 305.3.
     *
     * @var string $isLightingFactsLabelRequired
     */
    private $isLightingFactsLabelRequired = null;

    /**
     * URL of the location of the label. URLs must begin with http:// or https:// Label must include brightness specified lumens, estimated energy cost per year, life, light appearance, scale, energy used, and special handling information needed for disposal, as outlined by the FTC. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB.
     *
     * @var string $lightingFactsLabel
     */
    private $lightingFactsLabel = null;

    /**
     * Is product unassembled and must be put together before use?
     *
     * @var string $isAssemblyRequired
     */
    private $isAssemblyRequired = null;

    /**
     * Provide a URL to an image or PDF asset showing assembly instructions for items requiring assembly. URLs must be static and have no query parameters. URLs must begin with http:// or https:// and should end in in the file name.
     *
     * @var string $assemblyInstructions
     */
    private $assemblyInstructions = null;

    /**
     * Description of how the item should be cleaned and maintained.
     *
     * @var string $cleaningCareAndMaintenance
     */
    private $cleaningCareAndMaintenance = null;

    /**
     * The particles, liquids, and gases in the air or water which have harmful chemical properties that are removed by the item.
     *
     * @var string $contaminantsRemoved
     */
    private $contaminantsRemoved = null;

    /**
     * Further clarification of what the item may be used for. This improves searchability when customers search for general terms like "birthday party" that do not include the names of specific items.
     *
     * @var string[] $recommendedUses
     */
    private $recommendedUses = null;

    /**
     * The rooms where the item is likely or recommended to be used.
     *
     * @var string[] $recommendedRooms
     */
    private $recommendedRooms = null;

    /**
     * Material makeup of the item.
     *
     * @var \WalmartDSV\FabricContentValueType[] $fabricContent
     */
    private $fabricContent = null;

    /**
     * Color of a fabric part of an item, to distinguish it from the general color of the item, if needed.
     *
     * @var string $fabricColor
     */
    private $fabricColor = null;

    /**
     * Describes how the fabric should be cleaned. Enter details of the fabric care label found on the item. (For garments, typically located inside on the top of the back or the lower left side.)
     *
     * @var string[] $fabricCareInstructions
     */
    private $fabricCareInstructions = null;

    /**
     * Measurement of the length of the curtain as specified by the manufacturer. How length is measured varies with the type of curtain. Most are measured overall from the top edge of the curtain to the bottom edge (longest point) of the curtain. Curtains with large rod pockets are measured from the top of the rod pocket to the bottom edge of the curtain.
     *
     * @var \WalmartDSV\HomeOtherType\CurtainLengthAType $curtainLength
     */
    private $curtainLength = null;

    /**
     * Provides information on the exact type of power used by the item.
     *
     * @var string $powerType
     */
    private $powerType = null;

    /**
     * The volume of space available in this item to contain objects.
     *
     * @var \WalmartDSV\HomeOtherType\VolumeCapacityAType $volumeCapacity
     */
    private $volumeCapacity = null;

    /**
     * Physical shape of the item. Used in a wide variety of products including rugs, toys and large appliances.
     *
     * @var string $shape
     */
    private $shape = null;

    /**
     * The particular target time, event, or holiday for the product.
     *
     * @var \WalmartDSV\OccasionType $occasion
     */
    private $occasion = null;

    /**
     * A secondary product color.
     *
     * @var string $accentColor
     */
    private $accentColor = null;

    /**
     * Describes the bed's shape and appearance according to its design features.
     *
     * @var string $bedStyle
     */
    private $bedStyle = null;

    /**
     * Mark as "Y" if your item is any of the following: food used for human or domestic animal consumption; ingredients added to food; napkins; tissues; toilet paper; foil, plastic wrap, wax paper, parchment paper; paper towels; disposable plates, bowls, and cutlery; detergents, soaps, waxes, and other cleansing agents; non-prescription drugs, female hygeine products, and toiletries; automotive fluids and cleaners; rock salt; diapers, pullups and swimmers; fertilizer; kitty litter.
     *
     * @var string $hasPricePerUnit
     */
    private $hasPricePerUnit = null;

    /**
     * Enter the quantity of units for the item, based on the "PPU Unit of Measure" you selected. For example, a gallon of milk should be 128. NOTE: Do not enter the price.
     *
     * @var float $pricePerUnitQuantity
     */
    private $pricePerUnitQuantity = null;

    /**
     * The units that will be used to calculate the "Price Per Unit" for your product. For example, a gallon of milk has a "PPU Unit of Measure" of Fluid Ounces. NOTE: This may not be the Unit of Measure on the label.
     *
     * @var string $pricePerUnitUom
     */
    private $pricePerUnitUom = null;

    /**
     * Color of the base portion of the item, if it needs to be distinguished from other components.
     *
     * @var string $baseColor
     */
    private $baseColor = null;

    /**
     * The finish of a base component of an item, if it needs to be distinguished from another component.
     *
     * @var string $baseFinish
     */
    private $baseFinish = null;

    /**
     * The material of a lampshade, to distinguish it from the rest of the lamp or other lighting product
     *
     * @var string $shadeMaterial
     */
    private $shadeMaterial = null;

    /**
     * Descriptive term for the shape of a lamp or other lighting product. Many terms are industry standard.
     *
     * @var string $shadeStyle
     */
    private $shadeStyle = null;

    /**
     * A collection is a particular group of items that have the same visual style, made by the same brand.
     *
     * @var string $collection
     */
    private $collection = null;

    /**
     * The number of people that a set of dishes, glasses, flatware, etc. will serve.
     *
     * @var float $serviceCount
     */
    private $serviceCount = null;

    /**
     * The color of a frame component of a product, if it needs to be distinguished from other components.
     *
     * @var string $frameColor
     */
    private $frameColor = null;

    /**
     * The width of a slat component of a product, such as widow blinds. This measurement is distinguished from the width of the product itself.
     *
     * @var \WalmartDSV\HomeOtherType\SlatWidthAType $slatWidth
     */
    private $slatWidth = null;

    /**
     * Y indicates that the product consists of two or more different items sold together as a set. Example: A bedding set that includes sheets, pillow shams, and a comforter.
     *
     * @var string $isSet
     */
    private $isSet = null;

    /**
     * List notable features of the item.
     *
     * @var string[] $features
     */
    private $features = null;

    /**
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @var string $keywords
     */
    private $keywords = null;

    /**
     * Name of College or other school. This is to distinguish the school from its sports team.
     *
     * @var string $academicInstitution
     */
    private $academicInstitution = null;

    /**
     * If your item has any association with a specific sports league, enter the league name. Abbreviations are fine. NOTE: This attribute flags an item for inclusion in the online fan shop.
     *
     * @var \WalmartDSV\SportsLeagueType $sportsLeague
     */
    private $sportsLeague = null;

    /**
     * If your item has any association with a specific sports team, enter the team name. NOTE: This attribute flags an item for inclusion in the online fan shop.
     *
     * @var \WalmartDSV\SportsTeamType $sportsTeam
     */
    private $sportsTeam = null;

    /**
     * A well-known athlete associated with a product, if applicable. This is used to group items in Fan Shop, not to describe a line of clothing.
     *
     * @var \WalmartDSV\AthleteType $athlete
     */
    private $athlete = null;

    /**
     * Composite Wood - Indicates if any portion of the item contains any of the following types of composite wood: hardwood plywood veneer core, hardwood plywood composite core, particleboard, or medium density fiber board (MDF). Enter the code corresponding to the highest formaldehyde emission level on any portion of the item. Code Definitions: 1 - Does not contain composite wood; 7 - Product is not CARB compliant and cannot be sold in California; 8 - Product is CARB compliant and can be sold in California.
     *
     * @var int $compositeWoodCertificationCode
     */
    private $compositeWoodCertificationCode = null;

    /**
     * @var \WalmartDSV\HomeOtherType\SwatchImagesAType\SwatchImageAType[] $swatchImages
     */
    private $swatchImages = null;

    /**
     * Adds as additionalVariantAttributeName
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @return self
     * @param string $additionalVariantAttributeName
     */
    public function addToAdditionalVariantAttributeNames($additionalVariantAttributeName)
    {
        $this->additionalVariantAttributeNames[] = $additionalVariantAttributeName;
        return $this;
    }

    /**
     * isset additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAdditionalVariantAttributeNames($index)
    {
        return isset($this->additionalVariantAttributeNames[$index]);
    }

    /**
     * unset additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAdditionalVariantAttributeNames($index)
    {
        unset($this->additionalVariantAttributeNames[$index]);
    }

    /**
     * Gets as additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @return string[]
     */
    public function getAdditionalVariantAttributeNames()
    {
        return $this->additionalVariantAttributeNames;
    }

    /**
     * Sets a new additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param string $additionalVariantAttributeNames
     * @return self
     */
    public function setAdditionalVariantAttributeNames(array $additionalVariantAttributeNames)
    {
        $this->additionalVariantAttributeNames = $additionalVariantAttributeNames;
        return $this;
    }

    /**
     * Gets as shortDescription
     *
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @return string
     */
    public function getShortDescription()
    {
        return $this->shortDescription;
    }

    /**
     * Sets a new shortDescription
     *
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @param string $shortDescription
     * @return self
     */
    public function setShortDescription($shortDescription)
    {
        $this->shortDescription = $shortDescription;
        return $this;
    }

    /**
     * Adds as keyFeaturesValue
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @return self
     * @param string $keyFeaturesValue
     */
    public function addToKeyFeatures($keyFeaturesValue)
    {
        $this->keyFeatures[] = $keyFeaturesValue;
        return $this;
    }

    /**
     * isset keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetKeyFeatures($index)
    {
        return isset($this->keyFeatures[$index]);
    }

    /**
     * unset keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetKeyFeatures($index)
    {
        unset($this->keyFeatures[$index]);
    }

    /**
     * Gets as keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @return string[]
     */
    public function getKeyFeatures()
    {
        return $this->keyFeatures;
    }

    /**
     * Sets a new keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param string $keyFeatures
     * @return self
     */
    public function setKeyFeatures(array $keyFeatures)
    {
        $this->keyFeatures = $keyFeatures;
        return $this;
    }

    /**
     * Gets as brand
     *
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @return string
     */
    public function getBrand()
    {
        return $this->brand;
    }

    /**
     * Sets a new brand
     *
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @param string $brand
     * @return self
     */
    public function setBrand($brand)
    {
        $this->brand = $brand;
        return $this;
    }

    /**
     * Gets as manufacturer
     *
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @return string
     */
    public function getManufacturer()
    {
        return $this->manufacturer;
    }

    /**
     * Sets a new manufacturer
     *
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @param string $manufacturer
     * @return self
     */
    public function setManufacturer($manufacturer)
    {
        $this->manufacturer = $manufacturer;
        return $this;
    }

    /**
     * Gets as modelNumber
     *
     * Model numbers allow manufacturers to keep track of each hardware device and identify or replace the proper part when needed. Model numbers are often found on the bottom, back, or side of a product. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @return string
     */
    public function getModelNumber()
    {
        return $this->modelNumber;
    }

    /**
     * Sets a new modelNumber
     *
     * Model numbers allow manufacturers to keep track of each hardware device and identify or replace the proper part when needed. Model numbers are often found on the bottom, back, or side of a product. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @param string $modelNumber
     * @return self
     */
    public function setModelNumber($modelNumber)
    {
        $this->modelNumber = $modelNumber;
        return $this;
    }

    /**
     * Gets as manufacturerPartNumber
     *
     * MPN uniquely identifies the product to its manufacturer. For many products this will be identical to the model number. Some manufacturers distinguish part number from model number. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @return string
     */
    public function getManufacturerPartNumber()
    {
        return $this->manufacturerPartNumber;
    }

    /**
     * Sets a new manufacturerPartNumber
     *
     * MPN uniquely identifies the product to its manufacturer. For many products this will be identical to the model number. Some manufacturers distinguish part number from model number. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @param string $manufacturerPartNumber
     * @return self
     */
    public function setManufacturerPartNumber($manufacturerPartNumber)
    {
        $this->manufacturerPartNumber = $manufacturerPartNumber;
        return $this;
    }

    /**
     * Gets as multipackQuantity
     *
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @return int
     */
    public function getMultipackQuantity()
    {
        return $this->multipackQuantity;
    }

    /**
     * Sets a new multipackQuantity
     *
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @param int $multipackQuantity
     * @return self
     */
    public function setMultipackQuantity($multipackQuantity)
    {
        $this->multipackQuantity = $multipackQuantity;
        return $this;
    }

    /**
     * Gets as countPerPack
     *
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @return int
     */
    public function getCountPerPack()
    {
        return $this->countPerPack;
    }

    /**
     * Sets a new countPerPack
     *
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @param int $countPerPack
     * @return self
     */
    public function setCountPerPack($countPerPack)
    {
        $this->countPerPack = $countPerPack;
        return $this;
    }

    /**
     * Gets as count
     *
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @return string
     */
    public function getCount()
    {
        return $this->count;
    }

    /**
     * Sets a new count
     *
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @param string $count
     * @return self
     */
    public function setCount($count)
    {
        $this->count = $count;
        return $this;
    }

    /**
     * Gets as pieceCount
     *
     * The number of small pieces, slices, or different items within the product. Piece Count applies to things such as puzzles, building block sets, and products that contain multiple different items (such as tool sets, dinnerware sets, gift baskets, art sets, makeup kits, or shaving kits). EXAMPLE: (1) A 500-piece puzzle has a "Piece Count" of 500. (2) A 105-Piece Socket Wrench set has a piece count of "105." (3) A gift basket of 5 different items has a "Piece Count" of 5.
     *
     * @return int
     */
    public function getPieceCount()
    {
        return $this->pieceCount;
    }

    /**
     * Sets a new pieceCount
     *
     * The number of small pieces, slices, or different items within the product. Piece Count applies to things such as puzzles, building block sets, and products that contain multiple different items (such as tool sets, dinnerware sets, gift baskets, art sets, makeup kits, or shaving kits). EXAMPLE: (1) A 500-piece puzzle has a "Piece Count" of 500. (2) A 105-Piece Socket Wrench set has a piece count of "105." (3) A gift basket of 5 different items has a "Piece Count" of 5.
     *
     * @param int $pieceCount
     * @return self
     */
    public function setPieceCount($pieceCount)
    {
        $this->pieceCount = $pieceCount;
        return $this;
    }

    /**
     * Gets as mainImageUrl
     *
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return string
     */
    public function getMainImageUrl()
    {
        return $this->mainImageUrl;
    }

    /**
     * Sets a new mainImageUrl
     *
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param string $mainImageUrl
     * @return self
     */
    public function setMainImageUrl($mainImageUrl)
    {
        $this->mainImageUrl = $mainImageUrl;
        return $this;
    }

    /**
     * Adds as productSecondaryImageURLValue
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return self
     * @param string $productSecondaryImageURLValue
     */
    public function addToProductSecondaryImageURL($productSecondaryImageURLValue)
    {
        $this->productSecondaryImageURL[] = $productSecondaryImageURLValue;
        return $this;
    }

    /**
     * isset productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetProductSecondaryImageURL($index)
    {
        return isset($this->productSecondaryImageURL[$index]);
    }

    /**
     * unset productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetProductSecondaryImageURL($index)
    {
        unset($this->productSecondaryImageURL[$index]);
    }

    /**
     * Gets as productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return string[]
     */
    public function getProductSecondaryImageURL()
    {
        return $this->productSecondaryImageURL;
    }

    /**
     * Sets a new productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param string $productSecondaryImageURL
     * @return self
     */
    public function setProductSecondaryImageURL(array $productSecondaryImageURL)
    {
        $this->productSecondaryImageURL = $productSecondaryImageURL;
        return $this;
    }

    /**
     * Gets as color
     *
     * Color as described by the manufacturer.
     *
     * @return \WalmartDSV\ColorType
     */
    public function getColor()
    {
        return $this->color;
    }

    /**
     * Sets a new color
     *
     * Color as described by the manufacturer.
     *
     * @param \WalmartDSV\ColorType $color
     * @return self
     */
    public function setColor(\WalmartDSV\ColorType $color)
    {
        $this->color = $color;
        return $this;
    }

    /**
     * Adds as colorCategoryValue
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @return self
     * @param string $colorCategoryValue
     */
    public function addToColorCategory($colorCategoryValue)
    {
        $this->colorCategory[] = $colorCategoryValue;
        return $this;
    }

    /**
     * isset colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetColorCategory($index)
    {
        return isset($this->colorCategory[$index]);
    }

    /**
     * unset colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetColorCategory($index)
    {
        unset($this->colorCategory[$index]);
    }

    /**
     * Gets as colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @return string[]
     */
    public function getColorCategory()
    {
        return $this->colorCategory;
    }

    /**
     * Sets a new colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @param string $colorCategory
     * @return self
     */
    public function setColorCategory(array $colorCategory)
    {
        $this->colorCategory = $colorCategory;
        return $this;
    }

    /**
     * Gets as pattern
     *
     * Decorative design or visual ornamentation, often with a thematic, recurring motif.
     *
     * @return \WalmartDSV\PatternType
     */
    public function getPattern()
    {
        return $this->pattern;
    }

    /**
     * Sets a new pattern
     *
     * Decorative design or visual ornamentation, often with a thematic, recurring motif.
     *
     * @param \WalmartDSV\PatternType $pattern
     * @return self
     */
    public function setPattern(\WalmartDSV\PatternType $pattern)
    {
        $this->pattern = $pattern;
        return $this;
    }

    /**
     * Gets as material
     *
     * The main material(s) that a product is made of. This does not need to be an exhaustive list, but should contain the predominant or functionally important material/materials. Fabric material specifics should be entered using the "Fabric Content" attribute.
     *
     * @return \WalmartDSV\MaterialType
     */
    public function getMaterial()
    {
        return $this->material;
    }

    /**
     * Sets a new material
     *
     * The main material(s) that a product is made of. This does not need to be an exhaustive list, but should contain the predominant or functionally important material/materials. Fabric material specifics should be entered using the "Fabric Content" attribute.
     *
     * @param \WalmartDSV\MaterialType $material
     * @return self
     */
    public function setMaterial(\WalmartDSV\MaterialType $material)
    {
        $this->material = $material;
        return $this;
    }

    /**
     * Gets as finish
     *
     * Terms describing the overall external treatment applied to the item. Typically finishes give a distinct appearance, texture or additional performance to the item. This attribute is used in a wide variety products and materials including wood, metal and fabric.
     *
     * @return string
     */
    public function getFinish()
    {
        return $this->finish;
    }

    /**
     * Sets a new finish
     *
     * Terms describing the overall external treatment applied to the item. Typically finishes give a distinct appearance, texture or additional performance to the item. This attribute is used in a wide variety products and materials including wood, metal and fabric.
     *
     * @param string $finish
     * @return self
     */
    public function setFinish($finish)
    {
        $this->finish = $finish;
        return $this;
    }

    /**
     * Adds as ageGroupValue
     *
     * General grouping of ages into commonly used demographic labels.
     *
     * @return self
     * @param string $ageGroupValue
     */
    public function addToAgeGroup($ageGroupValue)
    {
        $this->ageGroup[] = $ageGroupValue;
        return $this;
    }

    /**
     * isset ageGroup
     *
     * General grouping of ages into commonly used demographic labels.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAgeGroup($index)
    {
        return isset($this->ageGroup[$index]);
    }

    /**
     * unset ageGroup
     *
     * General grouping of ages into commonly used demographic labels.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAgeGroup($index)
    {
        unset($this->ageGroup[$index]);
    }

    /**
     * Gets as ageGroup
     *
     * General grouping of ages into commonly used demographic labels.
     *
     * @return string[]
     */
    public function getAgeGroup()
    {
        return $this->ageGroup;
    }

    /**
     * Sets a new ageGroup
     *
     * General grouping of ages into commonly used demographic labels.
     *
     * @param string $ageGroup
     * @return self
     */
    public function setAgeGroup(array $ageGroup)
    {
        $this->ageGroup = $ageGroup;
        return $this;
    }

    /**
     * Gets as gender
     *
     * Indicate whether this item is meant for a particular gender or meant to be gender-agnostic (unisex).
     *
     * @return string
     */
    public function getGender()
    {
        return $this->gender;
    }

    /**
     * Sets a new gender
     *
     * Indicate whether this item is meant for a particular gender or meant to be gender-agnostic (unisex).
     *
     * @param string $gender
     * @return self
     */
    public function setGender($gender)
    {
        $this->gender = $gender;
        return $this;
    }

    /**
     * Gets as size
     *
     * Overall dimensions of an item. Used only for products that do not already have a more specific 'x size' attribute, such as ring size or clothing size.
     *
     * @return string
     */
    public function getSize()
    {
        return $this->size;
    }

    /**
     * Sets a new size
     *
     * Overall dimensions of an item. Used only for products that do not already have a more specific 'x size' attribute, such as ring size or clothing size.
     *
     * @param string $size
     * @return self
     */
    public function setSize($size)
    {
        $this->size = $size;
        return $this;
    }

    /**
     * Gets as threadCount
     *
     * The number of horizontal and vertical threads per square inch of fabric. This is considered an important indicator of item quality. For example, when selecting bed sheets, consumers correlate higher thread counts (over 300) with better quality and comfort.
     *
     * @return int
     */
    public function getThreadCount()
    {
        return $this->threadCount;
    }

    /**
     * Sets a new threadCount
     *
     * The number of horizontal and vertical threads per square inch of fabric. This is considered an important indicator of item quality. For example, when selecting bed sheets, consumers correlate higher thread counts (over 300) with better quality and comfort.
     *
     * @param int $threadCount
     * @return self
     */
    public function setThreadCount($threadCount)
    {
        $this->threadCount = $threadCount;
        return $this;
    }

    /**
     * Gets as rugSize
     *
     * The approximate size of rugs and rug runners, measured in feet, and and expressed as Length x Width (for example: a ten-foot-long by five-foot-wide rug's measurements should be entered as 10 x 5).
     *
     * @return string
     */
    public function getRugSize()
    {
        return $this->rugSize;
    }

    /**
     * Sets a new rugSize
     *
     * The approximate size of rugs and rug runners, measured in feet, and and expressed as Length x Width (for example: a ten-foot-long by five-foot-wide rug's measurements should be entered as 10 x 5).
     *
     * @param string $rugSize
     * @return self
     */
    public function setRugSize($rugSize)
    {
        $this->rugSize = $rugSize;
        return $this;
    }

    /**
     * Adds as rugStyleValue
     *
     * Style of rug expressed in fashion or form. Use the example values; if your style does not appear, you may enter it.
     *
     * @return self
     * @param string $rugStyleValue
     */
    public function addToRugStyle($rugStyleValue)
    {
        $this->rugStyle[] = $rugStyleValue;
        return $this;
    }

    /**
     * isset rugStyle
     *
     * Style of rug expressed in fashion or form. Use the example values; if your style does not appear, you may enter it.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetRugStyle($index)
    {
        return isset($this->rugStyle[$index]);
    }

    /**
     * unset rugStyle
     *
     * Style of rug expressed in fashion or form. Use the example values; if your style does not appear, you may enter it.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetRugStyle($index)
    {
        unset($this->rugStyle[$index]);
    }

    /**
     * Gets as rugStyle
     *
     * Style of rug expressed in fashion or form. Use the example values; if your style does not appear, you may enter it.
     *
     * @return string[]
     */
    public function getRugStyle()
    {
        return $this->rugStyle;
    }

    /**
     * Sets a new rugStyle
     *
     * Style of rug expressed in fashion or form. Use the example values; if your style does not appear, you may enter it.
     *
     * @param string $rugStyle
     * @return self
     */
    public function setRugStyle(array $rugStyle)
    {
        $this->rugStyle = $rugStyle;
        return $this;
    }

    /**
     * Gets as curtainPanelStyle
     *
     * Type of design for panel curtains typically based on method of attachment. Attribute is used on the front end to display panel types to customers. For example, some customers prefer a grommet style panel because it slides easily onto a rod or pole.
     *
     * @return string
     */
    public function getCurtainPanelStyle()
    {
        return $this->curtainPanelStyle;
    }

    /**
     * Sets a new curtainPanelStyle
     *
     * Type of design for panel curtains typically based on method of attachment. Attribute is used on the front end to display panel types to customers. For example, some customers prefer a grommet style panel because it slides easily onto a rod or pole.
     *
     * @param string $curtainPanelStyle
     * @return self
     */
    public function setCurtainPanelStyle($curtainPanelStyle)
    {
        $this->curtainPanelStyle = $curtainPanelStyle;
        return $this;
    }

    /**
     * Gets as scent
     *
     * Descriptive term for fragrance labeled on the product, if any. "Unscented" is a scent, if labeled. If no scent is specifically labeled, leave blank.
     *
     * @return string
     */
    public function getScent()
    {
        return $this->scent;
    }

    /**
     * Sets a new scent
     *
     * Descriptive term for fragrance labeled on the product, if any. "Unscented" is a scent, if labeled. If no scent is specifically labeled, leave blank.
     *
     * @param string $scent
     * @return self
     */
    public function setScent($scent)
    {
        $this->scent = $scent;
        return $this;
    }

    /**
     * Gets as isPetFriendly
     *
     * Y indicates the item has special features that are contusive to pet well-being, or living with a pet. Used for home products that are a good choice if you have pets. For example, if a chair cushion is made of fabric that's designed to resistant to stains, smells, bacteria and muddy paws.
     *
     * @return string
     */
    public function getIsPetFriendly()
    {
        return $this->isPetFriendly;
    }

    /**
     * Sets a new isPetFriendly
     *
     * Y indicates the item has special features that are contusive to pet well-being, or living with a pet. Used for home products that are a good choice if you have pets. For example, if a chair cushion is made of fabric that's designed to resistant to stains, smells, bacteria and muddy paws.
     *
     * @param string $isPetFriendly
     * @return self
     */
    public function setIsPetFriendly($isPetFriendly)
    {
        $this->isPetFriendly = $isPetFriendly;
        return $this;
    }

    /**
     * Adds as fillMaterialValue
     *
     * The material used to stuff the item (in a cushion or plush toy, for example).
     *
     * @return self
     * @param string $fillMaterialValue
     */
    public function addToFillMaterial($fillMaterialValue)
    {
        $this->fillMaterial[] = $fillMaterialValue;
        return $this;
    }

    /**
     * isset fillMaterial
     *
     * The material used to stuff the item (in a cushion or plush toy, for example).
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFillMaterial($index)
    {
        return isset($this->fillMaterial[$index]);
    }

    /**
     * unset fillMaterial
     *
     * The material used to stuff the item (in a cushion or plush toy, for example).
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFillMaterial($index)
    {
        unset($this->fillMaterial[$index]);
    }

    /**
     * Gets as fillMaterial
     *
     * The material used to stuff the item (in a cushion or plush toy, for example).
     *
     * @return string[]
     */
    public function getFillMaterial()
    {
        return $this->fillMaterial;
    }

    /**
     * Sets a new fillMaterial
     *
     * The material used to stuff the item (in a cushion or plush toy, for example).
     *
     * @param string $fillMaterial
     * @return self
     */
    public function setFillMaterial(array $fillMaterial)
    {
        $this->fillMaterial = $fillMaterial;
        return $this;
    }

    /**
     * Gets as clockNumberType
     *
     * The type of numbers on a clock or watch. Some consumers find Arabic numbers easier to read than Roman numerals. Others prefer the clean look of a clock with no numbers.
     *
     * @return string
     */
    public function getClockNumberType()
    {
        return $this->clockNumberType;
    }

    /**
     * Sets a new clockNumberType
     *
     * The type of numbers on a clock or watch. Some consumers find Arabic numbers easier to read than Roman numerals. Others prefer the clean look of a clock with no numbers.
     *
     * @param string $clockNumberType
     * @return self
     */
    public function setClockNumberType($clockNumberType)
    {
        $this->clockNumberType = $clockNumberType;
        return $this;
    }

    /**
     * Gets as homeDecorStyle
     *
     * Describes home furnishings and decorations according to various themes, styles and tastes.
     *
     * @return string
     */
    public function getHomeDecorStyle()
    {
        return $this->homeDecorStyle;
    }

    /**
     * Sets a new homeDecorStyle
     *
     * Describes home furnishings and decorations according to various themes, styles and tastes.
     *
     * @param string $homeDecorStyle
     * @return self
     */
    public function setHomeDecorStyle($homeDecorStyle)
    {
        $this->homeDecorStyle = $homeDecorStyle;
        return $this;
    }

    /**
     * Gets as diameter
     *
     * The measurement from one side of a circle to the other, through the middle.
     *
     * @return \WalmartDSV\HomeOtherType\DiameterAType
     */
    public function getDiameter()
    {
        return $this->diameter;
    }

    /**
     * Sets a new diameter
     *
     * The measurement from one side of a circle to the other, through the middle.
     *
     * @param \WalmartDSV\HomeOtherType\DiameterAType $diameter
     * @return self
     */
    public function setDiameter(\WalmartDSV\HomeOtherType\DiameterAType $diameter)
    {
        $this->diameter = $diameter;
        return $this;
    }

    /**
     * Gets as theme
     *
     * A dominant idea, meaning, or setting applied to an item. Used in a wide range of products including decorative objects, clothing, toys, and furniture. Can be an important selection criteria for consumers who want to achieve a particular ambiance for room décor or for a special occasion.
     *
     * @return \WalmartDSV\ThemeType
     */
    public function getTheme()
    {
        return $this->theme;
    }

    /**
     * Sets a new theme
     *
     * A dominant idea, meaning, or setting applied to an item. Used in a wide range of products including decorative objects, clothing, toys, and furniture. Can be an important selection criteria for consumers who want to achieve a particular ambiance for room décor or for a special occasion.
     *
     * @param \WalmartDSV\ThemeType $theme
     * @return self
     */
    public function setTheme(\WalmartDSV\ThemeType $theme)
    {
        $this->theme = $theme;
        return $this;
    }

    /**
     * Gets as character
     *
     * A person or entity portrayed in print or visual media. A character might be a fictional personality or an actual living person.
     *
     * @return \WalmartDSV\CharacterType
     */
    public function getCharacter()
    {
        return $this->character;
    }

    /**
     * Sets a new character
     *
     * A person or entity portrayed in print or visual media. A character might be a fictional personality or an actual living person.
     *
     * @param \WalmartDSV\CharacterType $character
     * @return self
     */
    public function setCharacter(\WalmartDSV\CharacterType $character)
    {
        $this->character = $character;
        return $this;
    }

    /**
     * Adds as globalBrandLicenseValue
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @return self
     * @param string $globalBrandLicenseValue
     */
    public function addToGlobalBrandLicense($globalBrandLicenseValue)
    {
        $this->globalBrandLicense[] = $globalBrandLicenseValue;
        return $this;
    }

    /**
     * isset globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @param int|string $index
     * @return bool
     */
    public function issetGlobalBrandLicense($index)
    {
        return isset($this->globalBrandLicense[$index]);
    }

    /**
     * unset globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @param int|string $index
     * @return void
     */
    public function unsetGlobalBrandLicense($index)
    {
        unset($this->globalBrandLicense[$index]);
    }

    /**
     * Gets as globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @return string[]
     */
    public function getGlobalBrandLicense()
    {
        return $this->globalBrandLicense;
    }

    /**
     * Sets a new globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @param string $globalBrandLicense
     * @return self
     */
    public function setGlobalBrandLicense(array $globalBrandLicense)
    {
        $this->globalBrandLicense = $globalBrandLicense;
        return $this;
    }

    /**
     * Gets as assembledProductLength
     *
     * The length of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @return \WalmartDSV\HomeOtherType\AssembledProductLengthAType
     */
    public function getAssembledProductLength()
    {
        return $this->assembledProductLength;
    }

    /**
     * Sets a new assembledProductLength
     *
     * The length of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @param \WalmartDSV\HomeOtherType\AssembledProductLengthAType $assembledProductLength
     * @return self
     */
    public function setAssembledProductLength(\WalmartDSV\HomeOtherType\AssembledProductLengthAType $assembledProductLength)
    {
        $this->assembledProductLength = $assembledProductLength;
        return $this;
    }

    /**
     * Gets as assembledProductWidth
     *
     * The width of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @return \WalmartDSV\HomeOtherType\AssembledProductWidthAType
     */
    public function getAssembledProductWidth()
    {
        return $this->assembledProductWidth;
    }

    /**
     * Sets a new assembledProductWidth
     *
     * The width of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @param \WalmartDSV\HomeOtherType\AssembledProductWidthAType $assembledProductWidth
     * @return self
     */
    public function setAssembledProductWidth(\WalmartDSV\HomeOtherType\AssembledProductWidthAType $assembledProductWidth)
    {
        $this->assembledProductWidth = $assembledProductWidth;
        return $this;
    }

    /**
     * Gets as assembledProductHeight
     *
     * The height of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @return \WalmartDSV\HomeOtherType\AssembledProductHeightAType
     */
    public function getAssembledProductHeight()
    {
        return $this->assembledProductHeight;
    }

    /**
     * Sets a new assembledProductHeight
     *
     * The height of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @param \WalmartDSV\HomeOtherType\AssembledProductHeightAType $assembledProductHeight
     * @return self
     */
    public function setAssembledProductHeight(\WalmartDSV\HomeOtherType\AssembledProductHeightAType $assembledProductHeight)
    {
        $this->assembledProductHeight = $assembledProductHeight;
        return $this;
    }

    /**
     * Gets as assembledProductWeight
     *
     * The weight of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @return \WalmartDSV\HomeOtherType\AssembledProductWeightAType
     */
    public function getAssembledProductWeight()
    {
        return $this->assembledProductWeight;
    }

    /**
     * Sets a new assembledProductWeight
     *
     * The weight of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @param \WalmartDSV\HomeOtherType\AssembledProductWeightAType $assembledProductWeight
     * @return self
     */
    public function setAssembledProductWeight(\WalmartDSV\HomeOtherType\AssembledProductWeightAType $assembledProductWeight)
    {
        $this->assembledProductWeight = $assembledProductWeight;
        return $this;
    }

    /**
     * Gets as variantGroupId
     *
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @return string
     */
    public function getVariantGroupId()
    {
        return $this->variantGroupId;
    }

    /**
     * Sets a new variantGroupId
     *
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @param string $variantGroupId
     * @return self
     */
    public function setVariantGroupId($variantGroupId)
    {
        $this->variantGroupId = $variantGroupId;
        return $this;
    }

    /**
     * Adds as variantAttributeName
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @return self
     * @param string $variantAttributeName
     */
    public function addToVariantAttributeNames($variantAttributeName)
    {
        $this->variantAttributeNames[] = $variantAttributeName;
        return $this;
    }

    /**
     * isset variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetVariantAttributeNames($index)
    {
        return isset($this->variantAttributeNames[$index]);
    }

    /**
     * unset variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetVariantAttributeNames($index)
    {
        unset($this->variantAttributeNames[$index]);
    }

    /**
     * Gets as variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @return string[]
     */
    public function getVariantAttributeNames()
    {
        return $this->variantAttributeNames;
    }

    /**
     * Sets a new variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param string $variantAttributeNames
     * @return self
     */
    public function setVariantAttributeNames(array $variantAttributeNames)
    {
        $this->variantAttributeNames = $variantAttributeNames;
        return $this;
    }

    /**
     * Gets as isPrimaryVariant
     *
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @return string
     */
    public function getIsPrimaryVariant()
    {
        return $this->isPrimaryVariant;
    }

    /**
     * Sets a new isPrimaryVariant
     *
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @param string $isPrimaryVariant
     * @return self
     */
    public function setIsPrimaryVariant($isPrimaryVariant)
    {
        $this->isPrimaryVariant = $isPrimaryVariant;
        return $this;
    }

    /**
     * Gets as isPrivateLabelOrUnbranded
     *
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @return string
     */
    public function getIsPrivateLabelOrUnbranded()
    {
        return $this->isPrivateLabelOrUnbranded;
    }

    /**
     * Sets a new isPrivateLabelOrUnbranded
     *
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @param string $isPrivateLabelOrUnbranded
     * @return self
     */
    public function setIsPrivateLabelOrUnbranded($isPrivateLabelOrUnbranded)
    {
        $this->isPrivateLabelOrUnbranded = $isPrivateLabelOrUnbranded;
        return $this;
    }

    /**
     * Gets as isProp65WarningRequired
     *
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @return string
     */
    public function getIsProp65WarningRequired()
    {
        return $this->isProp65WarningRequired;
    }

    /**
     * Sets a new isProp65WarningRequired
     *
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @param string $isProp65WarningRequired
     * @return self
     */
    public function setIsProp65WarningRequired($isProp65WarningRequired)
    {
        $this->isProp65WarningRequired = $isProp65WarningRequired;
        return $this;
    }

    /**
     * Gets as prop65WarningText
     *
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @return string
     */
    public function getProp65WarningText()
    {
        return $this->prop65WarningText;
    }

    /**
     * Sets a new prop65WarningText
     *
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @param string $prop65WarningText
     * @return self
     */
    public function setProp65WarningText($prop65WarningText)
    {
        $this->prop65WarningText = $prop65WarningText;
        return $this;
    }

    /**
     * Adds as smallPartsWarning
     *
     * To determine if any choking warnings are applicable, check current product packaging for choking warning message(s). Please indicate the warning number (0-6). 0 - No warning applicable; 1 - Choking hazard is a small ball; 2 - Choking hazard contains small ball; 3 - Choking hazard contains small parts; 4 - Choking hazard balloon; 5 - Choking hazard is a marble; 6 - Choking hazard contains a marble.
     *
     * @return self
     * @param int $smallPartsWarning
     */
    public function addToSmallPartsWarnings($smallPartsWarning)
    {
        $this->smallPartsWarnings[] = $smallPartsWarning;
        return $this;
    }

    /**
     * isset smallPartsWarnings
     *
     * To determine if any choking warnings are applicable, check current product packaging for choking warning message(s). Please indicate the warning number (0-6). 0 - No warning applicable; 1 - Choking hazard is a small ball; 2 - Choking hazard contains small ball; 3 - Choking hazard contains small parts; 4 - Choking hazard balloon; 5 - Choking hazard is a marble; 6 - Choking hazard contains a marble.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetSmallPartsWarnings($index)
    {
        return isset($this->smallPartsWarnings[$index]);
    }

    /**
     * unset smallPartsWarnings
     *
     * To determine if any choking warnings are applicable, check current product packaging for choking warning message(s). Please indicate the warning number (0-6). 0 - No warning applicable; 1 - Choking hazard is a small ball; 2 - Choking hazard contains small ball; 3 - Choking hazard contains small parts; 4 - Choking hazard balloon; 5 - Choking hazard is a marble; 6 - Choking hazard contains a marble.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetSmallPartsWarnings($index)
    {
        unset($this->smallPartsWarnings[$index]);
    }

    /**
     * Gets as smallPartsWarnings
     *
     * To determine if any choking warnings are applicable, check current product packaging for choking warning message(s). Please indicate the warning number (0-6). 0 - No warning applicable; 1 - Choking hazard is a small ball; 2 - Choking hazard contains small ball; 3 - Choking hazard contains small parts; 4 - Choking hazard balloon; 5 - Choking hazard is a marble; 6 - Choking hazard contains a marble.
     *
     * @return int[]
     */
    public function getSmallPartsWarnings()
    {
        return $this->smallPartsWarnings;
    }

    /**
     * Sets a new smallPartsWarnings
     *
     * To determine if any choking warnings are applicable, check current product packaging for choking warning message(s). Please indicate the warning number (0-6). 0 - No warning applicable; 1 - Choking hazard is a small ball; 2 - Choking hazard contains small ball; 3 - Choking hazard contains small parts; 4 - Choking hazard balloon; 5 - Choking hazard is a marble; 6 - Choking hazard contains a marble.
     *
     * @param int $smallPartsWarnings
     * @return self
     */
    public function setSmallPartsWarnings(array $smallPartsWarnings)
    {
        $this->smallPartsWarnings = $smallPartsWarnings;
        return $this;
    }

    /**
     * Gets as requiresTextileActLabeling
     *
     * Select "Y" if your item contains wool or is one of the following: clothing (except for hats and shoes), handkerchiefs, scarves, bedding (including sheets, covers, blankets, comforters, pillows, pillowcases, quilts, bedspreads and pads (but not outer coverings for mattresses or box springs)), curtains and casements, draperies, tablecloths, napkins, doilies, floor coverings (rugs, carpets and mats), towels, washcloths, dishcloths, ironing board covers and pads, umbrellas, parasols, bats or batting, flags with heading or that are bigger than 216 square inches, cushions, all fibers, yarns and fabrics (but not packaging ribbons), furniture slip covers and other furniture covers, afghans and throws, sleeping bags, antimacassars (doilies), hammocks, dresser and other furniture scarves. For further information on these requirements, refer to the labeling requirements of the Textile Act.
     *
     * @return string
     */
    public function getRequiresTextileActLabeling()
    {
        return $this->requiresTextileActLabeling;
    }

    /**
     * Sets a new requiresTextileActLabeling
     *
     * Select "Y" if your item contains wool or is one of the following: clothing (except for hats and shoes), handkerchiefs, scarves, bedding (including sheets, covers, blankets, comforters, pillows, pillowcases, quilts, bedspreads and pads (but not outer coverings for mattresses or box springs)), curtains and casements, draperies, tablecloths, napkins, doilies, floor coverings (rugs, carpets and mats), towels, washcloths, dishcloths, ironing board covers and pads, umbrellas, parasols, bats or batting, flags with heading or that are bigger than 216 square inches, cushions, all fibers, yarns and fabrics (but not packaging ribbons), furniture slip covers and other furniture covers, afghans and throws, sleeping bags, antimacassars (doilies), hammocks, dresser and other furniture scarves. For further information on these requirements, refer to the labeling requirements of the Textile Act.
     *
     * @param string $requiresTextileActLabeling
     * @return self
     */
    public function setRequiresTextileActLabeling($requiresTextileActLabeling)
    {
        $this->requiresTextileActLabeling = $requiresTextileActLabeling;
        return $this;
    }

    /**
     * Gets as countryOfOriginTextiles
     *
     * Use “Made in U.S.A. and Imported” to indicate manufacture in the U.S. from imported materials, or part processing in the U.S. and part in a foreign country. Use “Made in U.S.A. or Imported” to reflect that some units of an item originate from a domestic source and others from a foreign source. Use “Made in U.S.A.” only if all units were made completely in the U.S. using materials also made in the U.S. Use "Imported" if units are completely imported.
     *
     * @return string
     */
    public function getCountryOfOriginTextiles()
    {
        return $this->countryOfOriginTextiles;
    }

    /**
     * Sets a new countryOfOriginTextiles
     *
     * Use “Made in U.S.A. and Imported” to indicate manufacture in the U.S. from imported materials, or part processing in the U.S. and part in a foreign country. Use “Made in U.S.A. or Imported” to reflect that some units of an item originate from a domestic source and others from a foreign source. Use “Made in U.S.A.” only if all units were made completely in the U.S. using materials also made in the U.S. Use "Imported" if units are completely imported.
     *
     * @param string $countryOfOriginTextiles
     * @return self
     */
    public function setCountryOfOriginTextiles($countryOfOriginTextiles)
    {
        $this->countryOfOriginTextiles = $countryOfOriginTextiles;
        return $this;
    }

    /**
     * Gets as hasBatteries
     *
     * "Battery or battery containing product" is defined by Company to include any item of Merchandise that is a battery or any component of Merchandise, including reusable packaging intended to stay in use with the item, containing a battery of any chemistry/ type. Mark Y if this definition applies to your product.
     *
     * @return string
     */
    public function getHasBatteries()
    {
        return $this->hasBatteries;
    }

    /**
     * Sets a new hasBatteries
     *
     * "Battery or battery containing product" is defined by Company to include any item of Merchandise that is a battery or any component of Merchandise, including reusable packaging intended to stay in use with the item, containing a battery of any chemistry/ type. Mark Y if this definition applies to your product.
     *
     * @param string $hasBatteries
     * @return self
     */
    public function setHasBatteries($hasBatteries)
    {
        $this->hasBatteries = $hasBatteries;
        return $this;
    }

    /**
     * Gets as batteryTechnologyType
     *
     * Please select the Battery Technology Type from the list provided. NOTE: If battery type is lead acid, lead acid (nonspillable), lithium ion, or lithium metal, please ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @return string
     */
    public function getBatteryTechnologyType()
    {
        return $this->batteryTechnologyType;
    }

    /**
     * Sets a new batteryTechnologyType
     *
     * Please select the Battery Technology Type from the list provided. NOTE: If battery type is lead acid, lead acid (nonspillable), lithium ion, or lithium metal, please ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @param string $batteryTechnologyType
     * @return self
     */
    public function setBatteryTechnologyType($batteryTechnologyType)
    {
        $this->batteryTechnologyType = $batteryTechnologyType;
        return $this;
    }

    /**
     * Gets as hasWarranty
     *
     * Y indicates the item comes with a warranty. If an item has a warranty, then enter EITHER the warranty URL or the warranty text in the appropriate field.
     *
     * @return string
     */
    public function getHasWarranty()
    {
        return $this->hasWarranty;
    }

    /**
     * Sets a new hasWarranty
     *
     * Y indicates the item comes with a warranty. If an item has a warranty, then enter EITHER the warranty URL or the warranty text in the appropriate field.
     *
     * @param string $hasWarranty
     * @return self
     */
    public function setHasWarranty($hasWarranty)
    {
        $this->hasWarranty = $hasWarranty;
        return $this;
    }

    /**
     * Gets as warrantyURL
     *
     * If you indicated that your item has a warranty, provide either the Warranty URL or Warranty Text. The Warranty URL is the web location of the image, PDF, or link to the manufacturer's warranty page, showing the warranty and its terms, including the duration of the warranty. URLs must begin with http:// or https:// NOTE: Please remember to update the link and/or text of the warranty as the warranty changes. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @return string
     */
    public function getWarrantyURL()
    {
        return $this->warrantyURL;
    }

    /**
     * Sets a new warrantyURL
     *
     * If you indicated that your item has a warranty, provide either the Warranty URL or Warranty Text. The Warranty URL is the web location of the image, PDF, or link to the manufacturer's warranty page, showing the warranty and its terms, including the duration of the warranty. URLs must begin with http:// or https:// NOTE: Please remember to update the link and/or text of the warranty as the warranty changes. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @param string $warrantyURL
     * @return self
     */
    public function setWarrantyURL($warrantyURL)
    {
        $this->warrantyURL = $warrantyURL;
        return $this;
    }

    /**
     * Gets as warrantyText
     *
     * If you marked Y for "Has Warranty" provide the Warranty URL or Warranty Text (the full text of the warranty terms, including what is covered by the warranty and the duration of the warranty). NOTE: please remember to update the text of your warranty as your warranty changes.
     *
     * @return string
     */
    public function getWarrantyText()
    {
        return $this->warrantyText;
    }

    /**
     * Sets a new warrantyText
     *
     * If you marked Y for "Has Warranty" provide the Warranty URL or Warranty Text (the full text of the warranty terms, including what is covered by the warranty and the duration of the warranty). NOTE: please remember to update the text of your warranty as your warranty changes.
     *
     * @param string $warrantyText
     * @return self
     */
    public function setWarrantyText($warrantyText)
    {
        $this->warrantyText = $warrantyText;
        return $this;
    }

    /**
     * Gets as hasStateRestrictions
     *
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @return string
     */
    public function getHasStateRestrictions()
    {
        return $this->hasStateRestrictions;
    }

    /**
     * Sets a new hasStateRestrictions
     *
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @param string $hasStateRestrictions
     * @return self
     */
    public function setHasStateRestrictions($hasStateRestrictions)
    {
        $this->hasStateRestrictions = $hasStateRestrictions;
        return $this;
    }

    /**
     * Adds as stateRestriction
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param \WalmartDSV\StateRestrictionType $stateRestriction
     *@return self
     */
    public function addToStateRestrictions(\WalmartDSV\StateRestrictionType $stateRestriction)
    {
        $this->stateRestrictions[] = $stateRestriction;
        return $this;
    }

    /**
     * isset stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetStateRestrictions($index)
    {
        return isset($this->stateRestrictions[$index]);
    }

    /**
     * unset stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetStateRestrictions($index)
    {
        unset($this->stateRestrictions[$index]);
    }

    /**
     * Gets as stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @return \WalmartDSV\StateRestrictionType[]
     */
    public function getStateRestrictions()
    {
        return $this->stateRestrictions;
    }

    /**
     * Sets a new stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param \WalmartDSV\StateRestrictionType[] $stateRestrictions
     * @return self
     */
    public function setStateRestrictions(array $stateRestrictions)
    {
        $this->stateRestrictions = $stateRestrictions;
        return $this;
    }

    /**
     * Gets as hasFuelContainer
     *
     * Denotes any item with an empty container that may be filled with fluids, such as fuel, CO2, propane, etc.
     *
     * @return string
     */
    public function getHasFuelContainer()
    {
        return $this->hasFuelContainer;
    }

    /**
     * Sets a new hasFuelContainer
     *
     * Denotes any item with an empty container that may be filled with fluids, such as fuel, CO2, propane, etc.
     *
     * @param string $hasFuelContainer
     * @return self
     */
    public function setHasFuelContainer($hasFuelContainer)
    {
        $this->hasFuelContainer = $hasFuelContainer;
        return $this;
    }

    /**
     * Gets as isLightingFactsLabelRequired
     *
     * Does your item require a Lighting Facts label? A Lighting Facts label must appear on packaging for most general service "lamps" with medium screw bases. That includes most incandescent, compact fluorescent (CFL), and light emitting diode (LED) light bulbs. For more information on what items are covered, see 16 CFR § 305.2 and § 305.3.
     *
     * @return string
     */
    public function getIsLightingFactsLabelRequired()
    {
        return $this->isLightingFactsLabelRequired;
    }

    /**
     * Sets a new isLightingFactsLabelRequired
     *
     * Does your item require a Lighting Facts label? A Lighting Facts label must appear on packaging for most general service "lamps" with medium screw bases. That includes most incandescent, compact fluorescent (CFL), and light emitting diode (LED) light bulbs. For more information on what items are covered, see 16 CFR § 305.2 and § 305.3.
     *
     * @param string $isLightingFactsLabelRequired
     * @return self
     */
    public function setIsLightingFactsLabelRequired($isLightingFactsLabelRequired)
    {
        $this->isLightingFactsLabelRequired = $isLightingFactsLabelRequired;
        return $this;
    }

    /**
     * Gets as lightingFactsLabel
     *
     * URL of the location of the label. URLs must begin with http:// or https:// Label must include brightness specified lumens, estimated energy cost per year, life, light appearance, scale, energy used, and special handling information needed for disposal, as outlined by the FTC. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB.
     *
     * @return string
     */
    public function getLightingFactsLabel()
    {
        return $this->lightingFactsLabel;
    }

    /**
     * Sets a new lightingFactsLabel
     *
     * URL of the location of the label. URLs must begin with http:// or https:// Label must include brightness specified lumens, estimated energy cost per year, life, light appearance, scale, energy used, and special handling information needed for disposal, as outlined by the FTC. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB.
     *
     * @param string $lightingFactsLabel
     * @return self
     */
    public function setLightingFactsLabel($lightingFactsLabel)
    {
        $this->lightingFactsLabel = $lightingFactsLabel;
        return $this;
    }

    /**
     * Gets as isAssemblyRequired
     *
     * Is product unassembled and must be put together before use?
     *
     * @return string
     */
    public function getIsAssemblyRequired()
    {
        return $this->isAssemblyRequired;
    }

    /**
     * Sets a new isAssemblyRequired
     *
     * Is product unassembled and must be put together before use?
     *
     * @param string $isAssemblyRequired
     * @return self
     */
    public function setIsAssemblyRequired($isAssemblyRequired)
    {
        $this->isAssemblyRequired = $isAssemblyRequired;
        return $this;
    }

    /**
     * Gets as assemblyInstructions
     *
     * Provide a URL to an image or PDF asset showing assembly instructions for items requiring assembly. URLs must be static and have no query parameters. URLs must begin with http:// or https:// and should end in in the file name.
     *
     * @return string
     */
    public function getAssemblyInstructions()
    {
        return $this->assemblyInstructions;
    }

    /**
     * Sets a new assemblyInstructions
     *
     * Provide a URL to an image or PDF asset showing assembly instructions for items requiring assembly. URLs must be static and have no query parameters. URLs must begin with http:// or https:// and should end in in the file name.
     *
     * @param string $assemblyInstructions
     * @return self
     */
    public function setAssemblyInstructions($assemblyInstructions)
    {
        $this->assemblyInstructions = $assemblyInstructions;
        return $this;
    }

    /**
     * Gets as cleaningCareAndMaintenance
     *
     * Description of how the item should be cleaned and maintained.
     *
     * @return string
     */
    public function getCleaningCareAndMaintenance()
    {
        return $this->cleaningCareAndMaintenance;
    }

    /**
     * Sets a new cleaningCareAndMaintenance
     *
     * Description of how the item should be cleaned and maintained.
     *
     * @param string $cleaningCareAndMaintenance
     * @return self
     */
    public function setCleaningCareAndMaintenance($cleaningCareAndMaintenance)
    {
        $this->cleaningCareAndMaintenance = $cleaningCareAndMaintenance;
        return $this;
    }

    /**
     * Gets as contaminantsRemoved
     *
     * The particles, liquids, and gases in the air or water which have harmful chemical properties that are removed by the item.
     *
     * @return string
     */
    public function getContaminantsRemoved()
    {
        return $this->contaminantsRemoved;
    }

    /**
     * Sets a new contaminantsRemoved
     *
     * The particles, liquids, and gases in the air or water which have harmful chemical properties that are removed by the item.
     *
     * @param string $contaminantsRemoved
     * @return self
     */
    public function setContaminantsRemoved($contaminantsRemoved)
    {
        $this->contaminantsRemoved = $contaminantsRemoved;
        return $this;
    }

    /**
     * Adds as recommendedUse
     *
     * Further clarification of what the item may be used for. This improves searchability when customers search for general terms like "birthday party" that do not include the names of specific items.
     *
     * @return self
     * @param string $recommendedUse
     */
    public function addToRecommendedUses($recommendedUse)
    {
        $this->recommendedUses[] = $recommendedUse;
        return $this;
    }

    /**
     * isset recommendedUses
     *
     * Further clarification of what the item may be used for. This improves searchability when customers search for general terms like "birthday party" that do not include the names of specific items.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetRecommendedUses($index)
    {
        return isset($this->recommendedUses[$index]);
    }

    /**
     * unset recommendedUses
     *
     * Further clarification of what the item may be used for. This improves searchability when customers search for general terms like "birthday party" that do not include the names of specific items.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetRecommendedUses($index)
    {
        unset($this->recommendedUses[$index]);
    }

    /**
     * Gets as recommendedUses
     *
     * Further clarification of what the item may be used for. This improves searchability when customers search for general terms like "birthday party" that do not include the names of specific items.
     *
     * @return string[]
     */
    public function getRecommendedUses()
    {
        return $this->recommendedUses;
    }

    /**
     * Sets a new recommendedUses
     *
     * Further clarification of what the item may be used for. This improves searchability when customers search for general terms like "birthday party" that do not include the names of specific items.
     *
     * @param string $recommendedUses
     * @return self
     */
    public function setRecommendedUses(array $recommendedUses)
    {
        $this->recommendedUses = $recommendedUses;
        return $this;
    }

    /**
     * Adds as recommendedRoom
     *
     * The rooms where the item is likely or recommended to be used.
     *
     * @return self
     * @param string $recommendedRoom
     */
    public function addToRecommendedRooms($recommendedRoom)
    {
        $this->recommendedRooms[] = $recommendedRoom;
        return $this;
    }

    /**
     * isset recommendedRooms
     *
     * The rooms where the item is likely or recommended to be used.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetRecommendedRooms($index)
    {
        return isset($this->recommendedRooms[$index]);
    }

    /**
     * unset recommendedRooms
     *
     * The rooms where the item is likely or recommended to be used.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetRecommendedRooms($index)
    {
        unset($this->recommendedRooms[$index]);
    }

    /**
     * Gets as recommendedRooms
     *
     * The rooms where the item is likely or recommended to be used.
     *
     * @return string[]
     */
    public function getRecommendedRooms()
    {
        return $this->recommendedRooms;
    }

    /**
     * Sets a new recommendedRooms
     *
     * The rooms where the item is likely or recommended to be used.
     *
     * @param string $recommendedRooms
     * @return self
     */
    public function setRecommendedRooms(array $recommendedRooms)
    {
        $this->recommendedRooms = $recommendedRooms;
        return $this;
    }

    /**
     * Adds as fabricContentValue
     *
     * Material makeup of the item.
     *
     * @param \WalmartDSV\FabricContentValueType $fabricContentValue
     *@return self
     */
    public function addToFabricContent(\WalmartDSV\FabricContentValueType $fabricContentValue)
    {
        $this->fabricContent[] = $fabricContentValue;
        return $this;
    }

    /**
     * isset fabricContent
     *
     * Material makeup of the item.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFabricContent($index)
    {
        return isset($this->fabricContent[$index]);
    }

    /**
     * unset fabricContent
     *
     * Material makeup of the item.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFabricContent($index)
    {
        unset($this->fabricContent[$index]);
    }

    /**
     * Gets as fabricContent
     *
     * Material makeup of the item.
     *
     * @return \WalmartDSV\FabricContentValueType[]
     */
    public function getFabricContent()
    {
        return $this->fabricContent;
    }

    /**
     * Sets a new fabricContent
     *
     * Material makeup of the item.
     *
     * @param \WalmartDSV\FabricContentValueType[] $fabricContent
     * @return self
     */
    public function setFabricContent(array $fabricContent)
    {
        $this->fabricContent = $fabricContent;
        return $this;
    }

    /**
     * Gets as fabricColor
     *
     * Color of a fabric part of an item, to distinguish it from the general color of the item, if needed.
     *
     * @return string
     */
    public function getFabricColor()
    {
        return $this->fabricColor;
    }

    /**
     * Sets a new fabricColor
     *
     * Color of a fabric part of an item, to distinguish it from the general color of the item, if needed.
     *
     * @param string $fabricColor
     * @return self
     */
    public function setFabricColor($fabricColor)
    {
        $this->fabricColor = $fabricColor;
        return $this;
    }

    /**
     * Adds as fabricCareInstruction
     *
     * Describes how the fabric should be cleaned. Enter details of the fabric care label found on the item. (For garments, typically located inside on the top of the back or the lower left side.)
     *
     * @return self
     * @param string $fabricCareInstruction
     */
    public function addToFabricCareInstructions($fabricCareInstruction)
    {
        $this->fabricCareInstructions[] = $fabricCareInstruction;
        return $this;
    }

    /**
     * isset fabricCareInstructions
     *
     * Describes how the fabric should be cleaned. Enter details of the fabric care label found on the item. (For garments, typically located inside on the top of the back or the lower left side.)
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFabricCareInstructions($index)
    {
        return isset($this->fabricCareInstructions[$index]);
    }

    /**
     * unset fabricCareInstructions
     *
     * Describes how the fabric should be cleaned. Enter details of the fabric care label found on the item. (For garments, typically located inside on the top of the back or the lower left side.)
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFabricCareInstructions($index)
    {
        unset($this->fabricCareInstructions[$index]);
    }

    /**
     * Gets as fabricCareInstructions
     *
     * Describes how the fabric should be cleaned. Enter details of the fabric care label found on the item. (For garments, typically located inside on the top of the back or the lower left side.)
     *
     * @return string[]
     */
    public function getFabricCareInstructions()
    {
        return $this->fabricCareInstructions;
    }

    /**
     * Sets a new fabricCareInstructions
     *
     * Describes how the fabric should be cleaned. Enter details of the fabric care label found on the item. (For garments, typically located inside on the top of the back or the lower left side.)
     *
     * @param string $fabricCareInstructions
     * @return self
     */
    public function setFabricCareInstructions(array $fabricCareInstructions)
    {
        $this->fabricCareInstructions = $fabricCareInstructions;
        return $this;
    }

    /**
     * Gets as curtainLength
     *
     * Measurement of the length of the curtain as specified by the manufacturer. How length is measured varies with the type of curtain. Most are measured overall from the top edge of the curtain to the bottom edge (longest point) of the curtain. Curtains with large rod pockets are measured from the top of the rod pocket to the bottom edge of the curtain.
     *
     * @return \WalmartDSV\HomeOtherType\CurtainLengthAType
     */
    public function getCurtainLength()
    {
        return $this->curtainLength;
    }

    /**
     * Sets a new curtainLength
     *
     * Measurement of the length of the curtain as specified by the manufacturer. How length is measured varies with the type of curtain. Most are measured overall from the top edge of the curtain to the bottom edge (longest point) of the curtain. Curtains with large rod pockets are measured from the top of the rod pocket to the bottom edge of the curtain.
     *
     * @param \WalmartDSV\HomeOtherType\CurtainLengthAType $curtainLength
     * @return self
     */
    public function setCurtainLength(\WalmartDSV\HomeOtherType\CurtainLengthAType $curtainLength)
    {
        $this->curtainLength = $curtainLength;
        return $this;
    }

    /**
     * Gets as powerType
     *
     * Provides information on the exact type of power used by the item.
     *
     * @return string
     */
    public function getPowerType()
    {
        return $this->powerType;
    }

    /**
     * Sets a new powerType
     *
     * Provides information on the exact type of power used by the item.
     *
     * @param string $powerType
     * @return self
     */
    public function setPowerType($powerType)
    {
        $this->powerType = $powerType;
        return $this;
    }

    /**
     * Gets as volumeCapacity
     *
     * The volume of space available in this item to contain objects.
     *
     * @return \WalmartDSV\HomeOtherType\VolumeCapacityAType
     */
    public function getVolumeCapacity()
    {
        return $this->volumeCapacity;
    }

    /**
     * Sets a new volumeCapacity
     *
     * The volume of space available in this item to contain objects.
     *
     * @param \WalmartDSV\HomeOtherType\VolumeCapacityAType $volumeCapacity
     * @return self
     */
    public function setVolumeCapacity(\WalmartDSV\HomeOtherType\VolumeCapacityAType $volumeCapacity)
    {
        $this->volumeCapacity = $volumeCapacity;
        return $this;
    }

    /**
     * Gets as shape
     *
     * Physical shape of the item. Used in a wide variety of products including rugs, toys and large appliances.
     *
     * @return string
     */
    public function getShape()
    {
        return $this->shape;
    }

    /**
     * Sets a new shape
     *
     * Physical shape of the item. Used in a wide variety of products including rugs, toys and large appliances.
     *
     * @param string $shape
     * @return self
     */
    public function setShape($shape)
    {
        $this->shape = $shape;
        return $this;
    }

    /**
     * Gets as occasion
     *
     * The particular target time, event, or holiday for the product.
     *
     * @return \WalmartDSV\OccasionType
     */
    public function getOccasion()
    {
        return $this->occasion;
    }

    /**
     * Sets a new occasion
     *
     * The particular target time, event, or holiday for the product.
     *
     * @param \WalmartDSV\OccasionType $occasion
     * @return self
     */
    public function setOccasion(\WalmartDSV\OccasionType $occasion)
    {
        $this->occasion = $occasion;
        return $this;
    }

    /**
     * Gets as accentColor
     *
     * A secondary product color.
     *
     * @return string
     */
    public function getAccentColor()
    {
        return $this->accentColor;
    }

    /**
     * Sets a new accentColor
     *
     * A secondary product color.
     *
     * @param string $accentColor
     * @return self
     */
    public function setAccentColor($accentColor)
    {
        $this->accentColor = $accentColor;
        return $this;
    }

    /**
     * Gets as bedStyle
     *
     * Describes the bed's shape and appearance according to its design features.
     *
     * @return string
     */
    public function getBedStyle()
    {
        return $this->bedStyle;
    }

    /**
     * Sets a new bedStyle
     *
     * Describes the bed's shape and appearance according to its design features.
     *
     * @param string $bedStyle
     * @return self
     */
    public function setBedStyle($bedStyle)
    {
        $this->bedStyle = $bedStyle;
        return $this;
    }

    /**
     * Gets as hasPricePerUnit
     *
     * Mark as "Y" if your item is any of the following: food used for human or domestic animal consumption; ingredients added to food; napkins; tissues; toilet paper; foil, plastic wrap, wax paper, parchment paper; paper towels; disposable plates, bowls, and cutlery; detergents, soaps, waxes, and other cleansing agents; non-prescription drugs, female hygeine products, and toiletries; automotive fluids and cleaners; rock salt; diapers, pullups and swimmers; fertilizer; kitty litter.
     *
     * @return string
     */
    public function getHasPricePerUnit()
    {
        return $this->hasPricePerUnit;
    }

    /**
     * Sets a new hasPricePerUnit
     *
     * Mark as "Y" if your item is any of the following: food used for human or domestic animal consumption; ingredients added to food; napkins; tissues; toilet paper; foil, plastic wrap, wax paper, parchment paper; paper towels; disposable plates, bowls, and cutlery; detergents, soaps, waxes, and other cleansing agents; non-prescription drugs, female hygeine products, and toiletries; automotive fluids and cleaners; rock salt; diapers, pullups and swimmers; fertilizer; kitty litter.
     *
     * @param string $hasPricePerUnit
     * @return self
     */
    public function setHasPricePerUnit($hasPricePerUnit)
    {
        $this->hasPricePerUnit = $hasPricePerUnit;
        return $this;
    }

    /**
     * Gets as pricePerUnitQuantity
     *
     * Enter the quantity of units for the item, based on the "PPU Unit of Measure" you selected. For example, a gallon of milk should be 128. NOTE: Do not enter the price.
     *
     * @return float
     */
    public function getPricePerUnitQuantity()
    {
        return $this->pricePerUnitQuantity;
    }

    /**
     * Sets a new pricePerUnitQuantity
     *
     * Enter the quantity of units for the item, based on the "PPU Unit of Measure" you selected. For example, a gallon of milk should be 128. NOTE: Do not enter the price.
     *
     * @param float $pricePerUnitQuantity
     * @return self
     */
    public function setPricePerUnitQuantity($pricePerUnitQuantity)
    {
        $this->pricePerUnitQuantity = $pricePerUnitQuantity;
        return $this;
    }

    /**
     * Gets as pricePerUnitUom
     *
     * The units that will be used to calculate the "Price Per Unit" for your product. For example, a gallon of milk has a "PPU Unit of Measure" of Fluid Ounces. NOTE: This may not be the Unit of Measure on the label.
     *
     * @return string
     */
    public function getPricePerUnitUom()
    {
        return $this->pricePerUnitUom;
    }

    /**
     * Sets a new pricePerUnitUom
     *
     * The units that will be used to calculate the "Price Per Unit" for your product. For example, a gallon of milk has a "PPU Unit of Measure" of Fluid Ounces. NOTE: This may not be the Unit of Measure on the label.
     *
     * @param string $pricePerUnitUom
     * @return self
     */
    public function setPricePerUnitUom($pricePerUnitUom)
    {
        $this->pricePerUnitUom = $pricePerUnitUom;
        return $this;
    }

    /**
     * Gets as baseColor
     *
     * Color of the base portion of the item, if it needs to be distinguished from other components.
     *
     * @return string
     */
    public function getBaseColor()
    {
        return $this->baseColor;
    }

    /**
     * Sets a new baseColor
     *
     * Color of the base portion of the item, if it needs to be distinguished from other components.
     *
     * @param string $baseColor
     * @return self
     */
    public function setBaseColor($baseColor)
    {
        $this->baseColor = $baseColor;
        return $this;
    }

    /**
     * Gets as baseFinish
     *
     * The finish of a base component of an item, if it needs to be distinguished from another component.
     *
     * @return string
     */
    public function getBaseFinish()
    {
        return $this->baseFinish;
    }

    /**
     * Sets a new baseFinish
     *
     * The finish of a base component of an item, if it needs to be distinguished from another component.
     *
     * @param string $baseFinish
     * @return self
     */
    public function setBaseFinish($baseFinish)
    {
        $this->baseFinish = $baseFinish;
        return $this;
    }

    /**
     * Gets as shadeMaterial
     *
     * The material of a lampshade, to distinguish it from the rest of the lamp or other lighting product
     *
     * @return string
     */
    public function getShadeMaterial()
    {
        return $this->shadeMaterial;
    }

    /**
     * Sets a new shadeMaterial
     *
     * The material of a lampshade, to distinguish it from the rest of the lamp or other lighting product
     *
     * @param string $shadeMaterial
     * @return self
     */
    public function setShadeMaterial($shadeMaterial)
    {
        $this->shadeMaterial = $shadeMaterial;
        return $this;
    }

    /**
     * Gets as shadeStyle
     *
     * Descriptive term for the shape of a lamp or other lighting product. Many terms are industry standard.
     *
     * @return string
     */
    public function getShadeStyle()
    {
        return $this->shadeStyle;
    }

    /**
     * Sets a new shadeStyle
     *
     * Descriptive term for the shape of a lamp or other lighting product. Many terms are industry standard.
     *
     * @param string $shadeStyle
     * @return self
     */
    public function setShadeStyle($shadeStyle)
    {
        $this->shadeStyle = $shadeStyle;
        return $this;
    }

    /**
     * Gets as collection
     *
     * A collection is a particular group of items that have the same visual style, made by the same brand.
     *
     * @return string
     */
    public function getCollection()
    {
        return $this->collection;
    }

    /**
     * Sets a new collection
     *
     * A collection is a particular group of items that have the same visual style, made by the same brand.
     *
     * @param string $collection
     * @return self
     */
    public function setCollection($collection)
    {
        $this->collection = $collection;
        return $this;
    }

    /**
     * Gets as serviceCount
     *
     * The number of people that a set of dishes, glasses, flatware, etc. will serve.
     *
     * @return float
     */
    public function getServiceCount()
    {
        return $this->serviceCount;
    }

    /**
     * Sets a new serviceCount
     *
     * The number of people that a set of dishes, glasses, flatware, etc. will serve.
     *
     * @param float $serviceCount
     * @return self
     */
    public function setServiceCount($serviceCount)
    {
        $this->serviceCount = $serviceCount;
        return $this;
    }

    /**
     * Gets as frameColor
     *
     * The color of a frame component of a product, if it needs to be distinguished from other components.
     *
     * @return string
     */
    public function getFrameColor()
    {
        return $this->frameColor;
    }

    /**
     * Sets a new frameColor
     *
     * The color of a frame component of a product, if it needs to be distinguished from other components.
     *
     * @param string $frameColor
     * @return self
     */
    public function setFrameColor($frameColor)
    {
        $this->frameColor = $frameColor;
        return $this;
    }

    /**
     * Gets as slatWidth
     *
     * The width of a slat component of a product, such as widow blinds. This measurement is distinguished from the width of the product itself.
     *
     * @return \WalmartDSV\HomeOtherType\SlatWidthAType
     */
    public function getSlatWidth()
    {
        return $this->slatWidth;
    }

    /**
     * Sets a new slatWidth
     *
     * The width of a slat component of a product, such as widow blinds. This measurement is distinguished from the width of the product itself.
     *
     * @param \WalmartDSV\HomeOtherType\SlatWidthAType $slatWidth
     * @return self
     */
    public function setSlatWidth(\WalmartDSV\HomeOtherType\SlatWidthAType $slatWidth)
    {
        $this->slatWidth = $slatWidth;
        return $this;
    }

    /**
     * Gets as isSet
     *
     * Y indicates that the product consists of two or more different items sold together as a set. Example: A bedding set that includes sheets, pillow shams, and a comforter.
     *
     * @return string
     */
    public function getIsSet()
    {
        return $this->isSet;
    }

    /**
     * Sets a new isSet
     *
     * Y indicates that the product consists of two or more different items sold together as a set. Example: A bedding set that includes sheets, pillow shams, and a comforter.
     *
     * @param string $isSet
     * @return self
     */
    public function setIsSet($isSet)
    {
        $this->isSet = $isSet;
        return $this;
    }

    /**
     * Adds as feature
     *
     * List notable features of the item.
     *
     * @return self
     * @param string $feature
     */
    public function addToFeatures($feature)
    {
        $this->features[] = $feature;
        return $this;
    }

    /**
     * isset features
     *
     * List notable features of the item.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFeatures($index)
    {
        return isset($this->features[$index]);
    }

    /**
     * unset features
     *
     * List notable features of the item.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFeatures($index)
    {
        unset($this->features[$index]);
    }

    /**
     * Gets as features
     *
     * List notable features of the item.
     *
     * @return string[]
     */
    public function getFeatures()
    {
        return $this->features;
    }

    /**
     * Sets a new features
     *
     * List notable features of the item.
     *
     * @param string $features
     * @return self
     */
    public function setFeatures(array $features)
    {
        $this->features = $features;
        return $this;
    }

    /**
     * Gets as keywords
     *
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @return string
     */
    public function getKeywords()
    {
        return $this->keywords;
    }

    /**
     * Sets a new keywords
     *
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @param string $keywords
     * @return self
     */
    public function setKeywords($keywords)
    {
        $this->keywords = $keywords;
        return $this;
    }

    /**
     * Gets as academicInstitution
     *
     * Name of College or other school. This is to distinguish the school from its sports team.
     *
     * @return string
     */
    public function getAcademicInstitution()
    {
        return $this->academicInstitution;
    }

    /**
     * Sets a new academicInstitution
     *
     * Name of College or other school. This is to distinguish the school from its sports team.
     *
     * @param string $academicInstitution
     * @return self
     */
    public function setAcademicInstitution($academicInstitution)
    {
        $this->academicInstitution = $academicInstitution;
        return $this;
    }

    /**
     * Gets as sportsLeague
     *
     * If your item has any association with a specific sports league, enter the league name. Abbreviations are fine. NOTE: This attribute flags an item for inclusion in the online fan shop.
     *
     * @return \WalmartDSV\SportsLeagueType
     */
    public function getSportsLeague()
    {
        return $this->sportsLeague;
    }

    /**
     * Sets a new sportsLeague
     *
     * If your item has any association with a specific sports league, enter the league name. Abbreviations are fine. NOTE: This attribute flags an item for inclusion in the online fan shop.
     *
     * @param \WalmartDSV\SportsLeagueType $sportsLeague
     * @return self
     */
    public function setSportsLeague(\WalmartDSV\SportsLeagueType $sportsLeague)
    {
        $this->sportsLeague = $sportsLeague;
        return $this;
    }

    /**
     * Gets as sportsTeam
     *
     * If your item has any association with a specific sports team, enter the team name. NOTE: This attribute flags an item for inclusion in the online fan shop.
     *
     * @return \WalmartDSV\SportsTeamType
     */
    public function getSportsTeam()
    {
        return $this->sportsTeam;
    }

    /**
     * Sets a new sportsTeam
     *
     * If your item has any association with a specific sports team, enter the team name. NOTE: This attribute flags an item for inclusion in the online fan shop.
     *
     * @param \WalmartDSV\SportsTeamType $sportsTeam
     * @return self
     */
    public function setSportsTeam(\WalmartDSV\SportsTeamType $sportsTeam)
    {
        $this->sportsTeam = $sportsTeam;
        return $this;
    }

    /**
     * Gets as athlete
     *
     * A well-known athlete associated with a product, if applicable. This is used to group items in Fan Shop, not to describe a line of clothing.
     *
     * @return \WalmartDSV\AthleteType
     */
    public function getAthlete()
    {
        return $this->athlete;
    }

    /**
     * Sets a new athlete
     *
     * A well-known athlete associated with a product, if applicable. This is used to group items in Fan Shop, not to describe a line of clothing.
     *
     * @param \WalmartDSV\AthleteType $athlete
     * @return self
     */
    public function setAthlete(\WalmartDSV\AthleteType $athlete)
    {
        $this->athlete = $athlete;
        return $this;
    }

    /**
     * Gets as compositeWoodCertificationCode
     *
     * Composite Wood - Indicates if any portion of the item contains any of the following types of composite wood: hardwood plywood veneer core, hardwood plywood composite core, particleboard, or medium density fiber board (MDF). Enter the code corresponding to the highest formaldehyde emission level on any portion of the item. Code Definitions: 1 - Does not contain composite wood; 7 - Product is not CARB compliant and cannot be sold in California; 8 - Product is CARB compliant and can be sold in California.
     *
     * @return int
     */
    public function getCompositeWoodCertificationCode()
    {
        return $this->compositeWoodCertificationCode;
    }

    /**
     * Sets a new compositeWoodCertificationCode
     *
     * Composite Wood - Indicates if any portion of the item contains any of the following types of composite wood: hardwood plywood veneer core, hardwood plywood composite core, particleboard, or medium density fiber board (MDF). Enter the code corresponding to the highest formaldehyde emission level on any portion of the item. Code Definitions: 1 - Does not contain composite wood; 7 - Product is not CARB compliant and cannot be sold in California; 8 - Product is CARB compliant and can be sold in California.
     *
     * @param int $compositeWoodCertificationCode
     * @return self
     */
    public function setCompositeWoodCertificationCode($compositeWoodCertificationCode)
    {
        $this->compositeWoodCertificationCode = $compositeWoodCertificationCode;
        return $this;
    }

    /**
     * Adds as swatchImage
     *
     * @param \WalmartDSV\HomeOtherType\SwatchImagesAType\SwatchImageAType $swatchImage
     *@return self
     */
    public function addToSwatchImages(\WalmartDSV\HomeOtherType\SwatchImagesAType\SwatchImageAType $swatchImage)
    {
        $this->swatchImages[] = $swatchImage;
        return $this;
    }

    /**
     * isset swatchImages
     *
     * @param int|string $index
     * @return bool
     */
    public function issetSwatchImages($index)
    {
        return isset($this->swatchImages[$index]);
    }

    /**
     * unset swatchImages
     *
     * @param int|string $index
     * @return void
     */
    public function unsetSwatchImages($index)
    {
        unset($this->swatchImages[$index]);
    }

    /**
     * Gets as swatchImages
     *
     * @return \WalmartDSV\HomeOtherType\SwatchImagesAType\SwatchImageAType[]
     */
    public function getSwatchImages()
    {
        return $this->swatchImages;
    }

    /**
     * Sets a new swatchImages
     *
     * @param \WalmartDSV\HomeOtherType\SwatchImagesAType\SwatchImageAType[] $swatchImages
     * @return self
     */
    public function setSwatchImages(array $swatchImages)
    {
        $this->swatchImages = $swatchImages;
        return $this;
    }


}

