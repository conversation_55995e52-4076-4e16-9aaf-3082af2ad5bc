<?php

namespace WalmartDSV;

/**
 * Class representing AnimalType
 *
 *
 * XSD Type: Animal
 */
class AnimalType
{

    /**
     * @var \WalmartDSV\AnimalHealthAndGroomingType $animalHealthAndGrooming
     */
    private $animalHealthAndGrooming = null;

    /**
     * @var \WalmartDSV\AnimalAccessoriesType $animalAccessories
     */
    private $animalAccessories = null;

    /**
     * @var \WalmartDSV\AnimalFoodType $animalFood
     */
    private $animalFood = null;

    /**
     * @var \WalmartDSV\AnimalEverythingElseType $animalEverythingElse
     */
    private $animalEverythingElse = null;

    /**
     * Gets as animalHealthAndGrooming
     *
     * @return \WalmartDSV\AnimalHealthAndGroomingType
     */
    public function getAnimalHealthAndGrooming()
    {
        return $this->animalHealthAndGrooming;
    }

    /**
     * Sets a new animalHealthAndGrooming
     *
     * @param \WalmartDSV\AnimalHealthAndGroomingType $animalHealthAndGrooming
     * @return self
     */
    public function setAnimalHealthAndGrooming(\WalmartDSV\AnimalHealthAndGroomingType $animalHealthAndGrooming)
    {
        $this->animalHealthAndGrooming = $animalHealthAndGrooming;
        return $this;
    }

    /**
     * Gets as animalAccessories
     *
     * @return \WalmartDSV\AnimalAccessoriesType
     */
    public function getAnimalAccessories()
    {
        return $this->animalAccessories;
    }

    /**
     * Sets a new animalAccessories
     *
     * @param \WalmartDSV\AnimalAccessoriesType $animalAccessories
     * @return self
     */
    public function setAnimalAccessories(\WalmartDSV\AnimalAccessoriesType $animalAccessories)
    {
        $this->animalAccessories = $animalAccessories;
        return $this;
    }

    /**
     * Gets as animalFood
     *
     * @return \WalmartDSV\AnimalFoodType
     */
    public function getAnimalFood()
    {
        return $this->animalFood;
    }

    /**
     * Sets a new animalFood
     *
     * @param \WalmartDSV\AnimalFoodType $animalFood
     * @return self
     */
    public function setAnimalFood(\WalmartDSV\AnimalFoodType $animalFood)
    {
        $this->animalFood = $animalFood;
        return $this;
    }

    /**
     * Gets as animalEverythingElse
     *
     * @return \WalmartDSV\AnimalEverythingElseType
     */
    public function getAnimalEverythingElse()
    {
        return $this->animalEverythingElse;
    }

    /**
     * Sets a new animalEverythingElse
     *
     * @param \WalmartDSV\AnimalEverythingElseType $animalEverythingElse
     * @return self
     */
    public function setAnimalEverythingElse(\WalmartDSV\AnimalEverythingElseType $animalEverythingElse)
    {
        $this->animalEverythingElse = $animalEverythingElse;
        return $this;
    }


}

