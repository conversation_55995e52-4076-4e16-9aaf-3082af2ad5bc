<?php

namespace WalmartDSV;

/**
 * Class representing FoodAndBeverageCategoryType
 *
 *
 * XSD Type: FoodAndBeverageCategory
 */
class FoodAndBeverageCategoryType
{

    /**
     * @var \WalmartDSV\AlcoholicBeveragesType $alcoholicBeverages
     */
    private $alcoholicBeverages = null;

    /**
     * @var \WalmartDSV\FoodAndBeverageType $foodAndBeverage
     */
    private $foodAndBeverage = null;

    /**
     * Gets as alcoholicBeverages
     *
     * @return \WalmartDSV\AlcoholicBeveragesType
     */
    public function getAlcoholicBeverages()
    {
        return $this->alcoholicBeverages;
    }

    /**
     * Sets a new alcoholicBeverages
     *
     * @param \WalmartDSV\AlcoholicBeveragesType $alcoholicBeverages
     * @return self
     */
    public function setAlcoholicBeverages(\WalmartDSV\AlcoholicBeveragesType $alcoholicBeverages)
    {
        $this->alcoholicBeverages = $alcoholicBeverages;
        return $this;
    }

    /**
     * Gets as foodAndBeverage
     *
     * @return \WalmartDSV\FoodAndBeverageType
     */
    public function getFoodAndBeverage()
    {
        return $this->foodAndBeverage;
    }

    /**
     * Sets a new foodAndBeverage
     *
     * @param \WalmartDSV\FoodAndBeverageType $foodAndBeverage
     * @return self
     */
    public function setFoodAndBeverage(\WalmartDSV\FoodAndBeverageType $foodAndBeverage)
    {
        $this->foodAndBeverage = $foodAndBeverage;
        return $this;
    }


}

