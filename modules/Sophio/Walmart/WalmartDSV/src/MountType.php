<?php

namespace WalmartDSV;

/**
 * Class representing MountType
 *
 * How the item is attached. Used for products such as shelving and fixture hardware.
 * XSD Type: MountType
 */
class MountType
{

    /**
     * @var string $mountTypeValue
     */
    private $mountTypeValue = null;

    /**
     * Gets as mountTypeValue
     *
     * @return string
     */
    public function getMountTypeValue()
    {
        return $this->mountTypeValue;
    }

    /**
     * Sets a new mountTypeValue
     *
     * @param string $mountTypeValue
     * @return self
     */
    public function setMountTypeValue($mountTypeValue)
    {
        $this->mountTypeValue = $mountTypeValue;
        return $this;
    }


}

