<?php

namespace WalmartDSV;

/**
 * Class representing FoodPreparationTipsType
 *
 * Helpful information related to food preparation.
 * XSD Type: FoodPreparationTips
 */
class FoodPreparationTipsType
{

    /**
     * @var string[] $foodPreparationTipsValue
     */
    private $foodPreparationTipsValue = [
        
    ];

    /**
     * Adds as foodPreparationTipsValue
     *
     * @return self
     * @param string $foodPreparationTipsValue
     */
    public function addToFoodPreparationTipsValue($foodPreparationTipsValue)
    {
        $this->foodPreparationTipsValue[] = $foodPreparationTipsValue;
        return $this;
    }

    /**
     * isset foodPreparationTipsValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFoodPreparationTipsValue($index)
    {
        return isset($this->foodPreparationTipsValue[$index]);
    }

    /**
     * unset foodPreparationTipsValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFoodPreparationTipsValue($index)
    {
        unset($this->foodPreparationTipsValue[$index]);
    }

    /**
     * Gets as foodPreparationTipsValue
     *
     * @return string[]
     */
    public function getFoodPreparationTipsValue()
    {
        return $this->foodPreparationTipsValue;
    }

    /**
     * Sets a new foodPreparationTipsValue
     *
     * @param string $foodPreparationTipsValue
     * @return self
     */
    public function setFoodPreparationTipsValue(array $foodPreparationTipsValue)
    {
        $this->foodPreparationTipsValue = $foodPreparationTipsValue;
        return $this;
    }


}

