<?php

namespace WalmartDSV;

/**
 * Class representing LandVehiclesType
 *
 *
 * XSD Type: LandVehicles
 */
class LandVehiclesType
{

    /**
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @var string[] $additionalVariantAttributeNames
     */
    private $additionalVariantAttributeNames = null;

    /**
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @var string $shortDescription
     */
    private $shortDescription = null;

    /**
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @var string[] $keyFeatures
     */
    private $keyFeatures = null;

    /**
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @var string $brand
     */
    private $brand = null;

    /**
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @var string $manufacturer
     */
    private $manufacturer = null;

    /**
     * MPN uniquely identifies the product to its manufacturer. For many products this will be identical to the model number. Some manufacturers distinguish part number from model number. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @var string $manufacturerPartNumber
     */
    private $manufacturerPartNumber = null;

    /**
     * Model numbers allow manufacturers to keep track of each hardware device and identify or replace the proper part when needed. Model numbers are often found on the bottom, back, or side of a product. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @var string $modelNumber
     */
    private $modelNumber = null;

    /**
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @var string $mainImageUrl
     */
    private $mainImageUrl = null;

    /**
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @var string[] $productSecondaryImageURL
     */
    private $productSecondaryImageURL = null;

    /**
     * Color as described by the manufacturer.
     *
     * @var \WalmartDSV\ColorType $color
     */
    private $color = null;

    /**
     * Grouping of different kinds of vehicles based on use and form. Important selection criteria, especially for compatibility, for products including boat components, tires and auto accessories. Vehicle Type also used for toys such as model rocket ships and remote-controlled race cars.
     *
     * @var string $vehicleType
     */
    private $vehicleType = null;

    /**
     * The model year as provided by the manufacturer. If the product is a replacement part or accessory, this attribute is used to identify if a product fits a particular vehicle.
     *
     * @var int $vehicleYear
     */
    private $vehicleYear = null;

    /**
     * The manufacturer’s marque, under which the vehicle is produced and sold. For example, the Toyota Motor Corporation manufactures the vehicle makes of Toyota, Lexus and Scion. If the product is a replacement part or accessory, this attribute is used to identify if a product fits a particular vehicle.
     *
     * @var string $vehicleMake
     */
    private $vehicleMake = null;

    /**
     * The manufacturer’s name/letter/number designation given to a particular design or series of vehicles with similar characteristics. If the product is a replacement part or accessory, this attribute is used to identify if a product fits a particular vehicle.
     *
     * @var string $vehicleModel
     */
    private $vehicleModel = null;

    /**
     * Any secondary designation of model for the product
     *
     * @var string $submodel
     */
    private $submodel = null;

    /**
     * Description of the main components that generate power and deliver it to the method of propulsion (to the road or water surface).
     *
     * @var string $powertrain
     */
    private $powertrain = null;

    /**
     * Terms describing the type of system in a motor vehicle that connects the transmission to the drive axels.
     *
     * @var string $drivetrain
     */
    private $drivetrain = null;

    /**
     * Terms or standard abbreviations describing the type of vehicle transmission, as specified by the manufacturer. For example, 6MT 4WD would be 6-speed manual transmission, 4-wheel drive.
     *
     * @var string $transmissionDesignation
     */
    private $transmissionDesignation = null;

    /**
     * The manufacturer's name given to a particular design, product line, or series of engines with similar characteristics.
     *
     * @var string $engineModel
     */
    private $engineModel = null;

    /**
     * The combined volume, as displaced by the pistons for all cylinders of an internal combustion engine. Generally expressed in cubic centimeters up to 1000 cc, and liters thereafter, or in cubic inches. Engine displacement is often used as a rough indicator of an engine's power and potential fuel consumption.
     *
     * @var \WalmartDSV\LandVehiclesType\EngineDisplacementAType $engineDisplacement
     */
    private $engineDisplacement = null;

    /**
     * The ratio between the diameter of the cylinder and the stroke of the piston.
     *
     * @var string $boreStroke
     */
    private $boreStroke = null;

    /**
     * Method and mechanism used to meter and direct the flow of air into an engine.
     *
     * @var string $inductionSystem
     */
    private $inductionSystem = null;

    /**
     * A value representing the ratio of the volume of the combustion chamber from its largest capacity to its smallest capacity. Important specification for many common combustion engines. A gasoline- powered engine typically has a compression ratio of 10:1.
     *
     * @var string $compressionRatio
     */
    private $compressionRatio = null;

    /**
     * The maximum power output of the engine as specified by the manufacturer.
     *
     * @var \WalmartDSV\PowerUnitType $maximumEnginePower
     */
    private $maximumEnginePower = null;

    /**
     * Measurement of turning or twisting force as measured in foot pounds (ft-lbs). Significance varies with product. For example, for torque wrenches, torque value helps consumers select the correct torque wrench for tightening a specific nut or bolt. If item has an engine, torque is a general indicator of an engine's pulling power.
     *
     * @var float $torque
     */
    private $torque = null;

    /**
     * The rate of change in velocity as a function of time it takes for the product to reach a specific velocity. For example, a vehicles’ acceleration is typically calculated when the car is not in motion (0 mph), until the amount of time it takes to reach a velocity of 60 miles per hour.
     *
     * @var string $acceleration
     */
    private $acceleration = null;

    /**
     * The maximum speed the product can attain
     *
     * @var \WalmartDSV\SpeedUnitType $topSpeed
     */
    private $topSpeed = null;

    /**
     * Method by which the engine is cooled
     *
     * @var string $coolingSystem
     */
    private $coolingSystem = null;

    /**
     * The type of fuel the product requires, as specified by the manufacturer. For example most land vehicles and watercraft require gasoline with a specific octane rating.
     *
     * @var string $fuelRequirement
     */
    private $fuelRequirement = null;

    /**
     * Method and mechanisms used to deliver the flow of fuel into an engine. Independent of fuel type. For example, engines fueled by gasoline can have either a fuel injection or carbonated fuel system.
     *
     * @var string $fuelSystem
     */
    private $fuelSystem = null;

    /**
     * Volume of fuel the item can hold.
     *
     * @var \WalmartDSV\LandVehiclesType\FuelCapacityAType $fuelCapacity
     */
    private $fuelCapacity = null;

    /**
     * Amount of fuel used to cover a specific distance. For example, if a moped or boat motor uses an average of 12 gallons per mile, the value would be 12mpg
     *
     * @var \WalmartDSV\LandVehiclesType\AverageFuelConsumptionAType $averageFuelConsumption
     */
    private $averageFuelConsumption = null;

    /**
     * Terms describing type of suspension system including springs, shock absorbers and linkages that connects a vehicle to its front wheels.
     *
     * @var string $frontSuspension
     */
    private $frontSuspension = null;

    /**
     * Terms describing type of suspension system including springs, shock absorbers and linkages that connects a vehicle to its rear wheels.
     *
     * @var string $rearSuspension
     */
    private $rearSuspension = null;

    /**
     * Terms describing the type of braking system including disks, master cylinder connection, and caliper mountings on the front wheels of the product.
     *
     * @var string $frontBrakes
     */
    private $frontBrakes = null;

    /**
     * Terms describing the type of braking system including disks/drums, master cylinder connection, and caliper mountings on the rear wheels of the product.
     *
     * @var string $rearBrakes
     */
    private $rearBrakes = null;

    /**
     * Dimensions and terms describing the wheels on the front of the product.
     *
     * @var string $frontWheels
     */
    private $frontWheels = null;

    /**
     * Dimensions and terms describing the rear wheels of the product
     *
     * @var string $rearWheels
     */
    private $rearWheels = null;

    /**
     * Dimensions of the tires on the front wheels of the product.
     *
     * @var string $frontTires
     */
    private $frontTires = null;

    /**
     * The dimensions of the tires on the rear wheels of the product.
     *
     * @var string $rearTires
     */
    private $rearTires = null;

    /**
     * The number of people that can be accommodated by the available seats of an item.
     *
     * @var int $seatingCapacity
     */
    private $seatingCapacity = null;

    /**
     * The height from the floor to the top of the seat, in inches.
     *
     * @var \WalmartDSV\LandVehiclesType\SeatHeightAType $seatHeight
     */
    private $seatHeight = null;

    /**
     * The distance between the front and rear axles of a vehicle. Important component of vehicle stability and maneuverability.
     *
     * @var \WalmartDSV\LandVehiclesType\WheelbaseAType $wheelbase
     */
    private $wheelbase = null;

    /**
     * Total weight of a vehicle with standard equipment and all necessary support products (motor oil, fuel, coolant) but without either passengers or cargo.
     *
     * @var \WalmartDSV\LandVehiclesType\CurbWeightAType $curbWeight
     */
    private $curbWeight = null;

    /**
     * Maximum weight that can be pulled by the vehicle. This tow rating is specified by the manufacturer.
     *
     * @var \WalmartDSV\LandVehiclesType\TowingCapacityAType $towingCapacity
     */
    private $towingCapacity = null;

    /**
     * The length of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @var \WalmartDSV\LandVehiclesType\AssembledProductLengthAType $assembledProductLength
     */
    private $assembledProductLength = null;

    /**
     * The width of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @var \WalmartDSV\LandVehiclesType\AssembledProductWidthAType $assembledProductWidth
     */
    private $assembledProductWidth = null;

    /**
     * The height of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @var \WalmartDSV\LandVehiclesType\AssembledProductHeightAType $assembledProductHeight
     */
    private $assembledProductHeight = null;

    /**
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @var string $variantGroupId
     */
    private $variantGroupId = null;

    /**
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @var string[] $variantAttributeNames
     */
    private $variantAttributeNames = null;

    /**
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @var string $isPrimaryVariant
     */
    private $isPrimaryVariant = null;

    /**
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @var string $isPrivateLabelOrUnbranded
     */
    private $isPrivateLabelOrUnbranded = null;

    /**
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @var string $isProp65WarningRequired
     */
    private $isProp65WarningRequired = null;

    /**
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @var string $prop65WarningText
     */
    private $prop65WarningText = null;

    /**
     * "Battery or battery containing product" is defined by Company to include any item of Merchandise that is a battery or any component of Merchandise, including reusable packaging intended to stay in use with the item, containing a battery of any chemistry/ type. Mark Y if this definition applies to your product.
     *
     * @var string $hasBatteries
     */
    private $hasBatteries = null;

    /**
     * Please select the Battery Technology Type from the list provided. NOTE: If battery type is lead acid, lead acid (nonspillable), lithium ion, or lithium metal, please ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @var string $batteryTechnologyType
     */
    private $batteryTechnologyType = null;

    /**
     * Y indicates the item comes with a warranty. If an item has a warranty, then enter EITHER the warranty URL or the warranty text in the appropriate field.
     *
     * @var string $hasWarranty
     */
    private $hasWarranty = null;

    /**
     * If you indicated that your item has a warranty, provide either the Warranty URL or Warranty Text. The Warranty URL is the web location of the image, PDF, or link to the manufacturer's warranty page, showing the warranty and its terms, including the duration of the warranty. URLs must begin with http:// or https:// NOTE: Please remember to update the link and/or text of the warranty as the warranty changes. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @var string $warrantyURL
     */
    private $warrantyURL = null;

    /**
     * If you marked Y for "Has Warranty" provide the Warranty URL or Warranty Text (the full text of the warranty terms, including what is covered by the warranty and the duration of the warranty). NOTE: please remember to update the text of your warranty as your warranty changes.
     *
     * @var string $warrantyText
     */
    private $warrantyText = null;

    /**
     * Denotes any item with an empty container that may be filled with fluids, such as fuel, CO2, propane, etc.
     *
     * @var string $hasFuelContainer
     */
    private $hasFuelContainer = null;

    /**
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @var string $hasStateRestrictions
     */
    private $hasStateRestrictions = null;

    /**
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @var \WalmartDSV\StateRestrictionType[] $stateRestrictions
     */
    private $stateRestrictions = null;

    /**
     * Composite Wood - Indicates if any portion of the item contains any of the following types of composite wood: hardwood plywood veneer core, hardwood plywood composite core, particleboard, or medium density fiber board (MDF). Enter the code corresponding to the highest formaldehyde emission level on any portion of the item. Code Definitions: 1 - Does not contain composite wood; 7 - Product is not CARB compliant and cannot be sold in California; 8 - Product is CARB compliant and can be sold in California.
     *
     * @var int $compositeWoodCertificationCode
     */
    private $compositeWoodCertificationCode = null;

    /**
     * Select "Y" if your item contains wool or is one of the following: clothing (except for hats and shoes), handkerchiefs, scarves, bedding (including sheets, covers, blankets, comforters, pillows, pillowcases, quilts, bedspreads and pads (but not outer coverings for mattresses or box springs)), curtains and casements, draperies, tablecloths, napkins, doilies, floor coverings (rugs, carpets and mats), towels, washcloths, dishcloths, ironing board covers and pads, umbrellas, parasols, bats or batting, flags with heading or that are bigger than 216 square inches, cushions, all fibers, yarns and fabrics (but not packaging ribbons), furniture slip covers and other furniture covers, afghans and throws, sleeping bags, antimacassars (doilies), hammocks, dresser and other furniture scarves. For further information on these requirements, refer to the labeling requirements of the Textile Act.
     *
     * @var string $requiresTextileActLabeling
     */
    private $requiresTextileActLabeling = null;

    /**
     * Use “Made in U.S.A. and Imported” to indicate manufacture in the U.S. from imported materials, or part processing in the U.S. and part in a foreign country. Use “Made in U.S.A. or Imported” to reflect that some units of an item originate from a domestic source and others from a foreign source. Use “Made in U.S.A.” only if all units were made completely in the U.S. using materials also made in the U.S. Use "Imported" if units are completely imported.
     *
     * @var string $countryOfOriginTextiles
     */
    private $countryOfOriginTextiles = null;

    /**
     * Material makeup of the item.
     *
     * @var \WalmartDSV\FabricContentValueType[] $fabricContent
     */
    private $fabricContent = null;

    /**
     * List notable features of the item.
     *
     * @var string[] $features
     */
    private $features = null;

    /**
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @var string $keywords
     */
    private $keywords = null;

    /**
     * @var \WalmartDSV\LandVehiclesType\SwatchImagesAType\SwatchImageAType[] $swatchImages
     */
    private $swatchImages = null;

    /**
     * Adds as additionalVariantAttributeName
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @return self
     * @param string $additionalVariantAttributeName
     */
    public function addToAdditionalVariantAttributeNames($additionalVariantAttributeName)
    {
        $this->additionalVariantAttributeNames[] = $additionalVariantAttributeName;
        return $this;
    }

    /**
     * isset additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAdditionalVariantAttributeNames($index)
    {
        return isset($this->additionalVariantAttributeNames[$index]);
    }

    /**
     * unset additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAdditionalVariantAttributeNames($index)
    {
        unset($this->additionalVariantAttributeNames[$index]);
    }

    /**
     * Gets as additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @return string[]
     */
    public function getAdditionalVariantAttributeNames()
    {
        return $this->additionalVariantAttributeNames;
    }

    /**
     * Sets a new additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param string $additionalVariantAttributeNames
     * @return self
     */
    public function setAdditionalVariantAttributeNames(array $additionalVariantAttributeNames)
    {
        $this->additionalVariantAttributeNames = $additionalVariantAttributeNames;
        return $this;
    }

    /**
     * Gets as shortDescription
     *
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @return string
     */
    public function getShortDescription()
    {
        return $this->shortDescription;
    }

    /**
     * Sets a new shortDescription
     *
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @param string $shortDescription
     * @return self
     */
    public function setShortDescription($shortDescription)
    {
        $this->shortDescription = $shortDescription;
        return $this;
    }

    /**
     * Adds as keyFeaturesValue
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @return self
     * @param string $keyFeaturesValue
     */
    public function addToKeyFeatures($keyFeaturesValue)
    {
        $this->keyFeatures[] = $keyFeaturesValue;
        return $this;
    }

    /**
     * isset keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetKeyFeatures($index)
    {
        return isset($this->keyFeatures[$index]);
    }

    /**
     * unset keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetKeyFeatures($index)
    {
        unset($this->keyFeatures[$index]);
    }

    /**
     * Gets as keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @return string[]
     */
    public function getKeyFeatures()
    {
        return $this->keyFeatures;
    }

    /**
     * Sets a new keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param string $keyFeatures
     * @return self
     */
    public function setKeyFeatures(array $keyFeatures)
    {
        $this->keyFeatures = $keyFeatures;
        return $this;
    }

    /**
     * Gets as brand
     *
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @return string
     */
    public function getBrand()
    {
        return $this->brand;
    }

    /**
     * Sets a new brand
     *
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @param string $brand
     * @return self
     */
    public function setBrand($brand)
    {
        $this->brand = $brand;
        return $this;
    }

    /**
     * Gets as manufacturer
     *
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @return string
     */
    public function getManufacturer()
    {
        return $this->manufacturer;
    }

    /**
     * Sets a new manufacturer
     *
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @param string $manufacturer
     * @return self
     */
    public function setManufacturer($manufacturer)
    {
        $this->manufacturer = $manufacturer;
        return $this;
    }

    /**
     * Gets as manufacturerPartNumber
     *
     * MPN uniquely identifies the product to its manufacturer. For many products this will be identical to the model number. Some manufacturers distinguish part number from model number. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @return string
     */
    public function getManufacturerPartNumber()
    {
        return $this->manufacturerPartNumber;
    }

    /**
     * Sets a new manufacturerPartNumber
     *
     * MPN uniquely identifies the product to its manufacturer. For many products this will be identical to the model number. Some manufacturers distinguish part number from model number. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @param string $manufacturerPartNumber
     * @return self
     */
    public function setManufacturerPartNumber($manufacturerPartNumber)
    {
        $this->manufacturerPartNumber = $manufacturerPartNumber;
        return $this;
    }

    /**
     * Gets as modelNumber
     *
     * Model numbers allow manufacturers to keep track of each hardware device and identify or replace the proper part when needed. Model numbers are often found on the bottom, back, or side of a product. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @return string
     */
    public function getModelNumber()
    {
        return $this->modelNumber;
    }

    /**
     * Sets a new modelNumber
     *
     * Model numbers allow manufacturers to keep track of each hardware device and identify or replace the proper part when needed. Model numbers are often found on the bottom, back, or side of a product. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @param string $modelNumber
     * @return self
     */
    public function setModelNumber($modelNumber)
    {
        $this->modelNumber = $modelNumber;
        return $this;
    }

    /**
     * Gets as mainImageUrl
     *
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return string
     */
    public function getMainImageUrl()
    {
        return $this->mainImageUrl;
    }

    /**
     * Sets a new mainImageUrl
     *
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param string $mainImageUrl
     * @return self
     */
    public function setMainImageUrl($mainImageUrl)
    {
        $this->mainImageUrl = $mainImageUrl;
        return $this;
    }

    /**
     * Adds as productSecondaryImageURLValue
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return self
     * @param string $productSecondaryImageURLValue
     */
    public function addToProductSecondaryImageURL($productSecondaryImageURLValue)
    {
        $this->productSecondaryImageURL[] = $productSecondaryImageURLValue;
        return $this;
    }

    /**
     * isset productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetProductSecondaryImageURL($index)
    {
        return isset($this->productSecondaryImageURL[$index]);
    }

    /**
     * unset productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetProductSecondaryImageURL($index)
    {
        unset($this->productSecondaryImageURL[$index]);
    }

    /**
     * Gets as productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return string[]
     */
    public function getProductSecondaryImageURL()
    {
        return $this->productSecondaryImageURL;
    }

    /**
     * Sets a new productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param string $productSecondaryImageURL
     * @return self
     */
    public function setProductSecondaryImageURL(array $productSecondaryImageURL)
    {
        $this->productSecondaryImageURL = $productSecondaryImageURL;
        return $this;
    }

    /**
     * Gets as color
     *
     * Color as described by the manufacturer.
     *
     * @return \WalmartDSV\ColorType
     */
    public function getColor()
    {
        return $this->color;
    }

    /**
     * Sets a new color
     *
     * Color as described by the manufacturer.
     *
     * @param \WalmartDSV\ColorType $color
     * @return self
     */
    public function setColor(\WalmartDSV\ColorType $color)
    {
        $this->color = $color;
        return $this;
    }

    /**
     * Gets as vehicleType
     *
     * Grouping of different kinds of vehicles based on use and form. Important selection criteria, especially for compatibility, for products including boat components, tires and auto accessories. Vehicle Type also used for toys such as model rocket ships and remote-controlled race cars.
     *
     * @return string
     */
    public function getVehicleType()
    {
        return $this->vehicleType;
    }

    /**
     * Sets a new vehicleType
     *
     * Grouping of different kinds of vehicles based on use and form. Important selection criteria, especially for compatibility, for products including boat components, tires and auto accessories. Vehicle Type also used for toys such as model rocket ships and remote-controlled race cars.
     *
     * @param string $vehicleType
     * @return self
     */
    public function setVehicleType($vehicleType)
    {
        $this->vehicleType = $vehicleType;
        return $this;
    }

    /**
     * Gets as vehicleYear
     *
     * The model year as provided by the manufacturer. If the product is a replacement part or accessory, this attribute is used to identify if a product fits a particular vehicle.
     *
     * @return int
     */
    public function getVehicleYear()
    {
        return $this->vehicleYear;
    }

    /**
     * Sets a new vehicleYear
     *
     * The model year as provided by the manufacturer. If the product is a replacement part or accessory, this attribute is used to identify if a product fits a particular vehicle.
     *
     * @param int $vehicleYear
     * @return self
     */
    public function setVehicleYear($vehicleYear)
    {
        $this->vehicleYear = $vehicleYear;
        return $this;
    }

    /**
     * Gets as vehicleMake
     *
     * The manufacturer’s marque, under which the vehicle is produced and sold. For example, the Toyota Motor Corporation manufactures the vehicle makes of Toyota, Lexus and Scion. If the product is a replacement part or accessory, this attribute is used to identify if a product fits a particular vehicle.
     *
     * @return string
     */
    public function getVehicleMake()
    {
        return $this->vehicleMake;
    }

    /**
     * Sets a new vehicleMake
     *
     * The manufacturer’s marque, under which the vehicle is produced and sold. For example, the Toyota Motor Corporation manufactures the vehicle makes of Toyota, Lexus and Scion. If the product is a replacement part or accessory, this attribute is used to identify if a product fits a particular vehicle.
     *
     * @param string $vehicleMake
     * @return self
     */
    public function setVehicleMake($vehicleMake)
    {
        $this->vehicleMake = $vehicleMake;
        return $this;
    }

    /**
     * Gets as vehicleModel
     *
     * The manufacturer’s name/letter/number designation given to a particular design or series of vehicles with similar characteristics. If the product is a replacement part or accessory, this attribute is used to identify if a product fits a particular vehicle.
     *
     * @return string
     */
    public function getVehicleModel()
    {
        return $this->vehicleModel;
    }

    /**
     * Sets a new vehicleModel
     *
     * The manufacturer’s name/letter/number designation given to a particular design or series of vehicles with similar characteristics. If the product is a replacement part or accessory, this attribute is used to identify if a product fits a particular vehicle.
     *
     * @param string $vehicleModel
     * @return self
     */
    public function setVehicleModel($vehicleModel)
    {
        $this->vehicleModel = $vehicleModel;
        return $this;
    }

    /**
     * Gets as submodel
     *
     * Any secondary designation of model for the product
     *
     * @return string
     */
    public function getSubmodel()
    {
        return $this->submodel;
    }

    /**
     * Sets a new submodel
     *
     * Any secondary designation of model for the product
     *
     * @param string $submodel
     * @return self
     */
    public function setSubmodel($submodel)
    {
        $this->submodel = $submodel;
        return $this;
    }

    /**
     * Gets as powertrain
     *
     * Description of the main components that generate power and deliver it to the method of propulsion (to the road or water surface).
     *
     * @return string
     */
    public function getPowertrain()
    {
        return $this->powertrain;
    }

    /**
     * Sets a new powertrain
     *
     * Description of the main components that generate power and deliver it to the method of propulsion (to the road or water surface).
     *
     * @param string $powertrain
     * @return self
     */
    public function setPowertrain($powertrain)
    {
        $this->powertrain = $powertrain;
        return $this;
    }

    /**
     * Gets as drivetrain
     *
     * Terms describing the type of system in a motor vehicle that connects the transmission to the drive axels.
     *
     * @return string
     */
    public function getDrivetrain()
    {
        return $this->drivetrain;
    }

    /**
     * Sets a new drivetrain
     *
     * Terms describing the type of system in a motor vehicle that connects the transmission to the drive axels.
     *
     * @param string $drivetrain
     * @return self
     */
    public function setDrivetrain($drivetrain)
    {
        $this->drivetrain = $drivetrain;
        return $this;
    }

    /**
     * Gets as transmissionDesignation
     *
     * Terms or standard abbreviations describing the type of vehicle transmission, as specified by the manufacturer. For example, 6MT 4WD would be 6-speed manual transmission, 4-wheel drive.
     *
     * @return string
     */
    public function getTransmissionDesignation()
    {
        return $this->transmissionDesignation;
    }

    /**
     * Sets a new transmissionDesignation
     *
     * Terms or standard abbreviations describing the type of vehicle transmission, as specified by the manufacturer. For example, 6MT 4WD would be 6-speed manual transmission, 4-wheel drive.
     *
     * @param string $transmissionDesignation
     * @return self
     */
    public function setTransmissionDesignation($transmissionDesignation)
    {
        $this->transmissionDesignation = $transmissionDesignation;
        return $this;
    }

    /**
     * Gets as engineModel
     *
     * The manufacturer's name given to a particular design, product line, or series of engines with similar characteristics.
     *
     * @return string
     */
    public function getEngineModel()
    {
        return $this->engineModel;
    }

    /**
     * Sets a new engineModel
     *
     * The manufacturer's name given to a particular design, product line, or series of engines with similar characteristics.
     *
     * @param string $engineModel
     * @return self
     */
    public function setEngineModel($engineModel)
    {
        $this->engineModel = $engineModel;
        return $this;
    }

    /**
     * Gets as engineDisplacement
     *
     * The combined volume, as displaced by the pistons for all cylinders of an internal combustion engine. Generally expressed in cubic centimeters up to 1000 cc, and liters thereafter, or in cubic inches. Engine displacement is often used as a rough indicator of an engine's power and potential fuel consumption.
     *
     * @return \WalmartDSV\LandVehiclesType\EngineDisplacementAType
     */
    public function getEngineDisplacement()
    {
        return $this->engineDisplacement;
    }

    /**
     * Sets a new engineDisplacement
     *
     * The combined volume, as displaced by the pistons for all cylinders of an internal combustion engine. Generally expressed in cubic centimeters up to 1000 cc, and liters thereafter, or in cubic inches. Engine displacement is often used as a rough indicator of an engine's power and potential fuel consumption.
     *
     * @param \WalmartDSV\LandVehiclesType\EngineDisplacementAType $engineDisplacement
     * @return self
     */
    public function setEngineDisplacement(\WalmartDSV\LandVehiclesType\EngineDisplacementAType $engineDisplacement)
    {
        $this->engineDisplacement = $engineDisplacement;
        return $this;
    }

    /**
     * Gets as boreStroke
     *
     * The ratio between the diameter of the cylinder and the stroke of the piston.
     *
     * @return string
     */
    public function getBoreStroke()
    {
        return $this->boreStroke;
    }

    /**
     * Sets a new boreStroke
     *
     * The ratio between the diameter of the cylinder and the stroke of the piston.
     *
     * @param string $boreStroke
     * @return self
     */
    public function setBoreStroke($boreStroke)
    {
        $this->boreStroke = $boreStroke;
        return $this;
    }

    /**
     * Gets as inductionSystem
     *
     * Method and mechanism used to meter and direct the flow of air into an engine.
     *
     * @return string
     */
    public function getInductionSystem()
    {
        return $this->inductionSystem;
    }

    /**
     * Sets a new inductionSystem
     *
     * Method and mechanism used to meter and direct the flow of air into an engine.
     *
     * @param string $inductionSystem
     * @return self
     */
    public function setInductionSystem($inductionSystem)
    {
        $this->inductionSystem = $inductionSystem;
        return $this;
    }

    /**
     * Gets as compressionRatio
     *
     * A value representing the ratio of the volume of the combustion chamber from its largest capacity to its smallest capacity. Important specification for many common combustion engines. A gasoline- powered engine typically has a compression ratio of 10:1.
     *
     * @return string
     */
    public function getCompressionRatio()
    {
        return $this->compressionRatio;
    }

    /**
     * Sets a new compressionRatio
     *
     * A value representing the ratio of the volume of the combustion chamber from its largest capacity to its smallest capacity. Important specification for many common combustion engines. A gasoline- powered engine typically has a compression ratio of 10:1.
     *
     * @param string $compressionRatio
     * @return self
     */
    public function setCompressionRatio($compressionRatio)
    {
        $this->compressionRatio = $compressionRatio;
        return $this;
    }

    /**
     * Gets as maximumEnginePower
     *
     * The maximum power output of the engine as specified by the manufacturer.
     *
     * @return \WalmartDSV\PowerUnitType
     */
    public function getMaximumEnginePower()
    {
        return $this->maximumEnginePower;
    }

    /**
     * Sets a new maximumEnginePower
     *
     * The maximum power output of the engine as specified by the manufacturer.
     *
     * @param \WalmartDSV\PowerUnitType $maximumEnginePower
     * @return self
     */
    public function setMaximumEnginePower(\WalmartDSV\PowerUnitType $maximumEnginePower)
    {
        $this->maximumEnginePower = $maximumEnginePower;
        return $this;
    }

    /**
     * Gets as torque
     *
     * Measurement of turning or twisting force as measured in foot pounds (ft-lbs). Significance varies with product. For example, for torque wrenches, torque value helps consumers select the correct torque wrench for tightening a specific nut or bolt. If item has an engine, torque is a general indicator of an engine's pulling power.
     *
     * @return float
     */
    public function getTorque()
    {
        return $this->torque;
    }

    /**
     * Sets a new torque
     *
     * Measurement of turning or twisting force as measured in foot pounds (ft-lbs). Significance varies with product. For example, for torque wrenches, torque value helps consumers select the correct torque wrench for tightening a specific nut or bolt. If item has an engine, torque is a general indicator of an engine's pulling power.
     *
     * @param float $torque
     * @return self
     */
    public function setTorque($torque)
    {
        $this->torque = $torque;
        return $this;
    }

    /**
     * Gets as acceleration
     *
     * The rate of change in velocity as a function of time it takes for the product to reach a specific velocity. For example, a vehicles’ acceleration is typically calculated when the car is not in motion (0 mph), until the amount of time it takes to reach a velocity of 60 miles per hour.
     *
     * @return string
     */
    public function getAcceleration()
    {
        return $this->acceleration;
    }

    /**
     * Sets a new acceleration
     *
     * The rate of change in velocity as a function of time it takes for the product to reach a specific velocity. For example, a vehicles’ acceleration is typically calculated when the car is not in motion (0 mph), until the amount of time it takes to reach a velocity of 60 miles per hour.
     *
     * @param string $acceleration
     * @return self
     */
    public function setAcceleration($acceleration)
    {
        $this->acceleration = $acceleration;
        return $this;
    }

    /**
     * Gets as topSpeed
     *
     * The maximum speed the product can attain
     *
     * @return \WalmartDSV\SpeedUnitType
     */
    public function getTopSpeed()
    {
        return $this->topSpeed;
    }

    /**
     * Sets a new topSpeed
     *
     * The maximum speed the product can attain
     *
     * @param \WalmartDSV\SpeedUnitType $topSpeed
     * @return self
     */
    public function setTopSpeed(\WalmartDSV\SpeedUnitType $topSpeed)
    {
        $this->topSpeed = $topSpeed;
        return $this;
    }

    /**
     * Gets as coolingSystem
     *
     * Method by which the engine is cooled
     *
     * @return string
     */
    public function getCoolingSystem()
    {
        return $this->coolingSystem;
    }

    /**
     * Sets a new coolingSystem
     *
     * Method by which the engine is cooled
     *
     * @param string $coolingSystem
     * @return self
     */
    public function setCoolingSystem($coolingSystem)
    {
        $this->coolingSystem = $coolingSystem;
        return $this;
    }

    /**
     * Gets as fuelRequirement
     *
     * The type of fuel the product requires, as specified by the manufacturer. For example most land vehicles and watercraft require gasoline with a specific octane rating.
     *
     * @return string
     */
    public function getFuelRequirement()
    {
        return $this->fuelRequirement;
    }

    /**
     * Sets a new fuelRequirement
     *
     * The type of fuel the product requires, as specified by the manufacturer. For example most land vehicles and watercraft require gasoline with a specific octane rating.
     *
     * @param string $fuelRequirement
     * @return self
     */
    public function setFuelRequirement($fuelRequirement)
    {
        $this->fuelRequirement = $fuelRequirement;
        return $this;
    }

    /**
     * Gets as fuelSystem
     *
     * Method and mechanisms used to deliver the flow of fuel into an engine. Independent of fuel type. For example, engines fueled by gasoline can have either a fuel injection or carbonated fuel system.
     *
     * @return string
     */
    public function getFuelSystem()
    {
        return $this->fuelSystem;
    }

    /**
     * Sets a new fuelSystem
     *
     * Method and mechanisms used to deliver the flow of fuel into an engine. Independent of fuel type. For example, engines fueled by gasoline can have either a fuel injection or carbonated fuel system.
     *
     * @param string $fuelSystem
     * @return self
     */
    public function setFuelSystem($fuelSystem)
    {
        $this->fuelSystem = $fuelSystem;
        return $this;
    }

    /**
     * Gets as fuelCapacity
     *
     * Volume of fuel the item can hold.
     *
     * @return \WalmartDSV\LandVehiclesType\FuelCapacityAType
     */
    public function getFuelCapacity()
    {
        return $this->fuelCapacity;
    }

    /**
     * Sets a new fuelCapacity
     *
     * Volume of fuel the item can hold.
     *
     * @param \WalmartDSV\LandVehiclesType\FuelCapacityAType $fuelCapacity
     * @return self
     */
    public function setFuelCapacity(\WalmartDSV\LandVehiclesType\FuelCapacityAType $fuelCapacity)
    {
        $this->fuelCapacity = $fuelCapacity;
        return $this;
    }

    /**
     * Gets as averageFuelConsumption
     *
     * Amount of fuel used to cover a specific distance. For example, if a moped or boat motor uses an average of 12 gallons per mile, the value would be 12mpg
     *
     * @return \WalmartDSV\LandVehiclesType\AverageFuelConsumptionAType
     */
    public function getAverageFuelConsumption()
    {
        return $this->averageFuelConsumption;
    }

    /**
     * Sets a new averageFuelConsumption
     *
     * Amount of fuel used to cover a specific distance. For example, if a moped or boat motor uses an average of 12 gallons per mile, the value would be 12mpg
     *
     * @param \WalmartDSV\LandVehiclesType\AverageFuelConsumptionAType $averageFuelConsumption
     * @return self
     */
    public function setAverageFuelConsumption(\WalmartDSV\LandVehiclesType\AverageFuelConsumptionAType $averageFuelConsumption)
    {
        $this->averageFuelConsumption = $averageFuelConsumption;
        return $this;
    }

    /**
     * Gets as frontSuspension
     *
     * Terms describing type of suspension system including springs, shock absorbers and linkages that connects a vehicle to its front wheels.
     *
     * @return string
     */
    public function getFrontSuspension()
    {
        return $this->frontSuspension;
    }

    /**
     * Sets a new frontSuspension
     *
     * Terms describing type of suspension system including springs, shock absorbers and linkages that connects a vehicle to its front wheels.
     *
     * @param string $frontSuspension
     * @return self
     */
    public function setFrontSuspension($frontSuspension)
    {
        $this->frontSuspension = $frontSuspension;
        return $this;
    }

    /**
     * Gets as rearSuspension
     *
     * Terms describing type of suspension system including springs, shock absorbers and linkages that connects a vehicle to its rear wheels.
     *
     * @return string
     */
    public function getRearSuspension()
    {
        return $this->rearSuspension;
    }

    /**
     * Sets a new rearSuspension
     *
     * Terms describing type of suspension system including springs, shock absorbers and linkages that connects a vehicle to its rear wheels.
     *
     * @param string $rearSuspension
     * @return self
     */
    public function setRearSuspension($rearSuspension)
    {
        $this->rearSuspension = $rearSuspension;
        return $this;
    }

    /**
     * Gets as frontBrakes
     *
     * Terms describing the type of braking system including disks, master cylinder connection, and caliper mountings on the front wheels of the product.
     *
     * @return string
     */
    public function getFrontBrakes()
    {
        return $this->frontBrakes;
    }

    /**
     * Sets a new frontBrakes
     *
     * Terms describing the type of braking system including disks, master cylinder connection, and caliper mountings on the front wheels of the product.
     *
     * @param string $frontBrakes
     * @return self
     */
    public function setFrontBrakes($frontBrakes)
    {
        $this->frontBrakes = $frontBrakes;
        return $this;
    }

    /**
     * Gets as rearBrakes
     *
     * Terms describing the type of braking system including disks/drums, master cylinder connection, and caliper mountings on the rear wheels of the product.
     *
     * @return string
     */
    public function getRearBrakes()
    {
        return $this->rearBrakes;
    }

    /**
     * Sets a new rearBrakes
     *
     * Terms describing the type of braking system including disks/drums, master cylinder connection, and caliper mountings on the rear wheels of the product.
     *
     * @param string $rearBrakes
     * @return self
     */
    public function setRearBrakes($rearBrakes)
    {
        $this->rearBrakes = $rearBrakes;
        return $this;
    }

    /**
     * Gets as frontWheels
     *
     * Dimensions and terms describing the wheels on the front of the product.
     *
     * @return string
     */
    public function getFrontWheels()
    {
        return $this->frontWheels;
    }

    /**
     * Sets a new frontWheels
     *
     * Dimensions and terms describing the wheels on the front of the product.
     *
     * @param string $frontWheels
     * @return self
     */
    public function setFrontWheels($frontWheels)
    {
        $this->frontWheels = $frontWheels;
        return $this;
    }

    /**
     * Gets as rearWheels
     *
     * Dimensions and terms describing the rear wheels of the product
     *
     * @return string
     */
    public function getRearWheels()
    {
        return $this->rearWheels;
    }

    /**
     * Sets a new rearWheels
     *
     * Dimensions and terms describing the rear wheels of the product
     *
     * @param string $rearWheels
     * @return self
     */
    public function setRearWheels($rearWheels)
    {
        $this->rearWheels = $rearWheels;
        return $this;
    }

    /**
     * Gets as frontTires
     *
     * Dimensions of the tires on the front wheels of the product.
     *
     * @return string
     */
    public function getFrontTires()
    {
        return $this->frontTires;
    }

    /**
     * Sets a new frontTires
     *
     * Dimensions of the tires on the front wheels of the product.
     *
     * @param string $frontTires
     * @return self
     */
    public function setFrontTires($frontTires)
    {
        $this->frontTires = $frontTires;
        return $this;
    }

    /**
     * Gets as rearTires
     *
     * The dimensions of the tires on the rear wheels of the product.
     *
     * @return string
     */
    public function getRearTires()
    {
        return $this->rearTires;
    }

    /**
     * Sets a new rearTires
     *
     * The dimensions of the tires on the rear wheels of the product.
     *
     * @param string $rearTires
     * @return self
     */
    public function setRearTires($rearTires)
    {
        $this->rearTires = $rearTires;
        return $this;
    }

    /**
     * Gets as seatingCapacity
     *
     * The number of people that can be accommodated by the available seats of an item.
     *
     * @return int
     */
    public function getSeatingCapacity()
    {
        return $this->seatingCapacity;
    }

    /**
     * Sets a new seatingCapacity
     *
     * The number of people that can be accommodated by the available seats of an item.
     *
     * @param int $seatingCapacity
     * @return self
     */
    public function setSeatingCapacity($seatingCapacity)
    {
        $this->seatingCapacity = $seatingCapacity;
        return $this;
    }

    /**
     * Gets as seatHeight
     *
     * The height from the floor to the top of the seat, in inches.
     *
     * @return \WalmartDSV\LandVehiclesType\SeatHeightAType
     */
    public function getSeatHeight()
    {
        return $this->seatHeight;
    }

    /**
     * Sets a new seatHeight
     *
     * The height from the floor to the top of the seat, in inches.
     *
     * @param \WalmartDSV\LandVehiclesType\SeatHeightAType $seatHeight
     * @return self
     */
    public function setSeatHeight(\WalmartDSV\LandVehiclesType\SeatHeightAType $seatHeight)
    {
        $this->seatHeight = $seatHeight;
        return $this;
    }

    /**
     * Gets as wheelbase
     *
     * The distance between the front and rear axles of a vehicle. Important component of vehicle stability and maneuverability.
     *
     * @return \WalmartDSV\LandVehiclesType\WheelbaseAType
     */
    public function getWheelbase()
    {
        return $this->wheelbase;
    }

    /**
     * Sets a new wheelbase
     *
     * The distance between the front and rear axles of a vehicle. Important component of vehicle stability and maneuverability.
     *
     * @param \WalmartDSV\LandVehiclesType\WheelbaseAType $wheelbase
     * @return self
     */
    public function setWheelbase(\WalmartDSV\LandVehiclesType\WheelbaseAType $wheelbase)
    {
        $this->wheelbase = $wheelbase;
        return $this;
    }

    /**
     * Gets as curbWeight
     *
     * Total weight of a vehicle with standard equipment and all necessary support products (motor oil, fuel, coolant) but without either passengers or cargo.
     *
     * @return \WalmartDSV\LandVehiclesType\CurbWeightAType
     */
    public function getCurbWeight()
    {
        return $this->curbWeight;
    }

    /**
     * Sets a new curbWeight
     *
     * Total weight of a vehicle with standard equipment and all necessary support products (motor oil, fuel, coolant) but without either passengers or cargo.
     *
     * @param \WalmartDSV\LandVehiclesType\CurbWeightAType $curbWeight
     * @return self
     */
    public function setCurbWeight(\WalmartDSV\LandVehiclesType\CurbWeightAType $curbWeight)
    {
        $this->curbWeight = $curbWeight;
        return $this;
    }

    /**
     * Gets as towingCapacity
     *
     * Maximum weight that can be pulled by the vehicle. This tow rating is specified by the manufacturer.
     *
     * @return \WalmartDSV\LandVehiclesType\TowingCapacityAType
     */
    public function getTowingCapacity()
    {
        return $this->towingCapacity;
    }

    /**
     * Sets a new towingCapacity
     *
     * Maximum weight that can be pulled by the vehicle. This tow rating is specified by the manufacturer.
     *
     * @param \WalmartDSV\LandVehiclesType\TowingCapacityAType $towingCapacity
     * @return self
     */
    public function setTowingCapacity(\WalmartDSV\LandVehiclesType\TowingCapacityAType $towingCapacity)
    {
        $this->towingCapacity = $towingCapacity;
        return $this;
    }

    /**
     * Gets as assembledProductLength
     *
     * The length of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @return \WalmartDSV\LandVehiclesType\AssembledProductLengthAType
     */
    public function getAssembledProductLength()
    {
        return $this->assembledProductLength;
    }

    /**
     * Sets a new assembledProductLength
     *
     * The length of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @param \WalmartDSV\LandVehiclesType\AssembledProductLengthAType $assembledProductLength
     * @return self
     */
    public function setAssembledProductLength(\WalmartDSV\LandVehiclesType\AssembledProductLengthAType $assembledProductLength)
    {
        $this->assembledProductLength = $assembledProductLength;
        return $this;
    }

    /**
     * Gets as assembledProductWidth
     *
     * The width of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @return \WalmartDSV\LandVehiclesType\AssembledProductWidthAType
     */
    public function getAssembledProductWidth()
    {
        return $this->assembledProductWidth;
    }

    /**
     * Sets a new assembledProductWidth
     *
     * The width of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @param \WalmartDSV\LandVehiclesType\AssembledProductWidthAType $assembledProductWidth
     * @return self
     */
    public function setAssembledProductWidth(\WalmartDSV\LandVehiclesType\AssembledProductWidthAType $assembledProductWidth)
    {
        $this->assembledProductWidth = $assembledProductWidth;
        return $this;
    }

    /**
     * Gets as assembledProductHeight
     *
     * The height of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @return \WalmartDSV\LandVehiclesType\AssembledProductHeightAType
     */
    public function getAssembledProductHeight()
    {
        return $this->assembledProductHeight;
    }

    /**
     * Sets a new assembledProductHeight
     *
     * The height of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @param \WalmartDSV\LandVehiclesType\AssembledProductHeightAType $assembledProductHeight
     * @return self
     */
    public function setAssembledProductHeight(\WalmartDSV\LandVehiclesType\AssembledProductHeightAType $assembledProductHeight)
    {
        $this->assembledProductHeight = $assembledProductHeight;
        return $this;
    }

    /**
     * Gets as variantGroupId
     *
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @return string
     */
    public function getVariantGroupId()
    {
        return $this->variantGroupId;
    }

    /**
     * Sets a new variantGroupId
     *
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @param string $variantGroupId
     * @return self
     */
    public function setVariantGroupId($variantGroupId)
    {
        $this->variantGroupId = $variantGroupId;
        return $this;
    }

    /**
     * Adds as variantAttributeName
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @return self
     * @param string $variantAttributeName
     */
    public function addToVariantAttributeNames($variantAttributeName)
    {
        $this->variantAttributeNames[] = $variantAttributeName;
        return $this;
    }

    /**
     * isset variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetVariantAttributeNames($index)
    {
        return isset($this->variantAttributeNames[$index]);
    }

    /**
     * unset variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetVariantAttributeNames($index)
    {
        unset($this->variantAttributeNames[$index]);
    }

    /**
     * Gets as variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @return string[]
     */
    public function getVariantAttributeNames()
    {
        return $this->variantAttributeNames;
    }

    /**
     * Sets a new variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param string $variantAttributeNames
     * @return self
     */
    public function setVariantAttributeNames(array $variantAttributeNames)
    {
        $this->variantAttributeNames = $variantAttributeNames;
        return $this;
    }

    /**
     * Gets as isPrimaryVariant
     *
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @return string
     */
    public function getIsPrimaryVariant()
    {
        return $this->isPrimaryVariant;
    }

    /**
     * Sets a new isPrimaryVariant
     *
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @param string $isPrimaryVariant
     * @return self
     */
    public function setIsPrimaryVariant($isPrimaryVariant)
    {
        $this->isPrimaryVariant = $isPrimaryVariant;
        return $this;
    }

    /**
     * Gets as isPrivateLabelOrUnbranded
     *
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @return string
     */
    public function getIsPrivateLabelOrUnbranded()
    {
        return $this->isPrivateLabelOrUnbranded;
    }

    /**
     * Sets a new isPrivateLabelOrUnbranded
     *
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @param string $isPrivateLabelOrUnbranded
     * @return self
     */
    public function setIsPrivateLabelOrUnbranded($isPrivateLabelOrUnbranded)
    {
        $this->isPrivateLabelOrUnbranded = $isPrivateLabelOrUnbranded;
        return $this;
    }

    /**
     * Gets as isProp65WarningRequired
     *
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @return string
     */
    public function getIsProp65WarningRequired()
    {
        return $this->isProp65WarningRequired;
    }

    /**
     * Sets a new isProp65WarningRequired
     *
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @param string $isProp65WarningRequired
     * @return self
     */
    public function setIsProp65WarningRequired($isProp65WarningRequired)
    {
        $this->isProp65WarningRequired = $isProp65WarningRequired;
        return $this;
    }

    /**
     * Gets as prop65WarningText
     *
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @return string
     */
    public function getProp65WarningText()
    {
        return $this->prop65WarningText;
    }

    /**
     * Sets a new prop65WarningText
     *
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @param string $prop65WarningText
     * @return self
     */
    public function setProp65WarningText($prop65WarningText)
    {
        $this->prop65WarningText = $prop65WarningText;
        return $this;
    }

    /**
     * Gets as hasBatteries
     *
     * "Battery or battery containing product" is defined by Company to include any item of Merchandise that is a battery or any component of Merchandise, including reusable packaging intended to stay in use with the item, containing a battery of any chemistry/ type. Mark Y if this definition applies to your product.
     *
     * @return string
     */
    public function getHasBatteries()
    {
        return $this->hasBatteries;
    }

    /**
     * Sets a new hasBatteries
     *
     * "Battery or battery containing product" is defined by Company to include any item of Merchandise that is a battery or any component of Merchandise, including reusable packaging intended to stay in use with the item, containing a battery of any chemistry/ type. Mark Y if this definition applies to your product.
     *
     * @param string $hasBatteries
     * @return self
     */
    public function setHasBatteries($hasBatteries)
    {
        $this->hasBatteries = $hasBatteries;
        return $this;
    }

    /**
     * Gets as batteryTechnologyType
     *
     * Please select the Battery Technology Type from the list provided. NOTE: If battery type is lead acid, lead acid (nonspillable), lithium ion, or lithium metal, please ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @return string
     */
    public function getBatteryTechnologyType()
    {
        return $this->batteryTechnologyType;
    }

    /**
     * Sets a new batteryTechnologyType
     *
     * Please select the Battery Technology Type from the list provided. NOTE: If battery type is lead acid, lead acid (nonspillable), lithium ion, or lithium metal, please ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @param string $batteryTechnologyType
     * @return self
     */
    public function setBatteryTechnologyType($batteryTechnologyType)
    {
        $this->batteryTechnologyType = $batteryTechnologyType;
        return $this;
    }

    /**
     * Gets as hasWarranty
     *
     * Y indicates the item comes with a warranty. If an item has a warranty, then enter EITHER the warranty URL or the warranty text in the appropriate field.
     *
     * @return string
     */
    public function getHasWarranty()
    {
        return $this->hasWarranty;
    }

    /**
     * Sets a new hasWarranty
     *
     * Y indicates the item comes with a warranty. If an item has a warranty, then enter EITHER the warranty URL or the warranty text in the appropriate field.
     *
     * @param string $hasWarranty
     * @return self
     */
    public function setHasWarranty($hasWarranty)
    {
        $this->hasWarranty = $hasWarranty;
        return $this;
    }

    /**
     * Gets as warrantyURL
     *
     * If you indicated that your item has a warranty, provide either the Warranty URL or Warranty Text. The Warranty URL is the web location of the image, PDF, or link to the manufacturer's warranty page, showing the warranty and its terms, including the duration of the warranty. URLs must begin with http:// or https:// NOTE: Please remember to update the link and/or text of the warranty as the warranty changes. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @return string
     */
    public function getWarrantyURL()
    {
        return $this->warrantyURL;
    }

    /**
     * Sets a new warrantyURL
     *
     * If you indicated that your item has a warranty, provide either the Warranty URL or Warranty Text. The Warranty URL is the web location of the image, PDF, or link to the manufacturer's warranty page, showing the warranty and its terms, including the duration of the warranty. URLs must begin with http:// or https:// NOTE: Please remember to update the link and/or text of the warranty as the warranty changes. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @param string $warrantyURL
     * @return self
     */
    public function setWarrantyURL($warrantyURL)
    {
        $this->warrantyURL = $warrantyURL;
        return $this;
    }

    /**
     * Gets as warrantyText
     *
     * If you marked Y for "Has Warranty" provide the Warranty URL or Warranty Text (the full text of the warranty terms, including what is covered by the warranty and the duration of the warranty). NOTE: please remember to update the text of your warranty as your warranty changes.
     *
     * @return string
     */
    public function getWarrantyText()
    {
        return $this->warrantyText;
    }

    /**
     * Sets a new warrantyText
     *
     * If you marked Y for "Has Warranty" provide the Warranty URL or Warranty Text (the full text of the warranty terms, including what is covered by the warranty and the duration of the warranty). NOTE: please remember to update the text of your warranty as your warranty changes.
     *
     * @param string $warrantyText
     * @return self
     */
    public function setWarrantyText($warrantyText)
    {
        $this->warrantyText = $warrantyText;
        return $this;
    }

    /**
     * Gets as hasFuelContainer
     *
     * Denotes any item with an empty container that may be filled with fluids, such as fuel, CO2, propane, etc.
     *
     * @return string
     */
    public function getHasFuelContainer()
    {
        return $this->hasFuelContainer;
    }

    /**
     * Sets a new hasFuelContainer
     *
     * Denotes any item with an empty container that may be filled with fluids, such as fuel, CO2, propane, etc.
     *
     * @param string $hasFuelContainer
     * @return self
     */
    public function setHasFuelContainer($hasFuelContainer)
    {
        $this->hasFuelContainer = $hasFuelContainer;
        return $this;
    }

    /**
     * Gets as hasStateRestrictions
     *
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @return string
     */
    public function getHasStateRestrictions()
    {
        return $this->hasStateRestrictions;
    }

    /**
     * Sets a new hasStateRestrictions
     *
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @param string $hasStateRestrictions
     * @return self
     */
    public function setHasStateRestrictions($hasStateRestrictions)
    {
        $this->hasStateRestrictions = $hasStateRestrictions;
        return $this;
    }

    /**
     * Adds as stateRestriction
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param \WalmartDSV\StateRestrictionType $stateRestriction
     *@return self
     */
    public function addToStateRestrictions(\WalmartDSV\StateRestrictionType $stateRestriction)
    {
        $this->stateRestrictions[] = $stateRestriction;
        return $this;
    }

    /**
     * isset stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetStateRestrictions($index)
    {
        return isset($this->stateRestrictions[$index]);
    }

    /**
     * unset stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetStateRestrictions($index)
    {
        unset($this->stateRestrictions[$index]);
    }

    /**
     * Gets as stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @return \WalmartDSV\StateRestrictionType[]
     */
    public function getStateRestrictions()
    {
        return $this->stateRestrictions;
    }

    /**
     * Sets a new stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param \WalmartDSV\StateRestrictionType[] $stateRestrictions
     * @return self
     */
    public function setStateRestrictions(array $stateRestrictions)
    {
        $this->stateRestrictions = $stateRestrictions;
        return $this;
    }

    /**
     * Gets as compositeWoodCertificationCode
     *
     * Composite Wood - Indicates if any portion of the item contains any of the following types of composite wood: hardwood plywood veneer core, hardwood plywood composite core, particleboard, or medium density fiber board (MDF). Enter the code corresponding to the highest formaldehyde emission level on any portion of the item. Code Definitions: 1 - Does not contain composite wood; 7 - Product is not CARB compliant and cannot be sold in California; 8 - Product is CARB compliant and can be sold in California.
     *
     * @return int
     */
    public function getCompositeWoodCertificationCode()
    {
        return $this->compositeWoodCertificationCode;
    }

    /**
     * Sets a new compositeWoodCertificationCode
     *
     * Composite Wood - Indicates if any portion of the item contains any of the following types of composite wood: hardwood plywood veneer core, hardwood plywood composite core, particleboard, or medium density fiber board (MDF). Enter the code corresponding to the highest formaldehyde emission level on any portion of the item. Code Definitions: 1 - Does not contain composite wood; 7 - Product is not CARB compliant and cannot be sold in California; 8 - Product is CARB compliant and can be sold in California.
     *
     * @param int $compositeWoodCertificationCode
     * @return self
     */
    public function setCompositeWoodCertificationCode($compositeWoodCertificationCode)
    {
        $this->compositeWoodCertificationCode = $compositeWoodCertificationCode;
        return $this;
    }

    /**
     * Gets as requiresTextileActLabeling
     *
     * Select "Y" if your item contains wool or is one of the following: clothing (except for hats and shoes), handkerchiefs, scarves, bedding (including sheets, covers, blankets, comforters, pillows, pillowcases, quilts, bedspreads and pads (but not outer coverings for mattresses or box springs)), curtains and casements, draperies, tablecloths, napkins, doilies, floor coverings (rugs, carpets and mats), towels, washcloths, dishcloths, ironing board covers and pads, umbrellas, parasols, bats or batting, flags with heading or that are bigger than 216 square inches, cushions, all fibers, yarns and fabrics (but not packaging ribbons), furniture slip covers and other furniture covers, afghans and throws, sleeping bags, antimacassars (doilies), hammocks, dresser and other furniture scarves. For further information on these requirements, refer to the labeling requirements of the Textile Act.
     *
     * @return string
     */
    public function getRequiresTextileActLabeling()
    {
        return $this->requiresTextileActLabeling;
    }

    /**
     * Sets a new requiresTextileActLabeling
     *
     * Select "Y" if your item contains wool or is one of the following: clothing (except for hats and shoes), handkerchiefs, scarves, bedding (including sheets, covers, blankets, comforters, pillows, pillowcases, quilts, bedspreads and pads (but not outer coverings for mattresses or box springs)), curtains and casements, draperies, tablecloths, napkins, doilies, floor coverings (rugs, carpets and mats), towels, washcloths, dishcloths, ironing board covers and pads, umbrellas, parasols, bats or batting, flags with heading or that are bigger than 216 square inches, cushions, all fibers, yarns and fabrics (but not packaging ribbons), furniture slip covers and other furniture covers, afghans and throws, sleeping bags, antimacassars (doilies), hammocks, dresser and other furniture scarves. For further information on these requirements, refer to the labeling requirements of the Textile Act.
     *
     * @param string $requiresTextileActLabeling
     * @return self
     */
    public function setRequiresTextileActLabeling($requiresTextileActLabeling)
    {
        $this->requiresTextileActLabeling = $requiresTextileActLabeling;
        return $this;
    }

    /**
     * Gets as countryOfOriginTextiles
     *
     * Use “Made in U.S.A. and Imported” to indicate manufacture in the U.S. from imported materials, or part processing in the U.S. and part in a foreign country. Use “Made in U.S.A. or Imported” to reflect that some units of an item originate from a domestic source and others from a foreign source. Use “Made in U.S.A.” only if all units were made completely in the U.S. using materials also made in the U.S. Use "Imported" if units are completely imported.
     *
     * @return string
     */
    public function getCountryOfOriginTextiles()
    {
        return $this->countryOfOriginTextiles;
    }

    /**
     * Sets a new countryOfOriginTextiles
     *
     * Use “Made in U.S.A. and Imported” to indicate manufacture in the U.S. from imported materials, or part processing in the U.S. and part in a foreign country. Use “Made in U.S.A. or Imported” to reflect that some units of an item originate from a domestic source and others from a foreign source. Use “Made in U.S.A.” only if all units were made completely in the U.S. using materials also made in the U.S. Use "Imported" if units are completely imported.
     *
     * @param string $countryOfOriginTextiles
     * @return self
     */
    public function setCountryOfOriginTextiles($countryOfOriginTextiles)
    {
        $this->countryOfOriginTextiles = $countryOfOriginTextiles;
        return $this;
    }

    /**
     * Adds as fabricContentValue
     *
     * Material makeup of the item.
     *
     * @param \WalmartDSV\FabricContentValueType $fabricContentValue
     *@return self
     */
    public function addToFabricContent(\WalmartDSV\FabricContentValueType $fabricContentValue)
    {
        $this->fabricContent[] = $fabricContentValue;
        return $this;
    }

    /**
     * isset fabricContent
     *
     * Material makeup of the item.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFabricContent($index)
    {
        return isset($this->fabricContent[$index]);
    }

    /**
     * unset fabricContent
     *
     * Material makeup of the item.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFabricContent($index)
    {
        unset($this->fabricContent[$index]);
    }

    /**
     * Gets as fabricContent
     *
     * Material makeup of the item.
     *
     * @return \WalmartDSV\FabricContentValueType[]
     */
    public function getFabricContent()
    {
        return $this->fabricContent;
    }

    /**
     * Sets a new fabricContent
     *
     * Material makeup of the item.
     *
     * @param \WalmartDSV\FabricContentValueType[] $fabricContent
     * @return self
     */
    public function setFabricContent(array $fabricContent)
    {
        $this->fabricContent = $fabricContent;
        return $this;
    }

    /**
     * Adds as feature
     *
     * List notable features of the item.
     *
     * @return self
     * @param string $feature
     */
    public function addToFeatures($feature)
    {
        $this->features[] = $feature;
        return $this;
    }

    /**
     * isset features
     *
     * List notable features of the item.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFeatures($index)
    {
        return isset($this->features[$index]);
    }

    /**
     * unset features
     *
     * List notable features of the item.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFeatures($index)
    {
        unset($this->features[$index]);
    }

    /**
     * Gets as features
     *
     * List notable features of the item.
     *
     * @return string[]
     */
    public function getFeatures()
    {
        return $this->features;
    }

    /**
     * Sets a new features
     *
     * List notable features of the item.
     *
     * @param string $features
     * @return self
     */
    public function setFeatures(array $features)
    {
        $this->features = $features;
        return $this;
    }

    /**
     * Gets as keywords
     *
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @return string
     */
    public function getKeywords()
    {
        return $this->keywords;
    }

    /**
     * Sets a new keywords
     *
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @param string $keywords
     * @return self
     */
    public function setKeywords($keywords)
    {
        $this->keywords = $keywords;
        return $this;
    }

    /**
     * Adds as swatchImage
     *
     * @param \WalmartDSV\LandVehiclesType\SwatchImagesAType\SwatchImageAType $swatchImage
     *@return self
     */
    public function addToSwatchImages(\WalmartDSV\LandVehiclesType\SwatchImagesAType\SwatchImageAType $swatchImage)
    {
        $this->swatchImages[] = $swatchImage;
        return $this;
    }

    /**
     * isset swatchImages
     *
     * @param int|string $index
     * @return bool
     */
    public function issetSwatchImages($index)
    {
        return isset($this->swatchImages[$index]);
    }

    /**
     * unset swatchImages
     *
     * @param int|string $index
     * @return void
     */
    public function unsetSwatchImages($index)
    {
        unset($this->swatchImages[$index]);
    }

    /**
     * Gets as swatchImages
     *
     * @return \WalmartDSV\LandVehiclesType\SwatchImagesAType\SwatchImageAType[]
     */
    public function getSwatchImages()
    {
        return $this->swatchImages;
    }

    /**
     * Sets a new swatchImages
     *
     * @param \WalmartDSV\LandVehiclesType\SwatchImagesAType\SwatchImageAType[] $swatchImages
     * @return self
     */
    public function setSwatchImages(array $swatchImages)
    {
        $this->swatchImages = $swatchImages;
        return $this;
    }


}

