<?php

namespace WalmartDSV;

/**
 * Class representing ToolsType
 *
 *
 * XSD Type: Tools
 */
class ToolsType
{

    /**
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @var string[] $additionalVariantAttributeNames
     */
    private $additionalVariantAttributeNames = null;

    /**
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @var string $shortDescription
     */
    private $shortDescription = null;

    /**
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @var string[] $keyFeatures
     */
    private $keyFeatures = null;

    /**
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @var string $brand
     */
    private $brand = null;

    /**
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @var string $manufacturer
     */
    private $manufacturer = null;

    /**
     * MPN uniquely identifies the product to its manufacturer. For many products this will be identical to the model number. Some manufacturers distinguish part number from model number. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @var string $manufacturerPartNumber
     */
    private $manufacturerPartNumber = null;

    /**
     * Model numbers allow manufacturers to keep track of each hardware device and identify or replace the proper part when needed. Model numbers are often found on the bottom, back, or side of a product. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @var string $modelNumber
     */
    private $modelNumber = null;

    /**
     * If this is a component of a sellable item, select "Yes". Once you have set up all of the components of your sellable unit, reach out to your Walmart.com Merchant and ask them to group all of the item components together. Inflexible Kits must be configured and published by your Walmart Merchant.
     *
     * @var string $inflexKitComponent
     */
    private $inflexKitComponent = null;

    /**
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @var int $multipackQuantity
     */
    private $multipackQuantity = null;

    /**
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @var int $countPerPack
     */
    private $countPerPack = null;

    /**
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @var string $count
     */
    private $count = null;

    /**
     * The number of small pieces, slices, or different items within the product. Piece Count applies to things such as puzzles, building block sets, and products that contain multiple different items (such as tool sets, dinnerware sets, gift baskets, art sets, makeup kits, or shaving kits). EXAMPLE: (1) A 500-piece puzzle has a "Piece Count" of 500. (2) A 105-Piece Socket Wrench set has a piece count of "105." (3) A gift basket of 5 different items has a "Piece Count" of 5.
     *
     * @var int $pieceCount
     */
    private $pieceCount = null;

    /**
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @var string $mainImageUrl
     */
    private $mainImageUrl = null;

    /**
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @var string[] $productSecondaryImageURL
     */
    private $productSecondaryImageURL = null;

    /**
     * The main material(s) that a product is made of. This does not need to be an exhaustive list, but should contain the predominant or functionally important material/materials. Fabric material specifics should be entered using the "Fabric Content" attribute.
     *
     * @var \WalmartDSV\MaterialType $material
     */
    private $material = null;

    /**
     * Physical shape of the item. Used in a wide variety of products including rugs, toys and large appliances.
     *
     * @var string $shape
     */
    private $shape = null;

    /**
     * Overall dimensions of an item. Used only for products that do not already have a more specific 'x size' attribute, such as ring size or clothing size.
     *
     * @var string $size
     */
    private $size = null;

    /**
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @var string[] $globalBrandLicense
     */
    private $globalBrandLicense = null;

    /**
     * Provides information on the exact type of power used by the item.
     *
     * @var string $powerType
     */
    private $powerType = null;

    /**
     * The kind of material that a product consumes to produce heat or power. Attribute is also used to classify a fuel product.
     *
     * @var string $fuelType
     */
    private $fuelType = null;

    /**
     * The measure of the blade at the widest point. For circular blades, measured from the furthest edge of one tip to the furthest edge of the tip directly opposite. Important selection criteria for consumers for compatibility and use. For example, handheld circular saws accept blades that range from 4 ½ - 7 ¼ inches in diameter.
     *
     * @var \WalmartDSV\ToolsType\BladeDiameterAType $bladeDiameter
     */
    private $bladeDiameter = null;

    /**
     * The measurement of the length of the blade from the tip to base. For most knives the base would be the forward-most aspect of the handle. Used for products such as knives, scissors, and planers.
     *
     * @var \WalmartDSV\ToolsType\BladeLengthAType $bladeLength
     */
    private $bladeLength = null;

    /**
     * The type of design of the base of a blade used to attach the blade. Important selection criteria for compatibility between blades and tools. For example, some jigsaws are designed to accept two or more blade shank styles, while others can only accept one style.
     *
     * @var string $bladeShank
     */
    private $bladeShank = null;

    /**
     * Overall dimensions of the end piece or shaft portion of the item. In the case of tools, used to select items based on fit. Specific expression of shank size varies with item. For example, for twist drill bits, shank size is the diameter of the shank designed to fit in the chuck of a drill
     *
     * @var \WalmartDSV\ToolsType\ShankSizeAType $shankSize
     */
    private $shankSize = null;

    /**
     * The industry term that describes the design of the part of the device that holds the bit. Certain chuck types can be adjusted to accommodate different sizes of bits.
     *
     * @var string $chuckType
     */
    private $chuckType = null;

    /**
     * The measurement of the part of the device that holds the bit and indicates the maximum diameter bit shank that fits the device. Products that have chucks include power drills, lathes, and scroll saws.
     *
     * @var \WalmartDSV\ToolsType\ChuckSizeAType $chuckSize
     */
    private $chuckSize = null;

    /**
     * Measurement of the diameter of shaft or spindle that inserts into and holds an attachment such as a saw blade or grinder wheel. As applied machine tool attachments, describes the arbor hole size and is used to identify machine/attachment fit. For example, a table saw that has a 5/8 inch arbor would accommodate a saw blade with a 5/8 inch arbor opening.
     *
     * @var \WalmartDSV\ToolsType\ArborDiameterAType $arborDiameter
     */
    private $arborDiameter = null;

    /**
     * The size of the opening of the collar of the collet, which is designed to hold a tool attachment. Collets are commonly found on wood routers, precision grinders and lathes. Attribute also important in selecting compatible tool attachments.
     *
     * @var \WalmartDSV\ToolsType\ColletSizeAType $colletSize
     */
    private $colletSize = null;

    /**
     * For tools that have spindles, the design and sizing specifications. For example, the thread measurement of the chuck backplate adapter.
     *
     * @var string $spindleThread
     */
    private $spindleThread = null;

    /**
     * Measurement of the diameter of the disc portion of a tool attachment. Typically used to describe sanding discs and attachments for power tools.
     *
     * @var \WalmartDSV\ToolsType\DiscSizeAType $discSize
     */
    private $discSize = null;

    /**
     * The measurement of the width and length of a sandpaper belt. Used to select the correct size to fit a specific belt sander.
     *
     * @var string $sandingBeltSize
     */
    private $sandingBeltSize = null;

    /**
     * Measurement of the diameter of the air intake aperture, measured at its widest point. Attribute used for tools such as air inlet hoses, air impact wrenches, and air compressors.
     *
     * @var \WalmartDSV\ToolsType\AirInletAType $airInlet
     */
    private $airInlet = null;

    /**
     * Rate of airflow the product requires at the given pressure of 90 pounds per square inch. Used for products such as air brushes, framing nailers, and jackhammers. Important criteria that allows consumers to select items that are compatible with their air-delivery system. For example, a ½ʺ impact wrench typically requires 5 CFM at 90 psi.
     *
     * @var \WalmartDSV\ToolsType\AverageAirConsumptionAt90PSIAType $averageAirConsumptionAt90PSI
     */
    private $averageAirConsumptionAt90PSI = null;

    /**
     * The flow rate produced by the item. Expressed in cubic feet per minute, as measured at the pressure of 40 pounds per inch. Allows consumers to select products such as air compressors to accommodate specific tools and applications.
     *
     * @var \WalmartDSV\ToolsType\CfmAt40PsiAType $cfmAt40Psi
     */
    private $cfmAt40Psi = null;

    /**
     * The airflow rate produced by the item. Expressed in cubic feet per minute, as measured at the pressure of 90 pounds per inch. Allows consumers to select products such as air compressors to accommodate specific tools and applications.
     *
     * @var \WalmartDSV\ToolsType\CfmAt90PsiAType $cfmAt90Psi
     */
    private $cfmAt90Psi = null;

    /**
     * @var \WalmartDSV\ToolsType\VoltsAType $volts
     */
    private $volts = null;

    /**
     * Number of amps as a measure of electrical current draw. For products such as appliances, amps are usually specified as a peak value to help consumers select items that not overload household circuits. Also used as a measure of capacity (trip level) for electrical products such as circuit breakers and fuses
     *
     * @var \WalmartDSV\ToolsType\AmpsAType $amps
     */
    private $amps = null;

    /**
     * The length of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @var \WalmartDSV\ToolsType\AssembledProductLengthAType $assembledProductLength
     */
    private $assembledProductLength = null;

    /**
     * The width of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @var \WalmartDSV\ToolsType\AssembledProductWidthAType $assembledProductWidth
     */
    private $assembledProductWidth = null;

    /**
     * The height of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @var \WalmartDSV\ToolsType\AssembledProductHeightAType $assembledProductHeight
     */
    private $assembledProductHeight = null;

    /**
     * The weight of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @var \WalmartDSV\ToolsType\AssembledProductWeightAType $assembledProductWeight
     */
    private $assembledProductWeight = null;

    /**
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @var string $variantGroupId
     */
    private $variantGroupId = null;

    /**
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @var string[] $variantAttributeNames
     */
    private $variantAttributeNames = null;

    /**
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @var string $isPrimaryVariant
     */
    private $isPrimaryVariant = null;

    /**
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @var string $isPrivateLabelOrUnbranded
     */
    private $isPrivateLabelOrUnbranded = null;

    /**
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @var string $isProp65WarningRequired
     */
    private $isProp65WarningRequired = null;

    /**
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @var string $prop65WarningText
     */
    private $prop65WarningText = null;

    /**
     * "Battery or battery containing product" is defined by Company to include any item of Merchandise that is a battery or any component of Merchandise, including reusable packaging intended to stay in use with the item, containing a battery of any chemistry/ type. Mark Y if this definition applies to your product.
     *
     * @var string $hasBatteries
     */
    private $hasBatteries = null;

    /**
     * Please select the Battery Technology Type from the list provided. NOTE: If battery type is lead acid, lead acid (nonspillable), lithium ion, or lithium metal, please ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @var string $batteryTechnologyType
     */
    private $batteryTechnologyType = null;

    /**
     * Y indicates the item comes with a warranty. If an item has a warranty, then enter EITHER the warranty URL or the warranty text in the appropriate field.
     *
     * @var string $hasWarranty
     */
    private $hasWarranty = null;

    /**
     * If you indicated that your item has a warranty, provide either the Warranty URL or Warranty Text. The Warranty URL is the web location of the image, PDF, or link to the manufacturer's warranty page, showing the warranty and its terms, including the duration of the warranty. URLs must begin with http:// or https:// NOTE: Please remember to update the link and/or text of the warranty as the warranty changes. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @var string $warrantyURL
     */
    private $warrantyURL = null;

    /**
     * If you marked Y for "Has Warranty" provide the Warranty URL or Warranty Text (the full text of the warranty terms, including what is covered by the warranty and the duration of the warranty). NOTE: please remember to update the text of your warranty as your warranty changes.
     *
     * @var string $warrantyText
     */
    private $warrantyText = null;

    /**
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @var string $hasStateRestrictions
     */
    private $hasStateRestrictions = null;

    /**
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @var \WalmartDSV\StateRestrictionType[] $stateRestrictions
     */
    private $stateRestrictions = null;

    /**
     * Denotes any item with an empty container that may be filled with fluids, such as fuel, CO2, propane, etc.
     *
     * @var string $hasFuelContainer
     */
    private $hasFuelContainer = null;

    /**
     * Listing of any supplementary items that come with the product. Important information for consumers because accessories typically provide additional convenience, utility, attractiveness or safety to or for a product.
     *
     * @var string[] $accessoriesIncluded
     */
    private $accessoriesIncluded = null;

    /**
     * Color as described by the manufacturer.
     *
     * @var \WalmartDSV\ColorType $color
     */
    private $color = null;

    /**
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @var string[] $colorCategory
     */
    private $colorCategory = null;

    /**
     * Y indicates that the item has been especially made to be resistant to water, to some degree.
     *
     * @var string $isWaterproof
     */
    private $isWaterproof = null;

    /**
     * Indicates that an item can be used in an industrial setting or has an industrial application.
     *
     * @var string $isIndustrial
     */
    private $isIndustrial = null;

    /**
     * Y indicates that the item has been especially made to resist burning under certain conditions, as specified by the manufacturer. An important feature for products such as fire blankets, building materials, and safes.
     *
     * @var string $isFireResistant
     */
    private $isFireResistant = null;

    /**
     * Description of how the item should be cleaned and maintained.
     *
     * @var string $cleaningCareAndMaintenance
     */
    private $cleaningCareAndMaintenance = null;

    /**
     * Further clarification of what the item may be used for. This improves searchability when customers search for general terms like "birthday party" that do not include the names of specific items.
     *
     * @var string[] $recommendedUses
     */
    private $recommendedUses = null;

    /**
     * Number of blades in a multi-tool or fan.
     *
     * @var int $numberOfBlades
     */
    private $numberOfBlades = null;

    /**
     * The width of a blade of a cutting or wiping tool (saws, squeegees). How the measurement is taken can vary with the product.
     *
     * @var \WalmartDSV\LengthUnitType $bladeWidth
     */
    private $bladeWidth = null;

    /**
     * Category of light bulb based on method to produce the light. Important to consumers because each type has different characteristics including bulb life, energy efficiency and color temperature. For example LED bulbs have a greater bulb life than equivalent incandescent bulbs.
     *
     * @var string $lightBulbType
     */
    private $lightBulbType = null;

    /**
     * The standard numeric designation of abrasive as provided by the manufacturer. Grit size should be in the format for the applicable standard. For example, “P40” if using the ISOS/FEPA, and “40” if using the CAMI standard. Important factor selection factor for sandpapers and abrasive disks based on indented use. A small number (20 or 40) indicates a coarse grit designed for removing deep gouges and imperfections, while a large number (250) indicates a fine grit, which is best for smoothing varnished finishes between coats.
     *
     * @var string $gritSize
     */
    private $gritSize = null;

    /**
     * Standardized drive size used to hold a socket or other tooling interface.
     *
     * @var \WalmartDSV\LengthUnitType $squareDriveSize
     */
    private $squareDriveSize = null;

    /**
     * The internal, usable depth of a socket.
     *
     * @var \WalmartDSV\LengthUnitType $socketDepth
     */
    private $socketDepth = null;

    /**
     * The number of steps product has as in a ladder.
     *
     * @var float $numberOfSteps
     */
    private $numberOfSteps = null;

    /**
     * The number of points within a socket wrench.
     *
     * @var float $numberOfPoints
     */
    private $numberOfPoints = null;

    /**
     * List notable features of the item.
     *
     * @var string[] $features
     */
    private $features = null;

    /**
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @var string $keywords
     */
    private $keywords = null;

    /**
     * The primary, or dominant hand for which the device is designed. Example products include left-handed scissors and circular power saws.
     *
     * @var string $handing
     */
    private $handing = null;

    /**
     * Terms describing the overall external treatment applied to the item. Typically finishes give a distinct appearance, texture or additional performance to the item. This attribute is used in a wide variety products and materials including wood, metal and fabric.
     *
     * @var string $finish
     */
    private $finish = null;

    /**
     * Measurement of the length of cord that comes with the item. For electrical appliances the cord is typically a power cord. For other products, headphones for example, the cord would connect to the receiver.
     *
     * @var \WalmartDSV\ToolsType\CordLengthAType $cordLength
     */
    private $cordLength = null;

    /**
     * The measure of the current a battery can deliver over time, expressed in ampere-hours (Ah).
     *
     * @var \WalmartDSV\ToolsType\BatteryCapacityAType $batteryCapacity
     */
    private $batteryCapacity = null;

    /**
     * The combined volume, as displaced by the pistons for all cylinders of an internal combustion engine. Generally expressed in cubic centimeters up to 1000 cc, and liters thereafter, or in cubic inches. Engine displacement is often used as a rough indicator of an engine's power and potential fuel consumption.
     *
     * @var \WalmartDSV\ToolsType\EngineDisplacementAType $engineDisplacement
     */
    private $engineDisplacement = null;

    /**
     * Measurement of the power of the device. Significance of horsepower varies with type of product. For example, for products with engines, horsepower is a general indicator of an engine's size and power (along with other engine specifications of engine displacement and torque). Horsepower is also used as common rating on electric motors and products that contain them.
     *
     * @var \WalmartDSV\ToolsType\HorsepowerAType $horsepower
     */
    private $horsepower = null;

    /**
     * Measurement, expressed as decibels, of the intensity of sound volume a device produces. Important selection criteria for consumers concerned with effects of products that generate loud noise levels. Attribute applied to such products as power tools and security alarms.
     *
     * @var \WalmartDSV\ToolsType\DecibelRatingAType $decibelRating
     */
    private $decibelRating = null;

    /**
     * The maximum internal pressure that the product is designed to contain and/or control.
     *
     * @var \WalmartDSV\ToolsType\MaximumAirPressureAType $maximumAirPressure
     */
    private $maximumAirPressure = null;

    /**
     * Overall power rating as specified by the manufacturer, expressed as MWO. Used primarily for power tools.
     *
     * @var \WalmartDSV\ToolsType\MaximumWattsOutAType $maximumWattsOut
     */
    private $maximumWattsOut = null;

    /**
     * Measurement of turning or twisting force as measured in foot pounds (ft-lbs). Significance varies with product. For example, for torque wrenches, torque value helps consumers select the correct torque wrench for tightening a specific nut or bolt. If item has an engine, torque is a general indicator of an engine's pulling power.
     *
     * @var float $torque
     */
    private $torque = null;

    /**
     * Speed of the sander expressed in orbits per minute or surface feet per minute.
     *
     * @var \WalmartDSV\ToolsType\SandingSpeedAType $sandingSpeed
     */
    private $sandingSpeed = null;

    /**
     * The maximum speed the product can reach without a load. For example, the maximum speed, expressed in rpm, a cordless drill driver can reach when it's not driving screws or drilling holes.
     *
     * @var \WalmartDSV\ToolsType\NoLoadSpeedAType $noLoadSpeed
     */
    private $noLoadSpeed = null;

    /**
     * Measurement describing how far a component travels in one direction in each movement cycle. When applied to power tools, allows selection based on intended use. For example, for reciprocating saws, a shorter stroke length allows for better control in tight areas, while a longer stroke length provides more aggressive cutting action for heavy demolition.
     *
     * @var \WalmartDSV\ToolsType\StrokeLengthAType $strokeLength
     */
    private $strokeLength = null;

    /**
     * Number of stokes per minute. Attribute typically applied to power tools as a measure of speed, and allows selection based on intended use. For example, for jig saws, deep cuts in dense hardwoods require high speed, while hard steel demands much slower blade stokes per minute.
     *
     * @var string $strokesPerMinute
     */
    private $strokesPerMinute = null;

    /**
     * The number of blows per minute a tool, such as a hammer drill, is able to produce.
     *
     * @var string $blowsPerMinute
     */
    private $blowsPerMinute = null;

    /**
     * Measurement of force applied by the item, expressed in joules. Used as selection factor for tools such as impact wrenches.
     *
     * @var \WalmartDSV\ToolsType\ImpactEnergyAType $impactEnergy
     */
    private $impactEnergy = null;

    /**
     * The maximum load, expressed in psi, that the product is designed to have applied to it.
     *
     * @var \WalmartDSV\ToolsType\LoadCapacityAType $loadCapacity
     */
    private $loadCapacity = null;

    /**
     * The volume of space available in this item to contain objects.
     *
     * @var \WalmartDSV\ToolsType\VolumeCapacityAType $volumeCapacity
     */
    private $volumeCapacity = null;

    /**
     * Number of teeth per inch of the item. Important selection factor for saw blades. For example, more TPI gives a smoother cut but requires a slower saw cut, fewer TPI allows a faster cut with a slightly rougher finish.
     *
     * @var int $teethPerInch
     */
    private $teethPerInch = null;

    /**
     * Measurement of the widest opening with the jaw of the product in its widest position. Typically used for tools such as an adjustable wrench, or vise.
     *
     * @var \WalmartDSV\ToolsType\MaximumJawOpeningAType $maximumJawOpening
     */
    private $maximumJawOpening = null;

    /**
     * The type of arrangement of supply tanks associated with a tool such as an air compressor.
     *
     * @var string $tankConfiguration
     */
    private $tankConfiguration = null;

    /**
     * @var \WalmartDSV\ToolsType\SwatchImagesAType\SwatchImageAType[] $swatchImages
     */
    private $swatchImages = null;

    /**
     * Adds as additionalVariantAttributeName
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @return self
     * @param string $additionalVariantAttributeName
     */
    public function addToAdditionalVariantAttributeNames($additionalVariantAttributeName)
    {
        $this->additionalVariantAttributeNames[] = $additionalVariantAttributeName;
        return $this;
    }

    /**
     * isset additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAdditionalVariantAttributeNames($index)
    {
        return isset($this->additionalVariantAttributeNames[$index]);
    }

    /**
     * unset additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAdditionalVariantAttributeNames($index)
    {
        unset($this->additionalVariantAttributeNames[$index]);
    }

    /**
     * Gets as additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @return string[]
     */
    public function getAdditionalVariantAttributeNames()
    {
        return $this->additionalVariantAttributeNames;
    }

    /**
     * Sets a new additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param string $additionalVariantAttributeNames
     * @return self
     */
    public function setAdditionalVariantAttributeNames(array $additionalVariantAttributeNames)
    {
        $this->additionalVariantAttributeNames = $additionalVariantAttributeNames;
        return $this;
    }

    /**
     * Gets as shortDescription
     *
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @return string
     */
    public function getShortDescription()
    {
        return $this->shortDescription;
    }

    /**
     * Sets a new shortDescription
     *
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @param string $shortDescription
     * @return self
     */
    public function setShortDescription($shortDescription)
    {
        $this->shortDescription = $shortDescription;
        return $this;
    }

    /**
     * Adds as keyFeaturesValue
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @return self
     * @param string $keyFeaturesValue
     */
    public function addToKeyFeatures($keyFeaturesValue)
    {
        $this->keyFeatures[] = $keyFeaturesValue;
        return $this;
    }

    /**
     * isset keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetKeyFeatures($index)
    {
        return isset($this->keyFeatures[$index]);
    }

    /**
     * unset keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetKeyFeatures($index)
    {
        unset($this->keyFeatures[$index]);
    }

    /**
     * Gets as keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @return string[]
     */
    public function getKeyFeatures()
    {
        return $this->keyFeatures;
    }

    /**
     * Sets a new keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param string $keyFeatures
     * @return self
     */
    public function setKeyFeatures(array $keyFeatures)
    {
        $this->keyFeatures = $keyFeatures;
        return $this;
    }

    /**
     * Gets as brand
     *
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @return string
     */
    public function getBrand()
    {
        return $this->brand;
    }

    /**
     * Sets a new brand
     *
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @param string $brand
     * @return self
     */
    public function setBrand($brand)
    {
        $this->brand = $brand;
        return $this;
    }

    /**
     * Gets as manufacturer
     *
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @return string
     */
    public function getManufacturer()
    {
        return $this->manufacturer;
    }

    /**
     * Sets a new manufacturer
     *
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @param string $manufacturer
     * @return self
     */
    public function setManufacturer($manufacturer)
    {
        $this->manufacturer = $manufacturer;
        return $this;
    }

    /**
     * Gets as manufacturerPartNumber
     *
     * MPN uniquely identifies the product to its manufacturer. For many products this will be identical to the model number. Some manufacturers distinguish part number from model number. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @return string
     */
    public function getManufacturerPartNumber()
    {
        return $this->manufacturerPartNumber;
    }

    /**
     * Sets a new manufacturerPartNumber
     *
     * MPN uniquely identifies the product to its manufacturer. For many products this will be identical to the model number. Some manufacturers distinguish part number from model number. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @param string $manufacturerPartNumber
     * @return self
     */
    public function setManufacturerPartNumber($manufacturerPartNumber)
    {
        $this->manufacturerPartNumber = $manufacturerPartNumber;
        return $this;
    }

    /**
     * Gets as modelNumber
     *
     * Model numbers allow manufacturers to keep track of each hardware device and identify or replace the proper part when needed. Model numbers are often found on the bottom, back, or side of a product. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @return string
     */
    public function getModelNumber()
    {
        return $this->modelNumber;
    }

    /**
     * Sets a new modelNumber
     *
     * Model numbers allow manufacturers to keep track of each hardware device and identify or replace the proper part when needed. Model numbers are often found on the bottom, back, or side of a product. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @param string $modelNumber
     * @return self
     */
    public function setModelNumber($modelNumber)
    {
        $this->modelNumber = $modelNumber;
        return $this;
    }

    /**
     * Gets as inflexKitComponent
     *
     * If this is a component of a sellable item, select "Yes". Once you have set up all of the components of your sellable unit, reach out to your Walmart.com Merchant and ask them to group all of the item components together. Inflexible Kits must be configured and published by your Walmart Merchant.
     *
     * @return string
     */
    public function getInflexKitComponent()
    {
        return $this->inflexKitComponent;
    }

    /**
     * Sets a new inflexKitComponent
     *
     * If this is a component of a sellable item, select "Yes". Once you have set up all of the components of your sellable unit, reach out to your Walmart.com Merchant and ask them to group all of the item components together. Inflexible Kits must be configured and published by your Walmart Merchant.
     *
     * @param string $inflexKitComponent
     * @return self
     */
    public function setInflexKitComponent($inflexKitComponent)
    {
        $this->inflexKitComponent = $inflexKitComponent;
        return $this;
    }

    /**
     * Gets as multipackQuantity
     *
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @return int
     */
    public function getMultipackQuantity()
    {
        return $this->multipackQuantity;
    }

    /**
     * Sets a new multipackQuantity
     *
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @param int $multipackQuantity
     * @return self
     */
    public function setMultipackQuantity($multipackQuantity)
    {
        $this->multipackQuantity = $multipackQuantity;
        return $this;
    }

    /**
     * Gets as countPerPack
     *
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @return int
     */
    public function getCountPerPack()
    {
        return $this->countPerPack;
    }

    /**
     * Sets a new countPerPack
     *
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @param int $countPerPack
     * @return self
     */
    public function setCountPerPack($countPerPack)
    {
        $this->countPerPack = $countPerPack;
        return $this;
    }

    /**
     * Gets as count
     *
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @return string
     */
    public function getCount()
    {
        return $this->count;
    }

    /**
     * Sets a new count
     *
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @param string $count
     * @return self
     */
    public function setCount($count)
    {
        $this->count = $count;
        return $this;
    }

    /**
     * Gets as pieceCount
     *
     * The number of small pieces, slices, or different items within the product. Piece Count applies to things such as puzzles, building block sets, and products that contain multiple different items (such as tool sets, dinnerware sets, gift baskets, art sets, makeup kits, or shaving kits). EXAMPLE: (1) A 500-piece puzzle has a "Piece Count" of 500. (2) A 105-Piece Socket Wrench set has a piece count of "105." (3) A gift basket of 5 different items has a "Piece Count" of 5.
     *
     * @return int
     */
    public function getPieceCount()
    {
        return $this->pieceCount;
    }

    /**
     * Sets a new pieceCount
     *
     * The number of small pieces, slices, or different items within the product. Piece Count applies to things such as puzzles, building block sets, and products that contain multiple different items (such as tool sets, dinnerware sets, gift baskets, art sets, makeup kits, or shaving kits). EXAMPLE: (1) A 500-piece puzzle has a "Piece Count" of 500. (2) A 105-Piece Socket Wrench set has a piece count of "105." (3) A gift basket of 5 different items has a "Piece Count" of 5.
     *
     * @param int $pieceCount
     * @return self
     */
    public function setPieceCount($pieceCount)
    {
        $this->pieceCount = $pieceCount;
        return $this;
    }

    /**
     * Gets as mainImageUrl
     *
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return string
     */
    public function getMainImageUrl()
    {
        return $this->mainImageUrl;
    }

    /**
     * Sets a new mainImageUrl
     *
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param string $mainImageUrl
     * @return self
     */
    public function setMainImageUrl($mainImageUrl)
    {
        $this->mainImageUrl = $mainImageUrl;
        return $this;
    }

    /**
     * Adds as productSecondaryImageURLValue
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return self
     * @param string $productSecondaryImageURLValue
     */
    public function addToProductSecondaryImageURL($productSecondaryImageURLValue)
    {
        $this->productSecondaryImageURL[] = $productSecondaryImageURLValue;
        return $this;
    }

    /**
     * isset productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetProductSecondaryImageURL($index)
    {
        return isset($this->productSecondaryImageURL[$index]);
    }

    /**
     * unset productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetProductSecondaryImageURL($index)
    {
        unset($this->productSecondaryImageURL[$index]);
    }

    /**
     * Gets as productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return string[]
     */
    public function getProductSecondaryImageURL()
    {
        return $this->productSecondaryImageURL;
    }

    /**
     * Sets a new productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param string $productSecondaryImageURL
     * @return self
     */
    public function setProductSecondaryImageURL(array $productSecondaryImageURL)
    {
        $this->productSecondaryImageURL = $productSecondaryImageURL;
        return $this;
    }

    /**
     * Gets as material
     *
     * The main material(s) that a product is made of. This does not need to be an exhaustive list, but should contain the predominant or functionally important material/materials. Fabric material specifics should be entered using the "Fabric Content" attribute.
     *
     * @return \WalmartDSV\MaterialType
     */
    public function getMaterial()
    {
        return $this->material;
    }

    /**
     * Sets a new material
     *
     * The main material(s) that a product is made of. This does not need to be an exhaustive list, but should contain the predominant or functionally important material/materials. Fabric material specifics should be entered using the "Fabric Content" attribute.
     *
     * @param \WalmartDSV\MaterialType $material
     * @return self
     */
    public function setMaterial(\WalmartDSV\MaterialType $material)
    {
        $this->material = $material;
        return $this;
    }

    /**
     * Gets as shape
     *
     * Physical shape of the item. Used in a wide variety of products including rugs, toys and large appliances.
     *
     * @return string
     */
    public function getShape()
    {
        return $this->shape;
    }

    /**
     * Sets a new shape
     *
     * Physical shape of the item. Used in a wide variety of products including rugs, toys and large appliances.
     *
     * @param string $shape
     * @return self
     */
    public function setShape($shape)
    {
        $this->shape = $shape;
        return $this;
    }

    /**
     * Gets as size
     *
     * Overall dimensions of an item. Used only for products that do not already have a more specific 'x size' attribute, such as ring size or clothing size.
     *
     * @return string
     */
    public function getSize()
    {
        return $this->size;
    }

    /**
     * Sets a new size
     *
     * Overall dimensions of an item. Used only for products that do not already have a more specific 'x size' attribute, such as ring size or clothing size.
     *
     * @param string $size
     * @return self
     */
    public function setSize($size)
    {
        $this->size = $size;
        return $this;
    }

    /**
     * Adds as globalBrandLicenseValue
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @return self
     * @param string $globalBrandLicenseValue
     */
    public function addToGlobalBrandLicense($globalBrandLicenseValue)
    {
        $this->globalBrandLicense[] = $globalBrandLicenseValue;
        return $this;
    }

    /**
     * isset globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @param int|string $index
     * @return bool
     */
    public function issetGlobalBrandLicense($index)
    {
        return isset($this->globalBrandLicense[$index]);
    }

    /**
     * unset globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @param int|string $index
     * @return void
     */
    public function unsetGlobalBrandLicense($index)
    {
        unset($this->globalBrandLicense[$index]);
    }

    /**
     * Gets as globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @return string[]
     */
    public function getGlobalBrandLicense()
    {
        return $this->globalBrandLicense;
    }

    /**
     * Sets a new globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @param string $globalBrandLicense
     * @return self
     */
    public function setGlobalBrandLicense(array $globalBrandLicense)
    {
        $this->globalBrandLicense = $globalBrandLicense;
        return $this;
    }

    /**
     * Gets as powerType
     *
     * Provides information on the exact type of power used by the item.
     *
     * @return string
     */
    public function getPowerType()
    {
        return $this->powerType;
    }

    /**
     * Sets a new powerType
     *
     * Provides information on the exact type of power used by the item.
     *
     * @param string $powerType
     * @return self
     */
    public function setPowerType($powerType)
    {
        $this->powerType = $powerType;
        return $this;
    }

    /**
     * Gets as fuelType
     *
     * The kind of material that a product consumes to produce heat or power. Attribute is also used to classify a fuel product.
     *
     * @return string
     */
    public function getFuelType()
    {
        return $this->fuelType;
    }

    /**
     * Sets a new fuelType
     *
     * The kind of material that a product consumes to produce heat or power. Attribute is also used to classify a fuel product.
     *
     * @param string $fuelType
     * @return self
     */
    public function setFuelType($fuelType)
    {
        $this->fuelType = $fuelType;
        return $this;
    }

    /**
     * Gets as bladeDiameter
     *
     * The measure of the blade at the widest point. For circular blades, measured from the furthest edge of one tip to the furthest edge of the tip directly opposite. Important selection criteria for consumers for compatibility and use. For example, handheld circular saws accept blades that range from 4 ½ - 7 ¼ inches in diameter.
     *
     * @return \WalmartDSV\ToolsType\BladeDiameterAType
     */
    public function getBladeDiameter()
    {
        return $this->bladeDiameter;
    }

    /**
     * Sets a new bladeDiameter
     *
     * The measure of the blade at the widest point. For circular blades, measured from the furthest edge of one tip to the furthest edge of the tip directly opposite. Important selection criteria for consumers for compatibility and use. For example, handheld circular saws accept blades that range from 4 ½ - 7 ¼ inches in diameter.
     *
     * @param \WalmartDSV\ToolsType\BladeDiameterAType $bladeDiameter
     * @return self
     */
    public function setBladeDiameter(\WalmartDSV\ToolsType\BladeDiameterAType $bladeDiameter)
    {
        $this->bladeDiameter = $bladeDiameter;
        return $this;
    }

    /**
     * Gets as bladeLength
     *
     * The measurement of the length of the blade from the tip to base. For most knives the base would be the forward-most aspect of the handle. Used for products such as knives, scissors, and planers.
     *
     * @return \WalmartDSV\ToolsType\BladeLengthAType
     */
    public function getBladeLength()
    {
        return $this->bladeLength;
    }

    /**
     * Sets a new bladeLength
     *
     * The measurement of the length of the blade from the tip to base. For most knives the base would be the forward-most aspect of the handle. Used for products such as knives, scissors, and planers.
     *
     * @param \WalmartDSV\ToolsType\BladeLengthAType $bladeLength
     * @return self
     */
    public function setBladeLength(\WalmartDSV\ToolsType\BladeLengthAType $bladeLength)
    {
        $this->bladeLength = $bladeLength;
        return $this;
    }

    /**
     * Gets as bladeShank
     *
     * The type of design of the base of a blade used to attach the blade. Important selection criteria for compatibility between blades and tools. For example, some jigsaws are designed to accept two or more blade shank styles, while others can only accept one style.
     *
     * @return string
     */
    public function getBladeShank()
    {
        return $this->bladeShank;
    }

    /**
     * Sets a new bladeShank
     *
     * The type of design of the base of a blade used to attach the blade. Important selection criteria for compatibility between blades and tools. For example, some jigsaws are designed to accept two or more blade shank styles, while others can only accept one style.
     *
     * @param string $bladeShank
     * @return self
     */
    public function setBladeShank($bladeShank)
    {
        $this->bladeShank = $bladeShank;
        return $this;
    }

    /**
     * Gets as shankSize
     *
     * Overall dimensions of the end piece or shaft portion of the item. In the case of tools, used to select items based on fit. Specific expression of shank size varies with item. For example, for twist drill bits, shank size is the diameter of the shank designed to fit in the chuck of a drill
     *
     * @return \WalmartDSV\ToolsType\ShankSizeAType
     */
    public function getShankSize()
    {
        return $this->shankSize;
    }

    /**
     * Sets a new shankSize
     *
     * Overall dimensions of the end piece or shaft portion of the item. In the case of tools, used to select items based on fit. Specific expression of shank size varies with item. For example, for twist drill bits, shank size is the diameter of the shank designed to fit in the chuck of a drill
     *
     * @param \WalmartDSV\ToolsType\ShankSizeAType $shankSize
     * @return self
     */
    public function setShankSize(\WalmartDSV\ToolsType\ShankSizeAType $shankSize)
    {
        $this->shankSize = $shankSize;
        return $this;
    }

    /**
     * Gets as chuckType
     *
     * The industry term that describes the design of the part of the device that holds the bit. Certain chuck types can be adjusted to accommodate different sizes of bits.
     *
     * @return string
     */
    public function getChuckType()
    {
        return $this->chuckType;
    }

    /**
     * Sets a new chuckType
     *
     * The industry term that describes the design of the part of the device that holds the bit. Certain chuck types can be adjusted to accommodate different sizes of bits.
     *
     * @param string $chuckType
     * @return self
     */
    public function setChuckType($chuckType)
    {
        $this->chuckType = $chuckType;
        return $this;
    }

    /**
     * Gets as chuckSize
     *
     * The measurement of the part of the device that holds the bit and indicates the maximum diameter bit shank that fits the device. Products that have chucks include power drills, lathes, and scroll saws.
     *
     * @return \WalmartDSV\ToolsType\ChuckSizeAType
     */
    public function getChuckSize()
    {
        return $this->chuckSize;
    }

    /**
     * Sets a new chuckSize
     *
     * The measurement of the part of the device that holds the bit and indicates the maximum diameter bit shank that fits the device. Products that have chucks include power drills, lathes, and scroll saws.
     *
     * @param \WalmartDSV\ToolsType\ChuckSizeAType $chuckSize
     * @return self
     */
    public function setChuckSize(\WalmartDSV\ToolsType\ChuckSizeAType $chuckSize)
    {
        $this->chuckSize = $chuckSize;
        return $this;
    }

    /**
     * Gets as arborDiameter
     *
     * Measurement of the diameter of shaft or spindle that inserts into and holds an attachment such as a saw blade or grinder wheel. As applied machine tool attachments, describes the arbor hole size and is used to identify machine/attachment fit. For example, a table saw that has a 5/8 inch arbor would accommodate a saw blade with a 5/8 inch arbor opening.
     *
     * @return \WalmartDSV\ToolsType\ArborDiameterAType
     */
    public function getArborDiameter()
    {
        return $this->arborDiameter;
    }

    /**
     * Sets a new arborDiameter
     *
     * Measurement of the diameter of shaft or spindle that inserts into and holds an attachment such as a saw blade or grinder wheel. As applied machine tool attachments, describes the arbor hole size and is used to identify machine/attachment fit. For example, a table saw that has a 5/8 inch arbor would accommodate a saw blade with a 5/8 inch arbor opening.
     *
     * @param \WalmartDSV\ToolsType\ArborDiameterAType $arborDiameter
     * @return self
     */
    public function setArborDiameter(\WalmartDSV\ToolsType\ArborDiameterAType $arborDiameter)
    {
        $this->arborDiameter = $arborDiameter;
        return $this;
    }

    /**
     * Gets as colletSize
     *
     * The size of the opening of the collar of the collet, which is designed to hold a tool attachment. Collets are commonly found on wood routers, precision grinders and lathes. Attribute also important in selecting compatible tool attachments.
     *
     * @return \WalmartDSV\ToolsType\ColletSizeAType
     */
    public function getColletSize()
    {
        return $this->colletSize;
    }

    /**
     * Sets a new colletSize
     *
     * The size of the opening of the collar of the collet, which is designed to hold a tool attachment. Collets are commonly found on wood routers, precision grinders and lathes. Attribute also important in selecting compatible tool attachments.
     *
     * @param \WalmartDSV\ToolsType\ColletSizeAType $colletSize
     * @return self
     */
    public function setColletSize(\WalmartDSV\ToolsType\ColletSizeAType $colletSize)
    {
        $this->colletSize = $colletSize;
        return $this;
    }

    /**
     * Gets as spindleThread
     *
     * For tools that have spindles, the design and sizing specifications. For example, the thread measurement of the chuck backplate adapter.
     *
     * @return string
     */
    public function getSpindleThread()
    {
        return $this->spindleThread;
    }

    /**
     * Sets a new spindleThread
     *
     * For tools that have spindles, the design and sizing specifications. For example, the thread measurement of the chuck backplate adapter.
     *
     * @param string $spindleThread
     * @return self
     */
    public function setSpindleThread($spindleThread)
    {
        $this->spindleThread = $spindleThread;
        return $this;
    }

    /**
     * Gets as discSize
     *
     * Measurement of the diameter of the disc portion of a tool attachment. Typically used to describe sanding discs and attachments for power tools.
     *
     * @return \WalmartDSV\ToolsType\DiscSizeAType
     */
    public function getDiscSize()
    {
        return $this->discSize;
    }

    /**
     * Sets a new discSize
     *
     * Measurement of the diameter of the disc portion of a tool attachment. Typically used to describe sanding discs and attachments for power tools.
     *
     * @param \WalmartDSV\ToolsType\DiscSizeAType $discSize
     * @return self
     */
    public function setDiscSize(\WalmartDSV\ToolsType\DiscSizeAType $discSize)
    {
        $this->discSize = $discSize;
        return $this;
    }

    /**
     * Gets as sandingBeltSize
     *
     * The measurement of the width and length of a sandpaper belt. Used to select the correct size to fit a specific belt sander.
     *
     * @return string
     */
    public function getSandingBeltSize()
    {
        return $this->sandingBeltSize;
    }

    /**
     * Sets a new sandingBeltSize
     *
     * The measurement of the width and length of a sandpaper belt. Used to select the correct size to fit a specific belt sander.
     *
     * @param string $sandingBeltSize
     * @return self
     */
    public function setSandingBeltSize($sandingBeltSize)
    {
        $this->sandingBeltSize = $sandingBeltSize;
        return $this;
    }

    /**
     * Gets as airInlet
     *
     * Measurement of the diameter of the air intake aperture, measured at its widest point. Attribute used for tools such as air inlet hoses, air impact wrenches, and air compressors.
     *
     * @return \WalmartDSV\ToolsType\AirInletAType
     */
    public function getAirInlet()
    {
        return $this->airInlet;
    }

    /**
     * Sets a new airInlet
     *
     * Measurement of the diameter of the air intake aperture, measured at its widest point. Attribute used for tools such as air inlet hoses, air impact wrenches, and air compressors.
     *
     * @param \WalmartDSV\ToolsType\AirInletAType $airInlet
     * @return self
     */
    public function setAirInlet(\WalmartDSV\ToolsType\AirInletAType $airInlet)
    {
        $this->airInlet = $airInlet;
        return $this;
    }

    /**
     * Gets as averageAirConsumptionAt90PSI
     *
     * Rate of airflow the product requires at the given pressure of 90 pounds per square inch. Used for products such as air brushes, framing nailers, and jackhammers. Important criteria that allows consumers to select items that are compatible with their air-delivery system. For example, a ½ʺ impact wrench typically requires 5 CFM at 90 psi.
     *
     * @return \WalmartDSV\ToolsType\AverageAirConsumptionAt90PSIAType
     */
    public function getAverageAirConsumptionAt90PSI()
    {
        return $this->averageAirConsumptionAt90PSI;
    }

    /**
     * Sets a new averageAirConsumptionAt90PSI
     *
     * Rate of airflow the product requires at the given pressure of 90 pounds per square inch. Used for products such as air brushes, framing nailers, and jackhammers. Important criteria that allows consumers to select items that are compatible with their air-delivery system. For example, a ½ʺ impact wrench typically requires 5 CFM at 90 psi.
     *
     * @param \WalmartDSV\ToolsType\AverageAirConsumptionAt90PSIAType $averageAirConsumptionAt90PSI
     * @return self
     */
    public function setAverageAirConsumptionAt90PSI(\WalmartDSV\ToolsType\AverageAirConsumptionAt90PSIAType $averageAirConsumptionAt90PSI)
    {
        $this->averageAirConsumptionAt90PSI = $averageAirConsumptionAt90PSI;
        return $this;
    }

    /**
     * Gets as cfmAt40Psi
     *
     * The flow rate produced by the item. Expressed in cubic feet per minute, as measured at the pressure of 40 pounds per inch. Allows consumers to select products such as air compressors to accommodate specific tools and applications.
     *
     * @return \WalmartDSV\ToolsType\CfmAt40PsiAType
     */
    public function getCfmAt40Psi()
    {
        return $this->cfmAt40Psi;
    }

    /**
     * Sets a new cfmAt40Psi
     *
     * The flow rate produced by the item. Expressed in cubic feet per minute, as measured at the pressure of 40 pounds per inch. Allows consumers to select products such as air compressors to accommodate specific tools and applications.
     *
     * @param \WalmartDSV\ToolsType\CfmAt40PsiAType $cfmAt40Psi
     * @return self
     */
    public function setCfmAt40Psi(\WalmartDSV\ToolsType\CfmAt40PsiAType $cfmAt40Psi)
    {
        $this->cfmAt40Psi = $cfmAt40Psi;
        return $this;
    }

    /**
     * Gets as cfmAt90Psi
     *
     * The airflow rate produced by the item. Expressed in cubic feet per minute, as measured at the pressure of 90 pounds per inch. Allows consumers to select products such as air compressors to accommodate specific tools and applications.
     *
     * @return \WalmartDSV\ToolsType\CfmAt90PsiAType
     */
    public function getCfmAt90Psi()
    {
        return $this->cfmAt90Psi;
    }

    /**
     * Sets a new cfmAt90Psi
     *
     * The airflow rate produced by the item. Expressed in cubic feet per minute, as measured at the pressure of 90 pounds per inch. Allows consumers to select products such as air compressors to accommodate specific tools and applications.
     *
     * @param \WalmartDSV\ToolsType\CfmAt90PsiAType $cfmAt90Psi
     * @return self
     */
    public function setCfmAt90Psi(\WalmartDSV\ToolsType\CfmAt90PsiAType $cfmAt90Psi)
    {
        $this->cfmAt90Psi = $cfmAt90Psi;
        return $this;
    }

    /**
     * Gets as volts
     *
     * @return \WalmartDSV\ToolsType\VoltsAType
     */
    public function getVolts()
    {
        return $this->volts;
    }

    /**
     * Sets a new volts
     *
     * @param \WalmartDSV\ToolsType\VoltsAType $volts
     * @return self
     */
    public function setVolts(\WalmartDSV\ToolsType\VoltsAType $volts)
    {
        $this->volts = $volts;
        return $this;
    }

    /**
     * Gets as amps
     *
     * Number of amps as a measure of electrical current draw. For products such as appliances, amps are usually specified as a peak value to help consumers select items that not overload household circuits. Also used as a measure of capacity (trip level) for electrical products such as circuit breakers and fuses
     *
     * @return \WalmartDSV\ToolsType\AmpsAType
     */
    public function getAmps()
    {
        return $this->amps;
    }

    /**
     * Sets a new amps
     *
     * Number of amps as a measure of electrical current draw. For products such as appliances, amps are usually specified as a peak value to help consumers select items that not overload household circuits. Also used as a measure of capacity (trip level) for electrical products such as circuit breakers and fuses
     *
     * @param \WalmartDSV\ToolsType\AmpsAType $amps
     * @return self
     */
    public function setAmps(\WalmartDSV\ToolsType\AmpsAType $amps)
    {
        $this->amps = $amps;
        return $this;
    }

    /**
     * Gets as assembledProductLength
     *
     * The length of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @return \WalmartDSV\ToolsType\AssembledProductLengthAType
     */
    public function getAssembledProductLength()
    {
        return $this->assembledProductLength;
    }

    /**
     * Sets a new assembledProductLength
     *
     * The length of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @param \WalmartDSV\ToolsType\AssembledProductLengthAType $assembledProductLength
     * @return self
     */
    public function setAssembledProductLength(\WalmartDSV\ToolsType\AssembledProductLengthAType $assembledProductLength)
    {
        $this->assembledProductLength = $assembledProductLength;
        return $this;
    }

    /**
     * Gets as assembledProductWidth
     *
     * The width of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @return \WalmartDSV\ToolsType\AssembledProductWidthAType
     */
    public function getAssembledProductWidth()
    {
        return $this->assembledProductWidth;
    }

    /**
     * Sets a new assembledProductWidth
     *
     * The width of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @param \WalmartDSV\ToolsType\AssembledProductWidthAType $assembledProductWidth
     * @return self
     */
    public function setAssembledProductWidth(\WalmartDSV\ToolsType\AssembledProductWidthAType $assembledProductWidth)
    {
        $this->assembledProductWidth = $assembledProductWidth;
        return $this;
    }

    /**
     * Gets as assembledProductHeight
     *
     * The height of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @return \WalmartDSV\ToolsType\AssembledProductHeightAType
     */
    public function getAssembledProductHeight()
    {
        return $this->assembledProductHeight;
    }

    /**
     * Sets a new assembledProductHeight
     *
     * The height of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @param \WalmartDSV\ToolsType\AssembledProductHeightAType $assembledProductHeight
     * @return self
     */
    public function setAssembledProductHeight(\WalmartDSV\ToolsType\AssembledProductHeightAType $assembledProductHeight)
    {
        $this->assembledProductHeight = $assembledProductHeight;
        return $this;
    }

    /**
     * Gets as assembledProductWeight
     *
     * The weight of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @return \WalmartDSV\ToolsType\AssembledProductWeightAType
     */
    public function getAssembledProductWeight()
    {
        return $this->assembledProductWeight;
    }

    /**
     * Sets a new assembledProductWeight
     *
     * The weight of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @param \WalmartDSV\ToolsType\AssembledProductWeightAType $assembledProductWeight
     * @return self
     */
    public function setAssembledProductWeight(\WalmartDSV\ToolsType\AssembledProductWeightAType $assembledProductWeight)
    {
        $this->assembledProductWeight = $assembledProductWeight;
        return $this;
    }

    /**
     * Gets as variantGroupId
     *
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @return string
     */
    public function getVariantGroupId()
    {
        return $this->variantGroupId;
    }

    /**
     * Sets a new variantGroupId
     *
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @param string $variantGroupId
     * @return self
     */
    public function setVariantGroupId($variantGroupId)
    {
        $this->variantGroupId = $variantGroupId;
        return $this;
    }

    /**
     * Adds as variantAttributeName
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @return self
     * @param string $variantAttributeName
     */
    public function addToVariantAttributeNames($variantAttributeName)
    {
        $this->variantAttributeNames[] = $variantAttributeName;
        return $this;
    }

    /**
     * isset variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetVariantAttributeNames($index)
    {
        return isset($this->variantAttributeNames[$index]);
    }

    /**
     * unset variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetVariantAttributeNames($index)
    {
        unset($this->variantAttributeNames[$index]);
    }

    /**
     * Gets as variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @return string[]
     */
    public function getVariantAttributeNames()
    {
        return $this->variantAttributeNames;
    }

    /**
     * Sets a new variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param string $variantAttributeNames
     * @return self
     */
    public function setVariantAttributeNames(array $variantAttributeNames)
    {
        $this->variantAttributeNames = $variantAttributeNames;
        return $this;
    }

    /**
     * Gets as isPrimaryVariant
     *
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @return string
     */
    public function getIsPrimaryVariant()
    {
        return $this->isPrimaryVariant;
    }

    /**
     * Sets a new isPrimaryVariant
     *
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @param string $isPrimaryVariant
     * @return self
     */
    public function setIsPrimaryVariant($isPrimaryVariant)
    {
        $this->isPrimaryVariant = $isPrimaryVariant;
        return $this;
    }

    /**
     * Gets as isPrivateLabelOrUnbranded
     *
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @return string
     */
    public function getIsPrivateLabelOrUnbranded()
    {
        return $this->isPrivateLabelOrUnbranded;
    }

    /**
     * Sets a new isPrivateLabelOrUnbranded
     *
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @param string $isPrivateLabelOrUnbranded
     * @return self
     */
    public function setIsPrivateLabelOrUnbranded($isPrivateLabelOrUnbranded)
    {
        $this->isPrivateLabelOrUnbranded = $isPrivateLabelOrUnbranded;
        return $this;
    }

    /**
     * Gets as isProp65WarningRequired
     *
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @return string
     */
    public function getIsProp65WarningRequired()
    {
        return $this->isProp65WarningRequired;
    }

    /**
     * Sets a new isProp65WarningRequired
     *
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @param string $isProp65WarningRequired
     * @return self
     */
    public function setIsProp65WarningRequired($isProp65WarningRequired)
    {
        $this->isProp65WarningRequired = $isProp65WarningRequired;
        return $this;
    }

    /**
     * Gets as prop65WarningText
     *
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @return string
     */
    public function getProp65WarningText()
    {
        return $this->prop65WarningText;
    }

    /**
     * Sets a new prop65WarningText
     *
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @param string $prop65WarningText
     * @return self
     */
    public function setProp65WarningText($prop65WarningText)
    {
        $this->prop65WarningText = $prop65WarningText;
        return $this;
    }

    /**
     * Gets as hasBatteries
     *
     * "Battery or battery containing product" is defined by Company to include any item of Merchandise that is a battery or any component of Merchandise, including reusable packaging intended to stay in use with the item, containing a battery of any chemistry/ type. Mark Y if this definition applies to your product.
     *
     * @return string
     */
    public function getHasBatteries()
    {
        return $this->hasBatteries;
    }

    /**
     * Sets a new hasBatteries
     *
     * "Battery or battery containing product" is defined by Company to include any item of Merchandise that is a battery or any component of Merchandise, including reusable packaging intended to stay in use with the item, containing a battery of any chemistry/ type. Mark Y if this definition applies to your product.
     *
     * @param string $hasBatteries
     * @return self
     */
    public function setHasBatteries($hasBatteries)
    {
        $this->hasBatteries = $hasBatteries;
        return $this;
    }

    /**
     * Gets as batteryTechnologyType
     *
     * Please select the Battery Technology Type from the list provided. NOTE: If battery type is lead acid, lead acid (nonspillable), lithium ion, or lithium metal, please ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @return string
     */
    public function getBatteryTechnologyType()
    {
        return $this->batteryTechnologyType;
    }

    /**
     * Sets a new batteryTechnologyType
     *
     * Please select the Battery Technology Type from the list provided. NOTE: If battery type is lead acid, lead acid (nonspillable), lithium ion, or lithium metal, please ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @param string $batteryTechnologyType
     * @return self
     */
    public function setBatteryTechnologyType($batteryTechnologyType)
    {
        $this->batteryTechnologyType = $batteryTechnologyType;
        return $this;
    }

    /**
     * Gets as hasWarranty
     *
     * Y indicates the item comes with a warranty. If an item has a warranty, then enter EITHER the warranty URL or the warranty text in the appropriate field.
     *
     * @return string
     */
    public function getHasWarranty()
    {
        return $this->hasWarranty;
    }

    /**
     * Sets a new hasWarranty
     *
     * Y indicates the item comes with a warranty. If an item has a warranty, then enter EITHER the warranty URL or the warranty text in the appropriate field.
     *
     * @param string $hasWarranty
     * @return self
     */
    public function setHasWarranty($hasWarranty)
    {
        $this->hasWarranty = $hasWarranty;
        return $this;
    }

    /**
     * Gets as warrantyURL
     *
     * If you indicated that your item has a warranty, provide either the Warranty URL or Warranty Text. The Warranty URL is the web location of the image, PDF, or link to the manufacturer's warranty page, showing the warranty and its terms, including the duration of the warranty. URLs must begin with http:// or https:// NOTE: Please remember to update the link and/or text of the warranty as the warranty changes. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @return string
     */
    public function getWarrantyURL()
    {
        return $this->warrantyURL;
    }

    /**
     * Sets a new warrantyURL
     *
     * If you indicated that your item has a warranty, provide either the Warranty URL or Warranty Text. The Warranty URL is the web location of the image, PDF, or link to the manufacturer's warranty page, showing the warranty and its terms, including the duration of the warranty. URLs must begin with http:// or https:// NOTE: Please remember to update the link and/or text of the warranty as the warranty changes. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @param string $warrantyURL
     * @return self
     */
    public function setWarrantyURL($warrantyURL)
    {
        $this->warrantyURL = $warrantyURL;
        return $this;
    }

    /**
     * Gets as warrantyText
     *
     * If you marked Y for "Has Warranty" provide the Warranty URL or Warranty Text (the full text of the warranty terms, including what is covered by the warranty and the duration of the warranty). NOTE: please remember to update the text of your warranty as your warranty changes.
     *
     * @return string
     */
    public function getWarrantyText()
    {
        return $this->warrantyText;
    }

    /**
     * Sets a new warrantyText
     *
     * If you marked Y for "Has Warranty" provide the Warranty URL or Warranty Text (the full text of the warranty terms, including what is covered by the warranty and the duration of the warranty). NOTE: please remember to update the text of your warranty as your warranty changes.
     *
     * @param string $warrantyText
     * @return self
     */
    public function setWarrantyText($warrantyText)
    {
        $this->warrantyText = $warrantyText;
        return $this;
    }

    /**
     * Gets as hasStateRestrictions
     *
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @return string
     */
    public function getHasStateRestrictions()
    {
        return $this->hasStateRestrictions;
    }

    /**
     * Sets a new hasStateRestrictions
     *
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @param string $hasStateRestrictions
     * @return self
     */
    public function setHasStateRestrictions($hasStateRestrictions)
    {
        $this->hasStateRestrictions = $hasStateRestrictions;
        return $this;
    }

    /**
     * Adds as stateRestriction
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param \WalmartDSV\StateRestrictionType $stateRestriction
     *@return self
     */
    public function addToStateRestrictions(\WalmartDSV\StateRestrictionType $stateRestriction)
    {
        $this->stateRestrictions[] = $stateRestriction;
        return $this;
    }

    /**
     * isset stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetStateRestrictions($index)
    {
        return isset($this->stateRestrictions[$index]);
    }

    /**
     * unset stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetStateRestrictions($index)
    {
        unset($this->stateRestrictions[$index]);
    }

    /**
     * Gets as stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @return \WalmartDSV\StateRestrictionType[]
     */
    public function getStateRestrictions()
    {
        return $this->stateRestrictions;
    }

    /**
     * Sets a new stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param \WalmartDSV\StateRestrictionType[] $stateRestrictions
     * @return self
     */
    public function setStateRestrictions(array $stateRestrictions)
    {
        $this->stateRestrictions = $stateRestrictions;
        return $this;
    }

    /**
     * Gets as hasFuelContainer
     *
     * Denotes any item with an empty container that may be filled with fluids, such as fuel, CO2, propane, etc.
     *
     * @return string
     */
    public function getHasFuelContainer()
    {
        return $this->hasFuelContainer;
    }

    /**
     * Sets a new hasFuelContainer
     *
     * Denotes any item with an empty container that may be filled with fluids, such as fuel, CO2, propane, etc.
     *
     * @param string $hasFuelContainer
     * @return self
     */
    public function setHasFuelContainer($hasFuelContainer)
    {
        $this->hasFuelContainer = $hasFuelContainer;
        return $this;
    }

    /**
     * Adds as accessoriesIncludedValue
     *
     * Listing of any supplementary items that come with the product. Important information for consumers because accessories typically provide additional convenience, utility, attractiveness or safety to or for a product.
     *
     * @return self
     * @param string $accessoriesIncludedValue
     */
    public function addToAccessoriesIncluded($accessoriesIncludedValue)
    {
        $this->accessoriesIncluded[] = $accessoriesIncludedValue;
        return $this;
    }

    /**
     * isset accessoriesIncluded
     *
     * Listing of any supplementary items that come with the product. Important information for consumers because accessories typically provide additional convenience, utility, attractiveness or safety to or for a product.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAccessoriesIncluded($index)
    {
        return isset($this->accessoriesIncluded[$index]);
    }

    /**
     * unset accessoriesIncluded
     *
     * Listing of any supplementary items that come with the product. Important information for consumers because accessories typically provide additional convenience, utility, attractiveness or safety to or for a product.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAccessoriesIncluded($index)
    {
        unset($this->accessoriesIncluded[$index]);
    }

    /**
     * Gets as accessoriesIncluded
     *
     * Listing of any supplementary items that come with the product. Important information for consumers because accessories typically provide additional convenience, utility, attractiveness or safety to or for a product.
     *
     * @return string[]
     */
    public function getAccessoriesIncluded()
    {
        return $this->accessoriesIncluded;
    }

    /**
     * Sets a new accessoriesIncluded
     *
     * Listing of any supplementary items that come with the product. Important information for consumers because accessories typically provide additional convenience, utility, attractiveness or safety to or for a product.
     *
     * @param string $accessoriesIncluded
     * @return self
     */
    public function setAccessoriesIncluded(array $accessoriesIncluded)
    {
        $this->accessoriesIncluded = $accessoriesIncluded;
        return $this;
    }

    /**
     * Gets as color
     *
     * Color as described by the manufacturer.
     *
     * @return \WalmartDSV\ColorType
     */
    public function getColor()
    {
        return $this->color;
    }

    /**
     * Sets a new color
     *
     * Color as described by the manufacturer.
     *
     * @param \WalmartDSV\ColorType $color
     * @return self
     */
    public function setColor(\WalmartDSV\ColorType $color)
    {
        $this->color = $color;
        return $this;
    }

    /**
     * Adds as colorCategoryValue
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @return self
     * @param string $colorCategoryValue
     */
    public function addToColorCategory($colorCategoryValue)
    {
        $this->colorCategory[] = $colorCategoryValue;
        return $this;
    }

    /**
     * isset colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetColorCategory($index)
    {
        return isset($this->colorCategory[$index]);
    }

    /**
     * unset colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetColorCategory($index)
    {
        unset($this->colorCategory[$index]);
    }

    /**
     * Gets as colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @return string[]
     */
    public function getColorCategory()
    {
        return $this->colorCategory;
    }

    /**
     * Sets a new colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @param string $colorCategory
     * @return self
     */
    public function setColorCategory(array $colorCategory)
    {
        $this->colorCategory = $colorCategory;
        return $this;
    }

    /**
     * Gets as isWaterproof
     *
     * Y indicates that the item has been especially made to be resistant to water, to some degree.
     *
     * @return string
     */
    public function getIsWaterproof()
    {
        return $this->isWaterproof;
    }

    /**
     * Sets a new isWaterproof
     *
     * Y indicates that the item has been especially made to be resistant to water, to some degree.
     *
     * @param string $isWaterproof
     * @return self
     */
    public function setIsWaterproof($isWaterproof)
    {
        $this->isWaterproof = $isWaterproof;
        return $this;
    }

    /**
     * Gets as isIndustrial
     *
     * Indicates that an item can be used in an industrial setting or has an industrial application.
     *
     * @return string
     */
    public function getIsIndustrial()
    {
        return $this->isIndustrial;
    }

    /**
     * Sets a new isIndustrial
     *
     * Indicates that an item can be used in an industrial setting or has an industrial application.
     *
     * @param string $isIndustrial
     * @return self
     */
    public function setIsIndustrial($isIndustrial)
    {
        $this->isIndustrial = $isIndustrial;
        return $this;
    }

    /**
     * Gets as isFireResistant
     *
     * Y indicates that the item has been especially made to resist burning under certain conditions, as specified by the manufacturer. An important feature for products such as fire blankets, building materials, and safes.
     *
     * @return string
     */
    public function getIsFireResistant()
    {
        return $this->isFireResistant;
    }

    /**
     * Sets a new isFireResistant
     *
     * Y indicates that the item has been especially made to resist burning under certain conditions, as specified by the manufacturer. An important feature for products such as fire blankets, building materials, and safes.
     *
     * @param string $isFireResistant
     * @return self
     */
    public function setIsFireResistant($isFireResistant)
    {
        $this->isFireResistant = $isFireResistant;
        return $this;
    }

    /**
     * Gets as cleaningCareAndMaintenance
     *
     * Description of how the item should be cleaned and maintained.
     *
     * @return string
     */
    public function getCleaningCareAndMaintenance()
    {
        return $this->cleaningCareAndMaintenance;
    }

    /**
     * Sets a new cleaningCareAndMaintenance
     *
     * Description of how the item should be cleaned and maintained.
     *
     * @param string $cleaningCareAndMaintenance
     * @return self
     */
    public function setCleaningCareAndMaintenance($cleaningCareAndMaintenance)
    {
        $this->cleaningCareAndMaintenance = $cleaningCareAndMaintenance;
        return $this;
    }

    /**
     * Adds as recommendedUse
     *
     * Further clarification of what the item may be used for. This improves searchability when customers search for general terms like "birthday party" that do not include the names of specific items.
     *
     * @return self
     * @param string $recommendedUse
     */
    public function addToRecommendedUses($recommendedUse)
    {
        $this->recommendedUses[] = $recommendedUse;
        return $this;
    }

    /**
     * isset recommendedUses
     *
     * Further clarification of what the item may be used for. This improves searchability when customers search for general terms like "birthday party" that do not include the names of specific items.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetRecommendedUses($index)
    {
        return isset($this->recommendedUses[$index]);
    }

    /**
     * unset recommendedUses
     *
     * Further clarification of what the item may be used for. This improves searchability when customers search for general terms like "birthday party" that do not include the names of specific items.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetRecommendedUses($index)
    {
        unset($this->recommendedUses[$index]);
    }

    /**
     * Gets as recommendedUses
     *
     * Further clarification of what the item may be used for. This improves searchability when customers search for general terms like "birthday party" that do not include the names of specific items.
     *
     * @return string[]
     */
    public function getRecommendedUses()
    {
        return $this->recommendedUses;
    }

    /**
     * Sets a new recommendedUses
     *
     * Further clarification of what the item may be used for. This improves searchability when customers search for general terms like "birthday party" that do not include the names of specific items.
     *
     * @param string $recommendedUses
     * @return self
     */
    public function setRecommendedUses(array $recommendedUses)
    {
        $this->recommendedUses = $recommendedUses;
        return $this;
    }

    /**
     * Gets as numberOfBlades
     *
     * Number of blades in a multi-tool or fan.
     *
     * @return int
     */
    public function getNumberOfBlades()
    {
        return $this->numberOfBlades;
    }

    /**
     * Sets a new numberOfBlades
     *
     * Number of blades in a multi-tool or fan.
     *
     * @param int $numberOfBlades
     * @return self
     */
    public function setNumberOfBlades($numberOfBlades)
    {
        $this->numberOfBlades = $numberOfBlades;
        return $this;
    }

    /**
     * Gets as bladeWidth
     *
     * The width of a blade of a cutting or wiping tool (saws, squeegees). How the measurement is taken can vary with the product.
     *
     * @return \WalmartDSV\LengthUnitType
     */
    public function getBladeWidth()
    {
        return $this->bladeWidth;
    }

    /**
     * Sets a new bladeWidth
     *
     * The width of a blade of a cutting or wiping tool (saws, squeegees). How the measurement is taken can vary with the product.
     *
     * @param \WalmartDSV\LengthUnitType $bladeWidth
     * @return self
     */
    public function setBladeWidth(\WalmartDSV\LengthUnitType $bladeWidth)
    {
        $this->bladeWidth = $bladeWidth;
        return $this;
    }

    /**
     * Gets as lightBulbType
     *
     * Category of light bulb based on method to produce the light. Important to consumers because each type has different characteristics including bulb life, energy efficiency and color temperature. For example LED bulbs have a greater bulb life than equivalent incandescent bulbs.
     *
     * @return string
     */
    public function getLightBulbType()
    {
        return $this->lightBulbType;
    }

    /**
     * Sets a new lightBulbType
     *
     * Category of light bulb based on method to produce the light. Important to consumers because each type has different characteristics including bulb life, energy efficiency and color temperature. For example LED bulbs have a greater bulb life than equivalent incandescent bulbs.
     *
     * @param string $lightBulbType
     * @return self
     */
    public function setLightBulbType($lightBulbType)
    {
        $this->lightBulbType = $lightBulbType;
        return $this;
    }

    /**
     * Gets as gritSize
     *
     * The standard numeric designation of abrasive as provided by the manufacturer. Grit size should be in the format for the applicable standard. For example, “P40” if using the ISOS/FEPA, and “40” if using the CAMI standard. Important factor selection factor for sandpapers and abrasive disks based on indented use. A small number (20 or 40) indicates a coarse grit designed for removing deep gouges and imperfections, while a large number (250) indicates a fine grit, which is best for smoothing varnished finishes between coats.
     *
     * @return string
     */
    public function getGritSize()
    {
        return $this->gritSize;
    }

    /**
     * Sets a new gritSize
     *
     * The standard numeric designation of abrasive as provided by the manufacturer. Grit size should be in the format for the applicable standard. For example, “P40” if using the ISOS/FEPA, and “40” if using the CAMI standard. Important factor selection factor for sandpapers and abrasive disks based on indented use. A small number (20 or 40) indicates a coarse grit designed for removing deep gouges and imperfections, while a large number (250) indicates a fine grit, which is best for smoothing varnished finishes between coats.
     *
     * @param string $gritSize
     * @return self
     */
    public function setGritSize($gritSize)
    {
        $this->gritSize = $gritSize;
        return $this;
    }

    /**
     * Gets as squareDriveSize
     *
     * Standardized drive size used to hold a socket or other tooling interface.
     *
     * @return \WalmartDSV\LengthUnitType
     */
    public function getSquareDriveSize()
    {
        return $this->squareDriveSize;
    }

    /**
     * Sets a new squareDriveSize
     *
     * Standardized drive size used to hold a socket or other tooling interface.
     *
     * @param \WalmartDSV\LengthUnitType $squareDriveSize
     * @return self
     */
    public function setSquareDriveSize(\WalmartDSV\LengthUnitType $squareDriveSize)
    {
        $this->squareDriveSize = $squareDriveSize;
        return $this;
    }

    /**
     * Gets as socketDepth
     *
     * The internal, usable depth of a socket.
     *
     * @return \WalmartDSV\LengthUnitType
     */
    public function getSocketDepth()
    {
        return $this->socketDepth;
    }

    /**
     * Sets a new socketDepth
     *
     * The internal, usable depth of a socket.
     *
     * @param \WalmartDSV\LengthUnitType $socketDepth
     * @return self
     */
    public function setSocketDepth(\WalmartDSV\LengthUnitType $socketDepth)
    {
        $this->socketDepth = $socketDepth;
        return $this;
    }

    /**
     * Gets as numberOfSteps
     *
     * The number of steps product has as in a ladder.
     *
     * @return float
     */
    public function getNumberOfSteps()
    {
        return $this->numberOfSteps;
    }

    /**
     * Sets a new numberOfSteps
     *
     * The number of steps product has as in a ladder.
     *
     * @param float $numberOfSteps
     * @return self
     */
    public function setNumberOfSteps($numberOfSteps)
    {
        $this->numberOfSteps = $numberOfSteps;
        return $this;
    }

    /**
     * Gets as numberOfPoints
     *
     * The number of points within a socket wrench.
     *
     * @return float
     */
    public function getNumberOfPoints()
    {
        return $this->numberOfPoints;
    }

    /**
     * Sets a new numberOfPoints
     *
     * The number of points within a socket wrench.
     *
     * @param float $numberOfPoints
     * @return self
     */
    public function setNumberOfPoints($numberOfPoints)
    {
        $this->numberOfPoints = $numberOfPoints;
        return $this;
    }

    /**
     * Adds as feature
     *
     * List notable features of the item.
     *
     * @return self
     * @param string $feature
     */
    public function addToFeatures($feature)
    {
        $this->features[] = $feature;
        return $this;
    }

    /**
     * isset features
     *
     * List notable features of the item.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFeatures($index)
    {
        return isset($this->features[$index]);
    }

    /**
     * unset features
     *
     * List notable features of the item.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFeatures($index)
    {
        unset($this->features[$index]);
    }

    /**
     * Gets as features
     *
     * List notable features of the item.
     *
     * @return string[]
     */
    public function getFeatures()
    {
        return $this->features;
    }

    /**
     * Sets a new features
     *
     * List notable features of the item.
     *
     * @param string $features
     * @return self
     */
    public function setFeatures(array $features)
    {
        $this->features = $features;
        return $this;
    }

    /**
     * Gets as keywords
     *
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @return string
     */
    public function getKeywords()
    {
        return $this->keywords;
    }

    /**
     * Sets a new keywords
     *
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @param string $keywords
     * @return self
     */
    public function setKeywords($keywords)
    {
        $this->keywords = $keywords;
        return $this;
    }

    /**
     * Gets as handing
     *
     * The primary, or dominant hand for which the device is designed. Example products include left-handed scissors and circular power saws.
     *
     * @return string
     */
    public function getHanding()
    {
        return $this->handing;
    }

    /**
     * Sets a new handing
     *
     * The primary, or dominant hand for which the device is designed. Example products include left-handed scissors and circular power saws.
     *
     * @param string $handing
     * @return self
     */
    public function setHanding($handing)
    {
        $this->handing = $handing;
        return $this;
    }

    /**
     * Gets as finish
     *
     * Terms describing the overall external treatment applied to the item. Typically finishes give a distinct appearance, texture or additional performance to the item. This attribute is used in a wide variety products and materials including wood, metal and fabric.
     *
     * @return string
     */
    public function getFinish()
    {
        return $this->finish;
    }

    /**
     * Sets a new finish
     *
     * Terms describing the overall external treatment applied to the item. Typically finishes give a distinct appearance, texture or additional performance to the item. This attribute is used in a wide variety products and materials including wood, metal and fabric.
     *
     * @param string $finish
     * @return self
     */
    public function setFinish($finish)
    {
        $this->finish = $finish;
        return $this;
    }

    /**
     * Gets as cordLength
     *
     * Measurement of the length of cord that comes with the item. For electrical appliances the cord is typically a power cord. For other products, headphones for example, the cord would connect to the receiver.
     *
     * @return \WalmartDSV\ToolsType\CordLengthAType
     */
    public function getCordLength()
    {
        return $this->cordLength;
    }

    /**
     * Sets a new cordLength
     *
     * Measurement of the length of cord that comes with the item. For electrical appliances the cord is typically a power cord. For other products, headphones for example, the cord would connect to the receiver.
     *
     * @param \WalmartDSV\ToolsType\CordLengthAType $cordLength
     * @return self
     */
    public function setCordLength(\WalmartDSV\ToolsType\CordLengthAType $cordLength)
    {
        $this->cordLength = $cordLength;
        return $this;
    }

    /**
     * Gets as batteryCapacity
     *
     * The measure of the current a battery can deliver over time, expressed in ampere-hours (Ah).
     *
     * @return \WalmartDSV\ToolsType\BatteryCapacityAType
     */
    public function getBatteryCapacity()
    {
        return $this->batteryCapacity;
    }

    /**
     * Sets a new batteryCapacity
     *
     * The measure of the current a battery can deliver over time, expressed in ampere-hours (Ah).
     *
     * @param \WalmartDSV\ToolsType\BatteryCapacityAType $batteryCapacity
     * @return self
     */
    public function setBatteryCapacity(\WalmartDSV\ToolsType\BatteryCapacityAType $batteryCapacity)
    {
        $this->batteryCapacity = $batteryCapacity;
        return $this;
    }

    /**
     * Gets as engineDisplacement
     *
     * The combined volume, as displaced by the pistons for all cylinders of an internal combustion engine. Generally expressed in cubic centimeters up to 1000 cc, and liters thereafter, or in cubic inches. Engine displacement is often used as a rough indicator of an engine's power and potential fuel consumption.
     *
     * @return \WalmartDSV\ToolsType\EngineDisplacementAType
     */
    public function getEngineDisplacement()
    {
        return $this->engineDisplacement;
    }

    /**
     * Sets a new engineDisplacement
     *
     * The combined volume, as displaced by the pistons for all cylinders of an internal combustion engine. Generally expressed in cubic centimeters up to 1000 cc, and liters thereafter, or in cubic inches. Engine displacement is often used as a rough indicator of an engine's power and potential fuel consumption.
     *
     * @param \WalmartDSV\ToolsType\EngineDisplacementAType $engineDisplacement
     * @return self
     */
    public function setEngineDisplacement(\WalmartDSV\ToolsType\EngineDisplacementAType $engineDisplacement)
    {
        $this->engineDisplacement = $engineDisplacement;
        return $this;
    }

    /**
     * Gets as horsepower
     *
     * Measurement of the power of the device. Significance of horsepower varies with type of product. For example, for products with engines, horsepower is a general indicator of an engine's size and power (along with other engine specifications of engine displacement and torque). Horsepower is also used as common rating on electric motors and products that contain them.
     *
     * @return \WalmartDSV\ToolsType\HorsepowerAType
     */
    public function getHorsepower()
    {
        return $this->horsepower;
    }

    /**
     * Sets a new horsepower
     *
     * Measurement of the power of the device. Significance of horsepower varies with type of product. For example, for products with engines, horsepower is a general indicator of an engine's size and power (along with other engine specifications of engine displacement and torque). Horsepower is also used as common rating on electric motors and products that contain them.
     *
     * @param \WalmartDSV\ToolsType\HorsepowerAType $horsepower
     * @return self
     */
    public function setHorsepower(\WalmartDSV\ToolsType\HorsepowerAType $horsepower)
    {
        $this->horsepower = $horsepower;
        return $this;
    }

    /**
     * Gets as decibelRating
     *
     * Measurement, expressed as decibels, of the intensity of sound volume a device produces. Important selection criteria for consumers concerned with effects of products that generate loud noise levels. Attribute applied to such products as power tools and security alarms.
     *
     * @return \WalmartDSV\ToolsType\DecibelRatingAType
     */
    public function getDecibelRating()
    {
        return $this->decibelRating;
    }

    /**
     * Sets a new decibelRating
     *
     * Measurement, expressed as decibels, of the intensity of sound volume a device produces. Important selection criteria for consumers concerned with effects of products that generate loud noise levels. Attribute applied to such products as power tools and security alarms.
     *
     * @param \WalmartDSV\ToolsType\DecibelRatingAType $decibelRating
     * @return self
     */
    public function setDecibelRating(\WalmartDSV\ToolsType\DecibelRatingAType $decibelRating)
    {
        $this->decibelRating = $decibelRating;
        return $this;
    }

    /**
     * Gets as maximumAirPressure
     *
     * The maximum internal pressure that the product is designed to contain and/or control.
     *
     * @return \WalmartDSV\ToolsType\MaximumAirPressureAType
     */
    public function getMaximumAirPressure()
    {
        return $this->maximumAirPressure;
    }

    /**
     * Sets a new maximumAirPressure
     *
     * The maximum internal pressure that the product is designed to contain and/or control.
     *
     * @param \WalmartDSV\ToolsType\MaximumAirPressureAType $maximumAirPressure
     * @return self
     */
    public function setMaximumAirPressure(\WalmartDSV\ToolsType\MaximumAirPressureAType $maximumAirPressure)
    {
        $this->maximumAirPressure = $maximumAirPressure;
        return $this;
    }

    /**
     * Gets as maximumWattsOut
     *
     * Overall power rating as specified by the manufacturer, expressed as MWO. Used primarily for power tools.
     *
     * @return \WalmartDSV\ToolsType\MaximumWattsOutAType
     */
    public function getMaximumWattsOut()
    {
        return $this->maximumWattsOut;
    }

    /**
     * Sets a new maximumWattsOut
     *
     * Overall power rating as specified by the manufacturer, expressed as MWO. Used primarily for power tools.
     *
     * @param \WalmartDSV\ToolsType\MaximumWattsOutAType $maximumWattsOut
     * @return self
     */
    public function setMaximumWattsOut(\WalmartDSV\ToolsType\MaximumWattsOutAType $maximumWattsOut)
    {
        $this->maximumWattsOut = $maximumWattsOut;
        return $this;
    }

    /**
     * Gets as torque
     *
     * Measurement of turning or twisting force as measured in foot pounds (ft-lbs). Significance varies with product. For example, for torque wrenches, torque value helps consumers select the correct torque wrench for tightening a specific nut or bolt. If item has an engine, torque is a general indicator of an engine's pulling power.
     *
     * @return float
     */
    public function getTorque()
    {
        return $this->torque;
    }

    /**
     * Sets a new torque
     *
     * Measurement of turning or twisting force as measured in foot pounds (ft-lbs). Significance varies with product. For example, for torque wrenches, torque value helps consumers select the correct torque wrench for tightening a specific nut or bolt. If item has an engine, torque is a general indicator of an engine's pulling power.
     *
     * @param float $torque
     * @return self
     */
    public function setTorque($torque)
    {
        $this->torque = $torque;
        return $this;
    }

    /**
     * Gets as sandingSpeed
     *
     * Speed of the sander expressed in orbits per minute or surface feet per minute.
     *
     * @return \WalmartDSV\ToolsType\SandingSpeedAType
     */
    public function getSandingSpeed()
    {
        return $this->sandingSpeed;
    }

    /**
     * Sets a new sandingSpeed
     *
     * Speed of the sander expressed in orbits per minute or surface feet per minute.
     *
     * @param \WalmartDSV\ToolsType\SandingSpeedAType $sandingSpeed
     * @return self
     */
    public function setSandingSpeed(\WalmartDSV\ToolsType\SandingSpeedAType $sandingSpeed)
    {
        $this->sandingSpeed = $sandingSpeed;
        return $this;
    }

    /**
     * Gets as noLoadSpeed
     *
     * The maximum speed the product can reach without a load. For example, the maximum speed, expressed in rpm, a cordless drill driver can reach when it's not driving screws or drilling holes.
     *
     * @return \WalmartDSV\ToolsType\NoLoadSpeedAType
     */
    public function getNoLoadSpeed()
    {
        return $this->noLoadSpeed;
    }

    /**
     * Sets a new noLoadSpeed
     *
     * The maximum speed the product can reach without a load. For example, the maximum speed, expressed in rpm, a cordless drill driver can reach when it's not driving screws or drilling holes.
     *
     * @param \WalmartDSV\ToolsType\NoLoadSpeedAType $noLoadSpeed
     * @return self
     */
    public function setNoLoadSpeed(\WalmartDSV\ToolsType\NoLoadSpeedAType $noLoadSpeed)
    {
        $this->noLoadSpeed = $noLoadSpeed;
        return $this;
    }

    /**
     * Gets as strokeLength
     *
     * Measurement describing how far a component travels in one direction in each movement cycle. When applied to power tools, allows selection based on intended use. For example, for reciprocating saws, a shorter stroke length allows for better control in tight areas, while a longer stroke length provides more aggressive cutting action for heavy demolition.
     *
     * @return \WalmartDSV\ToolsType\StrokeLengthAType
     */
    public function getStrokeLength()
    {
        return $this->strokeLength;
    }

    /**
     * Sets a new strokeLength
     *
     * Measurement describing how far a component travels in one direction in each movement cycle. When applied to power tools, allows selection based on intended use. For example, for reciprocating saws, a shorter stroke length allows for better control in tight areas, while a longer stroke length provides more aggressive cutting action for heavy demolition.
     *
     * @param \WalmartDSV\ToolsType\StrokeLengthAType $strokeLength
     * @return self
     */
    public function setStrokeLength(\WalmartDSV\ToolsType\StrokeLengthAType $strokeLength)
    {
        $this->strokeLength = $strokeLength;
        return $this;
    }

    /**
     * Gets as strokesPerMinute
     *
     * Number of stokes per minute. Attribute typically applied to power tools as a measure of speed, and allows selection based on intended use. For example, for jig saws, deep cuts in dense hardwoods require high speed, while hard steel demands much slower blade stokes per minute.
     *
     * @return string
     */
    public function getStrokesPerMinute()
    {
        return $this->strokesPerMinute;
    }

    /**
     * Sets a new strokesPerMinute
     *
     * Number of stokes per minute. Attribute typically applied to power tools as a measure of speed, and allows selection based on intended use. For example, for jig saws, deep cuts in dense hardwoods require high speed, while hard steel demands much slower blade stokes per minute.
     *
     * @param string $strokesPerMinute
     * @return self
     */
    public function setStrokesPerMinute($strokesPerMinute)
    {
        $this->strokesPerMinute = $strokesPerMinute;
        return $this;
    }

    /**
     * Gets as blowsPerMinute
     *
     * The number of blows per minute a tool, such as a hammer drill, is able to produce.
     *
     * @return string
     */
    public function getBlowsPerMinute()
    {
        return $this->blowsPerMinute;
    }

    /**
     * Sets a new blowsPerMinute
     *
     * The number of blows per minute a tool, such as a hammer drill, is able to produce.
     *
     * @param string $blowsPerMinute
     * @return self
     */
    public function setBlowsPerMinute($blowsPerMinute)
    {
        $this->blowsPerMinute = $blowsPerMinute;
        return $this;
    }

    /**
     * Gets as impactEnergy
     *
     * Measurement of force applied by the item, expressed in joules. Used as selection factor for tools such as impact wrenches.
     *
     * @return \WalmartDSV\ToolsType\ImpactEnergyAType
     */
    public function getImpactEnergy()
    {
        return $this->impactEnergy;
    }

    /**
     * Sets a new impactEnergy
     *
     * Measurement of force applied by the item, expressed in joules. Used as selection factor for tools such as impact wrenches.
     *
     * @param \WalmartDSV\ToolsType\ImpactEnergyAType $impactEnergy
     * @return self
     */
    public function setImpactEnergy(\WalmartDSV\ToolsType\ImpactEnergyAType $impactEnergy)
    {
        $this->impactEnergy = $impactEnergy;
        return $this;
    }

    /**
     * Gets as loadCapacity
     *
     * The maximum load, expressed in psi, that the product is designed to have applied to it.
     *
     * @return \WalmartDSV\ToolsType\LoadCapacityAType
     */
    public function getLoadCapacity()
    {
        return $this->loadCapacity;
    }

    /**
     * Sets a new loadCapacity
     *
     * The maximum load, expressed in psi, that the product is designed to have applied to it.
     *
     * @param \WalmartDSV\ToolsType\LoadCapacityAType $loadCapacity
     * @return self
     */
    public function setLoadCapacity(\WalmartDSV\ToolsType\LoadCapacityAType $loadCapacity)
    {
        $this->loadCapacity = $loadCapacity;
        return $this;
    }

    /**
     * Gets as volumeCapacity
     *
     * The volume of space available in this item to contain objects.
     *
     * @return \WalmartDSV\ToolsType\VolumeCapacityAType
     */
    public function getVolumeCapacity()
    {
        return $this->volumeCapacity;
    }

    /**
     * Sets a new volumeCapacity
     *
     * The volume of space available in this item to contain objects.
     *
     * @param \WalmartDSV\ToolsType\VolumeCapacityAType $volumeCapacity
     * @return self
     */
    public function setVolumeCapacity(\WalmartDSV\ToolsType\VolumeCapacityAType $volumeCapacity)
    {
        $this->volumeCapacity = $volumeCapacity;
        return $this;
    }

    /**
     * Gets as teethPerInch
     *
     * Number of teeth per inch of the item. Important selection factor for saw blades. For example, more TPI gives a smoother cut but requires a slower saw cut, fewer TPI allows a faster cut with a slightly rougher finish.
     *
     * @return int
     */
    public function getTeethPerInch()
    {
        return $this->teethPerInch;
    }

    /**
     * Sets a new teethPerInch
     *
     * Number of teeth per inch of the item. Important selection factor for saw blades. For example, more TPI gives a smoother cut but requires a slower saw cut, fewer TPI allows a faster cut with a slightly rougher finish.
     *
     * @param int $teethPerInch
     * @return self
     */
    public function setTeethPerInch($teethPerInch)
    {
        $this->teethPerInch = $teethPerInch;
        return $this;
    }

    /**
     * Gets as maximumJawOpening
     *
     * Measurement of the widest opening with the jaw of the product in its widest position. Typically used for tools such as an adjustable wrench, or vise.
     *
     * @return \WalmartDSV\ToolsType\MaximumJawOpeningAType
     */
    public function getMaximumJawOpening()
    {
        return $this->maximumJawOpening;
    }

    /**
     * Sets a new maximumJawOpening
     *
     * Measurement of the widest opening with the jaw of the product in its widest position. Typically used for tools such as an adjustable wrench, or vise.
     *
     * @param \WalmartDSV\ToolsType\MaximumJawOpeningAType $maximumJawOpening
     * @return self
     */
    public function setMaximumJawOpening(\WalmartDSV\ToolsType\MaximumJawOpeningAType $maximumJawOpening)
    {
        $this->maximumJawOpening = $maximumJawOpening;
        return $this;
    }

    /**
     * Gets as tankConfiguration
     *
     * The type of arrangement of supply tanks associated with a tool such as an air compressor.
     *
     * @return string
     */
    public function getTankConfiguration()
    {
        return $this->tankConfiguration;
    }

    /**
     * Sets a new tankConfiguration
     *
     * The type of arrangement of supply tanks associated with a tool such as an air compressor.
     *
     * @param string $tankConfiguration
     * @return self
     */
    public function setTankConfiguration($tankConfiguration)
    {
        $this->tankConfiguration = $tankConfiguration;
        return $this;
    }

    /**
     * Adds as swatchImage
     *
     * @param \WalmartDSV\ToolsType\SwatchImagesAType\SwatchImageAType $swatchImage
     *@return self
     */
    public function addToSwatchImages(\WalmartDSV\ToolsType\SwatchImagesAType\SwatchImageAType $swatchImage)
    {
        $this->swatchImages[] = $swatchImage;
        return $this;
    }

    /**
     * isset swatchImages
     *
     * @param int|string $index
     * @return bool
     */
    public function issetSwatchImages($index)
    {
        return isset($this->swatchImages[$index]);
    }

    /**
     * unset swatchImages
     *
     * @param int|string $index
     * @return void
     */
    public function unsetSwatchImages($index)
    {
        unset($this->swatchImages[$index]);
    }

    /**
     * Gets as swatchImages
     *
     * @return \WalmartDSV\ToolsType\SwatchImagesAType\SwatchImageAType[]
     */
    public function getSwatchImages()
    {
        return $this->swatchImages;
    }

    /**
     * Sets a new swatchImages
     *
     * @param \WalmartDSV\ToolsType\SwatchImagesAType\SwatchImageAType[] $swatchImages
     * @return self
     */
    public function setSwatchImages(array $swatchImages)
    {
        $this->swatchImages = $swatchImages;
        return $this;
    }


}

