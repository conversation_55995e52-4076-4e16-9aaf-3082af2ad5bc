<?php

namespace WalmartDSV;

/**
 * Class representing ActiveIngredientType
 *
 *
 * XSD Type: activeIngredient
 */
class ActiveIngredientType
{

    /**
     * Ingredient name.
     *
     * @var string $activeIngredientName
     */
    private $activeIngredientName = null;

    /**
     * The percent of the active ingredient in the item.
     *
     * @var float $activeIngredientPercentage
     */
    private $activeIngredientPercentage = null;

    /**
     * Gets as activeIngredientName
     *
     * Ingredient name.
     *
     * @return string
     */
    public function getActiveIngredientName()
    {
        return $this->activeIngredientName;
    }

    /**
     * Sets a new activeIngredientName
     *
     * Ingredient name.
     *
     * @param string $activeIngredientName
     * @return self
     */
    public function setActiveIngredientName($activeIngredientName)
    {
        $this->activeIngredientName = $activeIngredientName;
        return $this;
    }

    /**
     * Gets as activeIngredientPercentage
     *
     * The percent of the active ingredient in the item.
     *
     * @return float
     */
    public function getActiveIngredientPercentage()
    {
        return $this->activeIngredientPercentage;
    }

    /**
     * Sets a new activeIngredientPercentage
     *
     * The percent of the active ingredient in the item.
     *
     * @param float $activeIngredientPercentage
     * @return self
     */
    public function setActiveIngredientPercentage($activeIngredientPercentage)
    {
        $this->activeIngredientPercentage = $activeIngredientPercentage;
        return $this;
    }


}

