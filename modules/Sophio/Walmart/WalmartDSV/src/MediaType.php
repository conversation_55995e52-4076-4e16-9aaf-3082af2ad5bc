<?php

namespace WalmartDSV;

/**
 * Class representing MediaType
 *
 *
 * XSD Type: Media
 */
class MediaType
{

    /**
     * @var \WalmartDSV\MoviesType $movies
     */
    private $movies = null;

    /**
     * @var \WalmartDSV\TVShowsType $tVShows
     */
    private $tVShows = null;

    /**
     * @var \WalmartDSV\MusicType $music
     */
    private $music = null;

    /**
     * @var \WalmartDSV\BooksAndMagazinesType $booksAndMagazines
     */
    private $booksAndMagazines = null;

    /**
     * Gets as movies
     *
     * @return \WalmartDSV\MoviesType
     */
    public function getMovies()
    {
        return $this->movies;
    }

    /**
     * Sets a new movies
     *
     * @param \WalmartDSV\MoviesType $movies
     * @return self
     */
    public function setMovies(\WalmartDSV\MoviesType $movies)
    {
        $this->movies = $movies;
        return $this;
    }

    /**
     * Gets as tVShows
     *
     * @return \WalmartDSV\TVShowsType
     */
    public function getTVShows()
    {
        return $this->tVShows;
    }

    /**
     * Sets a new tVShows
     *
     * @param \WalmartDSV\TVShowsType $tVShows
     * @return self
     */
    public function setTVShows(\WalmartDSV\TVShowsType $tVShows)
    {
        $this->tVShows = $tVShows;
        return $this;
    }

    /**
     * Gets as music
     *
     * @return \WalmartDSV\MusicType
     */
    public function getMusic()
    {
        return $this->music;
    }

    /**
     * Sets a new music
     *
     * @param \WalmartDSV\MusicType $music
     * @return self
     */
    public function setMusic(\WalmartDSV\MusicType $music)
    {
        $this->music = $music;
        return $this;
    }

    /**
     * Gets as booksAndMagazines
     *
     * @return \WalmartDSV\BooksAndMagazinesType
     */
    public function getBooksAndMagazines()
    {
        return $this->booksAndMagazines;
    }

    /**
     * Sets a new booksAndMagazines
     *
     * @param \WalmartDSV\BooksAndMagazinesType $booksAndMagazines
     * @return self
     */
    public function setBooksAndMagazines(\WalmartDSV\BooksAndMagazinesType $booksAndMagazines)
    {
        $this->booksAndMagazines = $booksAndMagazines;
        return $this;
    }


}

