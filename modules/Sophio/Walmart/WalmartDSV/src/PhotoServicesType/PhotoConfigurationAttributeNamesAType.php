<?php

namespace WalmartDSV\PhotoServicesType;

/**
 * Class representing PhotoConfigurationAttributeNamesAType
 */
class PhotoConfigurationAttributeNamesAType
{

    /**
     * @var string[] $photoConfigurationAttributeNamesValue
     */
    private $photoConfigurationAttributeNamesValue = [
        
    ];

    /**
     * Adds as photoConfigurationAttributeNamesValue
     *
     * @return self
     * @param string $photoConfigurationAttributeNamesValue
     */
    public function addToPhotoConfigurationAttributeNamesValue($photoConfigurationAttributeNamesValue)
    {
        $this->photoConfigurationAttributeNamesValue[] = $photoConfigurationAttributeNamesValue;
        return $this;
    }

    /**
     * isset photoConfigurationAttributeNamesValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetPhotoConfigurationAttributeNamesValue($index)
    {
        return isset($this->photoConfigurationAttributeNamesValue[$index]);
    }

    /**
     * unset photoConfigurationAttributeNamesValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetPhotoConfigurationAttributeNamesValue($index)
    {
        unset($this->photoConfigurationAttributeNamesValue[$index]);
    }

    /**
     * Gets as photoConfigurationAttributeNamesValue
     *
     * @return string[]
     */
    public function getPhotoConfigurationAttributeNamesValue()
    {
        return $this->photoConfigurationAttributeNamesValue;
    }

    /**
     * Sets a new photoConfigurationAttributeNamesValue
     *
     * @param string $photoConfigurationAttributeNamesValue
     * @return self
     */
    public function setPhotoConfigurationAttributeNamesValue(array $photoConfigurationAttributeNamesValue)
    {
        $this->photoConfigurationAttributeNamesValue = $photoConfigurationAttributeNamesValue;
        return $this;
    }


}

