<?php

namespace WalmartDSV;

/**
 * Class representing ClothingStyleType
 *
 * Styles and designations that apply generally to various types of clothing.
 * XSD Type: ClothingStyle
 */
class ClothingStyleType
{

    /**
     * @var string $clothingStyleValue
     */
    private $clothingStyleValue = null;

    /**
     * Gets as clothingStyleValue
     *
     * @return string
     */
    public function getClothingStyleValue()
    {
        return $this->clothingStyleValue;
    }

    /**
     * Sets a new clothingStyleValue
     *
     * @param string $clothingStyleValue
     * @return self
     */
    public function setClothingStyleValue($clothingStyleValue)
    {
        $this->clothingStyleValue = $clothingStyleValue;
        return $this;
    }


}

