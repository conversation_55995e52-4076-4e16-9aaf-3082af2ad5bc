<?php

namespace WalmartDSV;

/**
 * Class representing ThemeType
 *
 * A dominant idea, meaning, or setting applied to an item. Used in a wide range of products including decorative objects, clothing, toys, and furniture. Can be an important selection criteria for consumers who want to achieve a particular ambiance for room décor or for a special occasion.
 * XSD Type: Theme
 */
class ThemeType
{

    /**
     * @var string $themeValue
     */
    private $themeValue = null;

    /**
     * Gets as themeValue
     *
     * @return string
     */
    public function getThemeValue()
    {
        return $this->themeValue;
    }

    /**
     * Sets a new themeValue
     *
     * @param string $themeValue
     * @return self
     */
    public function setThemeValue($themeValue)
    {
        $this->themeValue = $themeValue;
        return $this;
    }


}

