<?php

namespace WalmartDSV;

/**
 * Class representing MetalStampType
 *
 * Most jewelry items made of precious metal are stamped with information about the purity level of the metal content. Generally the stamp is placed in an inconspicuous place on the item so it does not detract from the design. Stamps will usually be located on the inside of the band on a ring, on the post or basket setting on a pair of earrings, on the bail (the part that the chain slides through) on a pendant, and on the connecting ring or the clasp on a necklace or bracelet. All jewelry stamps adhere to strict guidelines set by the Federal Trade Commission.
 * XSD Type: MetalStamp
 */
class MetalStampType
{

    /**
     * @var string[] $metalStampValue
     */
    private $metalStampValue = [
        
    ];

    /**
     * Adds as metalStampValue
     *
     * @return self
     * @param string $metalStampValue
     */
    public function addToMetalStampValue($metalStampValue)
    {
        $this->metalStampValue[] = $metalStampValue;
        return $this;
    }

    /**
     * isset metalStampValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetMetalStampValue($index)
    {
        return isset($this->metalStampValue[$index]);
    }

    /**
     * unset metalStampValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetMetalStampValue($index)
    {
        unset($this->metalStampValue[$index]);
    }

    /**
     * Gets as metalStampValue
     *
     * @return string[]
     */
    public function getMetalStampValue()
    {
        return $this->metalStampValue;
    }

    /**
     * Sets a new metalStampValue
     *
     * @param string $metalStampValue
     * @return self
     */
    public function setMetalStampValue(array $metalStampValue)
    {
        $this->metalStampValue = $metalStampValue;
        return $this;
    }


}

