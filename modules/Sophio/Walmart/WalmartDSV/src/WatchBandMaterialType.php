<?php

namespace WalmartDSV;

/**
 * Class representing WatchBandMaterialType
 *
 * Use this attribute if the material of the watch band is a particular feature or varies between similar items.
 * XSD Type: WatchBandMaterial
 */
class WatchBandMaterialType
{

    /**
     * @var string $watchBandMaterialValue
     */
    private $watchBandMaterialValue = null;

    /**
     * Gets as watchBandMaterialValue
     *
     * @return string
     */
    public function getWatchBandMaterialValue()
    {
        return $this->watchBandMaterialValue;
    }

    /**
     * Sets a new watchBandMaterialValue
     *
     * @param string $watchBandMaterialValue
     * @return self
     */
    public function setWatchBandMaterialValue($watchBandMaterialValue)
    {
        $this->watchBandMaterialValue = $watchBandMaterialValue;
        return $this;
    }


}

