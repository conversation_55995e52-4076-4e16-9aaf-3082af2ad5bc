<?php

namespace WalmartDSV;

/**
 * Class representing WigStylesType
 *
 * Descriptive terms for wig styles.
 * XSD Type: WigStyles
 */
class WigStylesType
{

    /**
     * @var string[] $wigStyle
     */
    private $wigStyle = [
        
    ];

    /**
     * Adds as wigStyle
     *
     * @return self
     * @param string $wigStyle
     */
    public function addToWigStyle($wigStyle)
    {
        $this->wigStyle[] = $wigStyle;
        return $this;
    }

    /**
     * isset wigStyle
     *
     * @param int|string $index
     * @return bool
     */
    public function issetWigStyle($index)
    {
        return isset($this->wigStyle[$index]);
    }

    /**
     * unset wigStyle
     *
     * @param int|string $index
     * @return void
     */
    public function unsetWigStyle($index)
    {
        unset($this->wigStyle[$index]);
    }

    /**
     * Gets as wigStyle
     *
     * @return string[]
     */
    public function getWigStyle()
    {
        return $this->wigStyle;
    }

    /**
     * Sets a new wigStyle
     *
     * @param string $wigStyle
     * @return self
     */
    public function setWigStyle(array $wigStyle)
    {
        $this->wigStyle = $wigStyle;
        return $this;
    }


}

