<?php

namespace WalmartDSV;

/**
 * Class representing NutrientsType
 *
 * Additional nutrients, not including total fat or total carbohydrates, which should be entered in "Total Fat" and "Total Carbohydrate" respectively.
 * XSD Type: Nutrients
 */
class NutrientsType
{

    /**
     * @var \WalmartDSV\NutrientType[] $nutrient
     */
    private $nutrient = [
        
    ];

    /**
     * Adds as nutrient
     *
     * @param \WalmartDSV\NutrientType $nutrient
     *@return self
     */
    public function addToNutrient(\WalmartDSV\NutrientType $nutrient)
    {
        $this->nutrient[] = $nutrient;
        return $this;
    }

    /**
     * isset nutrient
     *
     * @param int|string $index
     * @return bool
     */
    public function issetNutrient($index)
    {
        return isset($this->nutrient[$index]);
    }

    /**
     * unset nutrient
     *
     * @param int|string $index
     * @return void
     */
    public function unsetNutrient($index)
    {
        unset($this->nutrient[$index]);
    }

    /**
     * Gets as nutrient
     *
     * @return \WalmartDSV\NutrientType[]
     */
    public function getNutrient()
    {
        return $this->nutrient;
    }

    /**
     * Sets a new nutrient
     *
     * @param \WalmartDSV\NutrientType[] $nutrient
     * @return self
     */
    public function setNutrient(array $nutrient)
    {
        $this->nutrient = $nutrient;
        return $this;
    }


}

