<?php

namespace WalmartDSV;

/**
 * Class representing ActivityType
 *
 * The general type of activity one might perform while wearing this garment.
 * XSD Type: Activity
 */
class ActivityType
{

    /**
     * @var string $activityValue
     */
    private $activityValue = null;

    /**
     * Gets as activityValue
     *
     * @return string
     */
    public function getActivityValue()
    {
        return $this->activityValue;
    }

    /**
     * Sets a new activityValue
     *
     * @param string $activityValue
     * @return self
     */
    public function setActivityValue($activityValue)
    {
        $this->activityValue = $activityValue;
        return $this;
    }


}

