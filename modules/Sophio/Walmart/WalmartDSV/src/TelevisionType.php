<?php

namespace WalmartDSV;

/**
 * Class representing TelevisionType
 *
 * The type of TV with reference to its technology and capabilities.
 * XSD Type: TelevisionType
 */
class TelevisionType
{

    /**
     * @var string[] $televisionTypeValue
     */
    private $televisionTypeValue = [
        
    ];

    /**
     * Adds as televisionTypeValue
     *
     * @return self
     * @param string $televisionTypeValue
     */
    public function addToTelevisionTypeValue($televisionTypeValue)
    {
        $this->televisionTypeValue[] = $televisionTypeValue;
        return $this;
    }

    /**
     * isset televisionTypeValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetTelevisionTypeValue($index)
    {
        return isset($this->televisionTypeValue[$index]);
    }

    /**
     * unset televisionTypeValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetTelevisionTypeValue($index)
    {
        unset($this->televisionTypeValue[$index]);
    }

    /**
     * Gets as televisionTypeValue
     *
     * @return string[]
     */
    public function getTelevisionTypeValue()
    {
        return $this->televisionTypeValue;
    }

    /**
     * Sets a new televisionTypeValue
     *
     * @param string $televisionTypeValue
     * @return self
     */
    public function setTelevisionTypeValue(array $televisionTypeValue)
    {
        $this->televisionTypeValue = $televisionTypeValue;
        return $this;
    }


}

