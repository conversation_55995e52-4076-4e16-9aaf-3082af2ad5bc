<?php

namespace WalmartDSV\SupplierItemFeed;

/**
 * Class representing SupplierItemFeedAType
 */
class SupplierItemFeedAType
{

    /**
     * Feed header
     *
     * @var \WalmartDSV\SupplierItemFeedHeaderType $supplierItemFeedHeader
     */
    private $supplierItemFeedHeader = null;

    /**
     * @var \WalmartDSV\SupplierItemType[] $supplierItem
     */
    private $supplierItem = [
        
    ];

    /**
     * Gets as supplierItemFeedHeader
     *
     * Feed header
     *
     * @return \WalmartDSV\SupplierItemFeedHeaderType
     */
    public function getSupplierItemFeedHeader()
    {
        return $this->supplierItemFeedHeader;
    }

    /**
     * Sets a new supplierItemFeedHeader
     *
     * Feed header
     *
     * @param \WalmartDSV\SupplierItemFeedHeaderType $supplierItemFeedHeader
     * @return self
     */
    public function setSupplierItemFeedHeader(\WalmartDSV\SupplierItemFeedHeaderType $supplierItemFeedHeader)
    {
        $this->supplierItemFeedHeader = $supplierItemFeedHeader;
        return $this;
    }

    /**
     * Adds as supplierItem
     *
     * @param \WalmartDSV\SupplierItemType $supplierItem
     *@return self
     */
    public function addToSupplierItem(\WalmartDSV\SupplierItemType $supplierItem)
    {
        $this->supplierItem[] = $supplierItem;
        return $this;
    }

    /**
     * isset supplierItem
     *
     * @param int|string $index
     * @return bool
     */
    public function issetSupplierItem($index)
    {
        return isset($this->supplierItem[$index]);
    }

    /**
     * unset supplierItem
     *
     * @param int|string $index
     * @return void
     */
    public function unsetSupplierItem($index)
    {
        unset($this->supplierItem[$index]);
    }

    /**
     * Gets as supplierItem
     *
     * @return \WalmartDSV\SupplierItemType[]
     */
    public function getSupplierItem()
    {
        return $this->supplierItem;
    }

    /**
     * Sets a new supplierItem
     *
     * @param \WalmartDSV\SupplierItemType[] $supplierItem
     * @return self
     */
    public function setSupplierItem(array $supplierItem)
    {
        $this->supplierItem = $supplierItem;
        return $this;
    }


}

