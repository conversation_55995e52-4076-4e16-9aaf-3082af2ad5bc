<?php

namespace WalmartDSV;

/**
 * Class representing SeasonType
 *
 * If designed to be used during a specific type of year, the appropriate season this item may be used.
 * XSD Type: Season
 */
class SeasonType
{

    /**
     * @var string $seasonValue
     */
    private $seasonValue = null;

    /**
     * Gets as seasonValue
     *
     * @return string
     */
    public function getSeasonValue()
    {
        return $this->seasonValue;
    }

    /**
     * Sets a new seasonValue
     *
     * @param string $seasonValue
     * @return self
     */
    public function setSeasonValue($seasonValue)
    {
        $this->seasonValue = $seasonValue;
        return $this;
    }


}

