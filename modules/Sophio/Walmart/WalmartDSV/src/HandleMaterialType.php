<?php

namespace WalmartDSV;

/**
 * Class representing HandleMaterialType
 *
 * Material of the handle, if different from the rest of the item.
 * XSD Type: HandleMaterial
 */
class HandleMaterialType
{

    /**
     * @var string[] $handleMaterialValue
     */
    private $handleMaterialValue = [
        
    ];

    /**
     * Adds as handleMaterialValue
     *
     * @return self
     * @param string $handleMaterialValue
     */
    public function addToHandleMaterialValue($handleMaterialValue)
    {
        $this->handleMaterialValue[] = $handleMaterialValue;
        return $this;
    }

    /**
     * isset handleMaterialValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetHandleMaterialValue($index)
    {
        return isset($this->handleMaterialValue[$index]);
    }

    /**
     * unset handleMaterialValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetHandleMaterialValue($index)
    {
        unset($this->handleMaterialValue[$index]);
    }

    /**
     * Gets as handleMaterialValue
     *
     * @return string[]
     */
    public function getHandleMaterialValue()
    {
        return $this->handleMaterialValue;
    }

    /**
     * Sets a new handleMaterialValue
     *
     * @param string $handleMaterialValue
     * @return self
     */
    public function setHandleMaterialValue(array $handleMaterialValue)
    {
        $this->handleMaterialValue = $handleMaterialValue;
        return $this;
    }


}

