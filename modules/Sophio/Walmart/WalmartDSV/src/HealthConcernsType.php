<?php

namespace WalmartDSV;

/**
 * Class representing HealthConcernsType
 *
 * Type of medical condition/illness/well being/life stage that the item is intended to address.
 * XSD Type: HealthConcerns
 */
class HealthConcernsType
{

    /**
     * @var string[] $healthConcern
     */
    private $healthConcern = [
        
    ];

    /**
     * Adds as healthConcern
     *
     * @return self
     * @param string $healthConcern
     */
    public function addToHealthConcern($healthConcern)
    {
        $this->healthConcern[] = $healthConcern;
        return $this;
    }

    /**
     * isset healthConcern
     *
     * @param int|string $index
     * @return bool
     */
    public function issetHealthConcern($index)
    {
        return isset($this->healthConcern[$index]);
    }

    /**
     * unset healthConcern
     *
     * @param int|string $index
     * @return void
     */
    public function unsetHealthConcern($index)
    {
        unset($this->healthConcern[$index]);
    }

    /**
     * Gets as healthConcern
     *
     * @return string[]
     */
    public function getHealthConcern()
    {
        return $this->healthConcern;
    }

    /**
     * Sets a new healthConcern
     *
     * @param string $healthConcern
     * @return self
     */
    public function setHealthConcern(array $healthConcern)
    {
        $this->healthConcern = $healthConcern;
        return $this;
    }


}

