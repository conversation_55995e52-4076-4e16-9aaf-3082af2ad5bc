<?php

namespace WalmartDSV;

/**
 * Class representing OtherCategoryType
 *
 *
 * XSD Type: OtherCategory
 */
class OtherCategoryType
{

    /**
     * @var \WalmartDSV\StorageType $storage
     */
    private $storage = null;

    /**
     * @var \WalmartDSV\GiftCardsType $giftCards
     */
    private $giftCards = null;

    /**
     * @var \WalmartDSV\CleaningAndChemicalType $cleaningAndChemical
     */
    private $cleaningAndChemical = null;

    /**
     * @var \WalmartDSV\SafetyAndEmergencyType $safetyAndEmergency
     */
    private $safetyAndEmergency = null;

    /**
     * @var \WalmartDSV\FuelsAndLubricantsType $fuelsAndLubricants
     */
    private $fuelsAndLubricants = null;

    /**
     * @var \WalmartDSV\OtherType $other
     */
    private $other = null;

    /**
     * Gets as storage
     *
     * @return \WalmartDSV\StorageType
     */
    public function getStorage()
    {
        return $this->storage;
    }

    /**
     * Sets a new storage
     *
     * @param \WalmartDSV\StorageType $storage
     * @return self
     */
    public function setStorage(\WalmartDSV\StorageType $storage)
    {
        $this->storage = $storage;
        return $this;
    }

    /**
     * Gets as giftCards
     *
     * @return \WalmartDSV\GiftCardsType
     */
    public function getGiftCards()
    {
        return $this->giftCards;
    }

    /**
     * Sets a new giftCards
     *
     * @param \WalmartDSV\GiftCardsType $giftCards
     * @return self
     */
    public function setGiftCards(\WalmartDSV\GiftCardsType $giftCards)
    {
        $this->giftCards = $giftCards;
        return $this;
    }

    /**
     * Gets as cleaningAndChemical
     *
     * @return \WalmartDSV\CleaningAndChemicalType
     */
    public function getCleaningAndChemical()
    {
        return $this->cleaningAndChemical;
    }

    /**
     * Sets a new cleaningAndChemical
     *
     * @param \WalmartDSV\CleaningAndChemicalType $cleaningAndChemical
     * @return self
     */
    public function setCleaningAndChemical(\WalmartDSV\CleaningAndChemicalType $cleaningAndChemical)
    {
        $this->cleaningAndChemical = $cleaningAndChemical;
        return $this;
    }

    /**
     * Gets as safetyAndEmergency
     *
     * @return \WalmartDSV\SafetyAndEmergencyType
     */
    public function getSafetyAndEmergency()
    {
        return $this->safetyAndEmergency;
    }

    /**
     * Sets a new safetyAndEmergency
     *
     * @param \WalmartDSV\SafetyAndEmergencyType $safetyAndEmergency
     * @return self
     */
    public function setSafetyAndEmergency(\WalmartDSV\SafetyAndEmergencyType $safetyAndEmergency)
    {
        $this->safetyAndEmergency = $safetyAndEmergency;
        return $this;
    }

    /**
     * Gets as fuelsAndLubricants
     *
     * @return \WalmartDSV\FuelsAndLubricantsType
     */
    public function getFuelsAndLubricants()
    {
        return $this->fuelsAndLubricants;
    }

    /**
     * Sets a new fuelsAndLubricants
     *
     * @param \WalmartDSV\FuelsAndLubricantsType $fuelsAndLubricants
     * @return self
     */
    public function setFuelsAndLubricants(\WalmartDSV\FuelsAndLubricantsType $fuelsAndLubricants)
    {
        $this->fuelsAndLubricants = $fuelsAndLubricants;
        return $this;
    }

    /**
     * Gets as other
     *
     * @return \WalmartDSV\OtherType
     */
    public function getOther()
    {
        return $this->other;
    }

    /**
     * Sets a new other
     *
     * @param \WalmartDSV\OtherType $other
     * @return self
     */
    public function setOther(\WalmartDSV\OtherType $other)
    {
        $this->other = $other;
        return $this;
    }


}

