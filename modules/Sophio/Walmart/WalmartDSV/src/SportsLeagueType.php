<?php

namespace WalmartDSV;

/**
 * Class representing SportsLeagueType
 *
 * If your item has any association with a specific sports league, enter the league name. Abbreviations are fine. NOTE: This attribute flags an item for inclusion in the online fan shop.
 * XSD Type: SportsLeague
 */
class SportsLeagueType
{

    /**
     * @var string $sportsLeagueValue
     */
    private $sportsLeagueValue = null;

    /**
     * Gets as sportsLeagueValue
     *
     * @return string
     */
    public function getSportsLeagueValue()
    {
        return $this->sportsLeagueValue;
    }

    /**
     * Sets a new sportsLeagueValue
     *
     * @param string $sportsLeagueValue
     * @return self
     */
    public function setSportsLeagueValue($sportsLeagueValue)
    {
        $this->sportsLeagueValue = $sportsLeagueValue;
        return $this;
    }


}

