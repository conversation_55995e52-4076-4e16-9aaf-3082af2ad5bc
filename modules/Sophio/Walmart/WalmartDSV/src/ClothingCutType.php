<?php

namespace WalmartDSV;

/**
 * Class representing ClothingCutType
 *
 * Common clothing cut styles.
 * XSD Type: ClothingCut
 */
class ClothingCutType
{

    /**
     * @var string $clothingCutValue
     */
    private $clothingCutValue = null;

    /**
     * Gets as clothingCutValue
     *
     * @return string
     */
    public function getClothingCutValue()
    {
        return $this->clothingCutValue;
    }

    /**
     * Sets a new clothingCutValue
     *
     * @param string $clothingCutValue
     * @return self
     */
    public function setClothingCutValue($clothingCutValue)
    {
        $this->clothingCutValue = $clothingCutValue;
        return $this;
    }


}

