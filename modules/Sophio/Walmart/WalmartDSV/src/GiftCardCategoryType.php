<?php

namespace WalmartDSV;

/**
 * Class representing GiftCardCategoryType
 *
 * A category describing how the gift card will be used.
 * XSD Type: GiftCardCategory
 */
class GiftCardCategoryType
{

    /**
     * @var string[] $giftCardCategoryValue
     */
    private $giftCardCategoryValue = [
        
    ];

    /**
     * Adds as giftCardCategoryValue
     *
     * @return self
     * @param string $giftCardCategoryValue
     */
    public function addToGiftCardCategoryValue($giftCardCategoryValue)
    {
        $this->giftCardCategoryValue[] = $giftCardCategoryValue;
        return $this;
    }

    /**
     * isset giftCardCategoryValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetGiftCardCategoryValue($index)
    {
        return isset($this->giftCardCategoryValue[$index]);
    }

    /**
     * unset giftCardCategoryValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetGiftCardCategoryValue($index)
    {
        unset($this->giftCardCategoryValue[$index]);
    }

    /**
     * Gets as giftCardCategoryValue
     *
     * @return string[]
     */
    public function getGiftCardCategoryValue()
    {
        return $this->giftCardCategoryValue;
    }

    /**
     * Sets a new giftCardCategoryValue
     *
     * @param string $giftCardCategoryValue
     * @return self
     */
    public function setGiftCardCategoryValue(array $giftCardCategoryValue)
    {
        $this->giftCardCategoryValue = $giftCardCategoryValue;
        return $this;
    }


}

