<?php

namespace WalmartDSV;

/**
 * Class representing MusicalInstrumentType
 *
 *
 * XSD Type: MusicalInstrument
 */
class MusicalInstrumentType
{

    /**
     * @var \WalmartDSV\MusicCasesAndBagsType $musicCasesAndBags
     */
    private $musicCasesAndBags = null;

    /**
     * @var \WalmartDSV\SoundAndRecordingType $soundAndRecording
     */
    private $soundAndRecording = null;

    /**
     * @var \WalmartDSV\MusicalInstrumentsType $musicalInstruments
     */
    private $musicalInstruments = null;

    /**
     * @var \WalmartDSV\InstrumentAccessoriesType $instrumentAccessories
     */
    private $instrumentAccessories = null;

    /**
     * Gets as musicCasesAndBags
     *
     * @return \WalmartDSV\MusicCasesAndBagsType
     */
    public function getMusicCasesAndBags()
    {
        return $this->musicCasesAndBags;
    }

    /**
     * Sets a new musicCasesAndBags
     *
     * @param \WalmartDSV\MusicCasesAndBagsType $musicCasesAndBags
     * @return self
     */
    public function setMusicCasesAndBags(\WalmartDSV\MusicCasesAndBagsType $musicCasesAndBags)
    {
        $this->musicCasesAndBags = $musicCasesAndBags;
        return $this;
    }

    /**
     * Gets as soundAndRecording
     *
     * @return \WalmartDSV\SoundAndRecordingType
     */
    public function getSoundAndRecording()
    {
        return $this->soundAndRecording;
    }

    /**
     * Sets a new soundAndRecording
     *
     * @param \WalmartDSV\SoundAndRecordingType $soundAndRecording
     * @return self
     */
    public function setSoundAndRecording(\WalmartDSV\SoundAndRecordingType $soundAndRecording)
    {
        $this->soundAndRecording = $soundAndRecording;
        return $this;
    }

    /**
     * Gets as musicalInstruments
     *
     * @return \WalmartDSV\MusicalInstrumentsType
     */
    public function getMusicalInstruments()
    {
        return $this->musicalInstruments;
    }

    /**
     * Sets a new musicalInstruments
     *
     * @param \WalmartDSV\MusicalInstrumentsType $musicalInstruments
     * @return self
     */
    public function setMusicalInstruments(\WalmartDSV\MusicalInstrumentsType $musicalInstruments)
    {
        $this->musicalInstruments = $musicalInstruments;
        return $this;
    }

    /**
     * Gets as instrumentAccessories
     *
     * @return \WalmartDSV\InstrumentAccessoriesType
     */
    public function getInstrumentAccessories()
    {
        return $this->instrumentAccessories;
    }

    /**
     * Sets a new instrumentAccessories
     *
     * @param \WalmartDSV\InstrumentAccessoriesType $instrumentAccessories
     * @return self
     */
    public function setInstrumentAccessories(\WalmartDSV\InstrumentAccessoriesType $instrumentAccessories)
    {
        $this->instrumentAccessories = $instrumentAccessories;
        return $this;
    }


}

