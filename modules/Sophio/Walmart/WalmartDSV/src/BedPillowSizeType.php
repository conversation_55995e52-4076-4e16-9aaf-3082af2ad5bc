<?php

namespace WalmartDSV;

/**
 * Class representing BedPillowSizeType
 *
 * General size label describing the size of a bed pillow or pillow cover.
 * XSD Type: BedPillowSize
 */
class BedPillowSizeType
{

    /**
     * @var string[] $bedPillowSizeValue
     */
    private $bedPillowSizeValue = [
        
    ];

    /**
     * Adds as bedPillowSizeValue
     *
     * @return self
     * @param string $bedPillowSizeValue
     */
    public function addToBedPillowSizeValue($bedPillowSizeValue)
    {
        $this->bedPillowSizeValue[] = $bedPillowSizeValue;
        return $this;
    }

    /**
     * isset bedPillowSizeValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetBedPillowSizeValue($index)
    {
        return isset($this->bedPillowSizeValue[$index]);
    }

    /**
     * unset bedPillowSizeValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetBedPillowSizeValue($index)
    {
        unset($this->bedPillowSizeValue[$index]);
    }

    /**
     * Gets as bedPillowSizeValue
     *
     * @return string[]
     */
    public function getBedPillowSizeValue()
    {
        return $this->bedPillowSizeValue;
    }

    /**
     * Sets a new bedPillowSizeValue
     *
     * @param string $bedPillowSizeValue
     * @return self
     */
    public function setBedPillowSizeValue(array $bedPillowSizeValue)
    {
        $this->bedPillowSizeValue = $bedPillowSizeValue;
        return $this;
    }


}

