<?php

namespace WalmartDSV;

/**
 * Class representing CpuSocketType
 *
 * The interface of, or required by, the central processing unit.
 * XSD Type: CpuSocketType
 */
class CpuSocketType
{

    /**
     * @var string[] $cpuSocketTypeValue
     */
    private $cpuSocketTypeValue = [
        
    ];

    /**
     * Adds as cpuSocketTypeValue
     *
     * @return self
     * @param string $cpuSocketTypeValue
     */
    public function addToCpuSocketTypeValue($cpuSocketTypeValue)
    {
        $this->cpuSocketTypeValue[] = $cpuSocketTypeValue;
        return $this;
    }

    /**
     * isset cpuSocketTypeValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetCpuSocketTypeValue($index)
    {
        return isset($this->cpuSocketTypeValue[$index]);
    }

    /**
     * unset cpuSocketTypeValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetCpuSocketTypeValue($index)
    {
        unset($this->cpuSocketTypeValue[$index]);
    }

    /**
     * Gets as cpuSocketTypeValue
     *
     * @return string[]
     */
    public function getCpuSocketTypeValue()
    {
        return $this->cpuSocketTypeValue;
    }

    /**
     * Sets a new cpuSocketTypeValue
     *
     * @param string $cpuSocketTypeValue
     * @return self
     */
    public function setCpuSocketTypeValue(array $cpuSocketTypeValue)
    {
        $this->cpuSocketTypeValue = $cpuSocketTypeValue;
        return $this;
    }


}

