<?php

namespace WalmartDSV\ElectricalType;

/**
 * Class representing VoltsAType
 *
 * Number of volts the product produces or requires. The significance of voltage varies with type of product. For example, if product is an appliance, used to describe the outlet voltage required. If an item (such as a power supply) both produces and requires different voltages, each type of voltage should be included.
 */
class VoltsAType
{

    /**
     * @var \WalmartDSV\ElectricalType\VoltsAType\VoltAType $volt
     */
    private $volt = null;

    /**
     * Gets as volt
     *
     * @return \WalmartDSV\ElectricalType\VoltsAType\VoltAType
     */
    public function getVolt()
    {
        return $this->volt;
    }

    /**
     * Sets a new volt
     *
     * @param \WalmartDSV\ElectricalType\VoltsAType\VoltAType $volt
     * @return self
     */
    public function setVolt(\WalmartDSV\ElectricalType\VoltsAType\VoltAType $volt)
    {
        $this->volt = $volt;
        return $this;
    }


}

