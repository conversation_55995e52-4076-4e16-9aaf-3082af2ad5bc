<?php

namespace WalmartDSV\ElectricalType;

/**
 * Class representing CompatibleConduitSizesAType
 *
 * The sizes of conduit that this product is compatible with, or fits. Typically used for conduit fittings based on diameter.
 */
class CompatibleConduitSizesAType
{

    /**
     * @var \WalmartDSV\ElectricalType\CompatibleConduitSizesAType\CompatibleConduitSizeAType[] $compatibleConduitSize
     */
    private $compatibleConduitSize = [
        
    ];

    /**
     * Adds as compatibleConduitSize
     *
     * @param \WalmartDSV\ElectricalType\CompatibleConduitSizesAType\CompatibleConduitSizeAType $compatibleConduitSize
     *@return self
     */
    public function addToCompatibleConduitSize(\WalmartDSV\ElectricalType\CompatibleConduitSizesAType\CompatibleConduitSizeAType $compatibleConduitSize)
    {
        $this->compatibleConduitSize[] = $compatibleConduitSize;
        return $this;
    }

    /**
     * isset compatibleConduitSize
     *
     * @param int|string $index
     * @return bool
     */
    public function issetCompatibleConduitSize($index)
    {
        return isset($this->compatibleConduitSize[$index]);
    }

    /**
     * unset compatibleConduitSize
     *
     * @param int|string $index
     * @return void
     */
    public function unsetCompatibleConduitSize($index)
    {
        unset($this->compatibleConduitSize[$index]);
    }

    /**
     * Gets as compatibleConduitSize
     *
     * @return \WalmartDSV\ElectricalType\CompatibleConduitSizesAType\CompatibleConduitSizeAType[]
     */
    public function getCompatibleConduitSize()
    {
        return $this->compatibleConduitSize;
    }

    /**
     * Sets a new compatibleConduitSize
     *
     * @param \WalmartDSV\ElectricalType\CompatibleConduitSizesAType\CompatibleConduitSizeAType[] $compatibleConduitSize
     * @return self
     */
    public function setCompatibleConduitSize(array $compatibleConduitSize)
    {
        $this->compatibleConduitSize = $compatibleConduitSize;
        return $this;
    }


}

