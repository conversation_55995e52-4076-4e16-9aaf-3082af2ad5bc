<?php

namespace WalmartDSV\ElectricalType;

/**
 * Class representing BeamAngleAType
 */
class BeamAngleAType
{

    /**
     * @var int $measure
     */
    private $measure = null;

    /**
     * @var string $unit
     */
    private $unit = null;

    /**
     * Gets as measure
     *
     * @return int
     */
    public function getMeasure()
    {
        return $this->measure;
    }

    /**
     * Sets a new measure
     *
     * @param int $measure
     * @return self
     */
    public function setMeasure($measure)
    {
        $this->measure = $measure;
        return $this;
    }

    /**
     * Gets as unit
     *
     * @return string
     */
    public function getUnit()
    {
        return $this->unit;
    }

    /**
     * Sets a new unit
     *
     * @param string $unit
     * @return self
     */
    public function setUnit($unit)
    {
        $this->unit = $unit;
        return $this;
    }


}

