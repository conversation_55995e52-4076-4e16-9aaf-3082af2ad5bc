<?php

namespace WalmartDSV;

/**
 * Class representing ToysCategoryType
 *
 *
 * XSD Type: ToysCategory
 */
class ToysCategoryType
{

    /**
     * @var \WalmartDSV\ToysType $toys
     */
    private $toys = null;

    /**
     * Gets as toys
     *
     * @return \WalmartDSV\ToysType
     */
    public function getToys()
    {
        return $this->toys;
    }

    /**
     * Sets a new toys
     *
     * @param \WalmartDSV\ToysType $toys
     * @return self
     */
    public function setToys(\WalmartDSV\ToysType $toys)
    {
        $this->toys = $toys;
        return $this;
    }


}

