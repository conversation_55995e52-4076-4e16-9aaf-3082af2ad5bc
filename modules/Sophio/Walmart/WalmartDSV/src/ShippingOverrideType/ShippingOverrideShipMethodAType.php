<?php

namespace WalmartDSV\ShippingOverrideType;

/**
 * Class representing ShippingOverrideShipMethodAType
 *
 * The ship method for which the ship prices will be provided.
 */
class ShippingOverrideShipMethodAType
{

    /**
     * @var bool $override
     */
    private $override = null;

    /**
     * @var string $shippingOverrideShipMethodValue
     */
    private $shippingOverrideShipMethodValue = null;

    /**
     * Gets as override
     *
     * @return bool
     */
    public function getOverride()
    {
        return $this->override;
    }

    /**
     * Sets a new override
     *
     * @param bool $override
     * @return self
     */
    public function setOverride($override)
    {
        $this->override = $override;
        return $this;
    }

    /**
     * Gets as shippingOverrideShipMethodValue
     *
     * @return string
     */
    public function getShippingOverrideShipMethodValue()
    {
        return $this->shippingOverrideShipMethodValue;
    }

    /**
     * Sets a new shippingOverrideShipMethodValue
     *
     * @param string $shippingOverrideShipMethodValue
     * @return self
     */
    public function setShippingOverrideShipMethodValue($shippingOverrideShipMethodValue)
    {
        $this->shippingOverrideShipMethodValue = $shippingOverrideShipMethodValue;
        return $this;
    }


}

