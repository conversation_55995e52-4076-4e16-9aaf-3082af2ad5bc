<?php

namespace WalmartDSV;

/**
 * Class representing ColorType
 *
 * Color as described by the manufacturer.
 * XSD Type: Color
 */
class ColorType
{

    /**
     * @var string $colorValue
     */
    private $colorValue = null;

    /**
     * Gets as colorValue
     *
     * @return string
     */
    public function getColorValue()
    {
        return $this->colorValue;
    }

    /**
     * Sets a new colorValue
     *
     * @param string $colorValue
     * @return self
     */
    public function setColorValue($colorValue)
    {
        $this->colorValue = $colorValue;
        return $this;
    }


}

