<?php

namespace WalmartDSV;

/**
 * Class representing BodyPartsType
 *
 * The body part/s for which the item is intended.
 * XSD Type: BodyParts
 */
class BodyPartsType
{

    /**
     * @var string[] $bodyPart
     */
    private $bodyPart = [
        
    ];

    /**
     * Adds as bodyPart
     *
     * @return self
     * @param string $bodyPart
     */
    public function addToBodyPart($bodyPart)
    {
        $this->bodyPart[] = $bodyPart;
        return $this;
    }

    /**
     * isset bodyPart
     *
     * @param int|string $index
     * @return bool
     */
    public function issetBodyPart($index)
    {
        return isset($this->bodyPart[$index]);
    }

    /**
     * unset bodyPart
     *
     * @param int|string $index
     * @return void
     */
    public function unsetBodyPart($index)
    {
        unset($this->bodyPart[$index]);
    }

    /**
     * Gets as bodyPart
     *
     * @return string[]
     */
    public function getBodyPart()
    {
        return $this->bodyPart;
    }

    /**
     * Sets a new bodyPart
     *
     * @param string $bodyPart
     * @return self
     */
    public function setBodyPart(array $bodyPart)
    {
        $this->bodyPart = $bodyPart;
        return $this;
    }


}

