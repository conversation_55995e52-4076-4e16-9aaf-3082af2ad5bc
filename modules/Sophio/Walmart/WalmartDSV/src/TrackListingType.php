<?php

namespace WalmartDSV;

/**
 * Class representing TrackListingType
 *
 *
 * XSD Type: trackListing
 */
class TrackListingType
{

    /**
     * The number of the individual track on an album.
     *
     * @var int $trackNumber
     */
    private $trackNumber = null;

    /**
     * The name of the individual track on an album.
     *
     * @var string $trackName
     */
    private $trackName = null;

    /**
     * The duration of the individual track on an album.
     *
     * @var string $trackDuration
     */
    private $trackDuration = null;

    /**
     * Gets as trackNumber
     *
     * The number of the individual track on an album.
     *
     * @return int
     */
    public function getTrackNumber()
    {
        return $this->trackNumber;
    }

    /**
     * Sets a new trackNumber
     *
     * The number of the individual track on an album.
     *
     * @param int $trackNumber
     * @return self
     */
    public function setTrackNumber($trackNumber)
    {
        $this->trackNumber = $trackNumber;
        return $this;
    }

    /**
     * Gets as trackName
     *
     * The name of the individual track on an album.
     *
     * @return string
     */
    public function getTrackName()
    {
        return $this->trackName;
    }

    /**
     * Sets a new trackName
     *
     * The name of the individual track on an album.
     *
     * @param string $trackName
     * @return self
     */
    public function setTrackName($trackName)
    {
        $this->trackName = $trackName;
        return $this;
    }

    /**
     * Gets as trackDuration
     *
     * The duration of the individual track on an album.
     *
     * @return string
     */
    public function getTrackDuration()
    {
        return $this->trackDuration;
    }

    /**
     * Sets a new trackDuration
     *
     * The duration of the individual track on an album.
     *
     * @param string $trackDuration
     * @return self
     */
    public function setTrackDuration($trackDuration)
    {
        $this->trackDuration = $trackDuration;
        return $this;
    }


}

