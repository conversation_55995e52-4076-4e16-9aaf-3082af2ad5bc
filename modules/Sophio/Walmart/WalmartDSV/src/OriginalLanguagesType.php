<?php

namespace WalmartDSV;

/**
 * Class representing OriginalLanguagesType
 *
 * The original language of the work. Usually this will be one language, but occasionally more than one is appropriate. For example, if a movie is dubbed in English but the original language is Chinese, enter "Chinese."
 * XSD Type: OriginalLanguages
 */
class OriginalLanguagesType
{

    /**
     * @var string[] $originalLanguage
     */
    private $originalLanguage = [
        
    ];

    /**
     * Adds as originalLanguage
     *
     * @return self
     * @param string $originalLanguage
     */
    public function addToOriginalLanguage($originalLanguage)
    {
        $this->originalLanguage[] = $originalLanguage;
        return $this;
    }

    /**
     * isset originalLanguage
     *
     * @param int|string $index
     * @return bool
     */
    public function issetOriginalLanguage($index)
    {
        return isset($this->originalLanguage[$index]);
    }

    /**
     * unset originalLanguage
     *
     * @param int|string $index
     * @return void
     */
    public function unsetOriginalLanguage($index)
    {
        unset($this->originalLanguage[$index]);
    }

    /**
     * Gets as originalLanguage
     *
     * @return string[]
     */
    public function getOriginalLanguage()
    {
        return $this->originalLanguage;
    }

    /**
     * Sets a new originalLanguage
     *
     * @param string $originalLanguage
     * @return self
     */
    public function setOriginalLanguage(array $originalLanguage)
    {
        $this->originalLanguage = $originalLanguage;
        return $this;
    }


}

