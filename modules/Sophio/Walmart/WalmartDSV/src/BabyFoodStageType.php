<?php

namespace WalmartDSV;

/**
 * Class representing BabyFoodStageType
 *
 * Attribute designed to capture the generalized age grouping (commonly referred to as "stage") of a given baby food, as specified by manufacturer. Specific description of each stage varies with baby food producer. Example descriptions: Stage 1 - Foods have a single ingredient and are pureed and generally contain about 2.5 oz of fruits, veggies or meats. Stage 2 – Foods are strained instead of pureed and have a combination of fruits or veggies instead of single ingredients, contain larger portions. Stage 3 – Foods are mashed and have more texture than the pureed foods and may have bits and chunks of meats or veggies.
 * XSD Type: BabyFoodStage
 */
class BabyFoodStageType
{

    /**
     * @var string[] $babyFoodStageValue
     */
    private $babyFoodStageValue = [
        
    ];

    /**
     * Adds as babyFoodStageValue
     *
     * @return self
     * @param string $babyFoodStageValue
     */
    public function addToBabyFoodStageValue($babyFoodStageValue)
    {
        $this->babyFoodStageValue[] = $babyFoodStageValue;
        return $this;
    }

    /**
     * isset babyFoodStageValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetBabyFoodStageValue($index)
    {
        return isset($this->babyFoodStageValue[$index]);
    }

    /**
     * unset babyFoodStageValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetBabyFoodStageValue($index)
    {
        unset($this->babyFoodStageValue[$index]);
    }

    /**
     * Gets as babyFoodStageValue
     *
     * @return string[]
     */
    public function getBabyFoodStageValue()
    {
        return $this->babyFoodStageValue;
    }

    /**
     * Sets a new babyFoodStageValue
     *
     * @param string $babyFoodStageValue
     * @return self
     */
    public function setBabyFoodStageValue(array $babyFoodStageValue)
    {
        $this->babyFoodStageValue = $babyFoodStageValue;
        return $this;
    }


}

