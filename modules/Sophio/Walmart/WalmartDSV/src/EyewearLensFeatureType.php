<?php

namespace WalmartDSV;

/**
 * Class representing EyewearLensFeatureType
 *
 * Features specific to eyewear lenses, including lens treatments and modular lens features.
 * XSD Type: EyewearLensFeature
 */
class EyewearLensFeatureType
{

    /**
     * @var string[] $eyewearLensFeatureValue
     */
    private $eyewearLensFeatureValue = [
        
    ];

    /**
     * Adds as eyewearLensFeatureValue
     *
     * @return self
     * @param string $eyewearLensFeatureValue
     */
    public function addToEyewearLensFeatureValue($eyewearLensFeatureValue)
    {
        $this->eyewearLensFeatureValue[] = $eyewearLensFeatureValue;
        return $this;
    }

    /**
     * isset eyewearLensFeatureValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetEyewearLensFeatureValue($index)
    {
        return isset($this->eyewearLensFeatureValue[$index]);
    }

    /**
     * unset eyewearLensFeatureValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetEyewearLensFeatureValue($index)
    {
        unset($this->eyewearLensFeatureValue[$index]);
    }

    /**
     * Gets as eyewearLensFeatureValue
     *
     * @return string[]
     */
    public function getEyewearLensFeatureValue()
    {
        return $this->eyewearLensFeatureValue;
    }

    /**
     * Sets a new eyewearLensFeatureValue
     *
     * @param string $eyewearLensFeatureValue
     * @return self
     */
    public function setEyewearLensFeatureValue(array $eyewearLensFeatureValue)
    {
        $this->eyewearLensFeatureValue = $eyewearLensFeatureValue;
        return $this;
    }


}

