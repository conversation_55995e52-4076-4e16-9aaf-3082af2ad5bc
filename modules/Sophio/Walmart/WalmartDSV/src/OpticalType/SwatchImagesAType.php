<?php

namespace WalmartDSV\OpticalType;

/**
 * Class representing SwatchImagesAType
 *
 * Enter the swatch image location in "Swatch Image URL" and its corresponding variant attribute name in "Swatch Variant Attribute". Required for products with visual variations, like color or pattern. List the swatches in the order you recommend they appear on the site.
 */
class SwatchImagesAType
{

    /**
     * @var \WalmartDSV\OpticalType\SwatchImagesAType\SwatchImageAType[] $swatchImage
     */
    private $swatchImage = [
        
    ];

    /**
     * Adds as swatchImage
     *
     * @param \WalmartDSV\OpticalType\SwatchImagesAType\SwatchImageAType $swatchImage
     *@return self
     */
    public function addToSwatchImage(\WalmartDSV\OpticalType\SwatchImagesAType\SwatchImageAType $swatchImage)
    {
        $this->swatchImage[] = $swatchImage;
        return $this;
    }

    /**
     * isset swatchImage
     *
     * @param int|string $index
     * @return bool
     */
    public function issetSwatchImage($index)
    {
        return isset($this->swatchImage[$index]);
    }

    /**
     * unset swatchImage
     *
     * @param int|string $index
     * @return void
     */
    public function unsetSwatchImage($index)
    {
        unset($this->swatchImage[$index]);
    }

    /**
     * Gets as swatchImage
     *
     * @return \WalmartDSV\OpticalType\SwatchImagesAType\SwatchImageAType[]
     */
    public function getSwatchImage()
    {
        return $this->swatchImage;
    }

    /**
     * Sets a new swatchImage
     *
     * @param \WalmartDSV\OpticalType\SwatchImagesAType\SwatchImageAType[] $swatchImage
     * @return self
     */
    public function setSwatchImage(array $swatchImage)
    {
        $this->swatchImage = $swatchImage;
        return $this;
    }


}

