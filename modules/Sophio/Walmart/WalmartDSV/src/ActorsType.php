<?php

namespace WalmartDSV;

/**
 * Class representing ActorsType
 *
 * Actors who receive top billing in a movie or television show.
 * XSD Type: Actors
 */
class ActorsType
{

    /**
     * @var string[] $actor
     */
    private $actor = [
        
    ];

    /**
     * Adds as actor
     *
     * @return self
     * @param string $actor
     */
    public function addToActor($actor)
    {
        $this->actor[] = $actor;
        return $this;
    }

    /**
     * isset actor
     *
     * @param int|string $index
     * @return bool
     */
    public function issetActor($index)
    {
        return isset($this->actor[$index]);
    }

    /**
     * unset actor
     *
     * @param int|string $index
     * @return void
     */
    public function unsetActor($index)
    {
        unset($this->actor[$index]);
    }

    /**
     * Gets as actor
     *
     * @return string[]
     */
    public function getActor()
    {
        return $this->actor;
    }

    /**
     * Sets a new actor
     *
     * @param string $actor
     * @return self
     */
    public function setActor(array $actor)
    {
        $this->actor = $actor;
        return $this;
    }


}

