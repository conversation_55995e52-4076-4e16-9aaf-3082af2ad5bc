<?php

namespace WalmartDSV;

/**
 * Class representing BisacSubjectCodeType
 *
 * A standardized code from the Book Industry Study Group used to assign a genre and classify a book based on its topical content.
 * XSD Type: BisacSubjectCode
 */
class BisacSubjectCodeType
{

    /**
     * @var string[] $bisacSubjectCode
     */
    private $bisacSubjectCode = [
        
    ];

    /**
     * Adds as bisacSubjectCode
     *
     * @return self
     * @param string $bisacSubjectCode
     */
    public function addToBisacSubjectCode($bisacSubjectCode)
    {
        $this->bisacSubjectCode[] = $bisacSubjectCode;
        return $this;
    }

    /**
     * isset bisacSubjectCode
     *
     * @param int|string $index
     * @return bool
     */
    public function issetBisacSubjectCode($index)
    {
        return isset($this->bisacSubjectCode[$index]);
    }

    /**
     * unset bisacSubjectCode
     *
     * @param int|string $index
     * @return void
     */
    public function unsetBisacSubjectCode($index)
    {
        unset($this->bisacSubjectCode[$index]);
    }

    /**
     * Gets as bisacSubjectCode
     *
     * @return string[]
     */
    public function getBisacSubjectCode()
    {
        return $this->bisacSubjectCode;
    }

    /**
     * Sets a new bisacSubjectCode
     *
     * @param string $bisacSubjectCode
     * @return self
     */
    public function setBisacSubjectCode(array $bisacSubjectCode)
    {
        $this->bisacSubjectCode = $bisacSubjectCode;
        return $this;
    }


}

