<?php

namespace WalmartDSV;

/**
 * Class representing FillMaterialType
 *
 * The material used to stuff the item (in a cushion or plush toy, for example).
 * XSD Type: FillMaterial
 */
class FillMaterialType
{

    /**
     * @var string[] $fillMaterialValue
     */
    private $fillMaterialValue = [
        
    ];

    /**
     * Adds as fillMaterialValue
     *
     * @return self
     * @param string $fillMaterialValue
     */
    public function addToFillMaterialValue($fillMaterialValue)
    {
        $this->fillMaterialValue[] = $fillMaterialValue;
        return $this;
    }

    /**
     * isset fillMaterialValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFillMaterialValue($index)
    {
        return isset($this->fillMaterialValue[$index]);
    }

    /**
     * unset fillMaterialValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFillMaterialValue($index)
    {
        unset($this->fillMaterialValue[$index]);
    }

    /**
     * Gets as fillMaterialValue
     *
     * @return string[]
     */
    public function getFillMaterialValue()
    {
        return $this->fillMaterialValue;
    }

    /**
     * Sets a new fillMaterialValue
     *
     * @param string $fillMaterialValue
     * @return self
     */
    public function setFillMaterialValue(array $fillMaterialValue)
    {
        $this->fillMaterialValue = $fillMaterialValue;
        return $this;
    }


}

