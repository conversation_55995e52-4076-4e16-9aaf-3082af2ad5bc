<?php

namespace WalmartDSV;

/**
 * Class representing WaistStyleType
 *
 * Waist styles/defining features.
 * XSD Type: WaistStyle
 */
class WaistStyleType
{

    /**
     * @var string $waistStyleValue
     */
    private $waistStyleValue = null;

    /**
     * Gets as waistStyleValue
     *
     * @return string
     */
    public function getWaistStyleValue()
    {
        return $this->waistStyleValue;
    }

    /**
     * Sets a new waistStyleValue
     *
     * @param string $waistStyleValue
     * @return self
     */
    public function setWaistStyleValue($waistStyleValue)
    {
        $this->waistStyleValue = $waistStyleValue;
        return $this;
    }


}

