<?php

namespace WalmartDSV;

/**
 * Class representing OccasionType
 *
 * The particular target time, event, or holiday for the product.
 * XSD Type: Occasion
 */
class OccasionType
{

    /**
     * @var string $occasionValue
     */
    private $occasionValue = null;

    /**
     * Gets as occasionValue
     *
     * @return string
     */
    public function getOccasionValue()
    {
        return $this->occasionValue;
    }

    /**
     * Sets a new occasionValue
     *
     * @param string $occasionValue
     * @return self
     */
    public function setOccasionValue($occasionValue)
    {
        $this->occasionValue = $occasionValue;
        return $this;
    }


}

