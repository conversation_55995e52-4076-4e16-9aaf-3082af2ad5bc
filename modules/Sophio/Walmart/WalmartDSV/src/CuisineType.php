<?php

namespace WalmartDSV;

/**
 * Class representing CuisineType
 *
 * Indicates the cooking style, especially regional or cultural cuisines, you might use the food to prepare.
 * XSD Type: Cuisine
 */
class CuisineType
{

    /**
     * @var string[] $cuisineValue
     */
    private $cuisineValue = [
        
    ];

    /**
     * Adds as cuisineValue
     *
     * @return self
     * @param string $cuisineValue
     */
    public function addToCuisineValue($cuisineValue)
    {
        $this->cuisineValue[] = $cuisineValue;
        return $this;
    }

    /**
     * isset cuisineValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetCuisineValue($index)
    {
        return isset($this->cuisineValue[$index]);
    }

    /**
     * unset cuisineValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetCuisineValue($index)
    {
        unset($this->cuisineValue[$index]);
    }

    /**
     * Gets as cuisineValue
     *
     * @return string[]
     */
    public function getCuisineValue()
    {
        return $this->cuisineValue;
    }

    /**
     * Sets a new cuisineValue
     *
     * @param string $cuisineValue
     * @return self
     */
    public function setCuisineValue(array $cuisineValue)
    {
        $this->cuisineValue = $cuisineValue;
        return $this;
    }


}

