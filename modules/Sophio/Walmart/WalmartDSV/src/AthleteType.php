<?php

namespace WalmartDSV;

/**
 * Class representing AthleteType
 *
 * A well-known athlete associated with a product, if applicable. This is used to group items in Fan Shop, not to describe a line of clothing.
 * XSD Type: Athlete
 */
class AthleteType
{

    /**
     * @var string $athleteValue
     */
    private $athleteValue = null;

    /**
     * Gets as athleteValue
     *
     * @return string
     */
    public function getAthleteValue()
    {
        return $this->athleteValue;
    }

    /**
     * Sets a new athleteValue
     *
     * @param string $athleteValue
     * @return self
     */
    public function setAthleteValue($athleteValue)
    {
        $this->athleteValue = $athleteValue;
        return $this;
    }


}

