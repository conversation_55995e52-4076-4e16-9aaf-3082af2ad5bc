<?php

namespace WalmartDSV;

/**
 * Class representing ScarfStyleType
 *
 * Styles specific to scarves.
 * XSD Type: ScarfStyle
 */
class ScarfStyleType
{

    /**
     * @var string $scarfStyleValue
     */
    private $scarfStyleValue = null;

    /**
     * Gets as scarfStyleValue
     *
     * @return string
     */
    public function getScarfStyleValue()
    {
        return $this->scarfStyleValue;
    }

    /**
     * Sets a new scarfStyleValue
     *
     * @param string $scarfStyleValue
     * @return self
     */
    public function setScarfStyleValue($scarfStyleValue)
    {
        $this->scarfStyleValue = $scarfStyleValue;
        return $this;
    }


}

