<?php

namespace WalmartDSV;

/**
 * Class representing LensType
 *
 * Whether the lens is single, multifocal, or tinted
 * XSD Type: LensType
 */
class LensType
{

    /**
     * @var string[] $lensTypeValue
     */
    private $lensTypeValue = [
        
    ];

    /**
     * Adds as lensTypeValue
     *
     * @return self
     * @param string $lensTypeValue
     */
    public function addToLensTypeValue($lensTypeValue)
    {
        $this->lensTypeValue[] = $lensTypeValue;
        return $this;
    }

    /**
     * isset lensTypeValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetLensTypeValue($index)
    {
        return isset($this->lensTypeValue[$index]);
    }

    /**
     * unset lensTypeValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetLensTypeValue($index)
    {
        unset($this->lensTypeValue[$index]);
    }

    /**
     * Gets as lensTypeValue
     *
     * @return string[]
     */
    public function getLensTypeValue()
    {
        return $this->lensTypeValue;
    }

    /**
     * Sets a new lensTypeValue
     *
     * @param string $lensTypeValue
     * @return self
     */
    public function setLensTypeValue(array $lensTypeValue)
    {
        $this->lensTypeValue = $lensTypeValue;
        return $this;
    }


}

