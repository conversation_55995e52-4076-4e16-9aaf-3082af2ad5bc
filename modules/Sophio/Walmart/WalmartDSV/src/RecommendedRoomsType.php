<?php

namespace WalmartDSV;

/**
 * Class representing RecommendedRoomsType
 *
 * The rooms where the item is likely or recommended to be used.
 * XSD Type: RecommendedRooms
 */
class RecommendedRoomsType
{

    /**
     * @var string[] $recommendedRoom
     */
    private $recommendedRoom = [
        
    ];

    /**
     * Adds as recommendedRoom
     *
     * @return self
     * @param string $recommendedRoom
     */
    public function addToRecommendedRoom($recommendedRoom)
    {
        $this->recommendedRoom[] = $recommendedRoom;
        return $this;
    }

    /**
     * isset recommendedRoom
     *
     * @param int|string $index
     * @return bool
     */
    public function issetRecommendedRoom($index)
    {
        return isset($this->recommendedRoom[$index]);
    }

    /**
     * unset recommendedRoom
     *
     * @param int|string $index
     * @return void
     */
    public function unsetRecommendedRoom($index)
    {
        unset($this->recommendedRoom[$index]);
    }

    /**
     * Gets as recommendedRoom
     *
     * @return string[]
     */
    public function getRecommendedRoom()
    {
        return $this->recommendedRoom;
    }

    /**
     * Sets a new recommendedRoom
     *
     * @param string $recommendedRoom
     * @return self
     */
    public function setRecommendedRoom(array $recommendedRoom)
    {
        $this->recommendedRoom = $recommendedRoom;
        return $this;
    }


}

