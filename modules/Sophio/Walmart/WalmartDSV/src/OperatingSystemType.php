<?php

namespace WalmartDSV;

/**
 * Class representing OperatingSystemType
 *
 * The operating system loaded on the device or upon which the software is designed to operate.
 * XSD Type: OperatingSystem
 */
class OperatingSystemType
{

    /**
     * @var string[] $operatingSystemValue
     */
    private $operatingSystemValue = [
        
    ];

    /**
     * Adds as operatingSystemValue
     *
     * @return self
     * @param string $operatingSystemValue
     */
    public function addToOperatingSystemValue($operatingSystemValue)
    {
        $this->operatingSystemValue[] = $operatingSystemValue;
        return $this;
    }

    /**
     * isset operatingSystemValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetOperatingSystemValue($index)
    {
        return isset($this->operatingSystemValue[$index]);
    }

    /**
     * unset operatingSystemValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetOperatingSystemValue($index)
    {
        unset($this->operatingSystemValue[$index]);
    }

    /**
     * Gets as operatingSystemValue
     *
     * @return string[]
     */
    public function getOperatingSystemValue()
    {
        return $this->operatingSystemValue;
    }

    /**
     * Sets a new operatingSystemValue
     *
     * @param string $operatingSystemValue
     * @return self
     */
    public function setOperatingSystemValue(array $operatingSystemValue)
    {
        $this->operatingSystemValue = $operatingSystemValue;
        return $this;
    }


}

