<?php

namespace WalmartDSV;

/**
 * Class representing CompatibleDevicesType
 *
 * A list of the devices compatible with the item.
 * XSD Type: CompatibleDevices
 */
class CompatibleDevicesType
{

    /**
     * @var string $compatibleDevice
     */
    private $compatibleDevice = null;

    /**
     * Gets as compatibleDevice
     *
     * @return string
     */
    public function getCompatibleDevice()
    {
        return $this->compatibleDevice;
    }

    /**
     * Sets a new compatibleDevice
     *
     * @param string $compatibleDevice
     * @return self
     */
    public function setCompatibleDevice($compatibleDevice)
    {
        $this->compatibleDevice = $compatibleDevice;
        return $this;
    }


}

