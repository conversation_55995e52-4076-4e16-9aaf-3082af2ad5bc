<?php

namespace WalmartDSV\HomeOtherType;

/**
 * Class representing VariantAttributeNamesAType
 */
class VariantAttributeNamesAType
{

    /**
     * @var string[] $variantAttributeName
     */
    private $variantAttributeName = [
        
    ];

    /**
     * Adds as variantAttributeName
     *
     * @return self
     * @param string $variantAttributeName
     */
    public function addToVariantAttributeName($variantAttributeName)
    {
        $this->variantAttributeName[] = $variantAttributeName;
        return $this;
    }

    /**
     * isset variantAttributeName
     *
     * @param int|string $index
     * @return bool
     */
    public function issetVariantAttributeName($index)
    {
        return isset($this->variantAttributeName[$index]);
    }

    /**
     * unset variantAttributeName
     *
     * @param int|string $index
     * @return void
     */
    public function unsetVariantAttributeName($index)
    {
        unset($this->variantAttributeName[$index]);
    }

    /**
     * Gets as variantAttributeName
     *
     * @return string[]
     */
    public function getVariantAttributeName()
    {
        return $this->variantAttributeName;
    }

    /**
     * Sets a new variantAttributeName
     *
     * @param string $variantAttributeName
     * @return self
     */
    public function setVariantAttributeName(array $variantAttributeName)
    {
        $this->variantAttributeName = $variantAttributeName;
        return $this;
    }


}

