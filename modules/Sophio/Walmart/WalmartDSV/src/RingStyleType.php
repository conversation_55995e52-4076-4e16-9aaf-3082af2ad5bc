<?php

namespace WalmartDSV;

/**
 * Class representing RingStyleType
 *
 * Form or design of a ring
 * XSD Type: RingStyle
 */
class RingStyleType
{

    /**
     * @var string[] $ringStyleValue
     */
    private $ringStyleValue = [
        
    ];

    /**
     * Adds as ringStyleValue
     *
     * @return self
     * @param string $ringStyleValue
     */
    public function addToRingStyleValue($ringStyleValue)
    {
        $this->ringStyleValue[] = $ringStyleValue;
        return $this;
    }

    /**
     * isset ringStyleValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetRingStyleValue($index)
    {
        return isset($this->ringStyleValue[$index]);
    }

    /**
     * unset ringStyleValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetRingStyleValue($index)
    {
        unset($this->ringStyleValue[$index]);
    }

    /**
     * Gets as ringStyleValue
     *
     * @return string[]
     */
    public function getRingStyleValue()
    {
        return $this->ringStyleValue;
    }

    /**
     * Sets a new ringStyleValue
     *
     * @param string $ringStyleValue
     * @return self
     */
    public function setRingStyleValue(array $ringStyleValue)
    {
        $this->ringStyleValue = $ringStyleValue;
        return $this;
    }


}

