<?php

namespace WalmartDSV;

/**
 * Class representing RugStyleType
 *
 * Style of rug expressed in fashion or form. Use the example values; if your style does not appear, you may enter it.
 * XSD Type: RugStyle
 */
class RugStyleType
{

    /**
     * @var string[] $rugStyleValue
     */
    private $rugStyleValue = [
        
    ];

    /**
     * Adds as rugStyleValue
     *
     * @return self
     * @param string $rugStyleValue
     */
    public function addToRugStyleValue($rugStyleValue)
    {
        $this->rugStyleValue[] = $rugStyleValue;
        return $this;
    }

    /**
     * isset rugStyleValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetRugStyleValue($index)
    {
        return isset($this->rugStyleValue[$index]);
    }

    /**
     * unset rugStyleValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetRugStyleValue($index)
    {
        unset($this->rugStyleValue[$index]);
    }

    /**
     * Gets as rugStyleValue
     *
     * @return string[]
     */
    public function getRugStyleValue()
    {
        return $this->rugStyleValue;
    }

    /**
     * Sets a new rugStyleValue
     *
     * @param string $rugStyleValue
     * @return self
     */
    public function setRugStyleValue(array $rugStyleValue)
    {
        $this->rugStyleValue = $rugStyleValue;
        return $this;
    }


}

