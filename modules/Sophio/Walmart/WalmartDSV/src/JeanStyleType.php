<?php

namespace WalmartDSV;

/**
 * Class representing JeanStyleType
 *
 * Style terms specific to jeans.
 * XSD Type: JeanStyle
 */
class JeanStyleType
{

    /**
     * @var string $jeanStyleValue
     */
    private $jeanStyleValue = null;

    /**
     * Gets as jeanStyleValue
     *
     * @return string
     */
    public function getJeanStyleValue()
    {
        return $this->jeanStyleValue;
    }

    /**
     * Sets a new jeanStyleValue
     *
     * @param string $jeanStyleValue
     * @return self
     */
    public function setJeanStyleValue($jeanStyleValue)
    {
        $this->jeanStyleValue = $jeanStyleValue;
        return $this;
    }


}

