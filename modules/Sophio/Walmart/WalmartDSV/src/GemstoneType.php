<?php

namespace WalmartDSV;

/**
 * Class representing GemstoneType
 *
 * The gemstone trade name (i.e., amethyst, not purple quartz).
 * XSD Type: Gemstone
 */
class GemstoneType
{

    /**
     * @var string $gemstoneValue
     */
    private $gemstoneValue = null;

    /**
     * Gets as gemstoneValue
     *
     * @return string
     */
    public function getGemstoneValue()
    {
        return $this->gemstoneValue;
    }

    /**
     * Sets a new gemstoneValue
     *
     * @param string $gemstoneValue
     * @return self
     */
    public function setGemstoneValue($gemstoneValue)
    {
        $this->gemstoneValue = $gemstoneValue;
        return $this;
    }


}

