<?php

namespace WalmartDSV;

/**
 * Class representing SupplierOfferType
 *
 *
 * XSD Type: SupplierOffer
 */
class SupplierOfferType
{

    /**
     * The price the customer pays for the product. Please do not use commas or currency symbols.
     *
     * @var float $price
     */
    private $price = null;

    /**
     * The item's lowest advertised price as agreed upon by the supplier and retailer.
     *
     * @var float $minimumAdvertisedPrice
     */
    private $minimumAdvertisedPrice = null;

    /**
     * Indicates the date when the item is available to be published on the site.
     *
     * @var \DateTime $startDate
     */
    private $startDate = null;

    /**
     * Indicates the date when the partner's item should be removed from the site.
     *
     * @var \DateTime $endDate
     */
    private $endDate = null;

    /**
     * Select "Y" if your item cannot ship with another item in the same box. If marked "Y," the ship alone item will not be grouped for ship price calculation.
     *
     * @var string $mustShipAlone
     */
    private $mustShipAlone = null;

    /**
     * Selling unit measurements. The longest measurement (side) should always be entered as “depth" with the remaining measurements being entered either as width or height.
     *
     * @var \WalmartDSV\SupplierOfferType\ShippingDimensionsDepthAType $shippingDimensionsDepth
     */
    private $shippingDimensionsDepth = null;

    /**
     * Selling unit measurements. The longest measurement (side) should always be entered as “depth" with the remaining measurements being entered either as width or height.
     *
     * @var \WalmartDSV\SupplierOfferType\ShippingDimensionsWidthAType $shippingDimensionsWidth
     */
    private $shippingDimensionsWidth = null;

    /**
     * Selling unit measurements. The longest measurement (side) should always be entered as “depth" with the remaining measurements being entered either as width or height.
     *
     * @var \WalmartDSV\SupplierOfferType\ShippingDimensionsHeightAType $shippingDimensionsHeight
     */
    private $shippingDimensionsHeight = null;

    /**
     * Input the weight of the individual selling unit.
     *
     * @var \WalmartDSV\SupplierOfferType\ShippingWeightAType $shippingWeight
     */
    private $shippingWeight = null;

    /**
     * Can this product be shipped in the original packaging without being put in an outer box? Notes: 1) This is to indicate whether it's *possible* for the item to be shipped in original box, not whether it is required. 2) This was previously known as "Ships As-Is."
     *
     * @var string $shipsInOriginalPackaging
     */
    private $shipsInOriginalPackaging = null;

    /**
     * If there is other information that is not in the Spec, you can create one using a name-value pair.
     *
     * @var \WalmartDSV\AdditionalOfferAttributeType[] $additionalOfferAttributes
     */
    private $additionalOfferAttributes = null;

    /**
     * Is this item available for pre-order? If selected, additional information will need to be provided to properly set up the pre-order.
     *
     * @var string $isPreorder
     */
    private $isPreorder = null;

    /**
     * Provide the date that the item will be available.
     *
     * @var \DateTime $streetDate
     */
    private $streetDate = null;

    /**
     * Select the type of street date provided. DELIVER_BY indicates that the the item will be delivered to the customer by the provided street date. SHIP_BY indicates that the item will be shipped by the provided street date.
     *
     * @var string $streetDateType
     */
    private $streetDateType = null;

    /**
     * Gets as price
     *
     * The price the customer pays for the product. Please do not use commas or currency symbols.
     *
     * @return float
     */
    public function getPrice()
    {
        return $this->price;
    }

    /**
     * Sets a new price
     *
     * The price the customer pays for the product. Please do not use commas or currency symbols.
     *
     * @param float $price
     * @return self
     */
    public function setPrice($price)
    {
        $this->price = $price;
        return $this;
    }

    /**
     * Gets as minimumAdvertisedPrice
     *
     * The item's lowest advertised price as agreed upon by the supplier and retailer.
     *
     * @return float
     */
    public function getMinimumAdvertisedPrice()
    {
        return $this->minimumAdvertisedPrice;
    }

    /**
     * Sets a new minimumAdvertisedPrice
     *
     * The item's lowest advertised price as agreed upon by the supplier and retailer.
     *
     * @param float $minimumAdvertisedPrice
     * @return self
     */
    public function setMinimumAdvertisedPrice($minimumAdvertisedPrice)
    {
        $this->minimumAdvertisedPrice = $minimumAdvertisedPrice;
        return $this;
    }

    /**
     * Gets as startDate
     *
     * Indicates the date when the item is available to be published on the site.
     *
     * @return \DateTime
     */
    public function getStartDate()
    {
        return $this->startDate;
    }

    /**
     * Sets a new startDate
     *
     * Indicates the date when the item is available to be published on the site.
     *
     * @param \DateTime $startDate
     * @return self
     */
    public function setStartDate(\DateTime $startDate)
    {
        $this->startDate = $startDate;
        return $this;
    }

    /**
     * Gets as endDate
     *
     * Indicates the date when the partner's item should be removed from the site.
     *
     * @return \DateTime
     */
    public function getEndDate()
    {
        return $this->endDate;
    }

    /**
     * Sets a new endDate
     *
     * Indicates the date when the partner's item should be removed from the site.
     *
     * @param \DateTime $endDate
     * @return self
     */
    public function setEndDate(\DateTime $endDate)
    {
        $this->endDate = $endDate;
        return $this;
    }

    /**
     * Gets as mustShipAlone
     *
     * Select "Y" if your item cannot ship with another item in the same box. If marked "Y," the ship alone item will not be grouped for ship price calculation.
     *
     * @return string
     */
    public function getMustShipAlone()
    {
        return $this->mustShipAlone;
    }

    /**
     * Sets a new mustShipAlone
     *
     * Select "Y" if your item cannot ship with another item in the same box. If marked "Y," the ship alone item will not be grouped for ship price calculation.
     *
     * @param string $mustShipAlone
     * @return self
     */
    public function setMustShipAlone($mustShipAlone)
    {
        $this->mustShipAlone = $mustShipAlone;
        return $this;
    }

    /**
     * Gets as shippingDimensionsDepth
     *
     * Selling unit measurements. The longest measurement (side) should always be entered as “depth" with the remaining measurements being entered either as width or height.
     *
     * @return \WalmartDSV\SupplierOfferType\ShippingDimensionsDepthAType
     */
    public function getShippingDimensionsDepth()
    {
        return $this->shippingDimensionsDepth;
    }

    /**
     * Sets a new shippingDimensionsDepth
     *
     * Selling unit measurements. The longest measurement (side) should always be entered as “depth" with the remaining measurements being entered either as width or height.
     *
     * @param \WalmartDSV\SupplierOfferType\ShippingDimensionsDepthAType $shippingDimensionsDepth
     * @return self
     */
    public function setShippingDimensionsDepth(\WalmartDSV\SupplierOfferType\ShippingDimensionsDepthAType $shippingDimensionsDepth)
    {
        $this->shippingDimensionsDepth = $shippingDimensionsDepth;
        return $this;
    }

    /**
     * Gets as shippingDimensionsWidth
     *
     * Selling unit measurements. The longest measurement (side) should always be entered as “depth" with the remaining measurements being entered either as width or height.
     *
     * @return \WalmartDSV\SupplierOfferType\ShippingDimensionsWidthAType
     */
    public function getShippingDimensionsWidth()
    {
        return $this->shippingDimensionsWidth;
    }

    /**
     * Sets a new shippingDimensionsWidth
     *
     * Selling unit measurements. The longest measurement (side) should always be entered as “depth" with the remaining measurements being entered either as width or height.
     *
     * @param \WalmartDSV\SupplierOfferType\ShippingDimensionsWidthAType $shippingDimensionsWidth
     * @return self
     */
    public function setShippingDimensionsWidth(\WalmartDSV\SupplierOfferType\ShippingDimensionsWidthAType $shippingDimensionsWidth)
    {
        $this->shippingDimensionsWidth = $shippingDimensionsWidth;
        return $this;
    }

    /**
     * Gets as shippingDimensionsHeight
     *
     * Selling unit measurements. The longest measurement (side) should always be entered as “depth" with the remaining measurements being entered either as width or height.
     *
     * @return \WalmartDSV\SupplierOfferType\ShippingDimensionsHeightAType
     */
    public function getShippingDimensionsHeight()
    {
        return $this->shippingDimensionsHeight;
    }

    /**
     * Sets a new shippingDimensionsHeight
     *
     * Selling unit measurements. The longest measurement (side) should always be entered as “depth" with the remaining measurements being entered either as width or height.
     *
     * @param \WalmartDSV\SupplierOfferType\ShippingDimensionsHeightAType $shippingDimensionsHeight
     * @return self
     */
    public function setShippingDimensionsHeight(\WalmartDSV\SupplierOfferType\ShippingDimensionsHeightAType $shippingDimensionsHeight)
    {
        $this->shippingDimensionsHeight = $shippingDimensionsHeight;
        return $this;
    }

    /**
     * Gets as shippingWeight
     *
     * Input the weight of the individual selling unit.
     *
     * @return \WalmartDSV\SupplierOfferType\ShippingWeightAType
     */
    public function getShippingWeight()
    {
        return $this->shippingWeight;
    }

    /**
     * Sets a new shippingWeight
     *
     * Input the weight of the individual selling unit.
     *
     * @param \WalmartDSV\SupplierOfferType\ShippingWeightAType $shippingWeight
     * @return self
     */
    public function setShippingWeight(\WalmartDSV\SupplierOfferType\ShippingWeightAType $shippingWeight)
    {
        $this->shippingWeight = $shippingWeight;
        return $this;
    }

    /**
     * Gets as shipsInOriginalPackaging
     *
     * Can this product be shipped in the original packaging without being put in an outer box? Notes: 1) This is to indicate whether it's *possible* for the item to be shipped in original box, not whether it is required. 2) This was previously known as "Ships As-Is."
     *
     * @return string
     */
    public function getShipsInOriginalPackaging()
    {
        return $this->shipsInOriginalPackaging;
    }

    /**
     * Sets a new shipsInOriginalPackaging
     *
     * Can this product be shipped in the original packaging without being put in an outer box? Notes: 1) This is to indicate whether it's *possible* for the item to be shipped in original box, not whether it is required. 2) This was previously known as "Ships As-Is."
     *
     * @param string $shipsInOriginalPackaging
     * @return self
     */
    public function setShipsInOriginalPackaging($shipsInOriginalPackaging)
    {
        $this->shipsInOriginalPackaging = $shipsInOriginalPackaging;
        return $this;
    }

    /**
     * Adds as additionalOfferAttribute
     *
     * If there is other information that is not in the Spec, you can create one using a name-value pair.
     *
     * @param \WalmartDSV\AdditionalOfferAttributeType $additionalOfferAttribute
     *@return self
     */
    public function addToAdditionalOfferAttributes(\WalmartDSV\AdditionalOfferAttributeType $additionalOfferAttribute)
    {
        $this->additionalOfferAttributes[] = $additionalOfferAttribute;
        return $this;
    }

    /**
     * isset additionalOfferAttributes
     *
     * If there is other information that is not in the Spec, you can create one using a name-value pair.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAdditionalOfferAttributes($index)
    {
        return isset($this->additionalOfferAttributes[$index]);
    }

    /**
     * unset additionalOfferAttributes
     *
     * If there is other information that is not in the Spec, you can create one using a name-value pair.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAdditionalOfferAttributes($index)
    {
        unset($this->additionalOfferAttributes[$index]);
    }

    /**
     * Gets as additionalOfferAttributes
     *
     * If there is other information that is not in the Spec, you can create one using a name-value pair.
     *
     * @return \WalmartDSV\AdditionalOfferAttributeType[]
     */
    public function getAdditionalOfferAttributes()
    {
        return $this->additionalOfferAttributes;
    }

    /**
     * Sets a new additionalOfferAttributes
     *
     * If there is other information that is not in the Spec, you can create one using a name-value pair.
     *
     * @param \WalmartDSV\AdditionalOfferAttributeType[] $additionalOfferAttributes
     * @return self
     */
    public function setAdditionalOfferAttributes(array $additionalOfferAttributes)
    {
        $this->additionalOfferAttributes = $additionalOfferAttributes;
        return $this;
    }

    /**
     * Gets as isPreorder
     *
     * Is this item available for pre-order? If selected, additional information will need to be provided to properly set up the pre-order.
     *
     * @return string
     */
    public function getIsPreorder()
    {
        return $this->isPreorder;
    }

    /**
     * Sets a new isPreorder
     *
     * Is this item available for pre-order? If selected, additional information will need to be provided to properly set up the pre-order.
     *
     * @param string $isPreorder
     * @return self
     */
    public function setIsPreorder($isPreorder)
    {
        $this->isPreorder = $isPreorder;
        return $this;
    }

    /**
     * Gets as streetDate
     *
     * Provide the date that the item will be available.
     *
     * @return \DateTime
     */
    public function getStreetDate()
    {
        return $this->streetDate;
    }

    /**
     * Sets a new streetDate
     *
     * Provide the date that the item will be available.
     *
     * @param \DateTime $streetDate
     * @return self
     */
    public function setStreetDate(\DateTime $streetDate)
    {
        $this->streetDate = $streetDate;
        return $this;
    }

    /**
     * Gets as streetDateType
     *
     * Select the type of street date provided. DELIVER_BY indicates that the the item will be delivered to the customer by the provided street date. SHIP_BY indicates that the item will be shipped by the provided street date.
     *
     * @return string
     */
    public function getStreetDateType()
    {
        return $this->streetDateType;
    }

    /**
     * Sets a new streetDateType
     *
     * Select the type of street date provided. DELIVER_BY indicates that the the item will be delivered to the customer by the provided street date. SHIP_BY indicates that the item will be shipped by the provided street date.
     *
     * @param string $streetDateType
     * @return self
     */
    public function setStreetDateType($streetDateType)
    {
        $this->streetDateType = $streetDateType;
        return $this;
    }


}

