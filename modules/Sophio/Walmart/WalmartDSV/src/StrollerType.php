<?php

namespace WalmartDSV;

/**
 * Class representing StrollerType
 *
 * Prominent stroller styles.
 * XSD Type: StrollerType
 */
class StrollerType
{

    /**
     * @var string[] $strollerTypeValue
     */
    private $strollerTypeValue = [
        
    ];

    /**
     * Adds as strollerTypeValue
     *
     * @return self
     * @param string $strollerTypeValue
     */
    public function addToStrollerTypeValue($strollerTypeValue)
    {
        $this->strollerTypeValue[] = $strollerTypeValue;
        return $this;
    }

    /**
     * isset strollerTypeValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetStrollerTypeValue($index)
    {
        return isset($this->strollerTypeValue[$index]);
    }

    /**
     * unset strollerTypeValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetStrollerTypeValue($index)
    {
        unset($this->strollerTypeValue[$index]);
    }

    /**
     * Gets as strollerTypeValue
     *
     * @return string[]
     */
    public function getStrollerTypeValue()
    {
        return $this->strollerTypeValue;
    }

    /**
     * Sets a new strollerTypeValue
     *
     * @param string $strollerTypeValue
     * @return self
     */
    public function setStrollerTypeValue(array $strollerTypeValue)
    {
        $this->strollerTypeValue = $strollerTypeValue;
        return $this;
    }


}

