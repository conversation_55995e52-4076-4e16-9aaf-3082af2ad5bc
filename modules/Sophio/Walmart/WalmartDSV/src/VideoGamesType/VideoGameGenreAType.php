<?php

namespace WalmartDSV\VideoGamesType;

/**
 * Class representing VideoGameGenreAType
 */
class VideoGameGenreAType
{

    /**
     * @var string[] $videoGameGenreValue
     */
    private $videoGameGenreValue = [
        
    ];

    /**
     * Adds as videoGameGenreValue
     *
     * @return self
     * @param string $videoGameGenreValue
     */
    public function addToVideoGameGenreValue($videoGameGenreValue)
    {
        $this->videoGameGenreValue[] = $videoGameGenreValue;
        return $this;
    }

    /**
     * isset videoGameGenreValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetVideoGameGenreValue($index)
    {
        return isset($this->videoGameGenreValue[$index]);
    }

    /**
     * unset videoGameGenreValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetVideoGameGenreValue($index)
    {
        unset($this->videoGameGenreValue[$index]);
    }

    /**
     * Gets as videoGameGenreValue
     *
     * @return string[]
     */
    public function getVideoGameGenreValue()
    {
        return $this->videoGameGenreValue;
    }

    /**
     * Sets a new videoGameGenreValue
     *
     * @param string $videoGameGenreValue
     * @return self
     */
    public function setVideoGameGenreValue(array $videoGameGenreValue)
    {
        $this->videoGameGenreValue = $videoGameGenreValue;
        return $this;
    }


}

