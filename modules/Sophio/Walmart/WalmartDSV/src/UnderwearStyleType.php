<?php

namespace WalmartDSV;

/**
 * Class representing UnderwearStyleType
 *
 * Descriptive styles specific to underwear.
 * XSD Type: UnderwearStyle
 */
class UnderwearStyleType
{

    /**
     * @var string $underwearStyleValue
     */
    private $underwearStyleValue = null;

    /**
     * Gets as underwearStyleValue
     *
     * @return string
     */
    public function getUnderwearStyleValue()
    {
        return $this->underwearStyleValue;
    }

    /**
     * Sets a new underwearStyleValue
     *
     * @param string $underwearStyleValue
     * @return self
     */
    public function setUnderwearStyleValue($underwearStyleValue)
    {
        $this->underwearStyleValue = $underwearStyleValue;
        return $this;
    }


}

