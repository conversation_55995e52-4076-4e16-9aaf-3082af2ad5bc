<?php

namespace WalmartDSV;

/**
 * Class representing ElectronicsType
 *
 *
 * XSD Type: Electronics
 */
class ElectronicsType
{

    /**
     * @var \WalmartDSV\VideoGamesType $videoGames
     */
    private $videoGames = null;

    /**
     * @var \WalmartDSV\VideoProjectorsType $videoProjectors
     */
    private $videoProjectors = null;

    /**
     * @var \WalmartDSV\ElectronicsAccessoriesType $electronicsAccessories
     */
    private $electronicsAccessories = null;

    /**
     * @var \WalmartDSV\ComputerComponentsType $computerComponents
     */
    private $computerComponents = null;

    /**
     * @var \WalmartDSV\ElectronicsCablesType $electronicsCables
     */
    private $electronicsCables = null;

    /**
     * @var \WalmartDSV\SoftwareType $software
     */
    private $software = null;

    /**
     * @var \WalmartDSV\ComputersType $computers
     */
    private $computers = null;

    /**
     * @var \WalmartDSV\TVsAndVideoDisplaysType $tVsAndVideoDisplays
     */
    private $tVsAndVideoDisplays = null;

    /**
     * @var \WalmartDSV\CellPhonesType $cellPhones
     */
    private $cellPhones = null;

    /**
     * @var \WalmartDSV\PrintersScannersAndImagingType $printersScannersAndImaging
     */
    private $printersScannersAndImaging = null;

    /**
     * @var \WalmartDSV\ElectronicsOtherType $electronicsOther
     */
    private $electronicsOther = null;

    /**
     * Gets as videoGames
     *
     * @return \WalmartDSV\VideoGamesType
     */
    public function getVideoGames()
    {
        return $this->videoGames;
    }

    /**
     * Sets a new videoGames
     *
     * @param \WalmartDSV\VideoGamesType $videoGames
     * @return self
     */
    public function setVideoGames(\WalmartDSV\VideoGamesType $videoGames)
    {
        $this->videoGames = $videoGames;
        return $this;
    }

    /**
     * Gets as videoProjectors
     *
     * @return \WalmartDSV\VideoProjectorsType
     */
    public function getVideoProjectors()
    {
        return $this->videoProjectors;
    }

    /**
     * Sets a new videoProjectors
     *
     * @param \WalmartDSV\VideoProjectorsType $videoProjectors
     * @return self
     */
    public function setVideoProjectors(\WalmartDSV\VideoProjectorsType $videoProjectors)
    {
        $this->videoProjectors = $videoProjectors;
        return $this;
    }

    /**
     * Gets as electronicsAccessories
     *
     * @return \WalmartDSV\ElectronicsAccessoriesType
     */
    public function getElectronicsAccessories()
    {
        return $this->electronicsAccessories;
    }

    /**
     * Sets a new electronicsAccessories
     *
     * @param \WalmartDSV\ElectronicsAccessoriesType $electronicsAccessories
     * @return self
     */
    public function setElectronicsAccessories(\WalmartDSV\ElectronicsAccessoriesType $electronicsAccessories)
    {
        $this->electronicsAccessories = $electronicsAccessories;
        return $this;
    }

    /**
     * Gets as computerComponents
     *
     * @return \WalmartDSV\ComputerComponentsType
     */
    public function getComputerComponents()
    {
        return $this->computerComponents;
    }

    /**
     * Sets a new computerComponents
     *
     * @param \WalmartDSV\ComputerComponentsType $computerComponents
     * @return self
     */
    public function setComputerComponents(\WalmartDSV\ComputerComponentsType $computerComponents)
    {
        $this->computerComponents = $computerComponents;
        return $this;
    }

    /**
     * Gets as electronicsCables
     *
     * @return \WalmartDSV\ElectronicsCablesType
     */
    public function getElectronicsCables()
    {
        return $this->electronicsCables;
    }

    /**
     * Sets a new electronicsCables
     *
     * @param \WalmartDSV\ElectronicsCablesType $electronicsCables
     * @return self
     */
    public function setElectronicsCables(\WalmartDSV\ElectronicsCablesType $electronicsCables)
    {
        $this->electronicsCables = $electronicsCables;
        return $this;
    }

    /**
     * Gets as software
     *
     * @return \WalmartDSV\SoftwareType
     */
    public function getSoftware()
    {
        return $this->software;
    }

    /**
     * Sets a new software
     *
     * @param \WalmartDSV\SoftwareType $software
     * @return self
     */
    public function setSoftware(\WalmartDSV\SoftwareType $software)
    {
        $this->software = $software;
        return $this;
    }

    /**
     * Gets as computers
     *
     * @return \WalmartDSV\ComputersType
     */
    public function getComputers()
    {
        return $this->computers;
    }

    /**
     * Sets a new computers
     *
     * @param \WalmartDSV\ComputersType $computers
     * @return self
     */
    public function setComputers(\WalmartDSV\ComputersType $computers)
    {
        $this->computers = $computers;
        return $this;
    }

    /**
     * Gets as tVsAndVideoDisplays
     *
     * @return \WalmartDSV\TVsAndVideoDisplaysType
     */
    public function getTVsAndVideoDisplays()
    {
        return $this->tVsAndVideoDisplays;
    }

    /**
     * Sets a new tVsAndVideoDisplays
     *
     * @param \WalmartDSV\TVsAndVideoDisplaysType $tVsAndVideoDisplays
     * @return self
     */
    public function setTVsAndVideoDisplays(\WalmartDSV\TVsAndVideoDisplaysType $tVsAndVideoDisplays)
    {
        $this->tVsAndVideoDisplays = $tVsAndVideoDisplays;
        return $this;
    }

    /**
     * Gets as cellPhones
     *
     * @return \WalmartDSV\CellPhonesType
     */
    public function getCellPhones()
    {
        return $this->cellPhones;
    }

    /**
     * Sets a new cellPhones
     *
     * @param \WalmartDSV\CellPhonesType $cellPhones
     * @return self
     */
    public function setCellPhones(\WalmartDSV\CellPhonesType $cellPhones)
    {
        $this->cellPhones = $cellPhones;
        return $this;
    }

    /**
     * Gets as printersScannersAndImaging
     *
     * @return \WalmartDSV\PrintersScannersAndImagingType
     */
    public function getPrintersScannersAndImaging()
    {
        return $this->printersScannersAndImaging;
    }

    /**
     * Sets a new printersScannersAndImaging
     *
     * @param \WalmartDSV\PrintersScannersAndImagingType $printersScannersAndImaging
     * @return self
     */
    public function setPrintersScannersAndImaging(\WalmartDSV\PrintersScannersAndImagingType $printersScannersAndImaging)
    {
        $this->printersScannersAndImaging = $printersScannersAndImaging;
        return $this;
    }

    /**
     * Gets as electronicsOther
     *
     * @return \WalmartDSV\ElectronicsOtherType
     */
    public function getElectronicsOther()
    {
        return $this->electronicsOther;
    }

    /**
     * Sets a new electronicsOther
     *
     * @param \WalmartDSV\ElectronicsOtherType $electronicsOther
     * @return self
     */
    public function setElectronicsOther(\WalmartDSV\ElectronicsOtherType $electronicsOther)
    {
        $this->electronicsOther = $electronicsOther;
        return $this;
    }


}

