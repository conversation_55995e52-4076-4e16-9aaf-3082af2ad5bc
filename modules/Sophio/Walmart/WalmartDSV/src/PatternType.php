<?php

namespace WalmartDSV;

/**
 * Class representing PatternType
 *
 * Decorative design or visual ornamentation, often with a thematic, recurring motif.
 * XSD Type: Pattern
 */
class PatternType
{

    /**
     * @var string $patternValue
     */
    private $patternValue = null;

    /**
     * Gets as patternValue
     *
     * @return string
     */
    public function getPatternValue()
    {
        return $this->patternValue;
    }

    /**
     * Sets a new patternValue
     *
     * @param string $patternValue
     * @return self
     */
    public function setPatternValue($patternValue)
    {
        $this->patternValue = $patternValue;
        return $this;
    }


}

