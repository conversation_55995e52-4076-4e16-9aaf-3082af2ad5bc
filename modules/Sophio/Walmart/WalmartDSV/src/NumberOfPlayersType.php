<?php

namespace WalmartDSV;

/**
 * Class representing NumberOfPlayersType
 *
 * Number of players that can participate in the game.
 * XSD Type: NumberOfPlayers
 */
class NumberOfPlayersType
{

    /**
     * The minimum number of people required to play the game.
     *
     * @var int $minimumNumberOfPlayers
     */
    private $minimumNumberOfPlayers = null;

    /**
     * The maximum number of people for which the game is intended.
     *
     * @var int $maximumNumberOfPlayers
     */
    private $maximumNumberOfPlayers = null;

    /**
     * Gets as minimumNumberOfPlayers
     *
     * The minimum number of people required to play the game.
     *
     * @return int
     */
    public function getMinimumNumberOfPlayers()
    {
        return $this->minimumNumberOfPlayers;
    }

    /**
     * Sets a new minimumNumberOfPlayers
     *
     * The minimum number of people required to play the game.
     *
     * @param int $minimumNumberOfPlayers
     * @return self
     */
    public function setMinimumNumberOfPlayers($minimumNumberOfPlayers)
    {
        $this->minimumNumberOfPlayers = $minimumNumberOfPlayers;
        return $this;
    }

    /**
     * Gets as maximumNumberOfPlayers
     *
     * The maximum number of people for which the game is intended.
     *
     * @return int
     */
    public function getMaximumNumberOfPlayers()
    {
        return $this->maximumNumberOfPlayers;
    }

    /**
     * Sets a new maximumNumberOfPlayers
     *
     * The maximum number of people for which the game is intended.
     *
     * @param int $maximumNumberOfPlayers
     * @return self
     */
    public function setMaximumNumberOfPlayers($maximumNumberOfPlayers)
    {
        $this->maximumNumberOfPlayers = $maximumNumberOfPlayers;
        return $this;
    }


}

