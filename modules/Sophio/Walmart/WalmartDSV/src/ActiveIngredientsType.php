<?php

namespace WalmartDSV;

/**
 * Class representing ActiveIngredientsType
 *
 * The list of active ingredients in order of potency, as shown on the item label.
 * XSD Type: ActiveIngredients
 */
class ActiveIngredientsType
{

    /**
     * @var \WalmartDSV\ActiveIngredientType[] $activeIngredient
     */
    private $activeIngredient = [
        
    ];

    /**
     * Adds as activeIngredient
     *
     * @param \WalmartDSV\ActiveIngredientType $activeIngredient
     *@return self
     */
    public function addToActiveIngredient(\WalmartDSV\ActiveIngredientType $activeIngredient)
    {
        $this->activeIngredient[] = $activeIngredient;
        return $this;
    }

    /**
     * isset activeIngredient
     *
     * @param int|string $index
     * @return bool
     */
    public function issetActiveIngredient($index)
    {
        return isset($this->activeIngredient[$index]);
    }

    /**
     * unset activeIngredient
     *
     * @param int|string $index
     * @return void
     */
    public function unsetActiveIngredient($index)
    {
        unset($this->activeIngredient[$index]);
    }

    /**
     * Gets as activeIngredient
     *
     * @return \WalmartDSV\ActiveIngredientType[]
     */
    public function getActiveIngredient()
    {
        return $this->activeIngredient;
    }

    /**
     * Sets a new activeIngredient
     *
     * @param \WalmartDSV\ActiveIngredientType[] $activeIngredient
     * @return self
     */
    public function setActiveIngredient(array $activeIngredient)
    {
        $this->activeIngredient = $activeIngredient;
        return $this;
    }


}

