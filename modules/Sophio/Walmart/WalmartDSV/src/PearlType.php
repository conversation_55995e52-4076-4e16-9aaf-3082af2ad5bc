<?php

namespace WalmartDSV;

/**
 * Class representing PearlType
 *
 * Describes the method for growing pearls and information about the origin of the pearl.
 * XSD Type: PearlType
 */
class PearlType
{

    /**
     * @var string[] $pearlTypeValue
     */
    private $pearlTypeValue = [
        
    ];

    /**
     * Adds as pearlTypeValue
     *
     * @return self
     * @param string $pearlTypeValue
     */
    public function addToPearlTypeValue($pearlTypeValue)
    {
        $this->pearlTypeValue[] = $pearlTypeValue;
        return $this;
    }

    /**
     * isset pearlTypeValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetPearlTypeValue($index)
    {
        return isset($this->pearlTypeValue[$index]);
    }

    /**
     * unset pearlTypeValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetPearlTypeValue($index)
    {
        unset($this->pearlTypeValue[$index]);
    }

    /**
     * Gets as pearlTypeValue
     *
     * @return string[]
     */
    public function getPearlTypeValue()
    {
        return $this->pearlTypeValue;
    }

    /**
     * Sets a new pearlTypeValue
     *
     * @param string $pearlTypeValue
     * @return self
     */
    public function setPearlTypeValue(array $pearlTypeValue)
    {
        $this->pearlTypeValue = $pearlTypeValue;
        return $this;
    }


}

