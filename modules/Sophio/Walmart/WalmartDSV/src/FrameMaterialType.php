<?php

namespace WalmartDSV;

/**
 * Class representing FrameMaterialType
 *
 * The material used in the item's frame if different than its main material makeup, which is described using the "Material" attribute.
 * XSD Type: FrameMaterial
 */
class FrameMaterialType
{

    /**
     * @var string[] $frameMaterialValue
     */
    private $frameMaterialValue = [
        
    ];

    /**
     * Adds as frameMaterialValue
     *
     * @return self
     * @param string $frameMaterialValue
     */
    public function addToFrameMaterialValue($frameMaterialValue)
    {
        $this->frameMaterialValue[] = $frameMaterialValue;
        return $this;
    }

    /**
     * isset frameMaterialValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFrameMaterialValue($index)
    {
        return isset($this->frameMaterialValue[$index]);
    }

    /**
     * unset frameMaterialValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFrameMaterialValue($index)
    {
        unset($this->frameMaterialValue[$index]);
    }

    /**
     * Gets as frameMaterialValue
     *
     * @return string[]
     */
    public function getFrameMaterialValue()
    {
        return $this->frameMaterialValue;
    }

    /**
     * Sets a new frameMaterialValue
     *
     * @param string $frameMaterialValue
     * @return self
     */
    public function setFrameMaterialValue(array $frameMaterialValue)
    {
        $this->frameMaterialValue = $frameMaterialValue;
        return $this;
    }


}

