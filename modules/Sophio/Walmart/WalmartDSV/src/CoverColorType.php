<?php

namespace WalmartDSV;

/**
 * Class representing CoverColorType
 *
 * Book color cover as described by manufacturer.
 * XSD Type: CoverColor
 */
class CoverColorType
{

    /**
     * @var string[] $coverColorValue
     */
    private $coverColorValue = [
        
    ];

    /**
     * Adds as coverColorValue
     *
     * @return self
     * @param string $coverColorValue
     */
    public function addToCoverColorValue($coverColorValue)
    {
        $this->coverColorValue[] = $coverColorValue;
        return $this;
    }

    /**
     * isset coverColorValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetCoverColorValue($index)
    {
        return isset($this->coverColorValue[$index]);
    }

    /**
     * unset coverColorValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetCoverColorValue($index)
    {
        unset($this->coverColorValue[$index]);
    }

    /**
     * Gets as coverColorValue
     *
     * @return string[]
     */
    public function getCoverColorValue()
    {
        return $this->coverColorValue;
    }

    /**
     * Sets a new coverColorValue
     *
     * @param string $coverColorValue
     * @return self
     */
    public function setCoverColorValue(array $coverColorValue)
    {
        $this->coverColorValue = $coverColorValue;
        return $this;
    }


}

