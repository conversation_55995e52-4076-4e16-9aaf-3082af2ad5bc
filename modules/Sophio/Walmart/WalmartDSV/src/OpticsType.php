<?php

namespace WalmartDSV;

/**
 * Class representing OpticsType
 *
 *
 * XSD Type: Optics
 */
class OpticsType
{

    /**
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @var string[] $additionalVariantAttributeNames
     */
    private $additionalVariantAttributeNames = null;

    /**
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @var string $shortDescription
     */
    private $shortDescription = null;

    /**
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @var string[] $keyFeatures
     */
    private $keyFeatures = null;

    /**
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @var string $brand
     */
    private $brand = null;

    /**
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @var string $manufacturer
     */
    private $manufacturer = null;

    /**
     * MPN uniquely identifies the product to its manufacturer. For many products this will be identical to the model number. Some manufacturers distinguish part number from model number. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @var string $manufacturerPartNumber
     */
    private $manufacturerPartNumber = null;

    /**
     * Model numbers allow manufacturers to keep track of each hardware device and identify or replace the proper part when needed. Model numbers are often found on the bottom, back, or side of a product. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @var string $modelNumber
     */
    private $modelNumber = null;

    /**
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @var int $multipackQuantity
     */
    private $multipackQuantity = null;

    /**
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @var int $countPerPack
     */
    private $countPerPack = null;

    /**
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @var string $count
     */
    private $count = null;

    /**
     * The number of small pieces, slices, or different items within the product. Piece Count applies to things such as puzzles, building block sets, and products that contain multiple different items (such as tool sets, dinnerware sets, gift baskets, art sets, makeup kits, or shaving kits). EXAMPLE: (1) A 500-piece puzzle has a "Piece Count" of 500. (2) A 105-Piece Socket Wrench set has a piece count of "105." (3) A gift basket of 5 different items has a "Piece Count" of 5.
     *
     * @var int $pieceCount
     */
    private $pieceCount = null;

    /**
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @var string $mainImageUrl
     */
    private $mainImageUrl = null;

    /**
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @var string[] $productSecondaryImageURL
     */
    private $productSecondaryImageURL = null;

    /**
     * Indicate whether this item is meant for a particular gender or meant to be gender-agnostic (unisex).
     *
     * @var string $gender
     */
    private $gender = null;

    /**
     * Overall dimensions of an item. Used only for products that do not already have a more specific 'x size' attribute, such as ring size or clothing size.
     *
     * @var string $size
     */
    private $size = null;

    /**
     * General grouping of ages into commonly used demographic labels.
     *
     * @var string[] $ageGroup
     */
    private $ageGroup = null;

    /**
     * Minimum and Maximum Ages for a product. Note: Both Min. and Max. attributes will be the same Unit of Measure: Months, or Years.
     *
     * @var \WalmartDSV\AgeRangeType $ageRange
     */
    private $ageRange = null;

    /**
     * Measure of the magnification power provided by a feature that electronically enlarges the image area at the center of the frame, trims away the outside edges of the picture, and interpolates the result to the pixel dimensions of the original. Important to consumers because digital zoom reduces the image resolution and the image quality vs. optical zoom, which does not affect the quality of the zoomed image.
     *
     * @var string $digitalZoom
     */
    private $digitalZoom = null;

    /**
     * Measure of the magnification power of a physical optical zoom lens. For example, a camera with an optical zoom of 4x allows the users to magnify image up to 4x larger. Important to consumers because optical zoom results in better image quality than zoom that is digitally generated.
     *
     * @var string $opticalZoom
     */
    private $opticalZoom = null;

    /**
     * Measurement of the diameter of the front portion of the lens, measured in mm. For cameras, important factor to fit accessories such as filters.
     *
     * @var \WalmartDSV\OpticsType\LensDiameterAType $lensDiameter
     */
    private $lensDiameter = null;

    /**
     * Type of thin layer of material applied to the surface of lenses or other optical elements that provide specific effects. Usually applied to components such as camera lenses to improve resistance to scratches, or provide a mirror effect for sunglasses.
     *
     * @var string $lensCoating
     */
    private $lensCoating = null;

    /**
     * One specification describing the smallest detectable incremental change of input parameter that can be detected in the output signal. Expressed either as a proportion of the full-scale reading, or as an absolute. Used for a variety of sensor types and importance varies with product. For example for digital cameras, image sensor resolution is an important factor for image quality.
     *
     * @var \WalmartDSV\ResolutionUnitType $sensorResolution
     */
    private $sensorResolution = null;

    /**
     * Number expressing a ratio of how much a device’s optical system can increase (or decrease) an image as compared to the true size. Typically applied to products such as magnifying lenses and microscopes and expressed as a number followed by an x.
     *
     * @var string $magnification
     */
    private $magnification = null;

    /**
     * Terms that describe modes, mechanisms, or control arrangements that adjust optical focus on the device. For example, if item is a pair of binoculars, focus type would describe weather the lenses on binoculars can be adjusted independently of one another.
     *
     * @var string[] $focusType
     */
    private $focusType = null;

    /**
     * Measure of the area that can be seen through a lens of an item, as specified by the manufacturer. Attribute applied to such products as microscopes, telescopes and rifle scopes. Can be expressed as the angular field of view (in degrees) or the true field of view (in feet).
     *
     * @var string $fieldOfView
     */
    private $fieldOfView = null;

    /**
     * Y indicates the item has a lens that stays in focus when magnification/focal length is changed. For example, a microscope that stays in focus when the microscope objective is rotated.
     *
     * @var string $isParfocal
     */
    private $isParfocal = null;

    /**
     * Ratio of the lens's focal length, to the diameter of the entrance pupil (optical image of the physical aperture stop, as 'seen' through the front of the lens system). Also known as the f-number or f-stop, this number indicates lens speed, an important selection criteria based on intended photographic use.
     *
     * @var string $focalRatio
     */
    private $focalRatio = null;

    /**
     * The primary technology used for the item's display.
     *
     * @var string $displayTechnology
     */
    private $displayTechnology = null;

    /**
     * If the item has a screen display, the resolution value of the screen component of the product. Typically measured as the number of pixels creating the display expressed as number of columns x number of rows. For example, a digital camera's screen resolution (vs. the image quality the camera can produce) of 640x480.
     *
     * @var \WalmartDSV\DisplayResolutionType $displayResolution
     */
    private $displayResolution = null;

    /**
     * Does this device have features that give users the ability to see in low light conditions?
     *
     * @var string $hasNightVision
     */
    private $hasNightVision = null;

    /**
     * The length of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @var \WalmartDSV\OpticsType\AssembledProductLengthAType $assembledProductLength
     */
    private $assembledProductLength = null;

    /**
     * The width of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @var \WalmartDSV\OpticsType\AssembledProductWidthAType $assembledProductWidth
     */
    private $assembledProductWidth = null;

    /**
     * The height of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @var \WalmartDSV\OpticsType\AssembledProductHeightAType $assembledProductHeight
     */
    private $assembledProductHeight = null;

    /**
     * The weight of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @var \WalmartDSV\OpticsType\AssembledProductWeightAType $assembledProductWeight
     */
    private $assembledProductWeight = null;

    /**
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @var string $variantGroupId
     */
    private $variantGroupId = null;

    /**
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @var string[] $variantAttributeNames
     */
    private $variantAttributeNames = null;

    /**
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @var string $isPrimaryVariant
     */
    private $isPrimaryVariant = null;

    /**
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @var string $isPrivateLabelOrUnbranded
     */
    private $isPrivateLabelOrUnbranded = null;

    /**
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @var string $isProp65WarningRequired
     */
    private $isProp65WarningRequired = null;

    /**
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @var string $prop65WarningText
     */
    private $prop65WarningText = null;

    /**
     * "Battery or battery containing product" is defined by Company to include any item of Merchandise that is a battery or any component of Merchandise, including reusable packaging intended to stay in use with the item, containing a battery of any chemistry/ type. Mark Y if this definition applies to your product.
     *
     * @var string $hasBatteries
     */
    private $hasBatteries = null;

    /**
     * Please select the Battery Technology Type from the list provided. NOTE: If battery type is lead acid, lead acid (nonspillable), lithium ion, or lithium metal, please ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @var string $batteryTechnologyType
     */
    private $batteryTechnologyType = null;

    /**
     * ‘Chemical’ is defined by Walmart to include any item of Merchandise that contains a powder, gel, paste, or liquid that is not intended for human consumption. ‘Chemical’ also includes the following types items that ARE intended for human consumption, inhalation, or absorption, or labeled with drug facts: All over-the-counter medications, including: Lozenges, pills or capsules (e.g. pain relievers; allergy medications; as well as vitamins and supplements that contain metals); Medicated swabs and wipes, acne medication, and sunscreen; Medicated patches (such as nicotine patches); Liquids (e.g. cough medicine, medicated drops, nasal spray and inhalers); Medicated shampoos, gums, ointments and creams; Medicated lip balm, lip creams and petroleum jelly; Contraceptive foam, films, and spermicides; and Product/Equipment sold with chemicals (e.g. vaporizer sold with medication) and electronic cigarettes. If your product meets this definition, Mark Y and ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @var string $isChemical
     */
    private $isChemical = null;

    /**
     * Y indicates the item comes with a warranty. If an item has a warranty, then enter EITHER the warranty URL or the warranty text in the appropriate field.
     *
     * @var string $hasWarranty
     */
    private $hasWarranty = null;

    /**
     * If you indicated that your item has a warranty, provide either the Warranty URL or Warranty Text. The Warranty URL is the web location of the image, PDF, or link to the manufacturer's warranty page, showing the warranty and its terms, including the duration of the warranty. URLs must begin with http:// or https:// NOTE: Please remember to update the link and/or text of the warranty as the warranty changes. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @var string $warrantyURL
     */
    private $warrantyURL = null;

    /**
     * If you marked Y for "Has Warranty" provide the Warranty URL or Warranty Text (the full text of the warranty terms, including what is covered by the warranty and the duration of the warranty). NOTE: please remember to update the text of your warranty as your warranty changes.
     *
     * @var string $warrantyText
     */
    private $warrantyText = null;

    /**
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @var string $hasStateRestrictions
     */
    private $hasStateRestrictions = null;

    /**
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @var \WalmartDSV\StateRestrictionType[] $stateRestrictions
     */
    private $stateRestrictions = null;

    /**
     * Is product unassembled and must be put together before use?
     *
     * @var string $isAssemblyRequired
     */
    private $isAssemblyRequired = null;

    /**
     * Provide a URL to an image or PDF asset showing assembly instructions for items requiring assembly. URLs must be static and have no query parameters. URLs must begin with http:// or https:// and should end in in the file name.
     *
     * @var string $assemblyInstructions
     */
    private $assemblyInstructions = null;

    /**
     * The main material(s) that a product is made of. This does not need to be an exhaustive list, but should contain the predominant or functionally important material/materials. Fabric material specifics should be entered using the "Fabric Content" attribute.
     *
     * @var \WalmartDSV\MaterialType $material
     */
    private $material = null;

    /**
     * Indicates whether the item is designed to be used by the right or left hand.
     *
     * @var string $dexterity
     */
    private $dexterity = null;

    /**
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @var string[] $globalBrandLicense
     */
    private $globalBrandLicense = null;

    /**
     * Typically measured on the diagonal in inches.
     *
     * @var \WalmartDSV\OpticsType\ScreenSizeAType $screenSize
     */
    private $screenSize = null;

    /**
     * Y indicates the item has a liquid-crystal display.
     *
     * @var string $hasLcdScreen
     */
    private $hasLcdScreen = null;

    /**
     * Provides information on the exact type of power used by the item.
     *
     * @var string $powerType
     */
    private $powerType = null;

    /**
     * Y indicates the item has a series of layers of coatings; For example, eye glasses that have layers of anti-reflective coatings.
     *
     * @var string $isMulticoated
     */
    private $isMulticoated = null;

    /**
     * Y indicates the item has the capability to be secured by a lock or a locking mechanism. Differentiates lockability as a feature of items such as a tool boxes, microscopes, or saddlebags.
     *
     * @var string $isLockable
     */
    private $isLockable = null;

    /**
     * The type of lock included with the device. Typically based on access mechanism. Example: electronic lock, padlock
     *
     * @var string $lockType
     */
    private $lockType = null;

    /**
     * Y indicates the item has a place to insert electronic memory data storage device used to record digital information.
     *
     * @var string $hasMemoryCardSlot
     */
    private $hasMemoryCardSlot = null;

    /**
     * Has this product been treated to prevent the condensation of water on its surface?
     *
     * @var string $isFogResistant
     */
    private $isFogResistant = null;

    /**
     * Temperature at which an electrical or mechanical device operates.
     *
     * @var \WalmartDSV\OpticsType\OperatingTemperatureAType $operatingTemperature
     */
    private $operatingTemperature = null;

    /**
     * Does this have a dovetail bar used to attach a telescope optical tube to a mount? Typical styles include Losmandy D and Vixen V.
     *
     * @var string $hasDovetailBarSystem
     */
    private $hasDovetailBarSystem = null;

    /**
     * Description of how the product is able to attach to other surfaces or items. Also used for product fit. For example, bayonet is an attachment style describing how a camera lens attaches to the camera body
     *
     * @var string $attachmentStyle
     */
    private $attachmentStyle = null;

    /**
     * List notable features of the item.
     *
     * @var string[] $features
     */
    private $features = null;

    /**
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @var string $keywords
     */
    private $keywords = null;

    /**
     * Color as described by the manufacturer.
     *
     * @var \WalmartDSV\ColorType $color
     */
    private $color = null;

    /**
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @var string[] $colorCategory
     */
    private $colorCategory = null;

    /**
     * If the product is sports-related, the name of the specific sport depicted on the product, or the target sport for the product use
     *
     * @var \WalmartDSV\SportType $sport
     */
    private $sport = null;

    /**
     * Decorative design or visual ornamentation, often with a thematic, recurring motif.
     *
     * @var \WalmartDSV\PatternType $pattern
     */
    private $pattern = null;

    /**
     * Any wireless communications standard used within or by the item.
     *
     * @var string[] $wirelessTechnologies
     */
    private $wirelessTechnologies = null;

    /**
     * Is the item designed to be easily moved?
     *
     * @var string $isPortable
     */
    private $isPortable = null;

    /**
     * Indicates that an item can be folded.
     *
     * @var string $isFoldable
     */
    private $isFoldable = null;

    /**
     * Y indicates that the item has been made or is marketed as being resistant to elements of weather, such as rain, wind or cold.
     *
     * @var string $isWeatherResistant
     */
    private $isWeatherResistant = null;

    /**
     * Y indicates that the item has been especially made to be resistant to water, to some degree.
     *
     * @var string $isWaterproof
     */
    private $isWaterproof = null;

    /**
     * Y indicates that an item uses electricity, requiring a power cord or batteries to operate. Useful for items that have non-powered equivalents (e.g. toothbrushes).
     *
     * @var string $isPowered
     */
    private $isPowered = null;

    /**
     * Description of how the item should be cleaned and maintained.
     *
     * @var string $cleaningCareAndMaintenance
     */
    private $cleaningCareAndMaintenance = null;

    /**
     * Further clarification of what the item may be used for. This improves searchability when customers search for general terms like "birthday party" that do not include the names of specific items.
     *
     * @var string[] $recommendedUses
     */
    private $recommendedUses = null;

    /**
     * The primary location recommended for the item's use.
     *
     * @var string[] $recommendedLocations
     */
    private $recommendedLocations = null;

    /**
     * @var \WalmartDSV\OpticsType\SwatchImagesAType\SwatchImageAType[] $swatchImages
     */
    private $swatchImages = null;

    /**
     * Adds as additionalVariantAttributeName
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @return self
     * @param string $additionalVariantAttributeName
     */
    public function addToAdditionalVariantAttributeNames($additionalVariantAttributeName)
    {
        $this->additionalVariantAttributeNames[] = $additionalVariantAttributeName;
        return $this;
    }

    /**
     * isset additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAdditionalVariantAttributeNames($index)
    {
        return isset($this->additionalVariantAttributeNames[$index]);
    }

    /**
     * unset additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAdditionalVariantAttributeNames($index)
    {
        unset($this->additionalVariantAttributeNames[$index]);
    }

    /**
     * Gets as additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @return string[]
     */
    public function getAdditionalVariantAttributeNames()
    {
        return $this->additionalVariantAttributeNames;
    }

    /**
     * Sets a new additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param string $additionalVariantAttributeNames
     * @return self
     */
    public function setAdditionalVariantAttributeNames(array $additionalVariantAttributeNames)
    {
        $this->additionalVariantAttributeNames = $additionalVariantAttributeNames;
        return $this;
    }

    /**
     * Gets as shortDescription
     *
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @return string
     */
    public function getShortDescription()
    {
        return $this->shortDescription;
    }

    /**
     * Sets a new shortDescription
     *
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @param string $shortDescription
     * @return self
     */
    public function setShortDescription($shortDescription)
    {
        $this->shortDescription = $shortDescription;
        return $this;
    }

    /**
     * Adds as keyFeaturesValue
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @return self
     * @param string $keyFeaturesValue
     */
    public function addToKeyFeatures($keyFeaturesValue)
    {
        $this->keyFeatures[] = $keyFeaturesValue;
        return $this;
    }

    /**
     * isset keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetKeyFeatures($index)
    {
        return isset($this->keyFeatures[$index]);
    }

    /**
     * unset keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetKeyFeatures($index)
    {
        unset($this->keyFeatures[$index]);
    }

    /**
     * Gets as keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @return string[]
     */
    public function getKeyFeatures()
    {
        return $this->keyFeatures;
    }

    /**
     * Sets a new keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param string $keyFeatures
     * @return self
     */
    public function setKeyFeatures(array $keyFeatures)
    {
        $this->keyFeatures = $keyFeatures;
        return $this;
    }

    /**
     * Gets as brand
     *
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @return string
     */
    public function getBrand()
    {
        return $this->brand;
    }

    /**
     * Sets a new brand
     *
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @param string $brand
     * @return self
     */
    public function setBrand($brand)
    {
        $this->brand = $brand;
        return $this;
    }

    /**
     * Gets as manufacturer
     *
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @return string
     */
    public function getManufacturer()
    {
        return $this->manufacturer;
    }

    /**
     * Sets a new manufacturer
     *
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @param string $manufacturer
     * @return self
     */
    public function setManufacturer($manufacturer)
    {
        $this->manufacturer = $manufacturer;
        return $this;
    }

    /**
     * Gets as manufacturerPartNumber
     *
     * MPN uniquely identifies the product to its manufacturer. For many products this will be identical to the model number. Some manufacturers distinguish part number from model number. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @return string
     */
    public function getManufacturerPartNumber()
    {
        return $this->manufacturerPartNumber;
    }

    /**
     * Sets a new manufacturerPartNumber
     *
     * MPN uniquely identifies the product to its manufacturer. For many products this will be identical to the model number. Some manufacturers distinguish part number from model number. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @param string $manufacturerPartNumber
     * @return self
     */
    public function setManufacturerPartNumber($manufacturerPartNumber)
    {
        $this->manufacturerPartNumber = $manufacturerPartNumber;
        return $this;
    }

    /**
     * Gets as modelNumber
     *
     * Model numbers allow manufacturers to keep track of each hardware device and identify or replace the proper part when needed. Model numbers are often found on the bottom, back, or side of a product. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @return string
     */
    public function getModelNumber()
    {
        return $this->modelNumber;
    }

    /**
     * Sets a new modelNumber
     *
     * Model numbers allow manufacturers to keep track of each hardware device and identify or replace the proper part when needed. Model numbers are often found on the bottom, back, or side of a product. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @param string $modelNumber
     * @return self
     */
    public function setModelNumber($modelNumber)
    {
        $this->modelNumber = $modelNumber;
        return $this;
    }

    /**
     * Gets as multipackQuantity
     *
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @return int
     */
    public function getMultipackQuantity()
    {
        return $this->multipackQuantity;
    }

    /**
     * Sets a new multipackQuantity
     *
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @param int $multipackQuantity
     * @return self
     */
    public function setMultipackQuantity($multipackQuantity)
    {
        $this->multipackQuantity = $multipackQuantity;
        return $this;
    }

    /**
     * Gets as countPerPack
     *
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @return int
     */
    public function getCountPerPack()
    {
        return $this->countPerPack;
    }

    /**
     * Sets a new countPerPack
     *
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @param int $countPerPack
     * @return self
     */
    public function setCountPerPack($countPerPack)
    {
        $this->countPerPack = $countPerPack;
        return $this;
    }

    /**
     * Gets as count
     *
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @return string
     */
    public function getCount()
    {
        return $this->count;
    }

    /**
     * Sets a new count
     *
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @param string $count
     * @return self
     */
    public function setCount($count)
    {
        $this->count = $count;
        return $this;
    }

    /**
     * Gets as pieceCount
     *
     * The number of small pieces, slices, or different items within the product. Piece Count applies to things such as puzzles, building block sets, and products that contain multiple different items (such as tool sets, dinnerware sets, gift baskets, art sets, makeup kits, or shaving kits). EXAMPLE: (1) A 500-piece puzzle has a "Piece Count" of 500. (2) A 105-Piece Socket Wrench set has a piece count of "105." (3) A gift basket of 5 different items has a "Piece Count" of 5.
     *
     * @return int
     */
    public function getPieceCount()
    {
        return $this->pieceCount;
    }

    /**
     * Sets a new pieceCount
     *
     * The number of small pieces, slices, or different items within the product. Piece Count applies to things such as puzzles, building block sets, and products that contain multiple different items (such as tool sets, dinnerware sets, gift baskets, art sets, makeup kits, or shaving kits). EXAMPLE: (1) A 500-piece puzzle has a "Piece Count" of 500. (2) A 105-Piece Socket Wrench set has a piece count of "105." (3) A gift basket of 5 different items has a "Piece Count" of 5.
     *
     * @param int $pieceCount
     * @return self
     */
    public function setPieceCount($pieceCount)
    {
        $this->pieceCount = $pieceCount;
        return $this;
    }

    /**
     * Gets as mainImageUrl
     *
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return string
     */
    public function getMainImageUrl()
    {
        return $this->mainImageUrl;
    }

    /**
     * Sets a new mainImageUrl
     *
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param string $mainImageUrl
     * @return self
     */
    public function setMainImageUrl($mainImageUrl)
    {
        $this->mainImageUrl = $mainImageUrl;
        return $this;
    }

    /**
     * Adds as productSecondaryImageURLValue
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return self
     * @param string $productSecondaryImageURLValue
     */
    public function addToProductSecondaryImageURL($productSecondaryImageURLValue)
    {
        $this->productSecondaryImageURL[] = $productSecondaryImageURLValue;
        return $this;
    }

    /**
     * isset productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetProductSecondaryImageURL($index)
    {
        return isset($this->productSecondaryImageURL[$index]);
    }

    /**
     * unset productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetProductSecondaryImageURL($index)
    {
        unset($this->productSecondaryImageURL[$index]);
    }

    /**
     * Gets as productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return string[]
     */
    public function getProductSecondaryImageURL()
    {
        return $this->productSecondaryImageURL;
    }

    /**
     * Sets a new productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param string $productSecondaryImageURL
     * @return self
     */
    public function setProductSecondaryImageURL(array $productSecondaryImageURL)
    {
        $this->productSecondaryImageURL = $productSecondaryImageURL;
        return $this;
    }

    /**
     * Gets as gender
     *
     * Indicate whether this item is meant for a particular gender or meant to be gender-agnostic (unisex).
     *
     * @return string
     */
    public function getGender()
    {
        return $this->gender;
    }

    /**
     * Sets a new gender
     *
     * Indicate whether this item is meant for a particular gender or meant to be gender-agnostic (unisex).
     *
     * @param string $gender
     * @return self
     */
    public function setGender($gender)
    {
        $this->gender = $gender;
        return $this;
    }

    /**
     * Gets as size
     *
     * Overall dimensions of an item. Used only for products that do not already have a more specific 'x size' attribute, such as ring size or clothing size.
     *
     * @return string
     */
    public function getSize()
    {
        return $this->size;
    }

    /**
     * Sets a new size
     *
     * Overall dimensions of an item. Used only for products that do not already have a more specific 'x size' attribute, such as ring size or clothing size.
     *
     * @param string $size
     * @return self
     */
    public function setSize($size)
    {
        $this->size = $size;
        return $this;
    }

    /**
     * Adds as ageGroupValue
     *
     * General grouping of ages into commonly used demographic labels.
     *
     * @return self
     * @param string $ageGroupValue
     */
    public function addToAgeGroup($ageGroupValue)
    {
        $this->ageGroup[] = $ageGroupValue;
        return $this;
    }

    /**
     * isset ageGroup
     *
     * General grouping of ages into commonly used demographic labels.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAgeGroup($index)
    {
        return isset($this->ageGroup[$index]);
    }

    /**
     * unset ageGroup
     *
     * General grouping of ages into commonly used demographic labels.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAgeGroup($index)
    {
        unset($this->ageGroup[$index]);
    }

    /**
     * Gets as ageGroup
     *
     * General grouping of ages into commonly used demographic labels.
     *
     * @return string[]
     */
    public function getAgeGroup()
    {
        return $this->ageGroup;
    }

    /**
     * Sets a new ageGroup
     *
     * General grouping of ages into commonly used demographic labels.
     *
     * @param string $ageGroup
     * @return self
     */
    public function setAgeGroup(array $ageGroup)
    {
        $this->ageGroup = $ageGroup;
        return $this;
    }

    /**
     * Gets as ageRange
     *
     * Minimum and Maximum Ages for a product. Note: Both Min. and Max. attributes will be the same Unit of Measure: Months, or Years.
     *
     * @return \WalmartDSV\AgeRangeType
     */
    public function getAgeRange()
    {
        return $this->ageRange;
    }

    /**
     * Sets a new ageRange
     *
     * Minimum and Maximum Ages for a product. Note: Both Min. and Max. attributes will be the same Unit of Measure: Months, or Years.
     *
     * @param \WalmartDSV\AgeRangeType $ageRange
     * @return self
     */
    public function setAgeRange(\WalmartDSV\AgeRangeType $ageRange)
    {
        $this->ageRange = $ageRange;
        return $this;
    }

    /**
     * Gets as digitalZoom
     *
     * Measure of the magnification power provided by a feature that electronically enlarges the image area at the center of the frame, trims away the outside edges of the picture, and interpolates the result to the pixel dimensions of the original. Important to consumers because digital zoom reduces the image resolution and the image quality vs. optical zoom, which does not affect the quality of the zoomed image.
     *
     * @return string
     */
    public function getDigitalZoom()
    {
        return $this->digitalZoom;
    }

    /**
     * Sets a new digitalZoom
     *
     * Measure of the magnification power provided by a feature that electronically enlarges the image area at the center of the frame, trims away the outside edges of the picture, and interpolates the result to the pixel dimensions of the original. Important to consumers because digital zoom reduces the image resolution and the image quality vs. optical zoom, which does not affect the quality of the zoomed image.
     *
     * @param string $digitalZoom
     * @return self
     */
    public function setDigitalZoom($digitalZoom)
    {
        $this->digitalZoom = $digitalZoom;
        return $this;
    }

    /**
     * Gets as opticalZoom
     *
     * Measure of the magnification power of a physical optical zoom lens. For example, a camera with an optical zoom of 4x allows the users to magnify image up to 4x larger. Important to consumers because optical zoom results in better image quality than zoom that is digitally generated.
     *
     * @return string
     */
    public function getOpticalZoom()
    {
        return $this->opticalZoom;
    }

    /**
     * Sets a new opticalZoom
     *
     * Measure of the magnification power of a physical optical zoom lens. For example, a camera with an optical zoom of 4x allows the users to magnify image up to 4x larger. Important to consumers because optical zoom results in better image quality than zoom that is digitally generated.
     *
     * @param string $opticalZoom
     * @return self
     */
    public function setOpticalZoom($opticalZoom)
    {
        $this->opticalZoom = $opticalZoom;
        return $this;
    }

    /**
     * Gets as lensDiameter
     *
     * Measurement of the diameter of the front portion of the lens, measured in mm. For cameras, important factor to fit accessories such as filters.
     *
     * @return \WalmartDSV\OpticsType\LensDiameterAType
     */
    public function getLensDiameter()
    {
        return $this->lensDiameter;
    }

    /**
     * Sets a new lensDiameter
     *
     * Measurement of the diameter of the front portion of the lens, measured in mm. For cameras, important factor to fit accessories such as filters.
     *
     * @param \WalmartDSV\OpticsType\LensDiameterAType $lensDiameter
     * @return self
     */
    public function setLensDiameter(\WalmartDSV\OpticsType\LensDiameterAType $lensDiameter)
    {
        $this->lensDiameter = $lensDiameter;
        return $this;
    }

    /**
     * Gets as lensCoating
     *
     * Type of thin layer of material applied to the surface of lenses or other optical elements that provide specific effects. Usually applied to components such as camera lenses to improve resistance to scratches, or provide a mirror effect for sunglasses.
     *
     * @return string
     */
    public function getLensCoating()
    {
        return $this->lensCoating;
    }

    /**
     * Sets a new lensCoating
     *
     * Type of thin layer of material applied to the surface of lenses or other optical elements that provide specific effects. Usually applied to components such as camera lenses to improve resistance to scratches, or provide a mirror effect for sunglasses.
     *
     * @param string $lensCoating
     * @return self
     */
    public function setLensCoating($lensCoating)
    {
        $this->lensCoating = $lensCoating;
        return $this;
    }

    /**
     * Gets as sensorResolution
     *
     * One specification describing the smallest detectable incremental change of input parameter that can be detected in the output signal. Expressed either as a proportion of the full-scale reading, or as an absolute. Used for a variety of sensor types and importance varies with product. For example for digital cameras, image sensor resolution is an important factor for image quality.
     *
     * @return \WalmartDSV\ResolutionUnitType
     */
    public function getSensorResolution()
    {
        return $this->sensorResolution;
    }

    /**
     * Sets a new sensorResolution
     *
     * One specification describing the smallest detectable incremental change of input parameter that can be detected in the output signal. Expressed either as a proportion of the full-scale reading, or as an absolute. Used for a variety of sensor types and importance varies with product. For example for digital cameras, image sensor resolution is an important factor for image quality.
     *
     * @param \WalmartDSV\ResolutionUnitType $sensorResolution
     * @return self
     */
    public function setSensorResolution(\WalmartDSV\ResolutionUnitType $sensorResolution)
    {
        $this->sensorResolution = $sensorResolution;
        return $this;
    }

    /**
     * Gets as magnification
     *
     * Number expressing a ratio of how much a device’s optical system can increase (or decrease) an image as compared to the true size. Typically applied to products such as magnifying lenses and microscopes and expressed as a number followed by an x.
     *
     * @return string
     */
    public function getMagnification()
    {
        return $this->magnification;
    }

    /**
     * Sets a new magnification
     *
     * Number expressing a ratio of how much a device’s optical system can increase (or decrease) an image as compared to the true size. Typically applied to products such as magnifying lenses and microscopes and expressed as a number followed by an x.
     *
     * @param string $magnification
     * @return self
     */
    public function setMagnification($magnification)
    {
        $this->magnification = $magnification;
        return $this;
    }

    /**
     * Adds as focusTypeValue
     *
     * Terms that describe modes, mechanisms, or control arrangements that adjust optical focus on the device. For example, if item is a pair of binoculars, focus type would describe weather the lenses on binoculars can be adjusted independently of one another.
     *
     * @return self
     * @param string $focusTypeValue
     */
    public function addToFocusType($focusTypeValue)
    {
        $this->focusType[] = $focusTypeValue;
        return $this;
    }

    /**
     * isset focusType
     *
     * Terms that describe modes, mechanisms, or control arrangements that adjust optical focus on the device. For example, if item is a pair of binoculars, focus type would describe weather the lenses on binoculars can be adjusted independently of one another.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFocusType($index)
    {
        return isset($this->focusType[$index]);
    }

    /**
     * unset focusType
     *
     * Terms that describe modes, mechanisms, or control arrangements that adjust optical focus on the device. For example, if item is a pair of binoculars, focus type would describe weather the lenses on binoculars can be adjusted independently of one another.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFocusType($index)
    {
        unset($this->focusType[$index]);
    }

    /**
     * Gets as focusType
     *
     * Terms that describe modes, mechanisms, or control arrangements that adjust optical focus on the device. For example, if item is a pair of binoculars, focus type would describe weather the lenses on binoculars can be adjusted independently of one another.
     *
     * @return string[]
     */
    public function getFocusType()
    {
        return $this->focusType;
    }

    /**
     * Sets a new focusType
     *
     * Terms that describe modes, mechanisms, or control arrangements that adjust optical focus on the device. For example, if item is a pair of binoculars, focus type would describe weather the lenses on binoculars can be adjusted independently of one another.
     *
     * @param string $focusType
     * @return self
     */
    public function setFocusType(array $focusType)
    {
        $this->focusType = $focusType;
        return $this;
    }

    /**
     * Gets as fieldOfView
     *
     * Measure of the area that can be seen through a lens of an item, as specified by the manufacturer. Attribute applied to such products as microscopes, telescopes and rifle scopes. Can be expressed as the angular field of view (in degrees) or the true field of view (in feet).
     *
     * @return string
     */
    public function getFieldOfView()
    {
        return $this->fieldOfView;
    }

    /**
     * Sets a new fieldOfView
     *
     * Measure of the area that can be seen through a lens of an item, as specified by the manufacturer. Attribute applied to such products as microscopes, telescopes and rifle scopes. Can be expressed as the angular field of view (in degrees) or the true field of view (in feet).
     *
     * @param string $fieldOfView
     * @return self
     */
    public function setFieldOfView($fieldOfView)
    {
        $this->fieldOfView = $fieldOfView;
        return $this;
    }

    /**
     * Gets as isParfocal
     *
     * Y indicates the item has a lens that stays in focus when magnification/focal length is changed. For example, a microscope that stays in focus when the microscope objective is rotated.
     *
     * @return string
     */
    public function getIsParfocal()
    {
        return $this->isParfocal;
    }

    /**
     * Sets a new isParfocal
     *
     * Y indicates the item has a lens that stays in focus when magnification/focal length is changed. For example, a microscope that stays in focus when the microscope objective is rotated.
     *
     * @param string $isParfocal
     * @return self
     */
    public function setIsParfocal($isParfocal)
    {
        $this->isParfocal = $isParfocal;
        return $this;
    }

    /**
     * Gets as focalRatio
     *
     * Ratio of the lens's focal length, to the diameter of the entrance pupil (optical image of the physical aperture stop, as 'seen' through the front of the lens system). Also known as the f-number or f-stop, this number indicates lens speed, an important selection criteria based on intended photographic use.
     *
     * @return string
     */
    public function getFocalRatio()
    {
        return $this->focalRatio;
    }

    /**
     * Sets a new focalRatio
     *
     * Ratio of the lens's focal length, to the diameter of the entrance pupil (optical image of the physical aperture stop, as 'seen' through the front of the lens system). Also known as the f-number or f-stop, this number indicates lens speed, an important selection criteria based on intended photographic use.
     *
     * @param string $focalRatio
     * @return self
     */
    public function setFocalRatio($focalRatio)
    {
        $this->focalRatio = $focalRatio;
        return $this;
    }

    /**
     * Gets as displayTechnology
     *
     * The primary technology used for the item's display.
     *
     * @return string
     */
    public function getDisplayTechnology()
    {
        return $this->displayTechnology;
    }

    /**
     * Sets a new displayTechnology
     *
     * The primary technology used for the item's display.
     *
     * @param string $displayTechnology
     * @return self
     */
    public function setDisplayTechnology($displayTechnology)
    {
        $this->displayTechnology = $displayTechnology;
        return $this;
    }

    /**
     * Gets as displayResolution
     *
     * If the item has a screen display, the resolution value of the screen component of the product. Typically measured as the number of pixels creating the display expressed as number of columns x number of rows. For example, a digital camera's screen resolution (vs. the image quality the camera can produce) of 640x480.
     *
     * @return \WalmartDSV\DisplayResolutionType
     */
    public function getDisplayResolution()
    {
        return $this->displayResolution;
    }

    /**
     * Sets a new displayResolution
     *
     * If the item has a screen display, the resolution value of the screen component of the product. Typically measured as the number of pixels creating the display expressed as number of columns x number of rows. For example, a digital camera's screen resolution (vs. the image quality the camera can produce) of 640x480.
     *
     * @param \WalmartDSV\DisplayResolutionType $displayResolution
     * @return self
     */
    public function setDisplayResolution(\WalmartDSV\DisplayResolutionType $displayResolution)
    {
        $this->displayResolution = $displayResolution;
        return $this;
    }

    /**
     * Gets as hasNightVision
     *
     * Does this device have features that give users the ability to see in low light conditions?
     *
     * @return string
     */
    public function getHasNightVision()
    {
        return $this->hasNightVision;
    }

    /**
     * Sets a new hasNightVision
     *
     * Does this device have features that give users the ability to see in low light conditions?
     *
     * @param string $hasNightVision
     * @return self
     */
    public function setHasNightVision($hasNightVision)
    {
        $this->hasNightVision = $hasNightVision;
        return $this;
    }

    /**
     * Gets as assembledProductLength
     *
     * The length of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @return \WalmartDSV\OpticsType\AssembledProductLengthAType
     */
    public function getAssembledProductLength()
    {
        return $this->assembledProductLength;
    }

    /**
     * Sets a new assembledProductLength
     *
     * The length of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @param \WalmartDSV\OpticsType\AssembledProductLengthAType $assembledProductLength
     * @return self
     */
    public function setAssembledProductLength(\WalmartDSV\OpticsType\AssembledProductLengthAType $assembledProductLength)
    {
        $this->assembledProductLength = $assembledProductLength;
        return $this;
    }

    /**
     * Gets as assembledProductWidth
     *
     * The width of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @return \WalmartDSV\OpticsType\AssembledProductWidthAType
     */
    public function getAssembledProductWidth()
    {
        return $this->assembledProductWidth;
    }

    /**
     * Sets a new assembledProductWidth
     *
     * The width of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @param \WalmartDSV\OpticsType\AssembledProductWidthAType $assembledProductWidth
     * @return self
     */
    public function setAssembledProductWidth(\WalmartDSV\OpticsType\AssembledProductWidthAType $assembledProductWidth)
    {
        $this->assembledProductWidth = $assembledProductWidth;
        return $this;
    }

    /**
     * Gets as assembledProductHeight
     *
     * The height of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @return \WalmartDSV\OpticsType\AssembledProductHeightAType
     */
    public function getAssembledProductHeight()
    {
        return $this->assembledProductHeight;
    }

    /**
     * Sets a new assembledProductHeight
     *
     * The height of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @param \WalmartDSV\OpticsType\AssembledProductHeightAType $assembledProductHeight
     * @return self
     */
    public function setAssembledProductHeight(\WalmartDSV\OpticsType\AssembledProductHeightAType $assembledProductHeight)
    {
        $this->assembledProductHeight = $assembledProductHeight;
        return $this;
    }

    /**
     * Gets as assembledProductWeight
     *
     * The weight of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @return \WalmartDSV\OpticsType\AssembledProductWeightAType
     */
    public function getAssembledProductWeight()
    {
        return $this->assembledProductWeight;
    }

    /**
     * Sets a new assembledProductWeight
     *
     * The weight of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @param \WalmartDSV\OpticsType\AssembledProductWeightAType $assembledProductWeight
     * @return self
     */
    public function setAssembledProductWeight(\WalmartDSV\OpticsType\AssembledProductWeightAType $assembledProductWeight)
    {
        $this->assembledProductWeight = $assembledProductWeight;
        return $this;
    }

    /**
     * Gets as variantGroupId
     *
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @return string
     */
    public function getVariantGroupId()
    {
        return $this->variantGroupId;
    }

    /**
     * Sets a new variantGroupId
     *
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @param string $variantGroupId
     * @return self
     */
    public function setVariantGroupId($variantGroupId)
    {
        $this->variantGroupId = $variantGroupId;
        return $this;
    }

    /**
     * Adds as variantAttributeName
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @return self
     * @param string $variantAttributeName
     */
    public function addToVariantAttributeNames($variantAttributeName)
    {
        $this->variantAttributeNames[] = $variantAttributeName;
        return $this;
    }

    /**
     * isset variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetVariantAttributeNames($index)
    {
        return isset($this->variantAttributeNames[$index]);
    }

    /**
     * unset variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetVariantAttributeNames($index)
    {
        unset($this->variantAttributeNames[$index]);
    }

    /**
     * Gets as variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @return string[]
     */
    public function getVariantAttributeNames()
    {
        return $this->variantAttributeNames;
    }

    /**
     * Sets a new variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param string $variantAttributeNames
     * @return self
     */
    public function setVariantAttributeNames(array $variantAttributeNames)
    {
        $this->variantAttributeNames = $variantAttributeNames;
        return $this;
    }

    /**
     * Gets as isPrimaryVariant
     *
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @return string
     */
    public function getIsPrimaryVariant()
    {
        return $this->isPrimaryVariant;
    }

    /**
     * Sets a new isPrimaryVariant
     *
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @param string $isPrimaryVariant
     * @return self
     */
    public function setIsPrimaryVariant($isPrimaryVariant)
    {
        $this->isPrimaryVariant = $isPrimaryVariant;
        return $this;
    }

    /**
     * Gets as isPrivateLabelOrUnbranded
     *
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @return string
     */
    public function getIsPrivateLabelOrUnbranded()
    {
        return $this->isPrivateLabelOrUnbranded;
    }

    /**
     * Sets a new isPrivateLabelOrUnbranded
     *
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @param string $isPrivateLabelOrUnbranded
     * @return self
     */
    public function setIsPrivateLabelOrUnbranded($isPrivateLabelOrUnbranded)
    {
        $this->isPrivateLabelOrUnbranded = $isPrivateLabelOrUnbranded;
        return $this;
    }

    /**
     * Gets as isProp65WarningRequired
     *
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @return string
     */
    public function getIsProp65WarningRequired()
    {
        return $this->isProp65WarningRequired;
    }

    /**
     * Sets a new isProp65WarningRequired
     *
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @param string $isProp65WarningRequired
     * @return self
     */
    public function setIsProp65WarningRequired($isProp65WarningRequired)
    {
        $this->isProp65WarningRequired = $isProp65WarningRequired;
        return $this;
    }

    /**
     * Gets as prop65WarningText
     *
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @return string
     */
    public function getProp65WarningText()
    {
        return $this->prop65WarningText;
    }

    /**
     * Sets a new prop65WarningText
     *
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @param string $prop65WarningText
     * @return self
     */
    public function setProp65WarningText($prop65WarningText)
    {
        $this->prop65WarningText = $prop65WarningText;
        return $this;
    }

    /**
     * Gets as hasBatteries
     *
     * "Battery or battery containing product" is defined by Company to include any item of Merchandise that is a battery or any component of Merchandise, including reusable packaging intended to stay in use with the item, containing a battery of any chemistry/ type. Mark Y if this definition applies to your product.
     *
     * @return string
     */
    public function getHasBatteries()
    {
        return $this->hasBatteries;
    }

    /**
     * Sets a new hasBatteries
     *
     * "Battery or battery containing product" is defined by Company to include any item of Merchandise that is a battery or any component of Merchandise, including reusable packaging intended to stay in use with the item, containing a battery of any chemistry/ type. Mark Y if this definition applies to your product.
     *
     * @param string $hasBatteries
     * @return self
     */
    public function setHasBatteries($hasBatteries)
    {
        $this->hasBatteries = $hasBatteries;
        return $this;
    }

    /**
     * Gets as batteryTechnologyType
     *
     * Please select the Battery Technology Type from the list provided. NOTE: If battery type is lead acid, lead acid (nonspillable), lithium ion, or lithium metal, please ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @return string
     */
    public function getBatteryTechnologyType()
    {
        return $this->batteryTechnologyType;
    }

    /**
     * Sets a new batteryTechnologyType
     *
     * Please select the Battery Technology Type from the list provided. NOTE: If battery type is lead acid, lead acid (nonspillable), lithium ion, or lithium metal, please ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @param string $batteryTechnologyType
     * @return self
     */
    public function setBatteryTechnologyType($batteryTechnologyType)
    {
        $this->batteryTechnologyType = $batteryTechnologyType;
        return $this;
    }

    /**
     * Gets as isChemical
     *
     * ‘Chemical’ is defined by Walmart to include any item of Merchandise that contains a powder, gel, paste, or liquid that is not intended for human consumption. ‘Chemical’ also includes the following types items that ARE intended for human consumption, inhalation, or absorption, or labeled with drug facts: All over-the-counter medications, including: Lozenges, pills or capsules (e.g. pain relievers; allergy medications; as well as vitamins and supplements that contain metals); Medicated swabs and wipes, acne medication, and sunscreen; Medicated patches (such as nicotine patches); Liquids (e.g. cough medicine, medicated drops, nasal spray and inhalers); Medicated shampoos, gums, ointments and creams; Medicated lip balm, lip creams and petroleum jelly; Contraceptive foam, films, and spermicides; and Product/Equipment sold with chemicals (e.g. vaporizer sold with medication) and electronic cigarettes. If your product meets this definition, Mark Y and ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @return string
     */
    public function getIsChemical()
    {
        return $this->isChemical;
    }

    /**
     * Sets a new isChemical
     *
     * ‘Chemical’ is defined by Walmart to include any item of Merchandise that contains a powder, gel, paste, or liquid that is not intended for human consumption. ‘Chemical’ also includes the following types items that ARE intended for human consumption, inhalation, or absorption, or labeled with drug facts: All over-the-counter medications, including: Lozenges, pills or capsules (e.g. pain relievers; allergy medications; as well as vitamins and supplements that contain metals); Medicated swabs and wipes, acne medication, and sunscreen; Medicated patches (such as nicotine patches); Liquids (e.g. cough medicine, medicated drops, nasal spray and inhalers); Medicated shampoos, gums, ointments and creams; Medicated lip balm, lip creams and petroleum jelly; Contraceptive foam, films, and spermicides; and Product/Equipment sold with chemicals (e.g. vaporizer sold with medication) and electronic cigarettes. If your product meets this definition, Mark Y and ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @param string $isChemical
     * @return self
     */
    public function setIsChemical($isChemical)
    {
        $this->isChemical = $isChemical;
        return $this;
    }

    /**
     * Gets as hasWarranty
     *
     * Y indicates the item comes with a warranty. If an item has a warranty, then enter EITHER the warranty URL or the warranty text in the appropriate field.
     *
     * @return string
     */
    public function getHasWarranty()
    {
        return $this->hasWarranty;
    }

    /**
     * Sets a new hasWarranty
     *
     * Y indicates the item comes with a warranty. If an item has a warranty, then enter EITHER the warranty URL or the warranty text in the appropriate field.
     *
     * @param string $hasWarranty
     * @return self
     */
    public function setHasWarranty($hasWarranty)
    {
        $this->hasWarranty = $hasWarranty;
        return $this;
    }

    /**
     * Gets as warrantyURL
     *
     * If you indicated that your item has a warranty, provide either the Warranty URL or Warranty Text. The Warranty URL is the web location of the image, PDF, or link to the manufacturer's warranty page, showing the warranty and its terms, including the duration of the warranty. URLs must begin with http:// or https:// NOTE: Please remember to update the link and/or text of the warranty as the warranty changes. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @return string
     */
    public function getWarrantyURL()
    {
        return $this->warrantyURL;
    }

    /**
     * Sets a new warrantyURL
     *
     * If you indicated that your item has a warranty, provide either the Warranty URL or Warranty Text. The Warranty URL is the web location of the image, PDF, or link to the manufacturer's warranty page, showing the warranty and its terms, including the duration of the warranty. URLs must begin with http:// or https:// NOTE: Please remember to update the link and/or text of the warranty as the warranty changes. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @param string $warrantyURL
     * @return self
     */
    public function setWarrantyURL($warrantyURL)
    {
        $this->warrantyURL = $warrantyURL;
        return $this;
    }

    /**
     * Gets as warrantyText
     *
     * If you marked Y for "Has Warranty" provide the Warranty URL or Warranty Text (the full text of the warranty terms, including what is covered by the warranty and the duration of the warranty). NOTE: please remember to update the text of your warranty as your warranty changes.
     *
     * @return string
     */
    public function getWarrantyText()
    {
        return $this->warrantyText;
    }

    /**
     * Sets a new warrantyText
     *
     * If you marked Y for "Has Warranty" provide the Warranty URL or Warranty Text (the full text of the warranty terms, including what is covered by the warranty and the duration of the warranty). NOTE: please remember to update the text of your warranty as your warranty changes.
     *
     * @param string $warrantyText
     * @return self
     */
    public function setWarrantyText($warrantyText)
    {
        $this->warrantyText = $warrantyText;
        return $this;
    }

    /**
     * Gets as hasStateRestrictions
     *
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @return string
     */
    public function getHasStateRestrictions()
    {
        return $this->hasStateRestrictions;
    }

    /**
     * Sets a new hasStateRestrictions
     *
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @param string $hasStateRestrictions
     * @return self
     */
    public function setHasStateRestrictions($hasStateRestrictions)
    {
        $this->hasStateRestrictions = $hasStateRestrictions;
        return $this;
    }

    /**
     * Adds as stateRestriction
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param \WalmartDSV\StateRestrictionType $stateRestriction
     *@return self
     */
    public function addToStateRestrictions(\WalmartDSV\StateRestrictionType $stateRestriction)
    {
        $this->stateRestrictions[] = $stateRestriction;
        return $this;
    }

    /**
     * isset stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetStateRestrictions($index)
    {
        return isset($this->stateRestrictions[$index]);
    }

    /**
     * unset stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetStateRestrictions($index)
    {
        unset($this->stateRestrictions[$index]);
    }

    /**
     * Gets as stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @return \WalmartDSV\StateRestrictionType[]
     */
    public function getStateRestrictions()
    {
        return $this->stateRestrictions;
    }

    /**
     * Sets a new stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param \WalmartDSV\StateRestrictionType[] $stateRestrictions
     * @return self
     */
    public function setStateRestrictions(array $stateRestrictions)
    {
        $this->stateRestrictions = $stateRestrictions;
        return $this;
    }

    /**
     * Gets as isAssemblyRequired
     *
     * Is product unassembled and must be put together before use?
     *
     * @return string
     */
    public function getIsAssemblyRequired()
    {
        return $this->isAssemblyRequired;
    }

    /**
     * Sets a new isAssemblyRequired
     *
     * Is product unassembled and must be put together before use?
     *
     * @param string $isAssemblyRequired
     * @return self
     */
    public function setIsAssemblyRequired($isAssemblyRequired)
    {
        $this->isAssemblyRequired = $isAssemblyRequired;
        return $this;
    }

    /**
     * Gets as assemblyInstructions
     *
     * Provide a URL to an image or PDF asset showing assembly instructions for items requiring assembly. URLs must be static and have no query parameters. URLs must begin with http:// or https:// and should end in in the file name.
     *
     * @return string
     */
    public function getAssemblyInstructions()
    {
        return $this->assemblyInstructions;
    }

    /**
     * Sets a new assemblyInstructions
     *
     * Provide a URL to an image or PDF asset showing assembly instructions for items requiring assembly. URLs must be static and have no query parameters. URLs must begin with http:// or https:// and should end in in the file name.
     *
     * @param string $assemblyInstructions
     * @return self
     */
    public function setAssemblyInstructions($assemblyInstructions)
    {
        $this->assemblyInstructions = $assemblyInstructions;
        return $this;
    }

    /**
     * Gets as material
     *
     * The main material(s) that a product is made of. This does not need to be an exhaustive list, but should contain the predominant or functionally important material/materials. Fabric material specifics should be entered using the "Fabric Content" attribute.
     *
     * @return \WalmartDSV\MaterialType
     */
    public function getMaterial()
    {
        return $this->material;
    }

    /**
     * Sets a new material
     *
     * The main material(s) that a product is made of. This does not need to be an exhaustive list, but should contain the predominant or functionally important material/materials. Fabric material specifics should be entered using the "Fabric Content" attribute.
     *
     * @param \WalmartDSV\MaterialType $material
     * @return self
     */
    public function setMaterial(\WalmartDSV\MaterialType $material)
    {
        $this->material = $material;
        return $this;
    }

    /**
     * Gets as dexterity
     *
     * Indicates whether the item is designed to be used by the right or left hand.
     *
     * @return string
     */
    public function getDexterity()
    {
        return $this->dexterity;
    }

    /**
     * Sets a new dexterity
     *
     * Indicates whether the item is designed to be used by the right or left hand.
     *
     * @param string $dexterity
     * @return self
     */
    public function setDexterity($dexterity)
    {
        $this->dexterity = $dexterity;
        return $this;
    }

    /**
     * Adds as globalBrandLicenseValue
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @return self
     * @param string $globalBrandLicenseValue
     */
    public function addToGlobalBrandLicense($globalBrandLicenseValue)
    {
        $this->globalBrandLicense[] = $globalBrandLicenseValue;
        return $this;
    }

    /**
     * isset globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @param int|string $index
     * @return bool
     */
    public function issetGlobalBrandLicense($index)
    {
        return isset($this->globalBrandLicense[$index]);
    }

    /**
     * unset globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @param int|string $index
     * @return void
     */
    public function unsetGlobalBrandLicense($index)
    {
        unset($this->globalBrandLicense[$index]);
    }

    /**
     * Gets as globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @return string[]
     */
    public function getGlobalBrandLicense()
    {
        return $this->globalBrandLicense;
    }

    /**
     * Sets a new globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @param string $globalBrandLicense
     * @return self
     */
    public function setGlobalBrandLicense(array $globalBrandLicense)
    {
        $this->globalBrandLicense = $globalBrandLicense;
        return $this;
    }

    /**
     * Gets as screenSize
     *
     * Typically measured on the diagonal in inches.
     *
     * @return \WalmartDSV\OpticsType\ScreenSizeAType
     */
    public function getScreenSize()
    {
        return $this->screenSize;
    }

    /**
     * Sets a new screenSize
     *
     * Typically measured on the diagonal in inches.
     *
     * @param \WalmartDSV\OpticsType\ScreenSizeAType $screenSize
     * @return self
     */
    public function setScreenSize(\WalmartDSV\OpticsType\ScreenSizeAType $screenSize)
    {
        $this->screenSize = $screenSize;
        return $this;
    }

    /**
     * Gets as hasLcdScreen
     *
     * Y indicates the item has a liquid-crystal display.
     *
     * @return string
     */
    public function getHasLcdScreen()
    {
        return $this->hasLcdScreen;
    }

    /**
     * Sets a new hasLcdScreen
     *
     * Y indicates the item has a liquid-crystal display.
     *
     * @param string $hasLcdScreen
     * @return self
     */
    public function setHasLcdScreen($hasLcdScreen)
    {
        $this->hasLcdScreen = $hasLcdScreen;
        return $this;
    }

    /**
     * Gets as powerType
     *
     * Provides information on the exact type of power used by the item.
     *
     * @return string
     */
    public function getPowerType()
    {
        return $this->powerType;
    }

    /**
     * Sets a new powerType
     *
     * Provides information on the exact type of power used by the item.
     *
     * @param string $powerType
     * @return self
     */
    public function setPowerType($powerType)
    {
        $this->powerType = $powerType;
        return $this;
    }

    /**
     * Gets as isMulticoated
     *
     * Y indicates the item has a series of layers of coatings; For example, eye glasses that have layers of anti-reflective coatings.
     *
     * @return string
     */
    public function getIsMulticoated()
    {
        return $this->isMulticoated;
    }

    /**
     * Sets a new isMulticoated
     *
     * Y indicates the item has a series of layers of coatings; For example, eye glasses that have layers of anti-reflective coatings.
     *
     * @param string $isMulticoated
     * @return self
     */
    public function setIsMulticoated($isMulticoated)
    {
        $this->isMulticoated = $isMulticoated;
        return $this;
    }

    /**
     * Gets as isLockable
     *
     * Y indicates the item has the capability to be secured by a lock or a locking mechanism. Differentiates lockability as a feature of items such as a tool boxes, microscopes, or saddlebags.
     *
     * @return string
     */
    public function getIsLockable()
    {
        return $this->isLockable;
    }

    /**
     * Sets a new isLockable
     *
     * Y indicates the item has the capability to be secured by a lock or a locking mechanism. Differentiates lockability as a feature of items such as a tool boxes, microscopes, or saddlebags.
     *
     * @param string $isLockable
     * @return self
     */
    public function setIsLockable($isLockable)
    {
        $this->isLockable = $isLockable;
        return $this;
    }

    /**
     * Gets as lockType
     *
     * The type of lock included with the device. Typically based on access mechanism. Example: electronic lock, padlock
     *
     * @return string
     */
    public function getLockType()
    {
        return $this->lockType;
    }

    /**
     * Sets a new lockType
     *
     * The type of lock included with the device. Typically based on access mechanism. Example: electronic lock, padlock
     *
     * @param string $lockType
     * @return self
     */
    public function setLockType($lockType)
    {
        $this->lockType = $lockType;
        return $this;
    }

    /**
     * Gets as hasMemoryCardSlot
     *
     * Y indicates the item has a place to insert electronic memory data storage device used to record digital information.
     *
     * @return string
     */
    public function getHasMemoryCardSlot()
    {
        return $this->hasMemoryCardSlot;
    }

    /**
     * Sets a new hasMemoryCardSlot
     *
     * Y indicates the item has a place to insert electronic memory data storage device used to record digital information.
     *
     * @param string $hasMemoryCardSlot
     * @return self
     */
    public function setHasMemoryCardSlot($hasMemoryCardSlot)
    {
        $this->hasMemoryCardSlot = $hasMemoryCardSlot;
        return $this;
    }

    /**
     * Gets as isFogResistant
     *
     * Has this product been treated to prevent the condensation of water on its surface?
     *
     * @return string
     */
    public function getIsFogResistant()
    {
        return $this->isFogResistant;
    }

    /**
     * Sets a new isFogResistant
     *
     * Has this product been treated to prevent the condensation of water on its surface?
     *
     * @param string $isFogResistant
     * @return self
     */
    public function setIsFogResistant($isFogResistant)
    {
        $this->isFogResistant = $isFogResistant;
        return $this;
    }

    /**
     * Gets as operatingTemperature
     *
     * Temperature at which an electrical or mechanical device operates.
     *
     * @return \WalmartDSV\OpticsType\OperatingTemperatureAType
     */
    public function getOperatingTemperature()
    {
        return $this->operatingTemperature;
    }

    /**
     * Sets a new operatingTemperature
     *
     * Temperature at which an electrical or mechanical device operates.
     *
     * @param \WalmartDSV\OpticsType\OperatingTemperatureAType $operatingTemperature
     * @return self
     */
    public function setOperatingTemperature(\WalmartDSV\OpticsType\OperatingTemperatureAType $operatingTemperature)
    {
        $this->operatingTemperature = $operatingTemperature;
        return $this;
    }

    /**
     * Gets as hasDovetailBarSystem
     *
     * Does this have a dovetail bar used to attach a telescope optical tube to a mount? Typical styles include Losmandy D and Vixen V.
     *
     * @return string
     */
    public function getHasDovetailBarSystem()
    {
        return $this->hasDovetailBarSystem;
    }

    /**
     * Sets a new hasDovetailBarSystem
     *
     * Does this have a dovetail bar used to attach a telescope optical tube to a mount? Typical styles include Losmandy D and Vixen V.
     *
     * @param string $hasDovetailBarSystem
     * @return self
     */
    public function setHasDovetailBarSystem($hasDovetailBarSystem)
    {
        $this->hasDovetailBarSystem = $hasDovetailBarSystem;
        return $this;
    }

    /**
     * Gets as attachmentStyle
     *
     * Description of how the product is able to attach to other surfaces or items. Also used for product fit. For example, bayonet is an attachment style describing how a camera lens attaches to the camera body
     *
     * @return string
     */
    public function getAttachmentStyle()
    {
        return $this->attachmentStyle;
    }

    /**
     * Sets a new attachmentStyle
     *
     * Description of how the product is able to attach to other surfaces or items. Also used for product fit. For example, bayonet is an attachment style describing how a camera lens attaches to the camera body
     *
     * @param string $attachmentStyle
     * @return self
     */
    public function setAttachmentStyle($attachmentStyle)
    {
        $this->attachmentStyle = $attachmentStyle;
        return $this;
    }

    /**
     * Adds as feature
     *
     * List notable features of the item.
     *
     * @return self
     * @param string $feature
     */
    public function addToFeatures($feature)
    {
        $this->features[] = $feature;
        return $this;
    }

    /**
     * isset features
     *
     * List notable features of the item.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFeatures($index)
    {
        return isset($this->features[$index]);
    }

    /**
     * unset features
     *
     * List notable features of the item.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFeatures($index)
    {
        unset($this->features[$index]);
    }

    /**
     * Gets as features
     *
     * List notable features of the item.
     *
     * @return string[]
     */
    public function getFeatures()
    {
        return $this->features;
    }

    /**
     * Sets a new features
     *
     * List notable features of the item.
     *
     * @param string $features
     * @return self
     */
    public function setFeatures(array $features)
    {
        $this->features = $features;
        return $this;
    }

    /**
     * Gets as keywords
     *
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @return string
     */
    public function getKeywords()
    {
        return $this->keywords;
    }

    /**
     * Sets a new keywords
     *
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @param string $keywords
     * @return self
     */
    public function setKeywords($keywords)
    {
        $this->keywords = $keywords;
        return $this;
    }

    /**
     * Gets as color
     *
     * Color as described by the manufacturer.
     *
     * @return \WalmartDSV\ColorType
     */
    public function getColor()
    {
        return $this->color;
    }

    /**
     * Sets a new color
     *
     * Color as described by the manufacturer.
     *
     * @param \WalmartDSV\ColorType $color
     * @return self
     */
    public function setColor(\WalmartDSV\ColorType $color)
    {
        $this->color = $color;
        return $this;
    }

    /**
     * Adds as colorCategoryValue
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @return self
     * @param string $colorCategoryValue
     */
    public function addToColorCategory($colorCategoryValue)
    {
        $this->colorCategory[] = $colorCategoryValue;
        return $this;
    }

    /**
     * isset colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetColorCategory($index)
    {
        return isset($this->colorCategory[$index]);
    }

    /**
     * unset colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetColorCategory($index)
    {
        unset($this->colorCategory[$index]);
    }

    /**
     * Gets as colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @return string[]
     */
    public function getColorCategory()
    {
        return $this->colorCategory;
    }

    /**
     * Sets a new colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @param string $colorCategory
     * @return self
     */
    public function setColorCategory(array $colorCategory)
    {
        $this->colorCategory = $colorCategory;
        return $this;
    }

    /**
     * Gets as sport
     *
     * If the product is sports-related, the name of the specific sport depicted on the product, or the target sport for the product use
     *
     * @return \WalmartDSV\SportType
     */
    public function getSport()
    {
        return $this->sport;
    }

    /**
     * Sets a new sport
     *
     * If the product is sports-related, the name of the specific sport depicted on the product, or the target sport for the product use
     *
     * @param \WalmartDSV\SportType $sport
     * @return self
     */
    public function setSport(\WalmartDSV\SportType $sport)
    {
        $this->sport = $sport;
        return $this;
    }

    /**
     * Gets as pattern
     *
     * Decorative design or visual ornamentation, often with a thematic, recurring motif.
     *
     * @return \WalmartDSV\PatternType
     */
    public function getPattern()
    {
        return $this->pattern;
    }

    /**
     * Sets a new pattern
     *
     * Decorative design or visual ornamentation, often with a thematic, recurring motif.
     *
     * @param \WalmartDSV\PatternType $pattern
     * @return self
     */
    public function setPattern(\WalmartDSV\PatternType $pattern)
    {
        $this->pattern = $pattern;
        return $this;
    }

    /**
     * Adds as wirelessTechnology
     *
     * Any wireless communications standard used within or by the item.
     *
     * @return self
     * @param string $wirelessTechnology
     */
    public function addToWirelessTechnologies($wirelessTechnology)
    {
        $this->wirelessTechnologies[] = $wirelessTechnology;
        return $this;
    }

    /**
     * isset wirelessTechnologies
     *
     * Any wireless communications standard used within or by the item.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetWirelessTechnologies($index)
    {
        return isset($this->wirelessTechnologies[$index]);
    }

    /**
     * unset wirelessTechnologies
     *
     * Any wireless communications standard used within or by the item.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetWirelessTechnologies($index)
    {
        unset($this->wirelessTechnologies[$index]);
    }

    /**
     * Gets as wirelessTechnologies
     *
     * Any wireless communications standard used within or by the item.
     *
     * @return string[]
     */
    public function getWirelessTechnologies()
    {
        return $this->wirelessTechnologies;
    }

    /**
     * Sets a new wirelessTechnologies
     *
     * Any wireless communications standard used within or by the item.
     *
     * @param string $wirelessTechnologies
     * @return self
     */
    public function setWirelessTechnologies(array $wirelessTechnologies)
    {
        $this->wirelessTechnologies = $wirelessTechnologies;
        return $this;
    }

    /**
     * Gets as isPortable
     *
     * Is the item designed to be easily moved?
     *
     * @return string
     */
    public function getIsPortable()
    {
        return $this->isPortable;
    }

    /**
     * Sets a new isPortable
     *
     * Is the item designed to be easily moved?
     *
     * @param string $isPortable
     * @return self
     */
    public function setIsPortable($isPortable)
    {
        $this->isPortable = $isPortable;
        return $this;
    }

    /**
     * Gets as isFoldable
     *
     * Indicates that an item can be folded.
     *
     * @return string
     */
    public function getIsFoldable()
    {
        return $this->isFoldable;
    }

    /**
     * Sets a new isFoldable
     *
     * Indicates that an item can be folded.
     *
     * @param string $isFoldable
     * @return self
     */
    public function setIsFoldable($isFoldable)
    {
        $this->isFoldable = $isFoldable;
        return $this;
    }

    /**
     * Gets as isWeatherResistant
     *
     * Y indicates that the item has been made or is marketed as being resistant to elements of weather, such as rain, wind or cold.
     *
     * @return string
     */
    public function getIsWeatherResistant()
    {
        return $this->isWeatherResistant;
    }

    /**
     * Sets a new isWeatherResistant
     *
     * Y indicates that the item has been made or is marketed as being resistant to elements of weather, such as rain, wind or cold.
     *
     * @param string $isWeatherResistant
     * @return self
     */
    public function setIsWeatherResistant($isWeatherResistant)
    {
        $this->isWeatherResistant = $isWeatherResistant;
        return $this;
    }

    /**
     * Gets as isWaterproof
     *
     * Y indicates that the item has been especially made to be resistant to water, to some degree.
     *
     * @return string
     */
    public function getIsWaterproof()
    {
        return $this->isWaterproof;
    }

    /**
     * Sets a new isWaterproof
     *
     * Y indicates that the item has been especially made to be resistant to water, to some degree.
     *
     * @param string $isWaterproof
     * @return self
     */
    public function setIsWaterproof($isWaterproof)
    {
        $this->isWaterproof = $isWaterproof;
        return $this;
    }

    /**
     * Gets as isPowered
     *
     * Y indicates that an item uses electricity, requiring a power cord or batteries to operate. Useful for items that have non-powered equivalents (e.g. toothbrushes).
     *
     * @return string
     */
    public function getIsPowered()
    {
        return $this->isPowered;
    }

    /**
     * Sets a new isPowered
     *
     * Y indicates that an item uses electricity, requiring a power cord or batteries to operate. Useful for items that have non-powered equivalents (e.g. toothbrushes).
     *
     * @param string $isPowered
     * @return self
     */
    public function setIsPowered($isPowered)
    {
        $this->isPowered = $isPowered;
        return $this;
    }

    /**
     * Gets as cleaningCareAndMaintenance
     *
     * Description of how the item should be cleaned and maintained.
     *
     * @return string
     */
    public function getCleaningCareAndMaintenance()
    {
        return $this->cleaningCareAndMaintenance;
    }

    /**
     * Sets a new cleaningCareAndMaintenance
     *
     * Description of how the item should be cleaned and maintained.
     *
     * @param string $cleaningCareAndMaintenance
     * @return self
     */
    public function setCleaningCareAndMaintenance($cleaningCareAndMaintenance)
    {
        $this->cleaningCareAndMaintenance = $cleaningCareAndMaintenance;
        return $this;
    }

    /**
     * Adds as recommendedUse
     *
     * Further clarification of what the item may be used for. This improves searchability when customers search for general terms like "birthday party" that do not include the names of specific items.
     *
     * @return self
     * @param string $recommendedUse
     */
    public function addToRecommendedUses($recommendedUse)
    {
        $this->recommendedUses[] = $recommendedUse;
        return $this;
    }

    /**
     * isset recommendedUses
     *
     * Further clarification of what the item may be used for. This improves searchability when customers search for general terms like "birthday party" that do not include the names of specific items.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetRecommendedUses($index)
    {
        return isset($this->recommendedUses[$index]);
    }

    /**
     * unset recommendedUses
     *
     * Further clarification of what the item may be used for. This improves searchability when customers search for general terms like "birthday party" that do not include the names of specific items.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetRecommendedUses($index)
    {
        unset($this->recommendedUses[$index]);
    }

    /**
     * Gets as recommendedUses
     *
     * Further clarification of what the item may be used for. This improves searchability when customers search for general terms like "birthday party" that do not include the names of specific items.
     *
     * @return string[]
     */
    public function getRecommendedUses()
    {
        return $this->recommendedUses;
    }

    /**
     * Sets a new recommendedUses
     *
     * Further clarification of what the item may be used for. This improves searchability when customers search for general terms like "birthday party" that do not include the names of specific items.
     *
     * @param string $recommendedUses
     * @return self
     */
    public function setRecommendedUses(array $recommendedUses)
    {
        $this->recommendedUses = $recommendedUses;
        return $this;
    }

    /**
     * Adds as recommendedLocation
     *
     * The primary location recommended for the item's use.
     *
     * @return self
     * @param string $recommendedLocation
     */
    public function addToRecommendedLocations($recommendedLocation)
    {
        $this->recommendedLocations[] = $recommendedLocation;
        return $this;
    }

    /**
     * isset recommendedLocations
     *
     * The primary location recommended for the item's use.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetRecommendedLocations($index)
    {
        return isset($this->recommendedLocations[$index]);
    }

    /**
     * unset recommendedLocations
     *
     * The primary location recommended for the item's use.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetRecommendedLocations($index)
    {
        unset($this->recommendedLocations[$index]);
    }

    /**
     * Gets as recommendedLocations
     *
     * The primary location recommended for the item's use.
     *
     * @return string[]
     */
    public function getRecommendedLocations()
    {
        return $this->recommendedLocations;
    }

    /**
     * Sets a new recommendedLocations
     *
     * The primary location recommended for the item's use.
     *
     * @param string $recommendedLocations
     * @return self
     */
    public function setRecommendedLocations(array $recommendedLocations)
    {
        $this->recommendedLocations = $recommendedLocations;
        return $this;
    }

    /**
     * Adds as swatchImage
     *
     * @param \WalmartDSV\OpticsType\SwatchImagesAType\SwatchImageAType $swatchImage
     *@return self
     */
    public function addToSwatchImages(\WalmartDSV\OpticsType\SwatchImagesAType\SwatchImageAType $swatchImage)
    {
        $this->swatchImages[] = $swatchImage;
        return $this;
    }

    /**
     * isset swatchImages
     *
     * @param int|string $index
     * @return bool
     */
    public function issetSwatchImages($index)
    {
        return isset($this->swatchImages[$index]);
    }

    /**
     * unset swatchImages
     *
     * @param int|string $index
     * @return void
     */
    public function unsetSwatchImages($index)
    {
        unset($this->swatchImages[$index]);
    }

    /**
     * Gets as swatchImages
     *
     * @return \WalmartDSV\OpticsType\SwatchImagesAType\SwatchImageAType[]
     */
    public function getSwatchImages()
    {
        return $this->swatchImages;
    }

    /**
     * Sets a new swatchImages
     *
     * @param \WalmartDSV\OpticsType\SwatchImagesAType\SwatchImageAType[] $swatchImages
     * @return self
     */
    public function setSwatchImages(array $swatchImages)
    {
        $this->swatchImages = $swatchImages;
        return $this;
    }


}

