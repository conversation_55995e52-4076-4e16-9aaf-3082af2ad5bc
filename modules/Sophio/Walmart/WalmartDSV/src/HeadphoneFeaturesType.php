<?php

namespace WalmartDSV;

/**
 * Class representing HeadphoneFeaturesType
 *
 * List of features specific to headphones including the aspects of the design that consumers would look for because they affect the comfort, portability, compatibility, and quality of sound.
 * XSD Type: HeadphoneFeatures
 */
class HeadphoneFeaturesType
{

    /**
     * @var string[] $headphoneFeature
     */
    private $headphoneFeature = [
        
    ];

    /**
     * Adds as headphoneFeature
     *
     * @return self
     * @param string $headphoneFeature
     */
    public function addToHeadphoneFeature($headphoneFeature)
    {
        $this->headphoneFeature[] = $headphoneFeature;
        return $this;
    }

    /**
     * isset headphoneFeature
     *
     * @param int|string $index
     * @return bool
     */
    public function issetHeadphoneFeature($index)
    {
        return isset($this->headphoneFeature[$index]);
    }

    /**
     * unset headphoneFeature
     *
     * @param int|string $index
     * @return void
     */
    public function unsetHeadphoneFeature($index)
    {
        unset($this->headphoneFeature[$index]);
    }

    /**
     * Gets as headphoneFeature
     *
     * @return string[]
     */
    public function getHeadphoneFeature()
    {
        return $this->headphoneFeature;
    }

    /**
     * Sets a new headphoneFeature
     *
     * @param string $headphoneFeature
     * @return self
     */
    public function setHeadphoneFeature(array $headphoneFeature)
    {
        $this->headphoneFeature = $headphoneFeature;
        return $this;
    }


}

