<?php

namespace WalmartDSV;

/**
 * Class representing JacketStyleType
 *
 * Styles specific to coats and jackets.
 * XSD Type: JacketStyle
 */
class JacketStyleType
{

    /**
     * @var string $jacketStyleValue
     */
    private $jacketStyleValue = null;

    /**
     * Gets as jacketStyleValue
     *
     * @return string
     */
    public function getJacketStyleValue()
    {
        return $this->jacketStyleValue;
    }

    /**
     * Sets a new jacketStyleValue
     *
     * @param string $jacketStyleValue
     * @return self
     */
    public function setJacketStyleValue($jacketStyleValue)
    {
        $this->jacketStyleValue = $jacketStyleValue;
        return $this;
    }


}

