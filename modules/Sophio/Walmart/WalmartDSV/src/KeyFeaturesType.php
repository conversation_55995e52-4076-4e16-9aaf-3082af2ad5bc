<?php

namespace WalmartDSV;

/**
 * Class representing KeyFeaturesType
 *
 * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
 * XSD Type: KeyFeatures
 */
class KeyFeaturesType
{

    /**
     * @var string[] $keyFeaturesValue
     */
    private $keyFeaturesValue = [
        
    ];

    /**
     * Adds as keyFeaturesValue
     *
     * @return self
     * @param string $keyFeaturesValue
     */
    public function addToKeyFeaturesValue($keyFeaturesValue)
    {
        $this->keyFeaturesValue[] = $keyFeaturesValue;
        return $this;
    }

    /**
     * isset keyFeaturesValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetKeyFeaturesValue($index)
    {
        return isset($this->keyFeaturesValue[$index]);
    }

    /**
     * unset keyFeaturesValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetKeyFeaturesValue($index)
    {
        unset($this->keyFeaturesValue[$index]);
    }

    /**
     * Gets as keyFeaturesValue
     *
     * @return string[]
     */
    public function getKeyFeaturesValue()
    {
        return $this->keyFeaturesValue;
    }

    /**
     * Sets a new keyFeaturesValue
     *
     * @param string $keyFeaturesValue
     * @return self
     */
    public function setKeyFeaturesValue(array $keyFeaturesValue)
    {
        $this->keyFeaturesValue = $keyFeaturesValue;
        return $this;
    }


}

