<?php

namespace WalmartDSV\FoodAndBeverageType\SwatchImagesAType;

/**
 * Class representing SwatchImageAType
 */
class SwatchImageAType
{

    /**
     * Attribute name corresponding to the swatch.
     *
     * @var string $swatchVariantAttribute
     */
    private $swatchVariantAttribute = null;

    /**
     * URL of the color or pattern swatch image. This is required for products with visual variations and will be shown as a small square on the item page. Recommended resolution is 100 x 100 pixels. Provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension such as, .jpg, .png or .gif.
     *
     * @var string $swatchImageUrl
     */
    private $swatchImageUrl = null;

    /**
     * Gets as swatchVariantAttribute
     *
     * Attribute name corresponding to the swatch.
     *
     * @return string
     */
    public function getSwatchVariantAttribute()
    {
        return $this->swatchVariantAttribute;
    }

    /**
     * Sets a new swatchVariantAttribute
     *
     * Attribute name corresponding to the swatch.
     *
     * @param string $swatchVariantAttribute
     * @return self
     */
    public function setSwatchVariantAttribute($swatchVariantAttribute)
    {
        $this->swatchVariantAttribute = $swatchVariantAttribute;
        return $this;
    }

    /**
     * Gets as swatchImageUrl
     *
     * URL of the color or pattern swatch image. This is required for products with visual variations and will be shown as a small square on the item page. Recommended resolution is 100 x 100 pixels. Provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension such as, .jpg, .png or .gif.
     *
     * @return string
     */
    public function getSwatchImageUrl()
    {
        return $this->swatchImageUrl;
    }

    /**
     * Sets a new swatchImageUrl
     *
     * URL of the color or pattern swatch image. This is required for products with visual variations and will be shown as a small square on the item page. Recommended resolution is 100 x 100 pixels. Provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension such as, .jpg, .png or .gif.
     *
     * @param string $swatchImageUrl
     * @return self
     */
    public function setSwatchImageUrl($swatchImageUrl)
    {
        $this->swatchImageUrl = $swatchImageUrl;
        return $this;
    }


}

