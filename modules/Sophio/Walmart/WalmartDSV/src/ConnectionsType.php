<?php

namespace WalmartDSV;

/**
 * Class representing ConnectionsType
 *
 * The standardized connections provided on the item.
 * XSD Type: Connections
 */
class ConnectionsType
{

    /**
     * @var string[] $connection
     */
    private $connection = [
        
    ];

    /**
     * Adds as connection
     *
     * @return self
     * @param string $connection
     */
    public function addToConnection($connection)
    {
        $this->connection[] = $connection;
        return $this;
    }

    /**
     * isset connection
     *
     * @param int|string $index
     * @return bool
     */
    public function issetConnection($index)
    {
        return isset($this->connection[$index]);
    }

    /**
     * unset connection
     *
     * @param int|string $index
     * @return void
     */
    public function unsetConnection($index)
    {
        unset($this->connection[$index]);
    }

    /**
     * Gets as connection
     *
     * @return string[]
     */
    public function getConnection()
    {
        return $this->connection;
    }

    /**
     * Sets a new connection
     *
     * @param string $connection
     * @return self
     */
    public function setConnection(array $connection)
    {
        $this->connection = $connection;
        return $this;
    }


}

