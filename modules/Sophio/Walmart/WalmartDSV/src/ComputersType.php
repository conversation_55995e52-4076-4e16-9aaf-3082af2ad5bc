<?php

namespace WalmartDSV;

/**
 * Class representing ComputersType
 *
 *
 * XSD Type: Computers
 */
class ComputersType
{

    /**
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @var string[] $additionalVariantAttributeNames
     */
    private $additionalVariantAttributeNames = null;

    /**
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @var string $shortDescription
     */
    private $shortDescription = null;

    /**
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @var string[] $keyFeatures
     */
    private $keyFeatures = null;

    /**
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @var string $brand
     */
    private $brand = null;

    /**
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @var string $manufacturer
     */
    private $manufacturer = null;

    /**
     * MPN uniquely identifies the product to its manufacturer. For many products this will be identical to the model number. Some manufacturers distinguish part number from model number. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @var string $manufacturerPartNumber
     */
    private $manufacturerPartNumber = null;

    /**
     * Model numbers allow manufacturers to keep track of each hardware device and identify or replace the proper part when needed. Model numbers are often found on the bottom, back, or side of a product. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @var string $modelNumber
     */
    private $modelNumber = null;

    /**
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @var int $multipackQuantity
     */
    private $multipackQuantity = null;

    /**
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @var int $countPerPack
     */
    private $countPerPack = null;

    /**
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @var string $count
     */
    private $count = null;

    /**
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @var string $mainImageUrl
     */
    private $mainImageUrl = null;

    /**
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @var string[] $productSecondaryImageURL
     */
    private $productSecondaryImageURL = null;

    /**
     * Color as described by the manufacturer.
     *
     * @var \WalmartDSV\ColorType $color
     */
    private $color = null;

    /**
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @var string[] $colorCategory
     */
    private $colorCategory = null;

    /**
     * Typically measured on the diagonal in inches.
     *
     * @var \WalmartDSV\ComputersType\ScreenSizeAType $screenSize
     */
    private $screenSize = null;

    /**
     * The commonly used resolution category of best fit for the panel. 1080p for a native resolution of 1920 x 1080, 4K for a native resolution of 4096 x 2160, and so on. Or the upper-bound resolution of the video output capability of an item (on a BluRay player or streaming media device for example).
     *
     * @var string $resolution
     */
    private $resolution = null;

    /**
     * The primary technology used for the item's display.
     *
     * @var string $displayTechnology
     */
    private $displayTechnology = null;

    /**
     * The amount of storage on a hard disk for retrieving and storing digital information, typically measured in megabytes, gigabytes, and terabytes.
     *
     * @var \WalmartDSV\ComputersType\HardDriveCapacityAType $hardDriveCapacity
     */
    private $hardDriveCapacity = null;

    /**
     * The amount of RAM preinstalled in the product. Random Access Memory is a type of computer data storage that allows information to be accessed rapidly. RAM is the most common type of memory found in computers, cellphones, and other devices.
     *
     * @var \WalmartDSV\ComputersType\RamMemoryAType $ramMemory
     */
    private $ramMemory = null;

    /**
     * The maximum amount of RAM that the product can accommodate. Random Access Memory is a type of computer data storage that allows information to be accessed rapidly. RAM is the most common type of memory found in computers, cellphones, and other devices.
     *
     * @var \WalmartDSV\ComputersType\MaximumRamSupportedAType $maximumRamSupported
     */
    private $maximumRamSupported = null;

    /**
     * Primarily used to describe various drives, including hard drives. Also applicable to a number of other items that may be designed for internal installation or may be designed for external use (e.g. sound cards, memory card readers, network adapters, etc.).
     *
     * @var string $internalExternal
     */
    private $internalExternal = null;

    /**
     * Operational frequency of the central processing unit.
     *
     * @var \WalmartDSV\ComputersType\ProcessorSpeedAType $processorSpeed
     */
    private $processorSpeed = null;

    /**
     * Commonly used retail name for the central processing unit.
     *
     * @var string[] $processorType
     */
    private $processorType = null;

    /**
     * Descriptive terms for the styles of computer products, often referring to where the computer is located (desk/lap), its physical profile (tablet).
     *
     * @var string $computerStyle
     */
    private $computerStyle = null;

    /**
     * The maximum resolution of the camera that faces the user, provided in megapixels.
     *
     * @var \WalmartDSV\ComputersType\FrontFacingCameraMegapixelsAType $frontFacingCameraMegapixels
     */
    private $frontFacingCameraMegapixels = null;

    /**
     * The maximum resolution of the camera that faces away from the user, provided in megapixels.
     *
     * @var \WalmartDSV\ComputersType\RearCameraMegapixelsAType $rearCameraMegapixels
     */
    private $rearCameraMegapixels = null;

    /**
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @var string $isPrivateLabelOrUnbranded
     */
    private $isPrivateLabelOrUnbranded = null;

    /**
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @var string $isProp65WarningRequired
     */
    private $isProp65WarningRequired = null;

    /**
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @var string $prop65WarningText
     */
    private $prop65WarningText = null;

    /**
     * "Battery or battery containing product" is defined by Company to include any item of Merchandise that is a battery or any component of Merchandise, including reusable packaging intended to stay in use with the item, containing a battery of any chemistry/ type. Mark Y if this definition applies to your product.
     *
     * @var string $hasBatteries
     */
    private $hasBatteries = null;

    /**
     * Please select the Battery Technology Type from the list provided. NOTE: If battery type is lead acid, lead acid (nonspillable), lithium ion, or lithium metal, please ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @var string $batteryTechnologyType
     */
    private $batteryTechnologyType = null;

    /**
     * Y indicates the item comes with a warranty. If an item has a warranty, then enter EITHER the warranty URL or the warranty text in the appropriate field.
     *
     * @var string $hasWarranty
     */
    private $hasWarranty = null;

    /**
     * If you indicated that your item has a warranty, provide either the Warranty URL or Warranty Text. The Warranty URL is the web location of the image, PDF, or link to the manufacturer's warranty page, showing the warranty and its terms, including the duration of the warranty. URLs must begin with http:// or https:// NOTE: Please remember to update the link and/or text of the warranty as the warranty changes. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @var string $warrantyURL
     */
    private $warrantyURL = null;

    /**
     * If you marked Y for "Has Warranty" provide the Warranty URL or Warranty Text (the full text of the warranty terms, including what is covered by the warranty and the duration of the warranty). NOTE: please remember to update the text of your warranty as your warranty changes.
     *
     * @var string $warrantyText
     */
    private $warrantyText = null;

    /**
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @var string $hasStateRestrictions
     */
    private $hasStateRestrictions = null;

    /**
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @var \WalmartDSV\StateRestrictionType[] $stateRestrictions
     */
    private $stateRestrictions = null;

    /**
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @var string $variantGroupId
     */
    private $variantGroupId = null;

    /**
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @var string[] $variantAttributeNames
     */
    private $variantAttributeNames = null;

    /**
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @var string $isPrimaryVariant
     */
    private $isPrimaryVariant = null;

    /**
     * The operating system loaded on the device or upon which the software is designed to operate.
     *
     * @var string[] $operatingSystem
     */
    private $operatingSystem = null;

    /**
     * Number of data transfers per second in Random Access Memory.
     *
     * @var \WalmartDSV\DataRateUnitType $rAMSpeed
     */
    private $rAMSpeed = null;

    /**
     * Does the display have touchscreen capabilities?
     *
     * @var string $hasTouchscreen
     */
    private $hasTouchscreen = null;

    /**
     * The standardized connections provided on the item.
     *
     * @var string[] $connections
     */
    private $connections = null;

    /**
     * The memory card format applicable to the product.
     *
     * @var string[] $memoryCardType
     */
    private $memoryCardType = null;

    /**
     * Technology of the optical disk drive if one be present.
     *
     * @var string $opticalDrive
     */
    private $opticalDrive = null;

    /**
     * Basic graphics processor or graphics card information.
     *
     * @var string $graphicsInformation
     */
    private $graphicsInformation = null;

    /**
     * Commonly used term for the physical size to which the computer conforms.
     *
     * @var string $formFactor
     */
    private $formFactor = null;

    /**
     * Denotes if any portion of the item has a signal booster. This information is necessary for certain FCC requirements and will be displayed on the item page.
     *
     * @var string $hasSignalBooster
     */
    private $hasSignalBooster = null;

    /**
     * Any wireless communications standard used within or by the item.
     *
     * @var string[] $wirelessTechnologies
     */
    private $wirelessTechnologies = null;

    /**
     * Life of the device's battery under ideal conditions (maximum run time).
     *
     * @var \WalmartDSV\ComputersType\BatteryLifeAType $batteryLife
     */
    private $batteryLife = null;

    /**
     * The number of keys on a musical instrument or a computer keyboard.
     *
     * @var float $numberOfKeys
     */
    private $numberOfKeys = null;

    /**
     * Industry standard classification of computer data storage memory (RAM) based on ability to test accuracy of data as passes in and out of memory.
     *
     * @var string $dataIntegrityCheck
     */
    private $dataIntegrityCheck = null;

    /**
     * Is the item designed to be easily moved?
     *
     * @var string $isPortable
     */
    private $isPortable = null;

    /**
     * List notable features of the item.
     *
     * @var string[] $features
     */
    private $features = null;

    /**
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @var string $keywords
     */
    private $keywords = null;

    /**
     * The number of separate frequencies available in a communication device (e.g. baby monitor, walkie talkie).
     *
     * @var string $numberOfChannels
     */
    private $numberOfChannels = null;

    /**
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @var string[] $globalBrandLicense
     */
    private $globalBrandLicense = null;

    /**
     * Used to distinguish between items that have different sets of features.
     *
     * @var string $configuration
     */
    private $configuration = null;

    /**
     * In computer storage, the standard RAID levels comprise a basic set of RAID (redundant array of independent disks) configurations that employ the techniques of striping, mirroring, or parity to create large reliable data stores from multiple general-purpose computer hard disk drives (HDDs).
     *
     * @var string $rAIDlevel
     */
    private $rAIDlevel = null;

    /**
     * @var \WalmartDSV\ComputersType\SwatchImagesAType\SwatchImageAType[] $swatchImages
     */
    private $swatchImages = null;

    /**
     * Adds as additionalVariantAttributeName
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @return self
     * @param string $additionalVariantAttributeName
     */
    public function addToAdditionalVariantAttributeNames($additionalVariantAttributeName)
    {
        $this->additionalVariantAttributeNames[] = $additionalVariantAttributeName;
        return $this;
    }

    /**
     * isset additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAdditionalVariantAttributeNames($index)
    {
        return isset($this->additionalVariantAttributeNames[$index]);
    }

    /**
     * unset additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAdditionalVariantAttributeNames($index)
    {
        unset($this->additionalVariantAttributeNames[$index]);
    }

    /**
     * Gets as additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @return string[]
     */
    public function getAdditionalVariantAttributeNames()
    {
        return $this->additionalVariantAttributeNames;
    }

    /**
     * Sets a new additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param string $additionalVariantAttributeNames
     * @return self
     */
    public function setAdditionalVariantAttributeNames(array $additionalVariantAttributeNames)
    {
        $this->additionalVariantAttributeNames = $additionalVariantAttributeNames;
        return $this;
    }

    /**
     * Gets as shortDescription
     *
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @return string
     */
    public function getShortDescription()
    {
        return $this->shortDescription;
    }

    /**
     * Sets a new shortDescription
     *
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @param string $shortDescription
     * @return self
     */
    public function setShortDescription($shortDescription)
    {
        $this->shortDescription = $shortDescription;
        return $this;
    }

    /**
     * Adds as keyFeaturesValue
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @return self
     * @param string $keyFeaturesValue
     */
    public function addToKeyFeatures($keyFeaturesValue)
    {
        $this->keyFeatures[] = $keyFeaturesValue;
        return $this;
    }

    /**
     * isset keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetKeyFeatures($index)
    {
        return isset($this->keyFeatures[$index]);
    }

    /**
     * unset keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetKeyFeatures($index)
    {
        unset($this->keyFeatures[$index]);
    }

    /**
     * Gets as keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @return string[]
     */
    public function getKeyFeatures()
    {
        return $this->keyFeatures;
    }

    /**
     * Sets a new keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param string $keyFeatures
     * @return self
     */
    public function setKeyFeatures(array $keyFeatures)
    {
        $this->keyFeatures = $keyFeatures;
        return $this;
    }

    /**
     * Gets as brand
     *
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @return string
     */
    public function getBrand()
    {
        return $this->brand;
    }

    /**
     * Sets a new brand
     *
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @param string $brand
     * @return self
     */
    public function setBrand($brand)
    {
        $this->brand = $brand;
        return $this;
    }

    /**
     * Gets as manufacturer
     *
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @return string
     */
    public function getManufacturer()
    {
        return $this->manufacturer;
    }

    /**
     * Sets a new manufacturer
     *
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @param string $manufacturer
     * @return self
     */
    public function setManufacturer($manufacturer)
    {
        $this->manufacturer = $manufacturer;
        return $this;
    }

    /**
     * Gets as manufacturerPartNumber
     *
     * MPN uniquely identifies the product to its manufacturer. For many products this will be identical to the model number. Some manufacturers distinguish part number from model number. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @return string
     */
    public function getManufacturerPartNumber()
    {
        return $this->manufacturerPartNumber;
    }

    /**
     * Sets a new manufacturerPartNumber
     *
     * MPN uniquely identifies the product to its manufacturer. For many products this will be identical to the model number. Some manufacturers distinguish part number from model number. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @param string $manufacturerPartNumber
     * @return self
     */
    public function setManufacturerPartNumber($manufacturerPartNumber)
    {
        $this->manufacturerPartNumber = $manufacturerPartNumber;
        return $this;
    }

    /**
     * Gets as modelNumber
     *
     * Model numbers allow manufacturers to keep track of each hardware device and identify or replace the proper part when needed. Model numbers are often found on the bottom, back, or side of a product. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @return string
     */
    public function getModelNumber()
    {
        return $this->modelNumber;
    }

    /**
     * Sets a new modelNumber
     *
     * Model numbers allow manufacturers to keep track of each hardware device and identify or replace the proper part when needed. Model numbers are often found on the bottom, back, or side of a product. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @param string $modelNumber
     * @return self
     */
    public function setModelNumber($modelNumber)
    {
        $this->modelNumber = $modelNumber;
        return $this;
    }

    /**
     * Gets as multipackQuantity
     *
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @return int
     */
    public function getMultipackQuantity()
    {
        return $this->multipackQuantity;
    }

    /**
     * Sets a new multipackQuantity
     *
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @param int $multipackQuantity
     * @return self
     */
    public function setMultipackQuantity($multipackQuantity)
    {
        $this->multipackQuantity = $multipackQuantity;
        return $this;
    }

    /**
     * Gets as countPerPack
     *
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @return int
     */
    public function getCountPerPack()
    {
        return $this->countPerPack;
    }

    /**
     * Sets a new countPerPack
     *
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @param int $countPerPack
     * @return self
     */
    public function setCountPerPack($countPerPack)
    {
        $this->countPerPack = $countPerPack;
        return $this;
    }

    /**
     * Gets as count
     *
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @return string
     */
    public function getCount()
    {
        return $this->count;
    }

    /**
     * Sets a new count
     *
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @param string $count
     * @return self
     */
    public function setCount($count)
    {
        $this->count = $count;
        return $this;
    }

    /**
     * Gets as mainImageUrl
     *
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return string
     */
    public function getMainImageUrl()
    {
        return $this->mainImageUrl;
    }

    /**
     * Sets a new mainImageUrl
     *
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param string $mainImageUrl
     * @return self
     */
    public function setMainImageUrl($mainImageUrl)
    {
        $this->mainImageUrl = $mainImageUrl;
        return $this;
    }

    /**
     * Adds as productSecondaryImageURLValue
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return self
     * @param string $productSecondaryImageURLValue
     */
    public function addToProductSecondaryImageURL($productSecondaryImageURLValue)
    {
        $this->productSecondaryImageURL[] = $productSecondaryImageURLValue;
        return $this;
    }

    /**
     * isset productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetProductSecondaryImageURL($index)
    {
        return isset($this->productSecondaryImageURL[$index]);
    }

    /**
     * unset productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetProductSecondaryImageURL($index)
    {
        unset($this->productSecondaryImageURL[$index]);
    }

    /**
     * Gets as productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return string[]
     */
    public function getProductSecondaryImageURL()
    {
        return $this->productSecondaryImageURL;
    }

    /**
     * Sets a new productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param string $productSecondaryImageURL
     * @return self
     */
    public function setProductSecondaryImageURL(array $productSecondaryImageURL)
    {
        $this->productSecondaryImageURL = $productSecondaryImageURL;
        return $this;
    }

    /**
     * Gets as color
     *
     * Color as described by the manufacturer.
     *
     * @return \WalmartDSV\ColorType
     */
    public function getColor()
    {
        return $this->color;
    }

    /**
     * Sets a new color
     *
     * Color as described by the manufacturer.
     *
     * @param \WalmartDSV\ColorType $color
     * @return self
     */
    public function setColor(\WalmartDSV\ColorType $color)
    {
        $this->color = $color;
        return $this;
    }

    /**
     * Adds as colorCategoryValue
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @return self
     * @param string $colorCategoryValue
     */
    public function addToColorCategory($colorCategoryValue)
    {
        $this->colorCategory[] = $colorCategoryValue;
        return $this;
    }

    /**
     * isset colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetColorCategory($index)
    {
        return isset($this->colorCategory[$index]);
    }

    /**
     * unset colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetColorCategory($index)
    {
        unset($this->colorCategory[$index]);
    }

    /**
     * Gets as colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @return string[]
     */
    public function getColorCategory()
    {
        return $this->colorCategory;
    }

    /**
     * Sets a new colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @param string $colorCategory
     * @return self
     */
    public function setColorCategory(array $colorCategory)
    {
        $this->colorCategory = $colorCategory;
        return $this;
    }

    /**
     * Gets as screenSize
     *
     * Typically measured on the diagonal in inches.
     *
     * @return \WalmartDSV\ComputersType\ScreenSizeAType
     */
    public function getScreenSize()
    {
        return $this->screenSize;
    }

    /**
     * Sets a new screenSize
     *
     * Typically measured on the diagonal in inches.
     *
     * @param \WalmartDSV\ComputersType\ScreenSizeAType $screenSize
     * @return self
     */
    public function setScreenSize(\WalmartDSV\ComputersType\ScreenSizeAType $screenSize)
    {
        $this->screenSize = $screenSize;
        return $this;
    }

    /**
     * Gets as resolution
     *
     * The commonly used resolution category of best fit for the panel. 1080p for a native resolution of 1920 x 1080, 4K for a native resolution of 4096 x 2160, and so on. Or the upper-bound resolution of the video output capability of an item (on a BluRay player or streaming media device for example).
     *
     * @return string
     */
    public function getResolution()
    {
        return $this->resolution;
    }

    /**
     * Sets a new resolution
     *
     * The commonly used resolution category of best fit for the panel. 1080p for a native resolution of 1920 x 1080, 4K for a native resolution of 4096 x 2160, and so on. Or the upper-bound resolution of the video output capability of an item (on a BluRay player or streaming media device for example).
     *
     * @param string $resolution
     * @return self
     */
    public function setResolution($resolution)
    {
        $this->resolution = $resolution;
        return $this;
    }

    /**
     * Gets as displayTechnology
     *
     * The primary technology used for the item's display.
     *
     * @return string
     */
    public function getDisplayTechnology()
    {
        return $this->displayTechnology;
    }

    /**
     * Sets a new displayTechnology
     *
     * The primary technology used for the item's display.
     *
     * @param string $displayTechnology
     * @return self
     */
    public function setDisplayTechnology($displayTechnology)
    {
        $this->displayTechnology = $displayTechnology;
        return $this;
    }

    /**
     * Gets as hardDriveCapacity
     *
     * The amount of storage on a hard disk for retrieving and storing digital information, typically measured in megabytes, gigabytes, and terabytes.
     *
     * @return \WalmartDSV\ComputersType\HardDriveCapacityAType
     */
    public function getHardDriveCapacity()
    {
        return $this->hardDriveCapacity;
    }

    /**
     * Sets a new hardDriveCapacity
     *
     * The amount of storage on a hard disk for retrieving and storing digital information, typically measured in megabytes, gigabytes, and terabytes.
     *
     * @param \WalmartDSV\ComputersType\HardDriveCapacityAType $hardDriveCapacity
     * @return self
     */
    public function setHardDriveCapacity(\WalmartDSV\ComputersType\HardDriveCapacityAType $hardDriveCapacity)
    {
        $this->hardDriveCapacity = $hardDriveCapacity;
        return $this;
    }

    /**
     * Gets as ramMemory
     *
     * The amount of RAM preinstalled in the product. Random Access Memory is a type of computer data storage that allows information to be accessed rapidly. RAM is the most common type of memory found in computers, cellphones, and other devices.
     *
     * @return \WalmartDSV\ComputersType\RamMemoryAType
     */
    public function getRamMemory()
    {
        return $this->ramMemory;
    }

    /**
     * Sets a new ramMemory
     *
     * The amount of RAM preinstalled in the product. Random Access Memory is a type of computer data storage that allows information to be accessed rapidly. RAM is the most common type of memory found in computers, cellphones, and other devices.
     *
     * @param \WalmartDSV\ComputersType\RamMemoryAType $ramMemory
     * @return self
     */
    public function setRamMemory(\WalmartDSV\ComputersType\RamMemoryAType $ramMemory)
    {
        $this->ramMemory = $ramMemory;
        return $this;
    }

    /**
     * Gets as maximumRamSupported
     *
     * The maximum amount of RAM that the product can accommodate. Random Access Memory is a type of computer data storage that allows information to be accessed rapidly. RAM is the most common type of memory found in computers, cellphones, and other devices.
     *
     * @return \WalmartDSV\ComputersType\MaximumRamSupportedAType
     */
    public function getMaximumRamSupported()
    {
        return $this->maximumRamSupported;
    }

    /**
     * Sets a new maximumRamSupported
     *
     * The maximum amount of RAM that the product can accommodate. Random Access Memory is a type of computer data storage that allows information to be accessed rapidly. RAM is the most common type of memory found in computers, cellphones, and other devices.
     *
     * @param \WalmartDSV\ComputersType\MaximumRamSupportedAType $maximumRamSupported
     * @return self
     */
    public function setMaximumRamSupported(\WalmartDSV\ComputersType\MaximumRamSupportedAType $maximumRamSupported)
    {
        $this->maximumRamSupported = $maximumRamSupported;
        return $this;
    }

    /**
     * Gets as internalExternal
     *
     * Primarily used to describe various drives, including hard drives. Also applicable to a number of other items that may be designed for internal installation or may be designed for external use (e.g. sound cards, memory card readers, network adapters, etc.).
     *
     * @return string
     */
    public function getInternalExternal()
    {
        return $this->internalExternal;
    }

    /**
     * Sets a new internalExternal
     *
     * Primarily used to describe various drives, including hard drives. Also applicable to a number of other items that may be designed for internal installation or may be designed for external use (e.g. sound cards, memory card readers, network adapters, etc.).
     *
     * @param string $internalExternal
     * @return self
     */
    public function setInternalExternal($internalExternal)
    {
        $this->internalExternal = $internalExternal;
        return $this;
    }

    /**
     * Gets as processorSpeed
     *
     * Operational frequency of the central processing unit.
     *
     * @return \WalmartDSV\ComputersType\ProcessorSpeedAType
     */
    public function getProcessorSpeed()
    {
        return $this->processorSpeed;
    }

    /**
     * Sets a new processorSpeed
     *
     * Operational frequency of the central processing unit.
     *
     * @param \WalmartDSV\ComputersType\ProcessorSpeedAType $processorSpeed
     * @return self
     */
    public function setProcessorSpeed(\WalmartDSV\ComputersType\ProcessorSpeedAType $processorSpeed)
    {
        $this->processorSpeed = $processorSpeed;
        return $this;
    }

    /**
     * Adds as processorTypeValue
     *
     * Commonly used retail name for the central processing unit.
     *
     * @return self
     * @param string $processorTypeValue
     */
    public function addToProcessorType($processorTypeValue)
    {
        $this->processorType[] = $processorTypeValue;
        return $this;
    }

    /**
     * isset processorType
     *
     * Commonly used retail name for the central processing unit.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetProcessorType($index)
    {
        return isset($this->processorType[$index]);
    }

    /**
     * unset processorType
     *
     * Commonly used retail name for the central processing unit.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetProcessorType($index)
    {
        unset($this->processorType[$index]);
    }

    /**
     * Gets as processorType
     *
     * Commonly used retail name for the central processing unit.
     *
     * @return string[]
     */
    public function getProcessorType()
    {
        return $this->processorType;
    }

    /**
     * Sets a new processorType
     *
     * Commonly used retail name for the central processing unit.
     *
     * @param string $processorType
     * @return self
     */
    public function setProcessorType(array $processorType)
    {
        $this->processorType = $processorType;
        return $this;
    }

    /**
     * Gets as computerStyle
     *
     * Descriptive terms for the styles of computer products, often referring to where the computer is located (desk/lap), its physical profile (tablet).
     *
     * @return string
     */
    public function getComputerStyle()
    {
        return $this->computerStyle;
    }

    /**
     * Sets a new computerStyle
     *
     * Descriptive terms for the styles of computer products, often referring to where the computer is located (desk/lap), its physical profile (tablet).
     *
     * @param string $computerStyle
     * @return self
     */
    public function setComputerStyle($computerStyle)
    {
        $this->computerStyle = $computerStyle;
        return $this;
    }

    /**
     * Gets as frontFacingCameraMegapixels
     *
     * The maximum resolution of the camera that faces the user, provided in megapixels.
     *
     * @return \WalmartDSV\ComputersType\FrontFacingCameraMegapixelsAType
     */
    public function getFrontFacingCameraMegapixels()
    {
        return $this->frontFacingCameraMegapixels;
    }

    /**
     * Sets a new frontFacingCameraMegapixels
     *
     * The maximum resolution of the camera that faces the user, provided in megapixels.
     *
     * @param \WalmartDSV\ComputersType\FrontFacingCameraMegapixelsAType $frontFacingCameraMegapixels
     * @return self
     */
    public function setFrontFacingCameraMegapixels(\WalmartDSV\ComputersType\FrontFacingCameraMegapixelsAType $frontFacingCameraMegapixels)
    {
        $this->frontFacingCameraMegapixels = $frontFacingCameraMegapixels;
        return $this;
    }

    /**
     * Gets as rearCameraMegapixels
     *
     * The maximum resolution of the camera that faces away from the user, provided in megapixels.
     *
     * @return \WalmartDSV\ComputersType\RearCameraMegapixelsAType
     */
    public function getRearCameraMegapixels()
    {
        return $this->rearCameraMegapixels;
    }

    /**
     * Sets a new rearCameraMegapixels
     *
     * The maximum resolution of the camera that faces away from the user, provided in megapixels.
     *
     * @param \WalmartDSV\ComputersType\RearCameraMegapixelsAType $rearCameraMegapixels
     * @return self
     */
    public function setRearCameraMegapixels(\WalmartDSV\ComputersType\RearCameraMegapixelsAType $rearCameraMegapixels)
    {
        $this->rearCameraMegapixels = $rearCameraMegapixels;
        return $this;
    }

    /**
     * Gets as isPrivateLabelOrUnbranded
     *
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @return string
     */
    public function getIsPrivateLabelOrUnbranded()
    {
        return $this->isPrivateLabelOrUnbranded;
    }

    /**
     * Sets a new isPrivateLabelOrUnbranded
     *
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @param string $isPrivateLabelOrUnbranded
     * @return self
     */
    public function setIsPrivateLabelOrUnbranded($isPrivateLabelOrUnbranded)
    {
        $this->isPrivateLabelOrUnbranded = $isPrivateLabelOrUnbranded;
        return $this;
    }

    /**
     * Gets as isProp65WarningRequired
     *
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @return string
     */
    public function getIsProp65WarningRequired()
    {
        return $this->isProp65WarningRequired;
    }

    /**
     * Sets a new isProp65WarningRequired
     *
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @param string $isProp65WarningRequired
     * @return self
     */
    public function setIsProp65WarningRequired($isProp65WarningRequired)
    {
        $this->isProp65WarningRequired = $isProp65WarningRequired;
        return $this;
    }

    /**
     * Gets as prop65WarningText
     *
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @return string
     */
    public function getProp65WarningText()
    {
        return $this->prop65WarningText;
    }

    /**
     * Sets a new prop65WarningText
     *
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @param string $prop65WarningText
     * @return self
     */
    public function setProp65WarningText($prop65WarningText)
    {
        $this->prop65WarningText = $prop65WarningText;
        return $this;
    }

    /**
     * Gets as hasBatteries
     *
     * "Battery or battery containing product" is defined by Company to include any item of Merchandise that is a battery or any component of Merchandise, including reusable packaging intended to stay in use with the item, containing a battery of any chemistry/ type. Mark Y if this definition applies to your product.
     *
     * @return string
     */
    public function getHasBatteries()
    {
        return $this->hasBatteries;
    }

    /**
     * Sets a new hasBatteries
     *
     * "Battery or battery containing product" is defined by Company to include any item of Merchandise that is a battery or any component of Merchandise, including reusable packaging intended to stay in use with the item, containing a battery of any chemistry/ type. Mark Y if this definition applies to your product.
     *
     * @param string $hasBatteries
     * @return self
     */
    public function setHasBatteries($hasBatteries)
    {
        $this->hasBatteries = $hasBatteries;
        return $this;
    }

    /**
     * Gets as batteryTechnologyType
     *
     * Please select the Battery Technology Type from the list provided. NOTE: If battery type is lead acid, lead acid (nonspillable), lithium ion, or lithium metal, please ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @return string
     */
    public function getBatteryTechnologyType()
    {
        return $this->batteryTechnologyType;
    }

    /**
     * Sets a new batteryTechnologyType
     *
     * Please select the Battery Technology Type from the list provided. NOTE: If battery type is lead acid, lead acid (nonspillable), lithium ion, or lithium metal, please ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @param string $batteryTechnologyType
     * @return self
     */
    public function setBatteryTechnologyType($batteryTechnologyType)
    {
        $this->batteryTechnologyType = $batteryTechnologyType;
        return $this;
    }

    /**
     * Gets as hasWarranty
     *
     * Y indicates the item comes with a warranty. If an item has a warranty, then enter EITHER the warranty URL or the warranty text in the appropriate field.
     *
     * @return string
     */
    public function getHasWarranty()
    {
        return $this->hasWarranty;
    }

    /**
     * Sets a new hasWarranty
     *
     * Y indicates the item comes with a warranty. If an item has a warranty, then enter EITHER the warranty URL or the warranty text in the appropriate field.
     *
     * @param string $hasWarranty
     * @return self
     */
    public function setHasWarranty($hasWarranty)
    {
        $this->hasWarranty = $hasWarranty;
        return $this;
    }

    /**
     * Gets as warrantyURL
     *
     * If you indicated that your item has a warranty, provide either the Warranty URL or Warranty Text. The Warranty URL is the web location of the image, PDF, or link to the manufacturer's warranty page, showing the warranty and its terms, including the duration of the warranty. URLs must begin with http:// or https:// NOTE: Please remember to update the link and/or text of the warranty as the warranty changes. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @return string
     */
    public function getWarrantyURL()
    {
        return $this->warrantyURL;
    }

    /**
     * Sets a new warrantyURL
     *
     * If you indicated that your item has a warranty, provide either the Warranty URL or Warranty Text. The Warranty URL is the web location of the image, PDF, or link to the manufacturer's warranty page, showing the warranty and its terms, including the duration of the warranty. URLs must begin with http:// or https:// NOTE: Please remember to update the link and/or text of the warranty as the warranty changes. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @param string $warrantyURL
     * @return self
     */
    public function setWarrantyURL($warrantyURL)
    {
        $this->warrantyURL = $warrantyURL;
        return $this;
    }

    /**
     * Gets as warrantyText
     *
     * If you marked Y for "Has Warranty" provide the Warranty URL or Warranty Text (the full text of the warranty terms, including what is covered by the warranty and the duration of the warranty). NOTE: please remember to update the text of your warranty as your warranty changes.
     *
     * @return string
     */
    public function getWarrantyText()
    {
        return $this->warrantyText;
    }

    /**
     * Sets a new warrantyText
     *
     * If you marked Y for "Has Warranty" provide the Warranty URL or Warranty Text (the full text of the warranty terms, including what is covered by the warranty and the duration of the warranty). NOTE: please remember to update the text of your warranty as your warranty changes.
     *
     * @param string $warrantyText
     * @return self
     */
    public function setWarrantyText($warrantyText)
    {
        $this->warrantyText = $warrantyText;
        return $this;
    }

    /**
     * Gets as hasStateRestrictions
     *
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @return string
     */
    public function getHasStateRestrictions()
    {
        return $this->hasStateRestrictions;
    }

    /**
     * Sets a new hasStateRestrictions
     *
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @param string $hasStateRestrictions
     * @return self
     */
    public function setHasStateRestrictions($hasStateRestrictions)
    {
        $this->hasStateRestrictions = $hasStateRestrictions;
        return $this;
    }

    /**
     * Adds as stateRestriction
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param \WalmartDSV\StateRestrictionType $stateRestriction
     *@return self
     */
    public function addToStateRestrictions(\WalmartDSV\StateRestrictionType $stateRestriction)
    {
        $this->stateRestrictions[] = $stateRestriction;
        return $this;
    }

    /**
     * isset stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetStateRestrictions($index)
    {
        return isset($this->stateRestrictions[$index]);
    }

    /**
     * unset stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetStateRestrictions($index)
    {
        unset($this->stateRestrictions[$index]);
    }

    /**
     * Gets as stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @return \WalmartDSV\StateRestrictionType[]
     */
    public function getStateRestrictions()
    {
        return $this->stateRestrictions;
    }

    /**
     * Sets a new stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param \WalmartDSV\StateRestrictionType[] $stateRestrictions
     * @return self
     */
    public function setStateRestrictions(array $stateRestrictions)
    {
        $this->stateRestrictions = $stateRestrictions;
        return $this;
    }

    /**
     * Gets as variantGroupId
     *
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @return string
     */
    public function getVariantGroupId()
    {
        return $this->variantGroupId;
    }

    /**
     * Sets a new variantGroupId
     *
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @param string $variantGroupId
     * @return self
     */
    public function setVariantGroupId($variantGroupId)
    {
        $this->variantGroupId = $variantGroupId;
        return $this;
    }

    /**
     * Adds as variantAttributeName
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @return self
     * @param string $variantAttributeName
     */
    public function addToVariantAttributeNames($variantAttributeName)
    {
        $this->variantAttributeNames[] = $variantAttributeName;
        return $this;
    }

    /**
     * isset variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetVariantAttributeNames($index)
    {
        return isset($this->variantAttributeNames[$index]);
    }

    /**
     * unset variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetVariantAttributeNames($index)
    {
        unset($this->variantAttributeNames[$index]);
    }

    /**
     * Gets as variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @return string[]
     */
    public function getVariantAttributeNames()
    {
        return $this->variantAttributeNames;
    }

    /**
     * Sets a new variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param string $variantAttributeNames
     * @return self
     */
    public function setVariantAttributeNames(array $variantAttributeNames)
    {
        $this->variantAttributeNames = $variantAttributeNames;
        return $this;
    }

    /**
     * Gets as isPrimaryVariant
     *
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @return string
     */
    public function getIsPrimaryVariant()
    {
        return $this->isPrimaryVariant;
    }

    /**
     * Sets a new isPrimaryVariant
     *
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @param string $isPrimaryVariant
     * @return self
     */
    public function setIsPrimaryVariant($isPrimaryVariant)
    {
        $this->isPrimaryVariant = $isPrimaryVariant;
        return $this;
    }

    /**
     * Adds as operatingSystemValue
     *
     * The operating system loaded on the device or upon which the software is designed to operate.
     *
     * @return self
     * @param string $operatingSystemValue
     */
    public function addToOperatingSystem($operatingSystemValue)
    {
        $this->operatingSystem[] = $operatingSystemValue;
        return $this;
    }

    /**
     * isset operatingSystem
     *
     * The operating system loaded on the device or upon which the software is designed to operate.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetOperatingSystem($index)
    {
        return isset($this->operatingSystem[$index]);
    }

    /**
     * unset operatingSystem
     *
     * The operating system loaded on the device or upon which the software is designed to operate.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetOperatingSystem($index)
    {
        unset($this->operatingSystem[$index]);
    }

    /**
     * Gets as operatingSystem
     *
     * The operating system loaded on the device or upon which the software is designed to operate.
     *
     * @return string[]
     */
    public function getOperatingSystem()
    {
        return $this->operatingSystem;
    }

    /**
     * Sets a new operatingSystem
     *
     * The operating system loaded on the device or upon which the software is designed to operate.
     *
     * @param string $operatingSystem
     * @return self
     */
    public function setOperatingSystem(array $operatingSystem)
    {
        $this->operatingSystem = $operatingSystem;
        return $this;
    }

    /**
     * Gets as rAMSpeed
     *
     * Number of data transfers per second in Random Access Memory.
     *
     * @return \WalmartDSV\DataRateUnitType
     */
    public function getRAMSpeed()
    {
        return $this->rAMSpeed;
    }

    /**
     * Sets a new rAMSpeed
     *
     * Number of data transfers per second in Random Access Memory.
     *
     * @param \WalmartDSV\DataRateUnitType $rAMSpeed
     * @return self
     */
    public function setRAMSpeed(\WalmartDSV\DataRateUnitType $rAMSpeed)
    {
        $this->rAMSpeed = $rAMSpeed;
        return $this;
    }

    /**
     * Gets as hasTouchscreen
     *
     * Does the display have touchscreen capabilities?
     *
     * @return string
     */
    public function getHasTouchscreen()
    {
        return $this->hasTouchscreen;
    }

    /**
     * Sets a new hasTouchscreen
     *
     * Does the display have touchscreen capabilities?
     *
     * @param string $hasTouchscreen
     * @return self
     */
    public function setHasTouchscreen($hasTouchscreen)
    {
        $this->hasTouchscreen = $hasTouchscreen;
        return $this;
    }

    /**
     * Adds as connection
     *
     * The standardized connections provided on the item.
     *
     * @return self
     * @param string $connection
     */
    public function addToConnections($connection)
    {
        $this->connections[] = $connection;
        return $this;
    }

    /**
     * isset connections
     *
     * The standardized connections provided on the item.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetConnections($index)
    {
        return isset($this->connections[$index]);
    }

    /**
     * unset connections
     *
     * The standardized connections provided on the item.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetConnections($index)
    {
        unset($this->connections[$index]);
    }

    /**
     * Gets as connections
     *
     * The standardized connections provided on the item.
     *
     * @return string[]
     */
    public function getConnections()
    {
        return $this->connections;
    }

    /**
     * Sets a new connections
     *
     * The standardized connections provided on the item.
     *
     * @param string $connections
     * @return self
     */
    public function setConnections(array $connections)
    {
        $this->connections = $connections;
        return $this;
    }

    /**
     * Adds as memoryCardTypeValue
     *
     * The memory card format applicable to the product.
     *
     * @return self
     * @param string $memoryCardTypeValue
     */
    public function addToMemoryCardType($memoryCardTypeValue)
    {
        $this->memoryCardType[] = $memoryCardTypeValue;
        return $this;
    }

    /**
     * isset memoryCardType
     *
     * The memory card format applicable to the product.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetMemoryCardType($index)
    {
        return isset($this->memoryCardType[$index]);
    }

    /**
     * unset memoryCardType
     *
     * The memory card format applicable to the product.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetMemoryCardType($index)
    {
        unset($this->memoryCardType[$index]);
    }

    /**
     * Gets as memoryCardType
     *
     * The memory card format applicable to the product.
     *
     * @return string[]
     */
    public function getMemoryCardType()
    {
        return $this->memoryCardType;
    }

    /**
     * Sets a new memoryCardType
     *
     * The memory card format applicable to the product.
     *
     * @param string $memoryCardType
     * @return self
     */
    public function setMemoryCardType(array $memoryCardType)
    {
        $this->memoryCardType = $memoryCardType;
        return $this;
    }

    /**
     * Gets as opticalDrive
     *
     * Technology of the optical disk drive if one be present.
     *
     * @return string
     */
    public function getOpticalDrive()
    {
        return $this->opticalDrive;
    }

    /**
     * Sets a new opticalDrive
     *
     * Technology of the optical disk drive if one be present.
     *
     * @param string $opticalDrive
     * @return self
     */
    public function setOpticalDrive($opticalDrive)
    {
        $this->opticalDrive = $opticalDrive;
        return $this;
    }

    /**
     * Gets as graphicsInformation
     *
     * Basic graphics processor or graphics card information.
     *
     * @return string
     */
    public function getGraphicsInformation()
    {
        return $this->graphicsInformation;
    }

    /**
     * Sets a new graphicsInformation
     *
     * Basic graphics processor or graphics card information.
     *
     * @param string $graphicsInformation
     * @return self
     */
    public function setGraphicsInformation($graphicsInformation)
    {
        $this->graphicsInformation = $graphicsInformation;
        return $this;
    }

    /**
     * Gets as formFactor
     *
     * Commonly used term for the physical size to which the computer conforms.
     *
     * @return string
     */
    public function getFormFactor()
    {
        return $this->formFactor;
    }

    /**
     * Sets a new formFactor
     *
     * Commonly used term for the physical size to which the computer conforms.
     *
     * @param string $formFactor
     * @return self
     */
    public function setFormFactor($formFactor)
    {
        $this->formFactor = $formFactor;
        return $this;
    }

    /**
     * Gets as hasSignalBooster
     *
     * Denotes if any portion of the item has a signal booster. This information is necessary for certain FCC requirements and will be displayed on the item page.
     *
     * @return string
     */
    public function getHasSignalBooster()
    {
        return $this->hasSignalBooster;
    }

    /**
     * Sets a new hasSignalBooster
     *
     * Denotes if any portion of the item has a signal booster. This information is necessary for certain FCC requirements and will be displayed on the item page.
     *
     * @param string $hasSignalBooster
     * @return self
     */
    public function setHasSignalBooster($hasSignalBooster)
    {
        $this->hasSignalBooster = $hasSignalBooster;
        return $this;
    }

    /**
     * Adds as wirelessTechnology
     *
     * Any wireless communications standard used within or by the item.
     *
     * @return self
     * @param string $wirelessTechnology
     */
    public function addToWirelessTechnologies($wirelessTechnology)
    {
        $this->wirelessTechnologies[] = $wirelessTechnology;
        return $this;
    }

    /**
     * isset wirelessTechnologies
     *
     * Any wireless communications standard used within or by the item.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetWirelessTechnologies($index)
    {
        return isset($this->wirelessTechnologies[$index]);
    }

    /**
     * unset wirelessTechnologies
     *
     * Any wireless communications standard used within or by the item.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetWirelessTechnologies($index)
    {
        unset($this->wirelessTechnologies[$index]);
    }

    /**
     * Gets as wirelessTechnologies
     *
     * Any wireless communications standard used within or by the item.
     *
     * @return string[]
     */
    public function getWirelessTechnologies()
    {
        return $this->wirelessTechnologies;
    }

    /**
     * Sets a new wirelessTechnologies
     *
     * Any wireless communications standard used within or by the item.
     *
     * @param string $wirelessTechnologies
     * @return self
     */
    public function setWirelessTechnologies(array $wirelessTechnologies)
    {
        $this->wirelessTechnologies = $wirelessTechnologies;
        return $this;
    }

    /**
     * Gets as batteryLife
     *
     * Life of the device's battery under ideal conditions (maximum run time).
     *
     * @return \WalmartDSV\ComputersType\BatteryLifeAType
     */
    public function getBatteryLife()
    {
        return $this->batteryLife;
    }

    /**
     * Sets a new batteryLife
     *
     * Life of the device's battery under ideal conditions (maximum run time).
     *
     * @param \WalmartDSV\ComputersType\BatteryLifeAType $batteryLife
     * @return self
     */
    public function setBatteryLife(\WalmartDSV\ComputersType\BatteryLifeAType $batteryLife)
    {
        $this->batteryLife = $batteryLife;
        return $this;
    }

    /**
     * Gets as numberOfKeys
     *
     * The number of keys on a musical instrument or a computer keyboard.
     *
     * @return float
     */
    public function getNumberOfKeys()
    {
        return $this->numberOfKeys;
    }

    /**
     * Sets a new numberOfKeys
     *
     * The number of keys on a musical instrument or a computer keyboard.
     *
     * @param float $numberOfKeys
     * @return self
     */
    public function setNumberOfKeys($numberOfKeys)
    {
        $this->numberOfKeys = $numberOfKeys;
        return $this;
    }

    /**
     * Gets as dataIntegrityCheck
     *
     * Industry standard classification of computer data storage memory (RAM) based on ability to test accuracy of data as passes in and out of memory.
     *
     * @return string
     */
    public function getDataIntegrityCheck()
    {
        return $this->dataIntegrityCheck;
    }

    /**
     * Sets a new dataIntegrityCheck
     *
     * Industry standard classification of computer data storage memory (RAM) based on ability to test accuracy of data as passes in and out of memory.
     *
     * @param string $dataIntegrityCheck
     * @return self
     */
    public function setDataIntegrityCheck($dataIntegrityCheck)
    {
        $this->dataIntegrityCheck = $dataIntegrityCheck;
        return $this;
    }

    /**
     * Gets as isPortable
     *
     * Is the item designed to be easily moved?
     *
     * @return string
     */
    public function getIsPortable()
    {
        return $this->isPortable;
    }

    /**
     * Sets a new isPortable
     *
     * Is the item designed to be easily moved?
     *
     * @param string $isPortable
     * @return self
     */
    public function setIsPortable($isPortable)
    {
        $this->isPortable = $isPortable;
        return $this;
    }

    /**
     * Adds as feature
     *
     * List notable features of the item.
     *
     * @return self
     * @param string $feature
     */
    public function addToFeatures($feature)
    {
        $this->features[] = $feature;
        return $this;
    }

    /**
     * isset features
     *
     * List notable features of the item.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFeatures($index)
    {
        return isset($this->features[$index]);
    }

    /**
     * unset features
     *
     * List notable features of the item.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFeatures($index)
    {
        unset($this->features[$index]);
    }

    /**
     * Gets as features
     *
     * List notable features of the item.
     *
     * @return string[]
     */
    public function getFeatures()
    {
        return $this->features;
    }

    /**
     * Sets a new features
     *
     * List notable features of the item.
     *
     * @param string $features
     * @return self
     */
    public function setFeatures(array $features)
    {
        $this->features = $features;
        return $this;
    }

    /**
     * Gets as keywords
     *
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @return string
     */
    public function getKeywords()
    {
        return $this->keywords;
    }

    /**
     * Sets a new keywords
     *
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @param string $keywords
     * @return self
     */
    public function setKeywords($keywords)
    {
        $this->keywords = $keywords;
        return $this;
    }

    /**
     * Gets as numberOfChannels
     *
     * The number of separate frequencies available in a communication device (e.g. baby monitor, walkie talkie).
     *
     * @return string
     */
    public function getNumberOfChannels()
    {
        return $this->numberOfChannels;
    }

    /**
     * Sets a new numberOfChannels
     *
     * The number of separate frequencies available in a communication device (e.g. baby monitor, walkie talkie).
     *
     * @param string $numberOfChannels
     * @return self
     */
    public function setNumberOfChannels($numberOfChannels)
    {
        $this->numberOfChannels = $numberOfChannels;
        return $this;
    }

    /**
     * Adds as globalBrandLicenseValue
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @return self
     * @param string $globalBrandLicenseValue
     */
    public function addToGlobalBrandLicense($globalBrandLicenseValue)
    {
        $this->globalBrandLicense[] = $globalBrandLicenseValue;
        return $this;
    }

    /**
     * isset globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @param int|string $index
     * @return bool
     */
    public function issetGlobalBrandLicense($index)
    {
        return isset($this->globalBrandLicense[$index]);
    }

    /**
     * unset globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @param int|string $index
     * @return void
     */
    public function unsetGlobalBrandLicense($index)
    {
        unset($this->globalBrandLicense[$index]);
    }

    /**
     * Gets as globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @return string[]
     */
    public function getGlobalBrandLicense()
    {
        return $this->globalBrandLicense;
    }

    /**
     * Sets a new globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @param string $globalBrandLicense
     * @return self
     */
    public function setGlobalBrandLicense(array $globalBrandLicense)
    {
        $this->globalBrandLicense = $globalBrandLicense;
        return $this;
    }

    /**
     * Gets as configuration
     *
     * Used to distinguish between items that have different sets of features.
     *
     * @return string
     */
    public function getConfiguration()
    {
        return $this->configuration;
    }

    /**
     * Sets a new configuration
     *
     * Used to distinguish between items that have different sets of features.
     *
     * @param string $configuration
     * @return self
     */
    public function setConfiguration($configuration)
    {
        $this->configuration = $configuration;
        return $this;
    }

    /**
     * Gets as rAIDlevel
     *
     * In computer storage, the standard RAID levels comprise a basic set of RAID (redundant array of independent disks) configurations that employ the techniques of striping, mirroring, or parity to create large reliable data stores from multiple general-purpose computer hard disk drives (HDDs).
     *
     * @return string
     */
    public function getRAIDlevel()
    {
        return $this->rAIDlevel;
    }

    /**
     * Sets a new rAIDlevel
     *
     * In computer storage, the standard RAID levels comprise a basic set of RAID (redundant array of independent disks) configurations that employ the techniques of striping, mirroring, or parity to create large reliable data stores from multiple general-purpose computer hard disk drives (HDDs).
     *
     * @param string $rAIDlevel
     * @return self
     */
    public function setRAIDlevel($rAIDlevel)
    {
        $this->rAIDlevel = $rAIDlevel;
        return $this;
    }

    /**
     * Adds as swatchImage
     *
     * @param \WalmartDSV\ComputersType\SwatchImagesAType\SwatchImageAType $swatchImage
     *@return self
     */
    public function addToSwatchImages(\WalmartDSV\ComputersType\SwatchImagesAType\SwatchImageAType $swatchImage)
    {
        $this->swatchImages[] = $swatchImage;
        return $this;
    }

    /**
     * isset swatchImages
     *
     * @param int|string $index
     * @return bool
     */
    public function issetSwatchImages($index)
    {
        return isset($this->swatchImages[$index]);
    }

    /**
     * unset swatchImages
     *
     * @param int|string $index
     * @return void
     */
    public function unsetSwatchImages($index)
    {
        unset($this->swatchImages[$index]);
    }

    /**
     * Gets as swatchImages
     *
     * @return \WalmartDSV\ComputersType\SwatchImagesAType\SwatchImageAType[]
     */
    public function getSwatchImages()
    {
        return $this->swatchImages;
    }

    /**
     * Sets a new swatchImages
     *
     * @param \WalmartDSV\ComputersType\SwatchImagesAType\SwatchImageAType[] $swatchImages
     * @return self
     */
    public function setSwatchImages(array $swatchImages)
    {
        $this->swatchImages = $swatchImages;
        return $this;
    }


}

