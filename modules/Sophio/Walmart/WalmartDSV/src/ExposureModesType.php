<?php

namespace WalmartDSV;

/**
 * Class representing ExposureModesType
 *
 * Available settings to control shutter speed and lens aperture.
 * XSD Type: ExposureModes
 */
class ExposureModesType
{

    /**
     * @var string[] $exposureMode
     */
    private $exposureMode = [
        
    ];

    /**
     * Adds as exposureMode
     *
     * @return self
     * @param string $exposureMode
     */
    public function addToExposureMode($exposureMode)
    {
        $this->exposureMode[] = $exposureMode;
        return $this;
    }

    /**
     * isset exposureMode
     *
     * @param int|string $index
     * @return bool
     */
    public function issetExposureMode($index)
    {
        return isset($this->exposureMode[$index]);
    }

    /**
     * unset exposureMode
     *
     * @param int|string $index
     * @return void
     */
    public function unsetExposureMode($index)
    {
        unset($this->exposureMode[$index]);
    }

    /**
     * Gets as exposureMode
     *
     * @return string[]
     */
    public function getExposureMode()
    {
        return $this->exposureMode;
    }

    /**
     * Sets a new exposureMode
     *
     * @param string $exposureMode
     * @return self
     */
    public function setExposureMode(array $exposureMode)
    {
        $this->exposureMode = $exposureMode;
        return $this;
    }


}

