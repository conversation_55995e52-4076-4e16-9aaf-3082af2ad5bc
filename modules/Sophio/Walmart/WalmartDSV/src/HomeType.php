<?php

namespace WalmartDSV;

/**
 * Class representing HomeType
 *
 *
 * XSD Type: Home
 */
class HomeType
{

    /**
     * @var \WalmartDSV\BeddingType $bedding
     */
    private $bedding = null;

    /**
     * @var \WalmartDSV\LargeAppliancesType $largeAppliances
     */
    private $largeAppliances = null;

    /**
     * @var \WalmartDSV\HomeOtherType $homeOther
     */
    private $homeOther = null;

    /**
     * Gets as bedding
     *
     * @return \WalmartDSV\BeddingType
     */
    public function getBedding()
    {
        return $this->bedding;
    }

    /**
     * Sets a new bedding
     *
     * @param \WalmartDSV\BeddingType $bedding
     * @return self
     */
    public function setBedding(\WalmartDSV\BeddingType $bedding)
    {
        $this->bedding = $bedding;
        return $this;
    }

    /**
     * Gets as largeAppliances
     *
     * @return \WalmartDSV\LargeAppliancesType
     */
    public function getLargeAppliances()
    {
        return $this->largeAppliances;
    }

    /**
     * Sets a new largeAppliances
     *
     * @param \WalmartDSV\LargeAppliancesType $largeAppliances
     * @return self
     */
    public function setLargeAppliances(\WalmartDSV\LargeAppliancesType $largeAppliances)
    {
        $this->largeAppliances = $largeAppliances;
        return $this;
    }

    /**
     * Gets as homeOther
     *
     * @return \WalmartDSV\HomeOtherType
     */
    public function getHomeOther()
    {
        return $this->homeOther;
    }

    /**
     * Sets a new homeOther
     *
     * @param \WalmartDSV\HomeOtherType $homeOther
     * @return self
     */
    public function setHomeOther(\WalmartDSV\HomeOtherType $homeOther)
    {
        $this->homeOther = $homeOther;
        return $this;
    }


}

