<?php

namespace WalmartDSV;

/**
 * Class representing WatchStyleType
 *
 * Styles that describe an overall profile of features and form specific to wristwatches.
 * XSD Type: WatchStyle
 */
class WatchStyleType
{

    /**
     * @var string $watchStyleValue
     */
    private $watchStyleValue = null;

    /**
     * Gets as watchStyleValue
     *
     * @return string
     */
    public function getWatchStyleValue()
    {
        return $this->watchStyleValue;
    }

    /**
     * Sets a new watchStyleValue
     *
     * @param string $watchStyleValue
     * @return self
     */
    public function setWatchStyleValue($watchStyleValue)
    {
        $this->watchStyleValue = $watchStyleValue;
        return $this;
    }


}

