<?php

namespace WalmartDSV\SoftwareType;

/**
 * Class representing PhysicalMediaFormatAType
 */
class PhysicalMediaFormatAType
{

    /**
     * @var string $physicalMediaFormatValue
     */
    private $physicalMediaFormatValue = null;

    /**
     * Gets as physicalMediaFormatValue
     *
     * @return string
     */
    public function getPhysicalMediaFormatValue()
    {
        return $this->physicalMediaFormatValue;
    }

    /**
     * Sets a new physicalMediaFormatValue
     *
     * @param string $physicalMediaFormatValue
     * @return self
     */
    public function setPhysicalMediaFormatValue($physicalMediaFormatValue)
    {
        $this->physicalMediaFormatValue = $physicalMediaFormatValue;
        return $this;
    }


}

