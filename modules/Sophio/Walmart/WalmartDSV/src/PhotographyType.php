<?php

namespace WalmartDSV;

/**
 * Class representing PhotographyType
 *
 *
 * XSD Type: Photography
 */
class PhotographyType
{

    /**
     * @var \WalmartDSV\CamerasAndLensesType $camerasAndLenses
     */
    private $camerasAndLenses = null;

    /**
     * @var \WalmartDSV\PhotoAccessoriesType $photoAccessories
     */
    private $photoAccessories = null;

    /**
     * Gets as camerasAndLenses
     *
     * @return \WalmartDSV\CamerasAndLensesType
     */
    public function getCamerasAndLenses()
    {
        return $this->camerasAndLenses;
    }

    /**
     * Sets a new camerasAndLenses
     *
     * @param \WalmartDSV\CamerasAndLensesType $camerasAndLenses
     * @return self
     */
    public function setCamerasAndLenses(\WalmartDSV\CamerasAndLensesType $camerasAndLenses)
    {
        $this->camerasAndLenses = $camerasAndLenses;
        return $this;
    }

    /**
     * Gets as photoAccessories
     *
     * @return \WalmartDSV\PhotoAccessoriesType
     */
    public function getPhotoAccessories()
    {
        return $this->photoAccessories;
    }

    /**
     * Sets a new photoAccessories
     *
     * @param \WalmartDSV\PhotoAccessoriesType $photoAccessories
     * @return self
     */
    public function setPhotoAccessories(\WalmartDSV\PhotoAccessoriesType $photoAccessories)
    {
        $this->photoAccessories = $photoAccessories;
        return $this;
    }


}

