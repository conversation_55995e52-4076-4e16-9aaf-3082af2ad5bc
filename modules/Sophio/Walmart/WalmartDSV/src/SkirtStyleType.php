<?php

namespace WalmartDSV;

/**
 * Class representing SkirtStyleType
 *
 * Terms describing a garment's skirt style, applicable to dresses and skirt products.
 * XSD Type: SkirtStyle
 */
class SkirtStyleType
{

    /**
     * @var string $skirtAndDressCutValue
     */
    private $skirtAndDressCutValue = null;

    /**
     * Gets as skirtAndDressCutValue
     *
     * @return string
     */
    public function getSkirtAndDressCutValue()
    {
        return $this->skirtAndDressCutValue;
    }

    /**
     * Sets a new skirtAndDressCutValue
     *
     * @param string $skirtAndDressCutValue
     * @return self
     */
    public function setSkirtAndDressCutValue($skirtAndDressCutValue)
    {
        $this->skirtAndDressCutValue = $skirtAndDressCutValue;
        return $this;
    }


}

