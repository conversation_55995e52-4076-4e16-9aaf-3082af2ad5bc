<?php

namespace WalmartDSV;

/**
 * Class representing AdditionalOfferAttributesType
 *
 * If there is other information that is not in the Spec, you can create one using a name-value pair.
 * XSD Type: AdditionalOfferAttributes
 */
class AdditionalOfferAttributesType
{

    /**
     * @var \WalmartDSV\AdditionalOfferAttributeType[] $additionalOfferAttribute
     */
    private $additionalOfferAttribute = [
        
    ];

    /**
     * Adds as additionalOfferAttribute
     *
     * @param \WalmartDSV\AdditionalOfferAttributeType $additionalOfferAttribute
     *@return self
     */
    public function addToAdditionalOfferAttribute(\WalmartDSV\AdditionalOfferAttributeType $additionalOfferAttribute)
    {
        $this->additionalOfferAttribute[] = $additionalOfferAttribute;
        return $this;
    }

    /**
     * isset additionalOfferAttribute
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAdditionalOfferAttribute($index)
    {
        return isset($this->additionalOfferAttribute[$index]);
    }

    /**
     * unset additionalOfferAttribute
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAdditionalOfferAttribute($index)
    {
        unset($this->additionalOfferAttribute[$index]);
    }

    /**
     * Gets as additionalOfferAttribute
     *
     * @return \WalmartDSV\AdditionalOfferAttributeType[]
     */
    public function getAdditionalOfferAttribute()
    {
        return $this->additionalOfferAttribute;
    }

    /**
     * Sets a new additionalOfferAttribute
     *
     * @param \WalmartDSV\AdditionalOfferAttributeType[] $additionalOfferAttribute
     * @return self
     */
    public function setAdditionalOfferAttribute(array $additionalOfferAttribute)
    {
        $this->additionalOfferAttribute = $additionalOfferAttribute;
        return $this;
    }


}

