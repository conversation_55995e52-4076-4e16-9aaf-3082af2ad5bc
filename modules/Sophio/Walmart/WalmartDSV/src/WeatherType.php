<?php

namespace WalmartDSV;

/**
 * Class representing WeatherType
 *
 * The type of weather the clothing or fashion accessory was designed for.
 * XSD Type: Weather
 */
class WeatherType
{

    /**
     * @var string $weatherValue
     */
    private $weatherValue = null;

    /**
     * Gets as weatherValue
     *
     * @return string
     */
    public function getWeatherValue()
    {
        return $this->weatherValue;
    }

    /**
     * Sets a new weatherValue
     *
     * @param string $weatherValue
     * @return self
     */
    public function setWeatherValue($weatherValue)
    {
        $this->weatherValue = $weatherValue;
        return $this;
    }


}

