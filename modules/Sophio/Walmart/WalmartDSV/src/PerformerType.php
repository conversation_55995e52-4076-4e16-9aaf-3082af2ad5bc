<?php

namespace WalmartDSV;

/**
 * Class representing PerformerType
 *
 * The performer/s or name of group on the album or single.
 * XSD Type: Performer
 */
class PerformerType
{

    /**
     * @var string[] $performerValue
     */
    private $performerValue = [
        
    ];

    /**
     * Adds as performerValue
     *
     * @return self
     * @param string $performerValue
     */
    public function addToPerformerValue($performerValue)
    {
        $this->performerValue[] = $performerValue;
        return $this;
    }

    /**
     * isset performerValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetPerformerValue($index)
    {
        return isset($this->performerValue[$index]);
    }

    /**
     * unset performerValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetPerformerValue($index)
    {
        unset($this->performerValue[$index]);
    }

    /**
     * Gets as performerValue
     *
     * @return string[]
     */
    public function getPerformerValue()
    {
        return $this->performerValue;
    }

    /**
     * Sets a new performerValue
     *
     * @param string $performerValue
     * @return self
     */
    public function setPerformerValue(array $performerValue)
    {
        $this->performerValue = $performerValue;
        return $this;
    }


}

