<?php

namespace WalmartDSV;

/**
 * Class representing AwardsWonType
 *
 * Use this attribute if the item has won any awards in its particular product category.
 * XSD Type: AwardsWon
 */
class AwardsWonType
{

    /**
     * @var string[] $awardsWonValue
     */
    private $awardsWonValue = [
        
    ];

    /**
     * Adds as awardsWonValue
     *
     * @return self
     * @param string $awardsWonValue
     */
    public function addToAwardsWonValue($awardsWonValue)
    {
        $this->awardsWonValue[] = $awardsWonValue;
        return $this;
    }

    /**
     * isset awardsWonValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAwardsWonValue($index)
    {
        return isset($this->awardsWonValue[$index]);
    }

    /**
     * unset awardsWonValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAwardsWonValue($index)
    {
        unset($this->awardsWonValue[$index]);
    }

    /**
     * Gets as awardsWonValue
     *
     * @return string[]
     */
    public function getAwardsWonValue()
    {
        return $this->awardsWonValue;
    }

    /**
     * Sets a new awardsWonValue
     *
     * @param string $awardsWonValue
     * @return self
     */
    public function setAwardsWonValue(array $awardsWonValue)
    {
        $this->awardsWonValue = $awardsWonValue;
        return $this;
    }


}

