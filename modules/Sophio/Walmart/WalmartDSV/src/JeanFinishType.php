<?php

namespace WalmartDSV;

/**
 * Class representing JeanFinishType
 *
 * F<PERSON>ric finishes specific to jeans.
 * XSD Type: JeanFinish
 */
class JeanFinishType
{

    /**
     * @var string $jeanFinishValue
     */
    private $jeanFinishValue = null;

    /**
     * Gets as jeanFinishValue
     *
     * @return string
     */
    public function getJeanFinishValue()
    {
        return $this->jeanFinishValue;
    }

    /**
     * Sets a new jeanFinishValue
     *
     * @param string $jeanFinishValue
     * @return self
     */
    public function setJeanFinishValue($jeanFinishValue)
    {
        $this->jeanFinishValue = $jeanFinishValue;
        return $this;
    }


}

