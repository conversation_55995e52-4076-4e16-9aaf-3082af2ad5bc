<?php

namespace WalmartDSV;

/**
 * Class representing ClothingType
 *
 *
 * XSD Type: Clothing
 */
class ClothingType
{

    /**
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @var string[] $additionalVariantAttributeNames
     */
    private $additionalVariantAttributeNames = null;

    /**
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @var string $shortDescription
     */
    private $shortDescription = null;

    /**
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @var string[] $keyFeatures
     */
    private $keyFeatures = null;

    /**
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @var string $brand
     */
    private $brand = null;

    /**
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @var string $manufacturer
     */
    private $manufacturer = null;

    /**
     * MPN uniquely identifies the product to its manufacturer. For many products this will be identical to the model number. Some manufacturers distinguish part number from model number. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @var string $manufacturerPartNumber
     */
    private $manufacturerPartNumber = null;

    /**
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @var int $multipackQuantity
     */
    private $multipackQuantity = null;

    /**
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @var int $countPerPack
     */
    private $countPerPack = null;

    /**
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @var string $count
     */
    private $count = null;

    /**
     * The number of small pieces, slices, or different items within the product. Piece Count applies to things such as puzzles, building block sets, and products that contain multiple different items (such as tool sets, dinnerware sets, gift baskets, art sets, makeup kits, or shaving kits). EXAMPLE: (1) A 500-piece puzzle has a "Piece Count" of 500. (2) A 105-Piece Socket Wrench set has a piece count of "105." (3) A gift basket of 5 different items has a "Piece Count" of 5.
     *
     * @var int $pieceCount
     */
    private $pieceCount = null;

    /**
     * Model numbers allow manufacturers to keep track of each hardware device and identify or replace the proper part when needed. Model numbers are often found on the bottom, back, or side of a product. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @var string $modelNumber
     */
    private $modelNumber = null;

    /**
     * Enter the Code for the apparel season: 0 - Basic; 1 - Spring; 2 - Summer; 3 - BTS/Fall; 4 - Winter
     *
     * @var int $seasonCode
     */
    private $seasonCode = null;

    /**
     * The four-digit year.
     *
     * @var int $seasonYear
     */
    private $seasonYear = null;

    /**
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @var string $mainImageUrl
     */
    private $mainImageUrl = null;

    /**
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @var string[] $productSecondaryImageURL
     */
    private $productSecondaryImageURL = null;

    /**
     * Color as described by the manufacturer.
     *
     * @var \WalmartDSV\ColorType $color
     */
    private $color = null;

    /**
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @var string[] $colorCategory
     */
    private $colorCategory = null;

    /**
     * Decorative design or visual ornamentation, often with a thematic, recurring motif.
     *
     * @var \WalmartDSV\PatternType $pattern
     */
    private $pattern = null;

    /**
     * The main material(s) that a product is made of. This does not need to be an exhaustive list, but should contain the predominant or functionally important material/materials. Fabric material specifics should be entered using the "Fabric Content" attribute.
     *
     * @var \WalmartDSV\MaterialType $material
     */
    private $material = null;

    /**
     * Indicate whether this item is meant for a particular gender or meant to be gender-agnostic (unisex).
     *
     * @var string $gender
     */
    private $gender = null;

    /**
     * General grouping of ages into commonly used demographic labels.
     *
     * @var string[] $ageGroup
     */
    private $ageGroup = null;

    /**
     * The common sizing groups used by the retail clothing industry.
     *
     * @var string $clothingSizeGroup
     */
    private $clothingSizeGroup = null;

    /**
     * Clothing size as it appears on the garment label. Use this attribute for general sizes (S, M, L) as well as general numbered sizes (2, 4, 6, etc). For items that have unique sizes (dress shirts, bras, etc.) use the specific size attribute.
     *
     * @var string $clothingSize
     */
    private $clothingSize = null;

    /**
     * Y indicates that the product consists of two or more different items sold together as a set. Example: A bedding set that includes sheets, pillow shams, and a comforter.
     *
     * @var string $isSet
     */
    private $isSet = null;

    /**
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @var string $variantGroupId
     */
    private $variantGroupId = null;

    /**
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @var string[] $variantAttributeNames
     */
    private $variantAttributeNames = null;

    /**
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @var string $isPrimaryVariant
     */
    private $isPrimaryVariant = null;

    /**
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @var string $isPrivateLabelOrUnbranded
     */
    private $isPrivateLabelOrUnbranded = null;

    /**
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @var string $isProp65WarningRequired
     */
    private $isProp65WarningRequired = null;

    /**
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @var string $prop65WarningText
     */
    private $prop65WarningText = null;

    /**
     * To determine if any choking warnings are applicable, check current product packaging for choking warning message(s). Please indicate the warning number (0-6). 0 - No warning applicable; 1 - Choking hazard is a small ball; 2 - Choking hazard contains small ball; 3 - Choking hazard contains small parts; 4 - Choking hazard balloon; 5 - Choking hazard is a marble; 6 - Choking hazard contains a marble.
     *
     * @var int[] $smallPartsWarnings
     */
    private $smallPartsWarnings = null;

    /**
     * Select "Y" if your item contains wool or is one of the following: clothing (except for hats and shoes), handkerchiefs, scarves, bedding (including sheets, covers, blankets, comforters, pillows, pillowcases, quilts, bedspreads and pads (but not outer coverings for mattresses or box springs)), curtains and casements, draperies, tablecloths, napkins, doilies, floor coverings (rugs, carpets and mats), towels, washcloths, dishcloths, ironing board covers and pads, umbrellas, parasols, bats or batting, flags with heading or that are bigger than 216 square inches, cushions, all fibers, yarns and fabrics (but not packaging ribbons), furniture slip covers and other furniture covers, afghans and throws, sleeping bags, antimacassars (doilies), hammocks, dresser and other furniture scarves. For further information on these requirements, refer to the labeling requirements of the Textile Act.
     *
     * @var string $requiresTextileActLabeling
     */
    private $requiresTextileActLabeling = null;

    /**
     * Use “Made in U.S.A. and Imported” to indicate manufacture in the U.S. from imported materials, or part processing in the U.S. and part in a foreign country. Use “Made in U.S.A. or Imported” to reflect that some units of an item originate from a domestic source and others from a foreign source. Use “Made in U.S.A.” only if all units were made completely in the U.S. using materials also made in the U.S. Use "Imported" if units are completely imported.
     *
     * @var string $countryOfOriginTextiles
     */
    private $countryOfOriginTextiles = null;

    /**
     * "Battery or battery containing product" is defined by Company to include any item of Merchandise that is a battery or any component of Merchandise, including reusable packaging intended to stay in use with the item, containing a battery of any chemistry/ type. Mark Y if this definition applies to your product.
     *
     * @var string $hasBatteries
     */
    private $hasBatteries = null;

    /**
     * Please select the Battery Technology Type from the list provided. NOTE: If battery type is lead acid, lead acid (nonspillable), lithium ion, or lithium metal, please ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @var string $batteryTechnologyType
     */
    private $batteryTechnologyType = null;

    /**
     * Y indicates the item comes with a warranty. If an item has a warranty, then enter EITHER the warranty URL or the warranty text in the appropriate field.
     *
     * @var string $hasWarranty
     */
    private $hasWarranty = null;

    /**
     * If you indicated that your item has a warranty, provide either the Warranty URL or Warranty Text. The Warranty URL is the web location of the image, PDF, or link to the manufacturer's warranty page, showing the warranty and its terms, including the duration of the warranty. URLs must begin with http:// or https:// NOTE: Please remember to update the link and/or text of the warranty as the warranty changes. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @var string $warrantyURL
     */
    private $warrantyURL = null;

    /**
     * If you marked Y for "Has Warranty" provide the Warranty URL or Warranty Text (the full text of the warranty terms, including what is covered by the warranty and the duration of the warranty). NOTE: please remember to update the text of your warranty as your warranty changes.
     *
     * @var string $warrantyText
     */
    private $warrantyText = null;

    /**
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @var string $hasStateRestrictions
     */
    private $hasStateRestrictions = null;

    /**
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @var \WalmartDSV\StateRestrictionType[] $stateRestrictions
     */
    private $stateRestrictions = null;

    /**
     * Style terms descriptive of clothing tops.
     *
     * @var \WalmartDSV\ClothingTopStyleType $clothingTopStyle
     */
    private $clothingTopStyle = null;

    /**
     * A grouping of sizes for dress shirts based on collar measurement and sleeve measurement.
     *
     * @var string $dressShirtSize
     */
    private $dressShirtSize = null;

    /**
     * Descriptive terms for the style of garment sleeves. Does not include descriptive terms for sleeve length (which appear under the Sleeve Length Style attribute).
     *
     * @var string $sleeveStyle
     */
    private $sleeveStyle = null;

    /**
     * Descriptive terms for sleeve length.
     *
     * @var string $sleeveLengthStyle
     */
    private $sleeveLengthStyle = null;

    /**
     * A garment's neckline or neck style.
     *
     * @var string $shirtNeckStyle
     */
    private $shirtNeckStyle = null;

    /**
     * Style of collar, as expressed by shape or design.
     *
     * @var string $collarType
     */
    private $collarType = null;

    /**
     * Styles specific to coats and jackets.
     *
     * @var \WalmartDSV\JacketStyleType $jacketStyle
     */
    private $jacketStyle = null;

    /**
     * The closure style of a suit jacket or coat; double-breasted suits have more fabric where they overlap in front and commonly have 2 rows of buttons.
     *
     * @var string $suitBreastingStyle
     */
    private $suitBreastingStyle = null;

    /**
     * Styles specific to sweaters.
     *
     * @var \WalmartDSV\SweaterStyleType $sweaterStyle
     */
    private $sweaterStyle = null;

    /**
     * Styles specific to scarves.
     *
     * @var \WalmartDSV\ScarfStyleType $scarfStyle
     */
    private $scarfStyle = null;

    /**
     * Strap configuration style for bras, swimsuit tops, leotards and other clothing tops.
     *
     * @var \WalmartDSV\UpperBodyStrapConfigurationType $upperBodyStrapConfiguration
     */
    private $upperBodyStrapConfiguration = null;

    /**
     * The sizing information specific to hats, ranging from 6 1/4 to 8 1/8. Generic sized (small/med/large) hats may not have this information (generic s/m/l size information should be entered under the "Clothing Size" attribute).
     *
     * @var string $hatSize
     */
    private $hatSize = null;

    /**
     * Styles specific to hats.
     *
     * @var \WalmartDSV\HatStyleType $hatStyle
     */
    private $hatStyle = null;

    /**
     * Brassiere styles/defining features. Enter as many as are relevant to the bra. Do not enter strap styles here. To enter descriptive terms for strap styles, please use the "Upper Body Strap Configuration" attribute.
     *
     * @var \WalmartDSV\BraStyleType $braStyle
     */
    private $braStyle = null;

    /**
     * Bra size, consisting of band and cup. Use this attribute if the garment has both measurements. If the bra has standard clothing sizes (e.g. S, M, L), enter the size under "Clothing Size". NOTE: for garments that have chest size (measured as the largest part of the chest, i.e. some women's slips), use "Chest Size".
     *
     * @var string $braSize
     */
    private $braSize = null;

    /**
     * Chest measurement in inches, around the largest part of the chest. Often used for men's jackets and women's full slips.
     *
     * @var \WalmartDSV\ClothingType\ChestSizeAType $chestSize
     */
    private $chestSize = null;

    /**
     * The height at which the waistline rests on the body.
     *
     * @var string $pantRise
     */
    private $pantRise = null;

    /**
     * Waist styles/defining features.
     *
     * @var \WalmartDSV\WaistStyleType $waistStyle
     */
    private $waistStyle = null;

    /**
     * Waist measurement in inches, around the smallest section of the natural waist (typically located just above the belly button).
     *
     * @var \WalmartDSV\ClothingType\WaistSizeAType $waistSize
     */
    private $waistSize = null;

    /**
     * Panty size. Generic sized (small/med/large) panties may not have this information (generic s/m/l size information should be entered under the "Clothing Size" attribute). Clothing size style values (Plus Size, Petite, Juniors, etc) should be entered under the "Clothing Size Style" attribute.
     *
     * @var string $pantySize
     */
    private $pantySize = null;

    /**
     * The measurement of the skirt, from waist to bottom hem, in inches.
     *
     * @var \WalmartDSV\ClothingType\SkirtLengthAType $skirtLength
     */
    private $skirtLength = null;

    /**
     * How high on the body the leg opening sits. Commonly referenced for leotards, swimwear, underwear or other legless garments.
     *
     * @var string $legOpeningCut
     */
    private $legOpeningCut = null;

    /**
     * Common style terms describing how the garment leg has been cut to make a specific sillhoutte/shape.
     *
     * @var string $pantLegCut
     */
    private $pantLegCut = null;

    /**
     * Style terms specific to jeans.
     *
     * @var \WalmartDSV\JeanStyleType $jeanStyle
     */
    private $jeanStyle = null;

    /**
     * Description of post-process wash treatment effecting the appearance of dye and fabric texture, specific to jeans.
     *
     * @var string $jeanWash
     */
    private $jeanWash = null;

    /**
     * Fabric finishes specific to jeans.
     *
     * @var \WalmartDSV\JeanFinishType $jeanFinish
     */
    private $jeanFinish = null;

    /**
     * A composite of Waist Size and Inseam. Do not fill in this attribute if you have separately filled in both "Waist Size" and "Inseam". If the pant size is in numbers (e.g. 12) or standard sizes (e.g. Large), enter the information under "Clothing Size".
     *
     * @var string $pantSize
     */
    private $pantSize = null;

    /**
     * Terms that describe the way pants will fit when worn.
     *
     * @var \WalmartDSV\PantFitType $pantFit
     */
    private $pantFit = null;

    /**
     * Style terms specific to pants.
     *
     * @var string $pantStyle
     */
    private $pantStyle = null;

    /**
     * Styles specific to belts.
     *
     * @var \WalmartDSV\BeltStyleType $beltStyle
     */
    private $beltStyle = null;

    /**
     * Belt buckle configurations.
     *
     * @var string $beltBuckleStyle
     */
    private $beltBuckleStyle = null;

    /**
     * Descriptive styles common to underwear bottoms and lower body elements of swimwear and leotards.
     *
     * @var string $pantyStyle
     */
    private $pantyStyle = null;

    /**
     * Styles specific to shorts.
     *
     * @var \WalmartDSV\ShortsStyleType $shortsStyle
     */
    private $shortsStyle = null;

    /**
     * Terms describing a garment's skirt style, applicable to dresses and skirt products.
     *
     * @var \WalmartDSV\SkirtStyleType $skirtAndDressCut
     */
    private $skirtAndDressCut = null;

    /**
     * Descriptive terms for length specific to skirts and dresses.
     *
     * @var string $skirtLengthStyle
     */
    private $skirtLengthStyle = null;

    /**
     * Styles/features specific to tights and hosiery.
     *
     * @var \WalmartDSV\HosieryStyleType $hosieryStyle
     */
    private $hosieryStyle = null;

    /**
     * Sheerness/opacities specific to tights and hosiery.
     *
     * @var string $tightsSheerness
     */
    private $tightsSheerness = null;

    /**
     * Descriptive styles specific to underwear.
     *
     * @var \WalmartDSV\UnderwearStyleType $underwearStyle
     */
    private $underwearStyle = null;

    /**
     * Sock size. Generic sized (small/med/large) socks may not have this information (generic s/m/l size information should be entered under the clothing size attribute).
     *
     * @var string $sockSize
     */
    private $sockSize = null;

    /**
     * Styles specific to socks. Does not include descriptive terms for sock height - those are in the Sock Rise attribute.
     *
     * @var string $sockStyle
     */
    private $sockStyle = null;

    /**
     * Descriptive terms for sock height.
     *
     * @var string $sockRise
     */
    private $sockRise = null;

    /**
     * Material makeup of the item.
     *
     * @var \WalmartDSV\FabricContentValueType[] $fabricContent
     */
    private $fabricContent = null;

    /**
     * Describes how the fabric should be cleaned. Enter details of the fabric care label found on the item. (For garments, typically located inside on the top of the back or the lower left side.)
     *
     * @var string[] $fabricCareInstructions
     */
    private $fabricCareInstructions = null;

    /**
     * A secondary product color.
     *
     * @var string $accentColor
     */
    private $accentColor = null;

    /**
     * Commonly used in retail clothing, these terms describe the density or weight of fabric material.
     *
     * @var string $clothingWeight
     */
    private $clothingWeight = null;

    /**
     * Styles and designations that apply generally to various types of clothing.
     *
     * @var \WalmartDSV\ClothingStyleType $clothingStyle
     */
    private $clothingStyle = null;

    /**
     * Terms that describe the way a garment will fit when worn. Does not include fit values specific to pants.
     *
     * @var string $clothingFit
     */
    private $clothingFit = null;

    /**
     * Common clothing cut styles.
     *
     * @var \WalmartDSV\ClothingCutType $clothingCut
     */
    private $clothingCut = null;

    /**
     * Descriptive terms for where on the lower-body a garment ends.
     *
     * @var string $clothingLengthStyle
     */
    private $clothingLengthStyle = null;

    /**
     * The type of fastener used to keep a garment closed on the wearer and to facilitate putting on the garment.
     *
     * @var string $fastenerType
     */
    private $fastenerType = null;

    /**
     * Descriptive styles common to swimwear. Style terms specific to the bottom half of swimwear appear under the Underpant/Swim Bottom Style attribute.
     *
     * @var string $swimsuitStyle
     */
    private $swimsuitStyle = null;

    /**
     * Style terms specific to dresses. Other style terms that specifically describe a dress's skirt may be found under the Skirt Style attribute. Some values are shared between skirts and dresses: both "mini" dresses and "mini" skirts exist as separate products.
     *
     * @var string $dressStyle
     */
    private $dressStyle = null;

    /**
     * Y indicates the product is certified under requirements of the Global Organic Textile Standard.
     *
     * @var string $gotsCertification
     */
    private $gotsCertification = null;

    /**
     * A dominant idea, meaning, or setting applied to an item. Used in a wide range of products including decorative objects, clothing, toys, and furniture. Can be an important selection criteria for consumers who want to achieve a particular ambiance for room décor or for a special occasion.
     *
     * @var \WalmartDSV\ThemeType $theme
     */
    private $theme = null;

    /**
     * A person or entity portrayed in print or visual media. A character might be a fictional personality or an actual living person.
     *
     * @var \WalmartDSV\CharacterType $character
     */
    private $character = null;

    /**
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @var string[] $globalBrandLicense
     */
    private $globalBrandLicense = null;

    /**
     * If your item has any association with a specific sports league, enter the league name. Abbreviations are fine. NOTE: This attribute flags an item for inclusion in the online fan shop.
     *
     * @var \WalmartDSV\SportsLeagueType $sportsLeague
     */
    private $sportsLeague = null;

    /**
     * If your item has any association with a specific sports team, enter the team name. NOTE: This attribute flags an item for inclusion in the online fan shop.
     *
     * @var \WalmartDSV\SportsTeamType $sportsTeam
     */
    private $sportsTeam = null;

    /**
     * The particular target time, event, or holiday for the product.
     *
     * @var \WalmartDSV\OccasionType $occasion
     */
    private $occasion = null;

    /**
     * The general type of activity one might perform while wearing this garment.
     *
     * @var \WalmartDSV\ActivityType $activity
     */
    private $activity = null;

    /**
     * If the product is sports-related, the name of the specific sport depicted on the product, or the target sport for the product use
     *
     * @var \WalmartDSV\SportType $sport
     */
    private $sport = null;

    /**
     * If designed to be used during a specific type of year, the appropriate season this item may be used.
     *
     * @var \WalmartDSV\SeasonType $season
     */
    private $season = null;

    /**
     * The type of weather the clothing or fashion accessory was designed for.
     *
     * @var \WalmartDSV\WeatherType $weather
     */
    private $weather = null;

    /**
     * Is this item marketed specifically to women who are pregnant?
     *
     * @var string $isMaternity
     */
    private $isMaternity = null;

    /**
     * Name of College or other school. This is to distinguish the school from its sports team.
     *
     * @var string $academicInstitution
     */
    private $academicInstitution = null;

    /**
     * A well-known athlete associated with a product, if applicable. This is used to group items in Fan Shop, not to describe a line of clothing.
     *
     * @var \WalmartDSV\AthleteType $athlete
     */
    private $athlete = null;

    /**
     * The full name of the person who has autographed this copy.
     *
     * @var string $autographedBy
     */
    private $autographedBy = null;

    /**
     * A measurement in inches of the band of the bra, which fits around the ribcage just under the bust. Use this if the garment has a band size only or to separate band and cup size for search discovery.
     *
     * @var \WalmartDSV\ClothingType\BraBandSizeAType $braBandSize
     */
    private $braBandSize = null;

    /**
     * A letter size to optimize bra fit, calculated by measuring the chest at its widest point and subtracting the bra band size. 1 inch corresponds to 1 cup letter (e.g. if the difference is 5, the bra cup size is DD or E). Use this to record separate cup sizes for search discovery.
     *
     * @var string $braCupSize
     */
    private $braCupSize = null;

    /**
     * Neck size in inches.
     *
     * @var \WalmartDSV\ClothingType\NeckSizeAType $neckSize
     */
    private $neckSize = null;

    /**
     * The distance from the shoulder to the bottom hem of the sleeve, measured in inches.
     *
     * @var \WalmartDSV\ClothingType\SleeveLengthAType $sleeveLength
     */
    private $sleeveLength = null;

    /**
     * For pants, the distance from the bottom of the leg to the seam in the crotch, measured in inches.
     *
     * @var \WalmartDSV\ClothingType\InseamAType $inseam
     */
    private $inseam = null;

    /**
     * Indicates that the item is made from recycled materials.
     *
     * @var string $isMadeFromRecycledMaterial
     */
    private $isMadeFromRecycledMaterial = null;

    /**
     * If item contains reused/recycled material, the percentage of all recycled material used to produce the item. This can also include specific material composition; cushions made from 30% recycled cotton fabric. Used to highlight sustainability to the customer.
     *
     * @var \WalmartDSV\RecycledMaterialContentValueType[] $recycledMaterialContent
     */
    private $recycledMaterialContent = null;

    /**
     * List notable features of the item.
     *
     * @var string[] $features
     */
    private $features = null;

    /**
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @var string $keywords
     */
    private $keywords = null;

    /**
     * @var \WalmartDSV\ClothingType\SwatchImagesAType\SwatchImageAType[] $swatchImages
     */
    private $swatchImages = null;

    /**
     * Adds as additionalVariantAttributeName
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @return self
     * @param string $additionalVariantAttributeName
     */
    public function addToAdditionalVariantAttributeNames($additionalVariantAttributeName)
    {
        $this->additionalVariantAttributeNames[] = $additionalVariantAttributeName;
        return $this;
    }

    /**
     * isset additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAdditionalVariantAttributeNames($index)
    {
        return isset($this->additionalVariantAttributeNames[$index]);
    }

    /**
     * unset additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAdditionalVariantAttributeNames($index)
    {
        unset($this->additionalVariantAttributeNames[$index]);
    }

    /**
     * Gets as additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @return string[]
     */
    public function getAdditionalVariantAttributeNames()
    {
        return $this->additionalVariantAttributeNames;
    }

    /**
     * Sets a new additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param string $additionalVariantAttributeNames
     * @return self
     */
    public function setAdditionalVariantAttributeNames(array $additionalVariantAttributeNames)
    {
        $this->additionalVariantAttributeNames = $additionalVariantAttributeNames;
        return $this;
    }

    /**
     * Gets as shortDescription
     *
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @return string
     */
    public function getShortDescription()
    {
        return $this->shortDescription;
    }

    /**
     * Sets a new shortDescription
     *
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @param string $shortDescription
     * @return self
     */
    public function setShortDescription($shortDescription)
    {
        $this->shortDescription = $shortDescription;
        return $this;
    }

    /**
     * Adds as keyFeaturesValue
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @return self
     * @param string $keyFeaturesValue
     */
    public function addToKeyFeatures($keyFeaturesValue)
    {
        $this->keyFeatures[] = $keyFeaturesValue;
        return $this;
    }

    /**
     * isset keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetKeyFeatures($index)
    {
        return isset($this->keyFeatures[$index]);
    }

    /**
     * unset keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetKeyFeatures($index)
    {
        unset($this->keyFeatures[$index]);
    }

    /**
     * Gets as keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @return string[]
     */
    public function getKeyFeatures()
    {
        return $this->keyFeatures;
    }

    /**
     * Sets a new keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param string $keyFeatures
     * @return self
     */
    public function setKeyFeatures(array $keyFeatures)
    {
        $this->keyFeatures = $keyFeatures;
        return $this;
    }

    /**
     * Gets as brand
     *
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @return string
     */
    public function getBrand()
    {
        return $this->brand;
    }

    /**
     * Sets a new brand
     *
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @param string $brand
     * @return self
     */
    public function setBrand($brand)
    {
        $this->brand = $brand;
        return $this;
    }

    /**
     * Gets as manufacturer
     *
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @return string
     */
    public function getManufacturer()
    {
        return $this->manufacturer;
    }

    /**
     * Sets a new manufacturer
     *
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @param string $manufacturer
     * @return self
     */
    public function setManufacturer($manufacturer)
    {
        $this->manufacturer = $manufacturer;
        return $this;
    }

    /**
     * Gets as manufacturerPartNumber
     *
     * MPN uniquely identifies the product to its manufacturer. For many products this will be identical to the model number. Some manufacturers distinguish part number from model number. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @return string
     */
    public function getManufacturerPartNumber()
    {
        return $this->manufacturerPartNumber;
    }

    /**
     * Sets a new manufacturerPartNumber
     *
     * MPN uniquely identifies the product to its manufacturer. For many products this will be identical to the model number. Some manufacturers distinguish part number from model number. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @param string $manufacturerPartNumber
     * @return self
     */
    public function setManufacturerPartNumber($manufacturerPartNumber)
    {
        $this->manufacturerPartNumber = $manufacturerPartNumber;
        return $this;
    }

    /**
     * Gets as multipackQuantity
     *
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @return int
     */
    public function getMultipackQuantity()
    {
        return $this->multipackQuantity;
    }

    /**
     * Sets a new multipackQuantity
     *
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @param int $multipackQuantity
     * @return self
     */
    public function setMultipackQuantity($multipackQuantity)
    {
        $this->multipackQuantity = $multipackQuantity;
        return $this;
    }

    /**
     * Gets as countPerPack
     *
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @return int
     */
    public function getCountPerPack()
    {
        return $this->countPerPack;
    }

    /**
     * Sets a new countPerPack
     *
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @param int $countPerPack
     * @return self
     */
    public function setCountPerPack($countPerPack)
    {
        $this->countPerPack = $countPerPack;
        return $this;
    }

    /**
     * Gets as count
     *
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @return string
     */
    public function getCount()
    {
        return $this->count;
    }

    /**
     * Sets a new count
     *
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @param string $count
     * @return self
     */
    public function setCount($count)
    {
        $this->count = $count;
        return $this;
    }

    /**
     * Gets as pieceCount
     *
     * The number of small pieces, slices, or different items within the product. Piece Count applies to things such as puzzles, building block sets, and products that contain multiple different items (such as tool sets, dinnerware sets, gift baskets, art sets, makeup kits, or shaving kits). EXAMPLE: (1) A 500-piece puzzle has a "Piece Count" of 500. (2) A 105-Piece Socket Wrench set has a piece count of "105." (3) A gift basket of 5 different items has a "Piece Count" of 5.
     *
     * @return int
     */
    public function getPieceCount()
    {
        return $this->pieceCount;
    }

    /**
     * Sets a new pieceCount
     *
     * The number of small pieces, slices, or different items within the product. Piece Count applies to things such as puzzles, building block sets, and products that contain multiple different items (such as tool sets, dinnerware sets, gift baskets, art sets, makeup kits, or shaving kits). EXAMPLE: (1) A 500-piece puzzle has a "Piece Count" of 500. (2) A 105-Piece Socket Wrench set has a piece count of "105." (3) A gift basket of 5 different items has a "Piece Count" of 5.
     *
     * @param int $pieceCount
     * @return self
     */
    public function setPieceCount($pieceCount)
    {
        $this->pieceCount = $pieceCount;
        return $this;
    }

    /**
     * Gets as modelNumber
     *
     * Model numbers allow manufacturers to keep track of each hardware device and identify or replace the proper part when needed. Model numbers are often found on the bottom, back, or side of a product. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @return string
     */
    public function getModelNumber()
    {
        return $this->modelNumber;
    }

    /**
     * Sets a new modelNumber
     *
     * Model numbers allow manufacturers to keep track of each hardware device and identify or replace the proper part when needed. Model numbers are often found on the bottom, back, or side of a product. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @param string $modelNumber
     * @return self
     */
    public function setModelNumber($modelNumber)
    {
        $this->modelNumber = $modelNumber;
        return $this;
    }

    /**
     * Gets as seasonCode
     *
     * Enter the Code for the apparel season: 0 - Basic; 1 - Spring; 2 - Summer; 3 - BTS/Fall; 4 - Winter
     *
     * @return int
     */
    public function getSeasonCode()
    {
        return $this->seasonCode;
    }

    /**
     * Sets a new seasonCode
     *
     * Enter the Code for the apparel season: 0 - Basic; 1 - Spring; 2 - Summer; 3 - BTS/Fall; 4 - Winter
     *
     * @param int $seasonCode
     * @return self
     */
    public function setSeasonCode($seasonCode)
    {
        $this->seasonCode = $seasonCode;
        return $this;
    }

    /**
     * Gets as seasonYear
     *
     * The four-digit year.
     *
     * @return int
     */
    public function getSeasonYear()
    {
        return $this->seasonYear;
    }

    /**
     * Sets a new seasonYear
     *
     * The four-digit year.
     *
     * @param int $seasonYear
     * @return self
     */
    public function setSeasonYear($seasonYear)
    {
        $this->seasonYear = $seasonYear;
        return $this;
    }

    /**
     * Gets as mainImageUrl
     *
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return string
     */
    public function getMainImageUrl()
    {
        return $this->mainImageUrl;
    }

    /**
     * Sets a new mainImageUrl
     *
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param string $mainImageUrl
     * @return self
     */
    public function setMainImageUrl($mainImageUrl)
    {
        $this->mainImageUrl = $mainImageUrl;
        return $this;
    }

    /**
     * Adds as productSecondaryImageURLValue
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return self
     * @param string $productSecondaryImageURLValue
     */
    public function addToProductSecondaryImageURL($productSecondaryImageURLValue)
    {
        $this->productSecondaryImageURL[] = $productSecondaryImageURLValue;
        return $this;
    }

    /**
     * isset productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetProductSecondaryImageURL($index)
    {
        return isset($this->productSecondaryImageURL[$index]);
    }

    /**
     * unset productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetProductSecondaryImageURL($index)
    {
        unset($this->productSecondaryImageURL[$index]);
    }

    /**
     * Gets as productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return string[]
     */
    public function getProductSecondaryImageURL()
    {
        return $this->productSecondaryImageURL;
    }

    /**
     * Sets a new productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param string $productSecondaryImageURL
     * @return self
     */
    public function setProductSecondaryImageURL(array $productSecondaryImageURL)
    {
        $this->productSecondaryImageURL = $productSecondaryImageURL;
        return $this;
    }

    /**
     * Gets as color
     *
     * Color as described by the manufacturer.
     *
     * @return \WalmartDSV\ColorType
     */
    public function getColor()
    {
        return $this->color;
    }

    /**
     * Sets a new color
     *
     * Color as described by the manufacturer.
     *
     * @param \WalmartDSV\ColorType $color
     * @return self
     */
    public function setColor(\WalmartDSV\ColorType $color)
    {
        $this->color = $color;
        return $this;
    }

    /**
     * Adds as colorCategoryValue
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @return self
     * @param string $colorCategoryValue
     */
    public function addToColorCategory($colorCategoryValue)
    {
        $this->colorCategory[] = $colorCategoryValue;
        return $this;
    }

    /**
     * isset colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetColorCategory($index)
    {
        return isset($this->colorCategory[$index]);
    }

    /**
     * unset colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetColorCategory($index)
    {
        unset($this->colorCategory[$index]);
    }

    /**
     * Gets as colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @return string[]
     */
    public function getColorCategory()
    {
        return $this->colorCategory;
    }

    /**
     * Sets a new colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @param string $colorCategory
     * @return self
     */
    public function setColorCategory(array $colorCategory)
    {
        $this->colorCategory = $colorCategory;
        return $this;
    }

    /**
     * Gets as pattern
     *
     * Decorative design or visual ornamentation, often with a thematic, recurring motif.
     *
     * @return \WalmartDSV\PatternType
     */
    public function getPattern()
    {
        return $this->pattern;
    }

    /**
     * Sets a new pattern
     *
     * Decorative design or visual ornamentation, often with a thematic, recurring motif.
     *
     * @param \WalmartDSV\PatternType $pattern
     * @return self
     */
    public function setPattern(\WalmartDSV\PatternType $pattern)
    {
        $this->pattern = $pattern;
        return $this;
    }

    /**
     * Gets as material
     *
     * The main material(s) that a product is made of. This does not need to be an exhaustive list, but should contain the predominant or functionally important material/materials. Fabric material specifics should be entered using the "Fabric Content" attribute.
     *
     * @return \WalmartDSV\MaterialType
     */
    public function getMaterial()
    {
        return $this->material;
    }

    /**
     * Sets a new material
     *
     * The main material(s) that a product is made of. This does not need to be an exhaustive list, but should contain the predominant or functionally important material/materials. Fabric material specifics should be entered using the "Fabric Content" attribute.
     *
     * @param \WalmartDSV\MaterialType $material
     * @return self
     */
    public function setMaterial(\WalmartDSV\MaterialType $material)
    {
        $this->material = $material;
        return $this;
    }

    /**
     * Gets as gender
     *
     * Indicate whether this item is meant for a particular gender or meant to be gender-agnostic (unisex).
     *
     * @return string
     */
    public function getGender()
    {
        return $this->gender;
    }

    /**
     * Sets a new gender
     *
     * Indicate whether this item is meant for a particular gender or meant to be gender-agnostic (unisex).
     *
     * @param string $gender
     * @return self
     */
    public function setGender($gender)
    {
        $this->gender = $gender;
        return $this;
    }

    /**
     * Adds as ageGroupValue
     *
     * General grouping of ages into commonly used demographic labels.
     *
     * @return self
     * @param string $ageGroupValue
     */
    public function addToAgeGroup($ageGroupValue)
    {
        $this->ageGroup[] = $ageGroupValue;
        return $this;
    }

    /**
     * isset ageGroup
     *
     * General grouping of ages into commonly used demographic labels.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAgeGroup($index)
    {
        return isset($this->ageGroup[$index]);
    }

    /**
     * unset ageGroup
     *
     * General grouping of ages into commonly used demographic labels.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAgeGroup($index)
    {
        unset($this->ageGroup[$index]);
    }

    /**
     * Gets as ageGroup
     *
     * General grouping of ages into commonly used demographic labels.
     *
     * @return string[]
     */
    public function getAgeGroup()
    {
        return $this->ageGroup;
    }

    /**
     * Sets a new ageGroup
     *
     * General grouping of ages into commonly used demographic labels.
     *
     * @param string $ageGroup
     * @return self
     */
    public function setAgeGroup(array $ageGroup)
    {
        $this->ageGroup = $ageGroup;
        return $this;
    }

    /**
     * Gets as clothingSizeGroup
     *
     * The common sizing groups used by the retail clothing industry.
     *
     * @return string
     */
    public function getClothingSizeGroup()
    {
        return $this->clothingSizeGroup;
    }

    /**
     * Sets a new clothingSizeGroup
     *
     * The common sizing groups used by the retail clothing industry.
     *
     * @param string $clothingSizeGroup
     * @return self
     */
    public function setClothingSizeGroup($clothingSizeGroup)
    {
        $this->clothingSizeGroup = $clothingSizeGroup;
        return $this;
    }

    /**
     * Gets as clothingSize
     *
     * Clothing size as it appears on the garment label. Use this attribute for general sizes (S, M, L) as well as general numbered sizes (2, 4, 6, etc). For items that have unique sizes (dress shirts, bras, etc.) use the specific size attribute.
     *
     * @return string
     */
    public function getClothingSize()
    {
        return $this->clothingSize;
    }

    /**
     * Sets a new clothingSize
     *
     * Clothing size as it appears on the garment label. Use this attribute for general sizes (S, M, L) as well as general numbered sizes (2, 4, 6, etc). For items that have unique sizes (dress shirts, bras, etc.) use the specific size attribute.
     *
     * @param string $clothingSize
     * @return self
     */
    public function setClothingSize($clothingSize)
    {
        $this->clothingSize = $clothingSize;
        return $this;
    }

    /**
     * Gets as isSet
     *
     * Y indicates that the product consists of two or more different items sold together as a set. Example: A bedding set that includes sheets, pillow shams, and a comforter.
     *
     * @return string
     */
    public function getIsSet()
    {
        return $this->isSet;
    }

    /**
     * Sets a new isSet
     *
     * Y indicates that the product consists of two or more different items sold together as a set. Example: A bedding set that includes sheets, pillow shams, and a comforter.
     *
     * @param string $isSet
     * @return self
     */
    public function setIsSet($isSet)
    {
        $this->isSet = $isSet;
        return $this;
    }

    /**
     * Gets as variantGroupId
     *
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @return string
     */
    public function getVariantGroupId()
    {
        return $this->variantGroupId;
    }

    /**
     * Sets a new variantGroupId
     *
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @param string $variantGroupId
     * @return self
     */
    public function setVariantGroupId($variantGroupId)
    {
        $this->variantGroupId = $variantGroupId;
        return $this;
    }

    /**
     * Adds as variantAttributeName
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @return self
     * @param string $variantAttributeName
     */
    public function addToVariantAttributeNames($variantAttributeName)
    {
        $this->variantAttributeNames[] = $variantAttributeName;
        return $this;
    }

    /**
     * isset variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetVariantAttributeNames($index)
    {
        return isset($this->variantAttributeNames[$index]);
    }

    /**
     * unset variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetVariantAttributeNames($index)
    {
        unset($this->variantAttributeNames[$index]);
    }

    /**
     * Gets as variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @return string[]
     */
    public function getVariantAttributeNames()
    {
        return $this->variantAttributeNames;
    }

    /**
     * Sets a new variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param string $variantAttributeNames
     * @return self
     */
    public function setVariantAttributeNames(array $variantAttributeNames)
    {
        $this->variantAttributeNames = $variantAttributeNames;
        return $this;
    }

    /**
     * Gets as isPrimaryVariant
     *
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @return string
     */
    public function getIsPrimaryVariant()
    {
        return $this->isPrimaryVariant;
    }

    /**
     * Sets a new isPrimaryVariant
     *
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @param string $isPrimaryVariant
     * @return self
     */
    public function setIsPrimaryVariant($isPrimaryVariant)
    {
        $this->isPrimaryVariant = $isPrimaryVariant;
        return $this;
    }

    /**
     * Gets as isPrivateLabelOrUnbranded
     *
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @return string
     */
    public function getIsPrivateLabelOrUnbranded()
    {
        return $this->isPrivateLabelOrUnbranded;
    }

    /**
     * Sets a new isPrivateLabelOrUnbranded
     *
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @param string $isPrivateLabelOrUnbranded
     * @return self
     */
    public function setIsPrivateLabelOrUnbranded($isPrivateLabelOrUnbranded)
    {
        $this->isPrivateLabelOrUnbranded = $isPrivateLabelOrUnbranded;
        return $this;
    }

    /**
     * Gets as isProp65WarningRequired
     *
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @return string
     */
    public function getIsProp65WarningRequired()
    {
        return $this->isProp65WarningRequired;
    }

    /**
     * Sets a new isProp65WarningRequired
     *
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @param string $isProp65WarningRequired
     * @return self
     */
    public function setIsProp65WarningRequired($isProp65WarningRequired)
    {
        $this->isProp65WarningRequired = $isProp65WarningRequired;
        return $this;
    }

    /**
     * Gets as prop65WarningText
     *
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @return string
     */
    public function getProp65WarningText()
    {
        return $this->prop65WarningText;
    }

    /**
     * Sets a new prop65WarningText
     *
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @param string $prop65WarningText
     * @return self
     */
    public function setProp65WarningText($prop65WarningText)
    {
        $this->prop65WarningText = $prop65WarningText;
        return $this;
    }

    /**
     * Adds as smallPartsWarning
     *
     * To determine if any choking warnings are applicable, check current product packaging for choking warning message(s). Please indicate the warning number (0-6). 0 - No warning applicable; 1 - Choking hazard is a small ball; 2 - Choking hazard contains small ball; 3 - Choking hazard contains small parts; 4 - Choking hazard balloon; 5 - Choking hazard is a marble; 6 - Choking hazard contains a marble.
     *
     * @return self
     * @param int $smallPartsWarning
     */
    public function addToSmallPartsWarnings($smallPartsWarning)
    {
        $this->smallPartsWarnings[] = $smallPartsWarning;
        return $this;
    }

    /**
     * isset smallPartsWarnings
     *
     * To determine if any choking warnings are applicable, check current product packaging for choking warning message(s). Please indicate the warning number (0-6). 0 - No warning applicable; 1 - Choking hazard is a small ball; 2 - Choking hazard contains small ball; 3 - Choking hazard contains small parts; 4 - Choking hazard balloon; 5 - Choking hazard is a marble; 6 - Choking hazard contains a marble.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetSmallPartsWarnings($index)
    {
        return isset($this->smallPartsWarnings[$index]);
    }

    /**
     * unset smallPartsWarnings
     *
     * To determine if any choking warnings are applicable, check current product packaging for choking warning message(s). Please indicate the warning number (0-6). 0 - No warning applicable; 1 - Choking hazard is a small ball; 2 - Choking hazard contains small ball; 3 - Choking hazard contains small parts; 4 - Choking hazard balloon; 5 - Choking hazard is a marble; 6 - Choking hazard contains a marble.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetSmallPartsWarnings($index)
    {
        unset($this->smallPartsWarnings[$index]);
    }

    /**
     * Gets as smallPartsWarnings
     *
     * To determine if any choking warnings are applicable, check current product packaging for choking warning message(s). Please indicate the warning number (0-6). 0 - No warning applicable; 1 - Choking hazard is a small ball; 2 - Choking hazard contains small ball; 3 - Choking hazard contains small parts; 4 - Choking hazard balloon; 5 - Choking hazard is a marble; 6 - Choking hazard contains a marble.
     *
     * @return int[]
     */
    public function getSmallPartsWarnings()
    {
        return $this->smallPartsWarnings;
    }

    /**
     * Sets a new smallPartsWarnings
     *
     * To determine if any choking warnings are applicable, check current product packaging for choking warning message(s). Please indicate the warning number (0-6). 0 - No warning applicable; 1 - Choking hazard is a small ball; 2 - Choking hazard contains small ball; 3 - Choking hazard contains small parts; 4 - Choking hazard balloon; 5 - Choking hazard is a marble; 6 - Choking hazard contains a marble.
     *
     * @param int $smallPartsWarnings
     * @return self
     */
    public function setSmallPartsWarnings(array $smallPartsWarnings)
    {
        $this->smallPartsWarnings = $smallPartsWarnings;
        return $this;
    }

    /**
     * Gets as requiresTextileActLabeling
     *
     * Select "Y" if your item contains wool or is one of the following: clothing (except for hats and shoes), handkerchiefs, scarves, bedding (including sheets, covers, blankets, comforters, pillows, pillowcases, quilts, bedspreads and pads (but not outer coverings for mattresses or box springs)), curtains and casements, draperies, tablecloths, napkins, doilies, floor coverings (rugs, carpets and mats), towels, washcloths, dishcloths, ironing board covers and pads, umbrellas, parasols, bats or batting, flags with heading or that are bigger than 216 square inches, cushions, all fibers, yarns and fabrics (but not packaging ribbons), furniture slip covers and other furniture covers, afghans and throws, sleeping bags, antimacassars (doilies), hammocks, dresser and other furniture scarves. For further information on these requirements, refer to the labeling requirements of the Textile Act.
     *
     * @return string
     */
    public function getRequiresTextileActLabeling()
    {
        return $this->requiresTextileActLabeling;
    }

    /**
     * Sets a new requiresTextileActLabeling
     *
     * Select "Y" if your item contains wool or is one of the following: clothing (except for hats and shoes), handkerchiefs, scarves, bedding (including sheets, covers, blankets, comforters, pillows, pillowcases, quilts, bedspreads and pads (but not outer coverings for mattresses or box springs)), curtains and casements, draperies, tablecloths, napkins, doilies, floor coverings (rugs, carpets and mats), towels, washcloths, dishcloths, ironing board covers and pads, umbrellas, parasols, bats or batting, flags with heading or that are bigger than 216 square inches, cushions, all fibers, yarns and fabrics (but not packaging ribbons), furniture slip covers and other furniture covers, afghans and throws, sleeping bags, antimacassars (doilies), hammocks, dresser and other furniture scarves. For further information on these requirements, refer to the labeling requirements of the Textile Act.
     *
     * @param string $requiresTextileActLabeling
     * @return self
     */
    public function setRequiresTextileActLabeling($requiresTextileActLabeling)
    {
        $this->requiresTextileActLabeling = $requiresTextileActLabeling;
        return $this;
    }

    /**
     * Gets as countryOfOriginTextiles
     *
     * Use “Made in U.S.A. and Imported” to indicate manufacture in the U.S. from imported materials, or part processing in the U.S. and part in a foreign country. Use “Made in U.S.A. or Imported” to reflect that some units of an item originate from a domestic source and others from a foreign source. Use “Made in U.S.A.” only if all units were made completely in the U.S. using materials also made in the U.S. Use "Imported" if units are completely imported.
     *
     * @return string
     */
    public function getCountryOfOriginTextiles()
    {
        return $this->countryOfOriginTextiles;
    }

    /**
     * Sets a new countryOfOriginTextiles
     *
     * Use “Made in U.S.A. and Imported” to indicate manufacture in the U.S. from imported materials, or part processing in the U.S. and part in a foreign country. Use “Made in U.S.A. or Imported” to reflect that some units of an item originate from a domestic source and others from a foreign source. Use “Made in U.S.A.” only if all units were made completely in the U.S. using materials also made in the U.S. Use "Imported" if units are completely imported.
     *
     * @param string $countryOfOriginTextiles
     * @return self
     */
    public function setCountryOfOriginTextiles($countryOfOriginTextiles)
    {
        $this->countryOfOriginTextiles = $countryOfOriginTextiles;
        return $this;
    }

    /**
     * Gets as hasBatteries
     *
     * "Battery or battery containing product" is defined by Company to include any item of Merchandise that is a battery or any component of Merchandise, including reusable packaging intended to stay in use with the item, containing a battery of any chemistry/ type. Mark Y if this definition applies to your product.
     *
     * @return string
     */
    public function getHasBatteries()
    {
        return $this->hasBatteries;
    }

    /**
     * Sets a new hasBatteries
     *
     * "Battery or battery containing product" is defined by Company to include any item of Merchandise that is a battery or any component of Merchandise, including reusable packaging intended to stay in use with the item, containing a battery of any chemistry/ type. Mark Y if this definition applies to your product.
     *
     * @param string $hasBatteries
     * @return self
     */
    public function setHasBatteries($hasBatteries)
    {
        $this->hasBatteries = $hasBatteries;
        return $this;
    }

    /**
     * Gets as batteryTechnologyType
     *
     * Please select the Battery Technology Type from the list provided. NOTE: If battery type is lead acid, lead acid (nonspillable), lithium ion, or lithium metal, please ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @return string
     */
    public function getBatteryTechnologyType()
    {
        return $this->batteryTechnologyType;
    }

    /**
     * Sets a new batteryTechnologyType
     *
     * Please select the Battery Technology Type from the list provided. NOTE: If battery type is lead acid, lead acid (nonspillable), lithium ion, or lithium metal, please ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @param string $batteryTechnologyType
     * @return self
     */
    public function setBatteryTechnologyType($batteryTechnologyType)
    {
        $this->batteryTechnologyType = $batteryTechnologyType;
        return $this;
    }

    /**
     * Gets as hasWarranty
     *
     * Y indicates the item comes with a warranty. If an item has a warranty, then enter EITHER the warranty URL or the warranty text in the appropriate field.
     *
     * @return string
     */
    public function getHasWarranty()
    {
        return $this->hasWarranty;
    }

    /**
     * Sets a new hasWarranty
     *
     * Y indicates the item comes with a warranty. If an item has a warranty, then enter EITHER the warranty URL or the warranty text in the appropriate field.
     *
     * @param string $hasWarranty
     * @return self
     */
    public function setHasWarranty($hasWarranty)
    {
        $this->hasWarranty = $hasWarranty;
        return $this;
    }

    /**
     * Gets as warrantyURL
     *
     * If you indicated that your item has a warranty, provide either the Warranty URL or Warranty Text. The Warranty URL is the web location of the image, PDF, or link to the manufacturer's warranty page, showing the warranty and its terms, including the duration of the warranty. URLs must begin with http:// or https:// NOTE: Please remember to update the link and/or text of the warranty as the warranty changes. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @return string
     */
    public function getWarrantyURL()
    {
        return $this->warrantyURL;
    }

    /**
     * Sets a new warrantyURL
     *
     * If you indicated that your item has a warranty, provide either the Warranty URL or Warranty Text. The Warranty URL is the web location of the image, PDF, or link to the manufacturer's warranty page, showing the warranty and its terms, including the duration of the warranty. URLs must begin with http:// or https:// NOTE: Please remember to update the link and/or text of the warranty as the warranty changes. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @param string $warrantyURL
     * @return self
     */
    public function setWarrantyURL($warrantyURL)
    {
        $this->warrantyURL = $warrantyURL;
        return $this;
    }

    /**
     * Gets as warrantyText
     *
     * If you marked Y for "Has Warranty" provide the Warranty URL or Warranty Text (the full text of the warranty terms, including what is covered by the warranty and the duration of the warranty). NOTE: please remember to update the text of your warranty as your warranty changes.
     *
     * @return string
     */
    public function getWarrantyText()
    {
        return $this->warrantyText;
    }

    /**
     * Sets a new warrantyText
     *
     * If you marked Y for "Has Warranty" provide the Warranty URL or Warranty Text (the full text of the warranty terms, including what is covered by the warranty and the duration of the warranty). NOTE: please remember to update the text of your warranty as your warranty changes.
     *
     * @param string $warrantyText
     * @return self
     */
    public function setWarrantyText($warrantyText)
    {
        $this->warrantyText = $warrantyText;
        return $this;
    }

    /**
     * Gets as hasStateRestrictions
     *
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @return string
     */
    public function getHasStateRestrictions()
    {
        return $this->hasStateRestrictions;
    }

    /**
     * Sets a new hasStateRestrictions
     *
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @param string $hasStateRestrictions
     * @return self
     */
    public function setHasStateRestrictions($hasStateRestrictions)
    {
        $this->hasStateRestrictions = $hasStateRestrictions;
        return $this;
    }

    /**
     * Adds as stateRestriction
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param \WalmartDSV\StateRestrictionType $stateRestriction
     *@return self
     */
    public function addToStateRestrictions(\WalmartDSV\StateRestrictionType $stateRestriction)
    {
        $this->stateRestrictions[] = $stateRestriction;
        return $this;
    }

    /**
     * isset stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetStateRestrictions($index)
    {
        return isset($this->stateRestrictions[$index]);
    }

    /**
     * unset stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetStateRestrictions($index)
    {
        unset($this->stateRestrictions[$index]);
    }

    /**
     * Gets as stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @return \WalmartDSV\StateRestrictionType[]
     */
    public function getStateRestrictions()
    {
        return $this->stateRestrictions;
    }

    /**
     * Sets a new stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param \WalmartDSV\StateRestrictionType[] $stateRestrictions
     * @return self
     */
    public function setStateRestrictions(array $stateRestrictions)
    {
        $this->stateRestrictions = $stateRestrictions;
        return $this;
    }

    /**
     * Gets as clothingTopStyle
     *
     * Style terms descriptive of clothing tops.
     *
     * @return \WalmartDSV\ClothingTopStyleType
     */
    public function getClothingTopStyle()
    {
        return $this->clothingTopStyle;
    }

    /**
     * Sets a new clothingTopStyle
     *
     * Style terms descriptive of clothing tops.
     *
     * @param \WalmartDSV\ClothingTopStyleType $clothingTopStyle
     * @return self
     */
    public function setClothingTopStyle(\WalmartDSV\ClothingTopStyleType $clothingTopStyle)
    {
        $this->clothingTopStyle = $clothingTopStyle;
        return $this;
    }

    /**
     * Gets as dressShirtSize
     *
     * A grouping of sizes for dress shirts based on collar measurement and sleeve measurement.
     *
     * @return string
     */
    public function getDressShirtSize()
    {
        return $this->dressShirtSize;
    }

    /**
     * Sets a new dressShirtSize
     *
     * A grouping of sizes for dress shirts based on collar measurement and sleeve measurement.
     *
     * @param string $dressShirtSize
     * @return self
     */
    public function setDressShirtSize($dressShirtSize)
    {
        $this->dressShirtSize = $dressShirtSize;
        return $this;
    }

    /**
     * Gets as sleeveStyle
     *
     * Descriptive terms for the style of garment sleeves. Does not include descriptive terms for sleeve length (which appear under the Sleeve Length Style attribute).
     *
     * @return string
     */
    public function getSleeveStyle()
    {
        return $this->sleeveStyle;
    }

    /**
     * Sets a new sleeveStyle
     *
     * Descriptive terms for the style of garment sleeves. Does not include descriptive terms for sleeve length (which appear under the Sleeve Length Style attribute).
     *
     * @param string $sleeveStyle
     * @return self
     */
    public function setSleeveStyle($sleeveStyle)
    {
        $this->sleeveStyle = $sleeveStyle;
        return $this;
    }

    /**
     * Gets as sleeveLengthStyle
     *
     * Descriptive terms for sleeve length.
     *
     * @return string
     */
    public function getSleeveLengthStyle()
    {
        return $this->sleeveLengthStyle;
    }

    /**
     * Sets a new sleeveLengthStyle
     *
     * Descriptive terms for sleeve length.
     *
     * @param string $sleeveLengthStyle
     * @return self
     */
    public function setSleeveLengthStyle($sleeveLengthStyle)
    {
        $this->sleeveLengthStyle = $sleeveLengthStyle;
        return $this;
    }

    /**
     * Gets as shirtNeckStyle
     *
     * A garment's neckline or neck style.
     *
     * @return string
     */
    public function getShirtNeckStyle()
    {
        return $this->shirtNeckStyle;
    }

    /**
     * Sets a new shirtNeckStyle
     *
     * A garment's neckline or neck style.
     *
     * @param string $shirtNeckStyle
     * @return self
     */
    public function setShirtNeckStyle($shirtNeckStyle)
    {
        $this->shirtNeckStyle = $shirtNeckStyle;
        return $this;
    }

    /**
     * Gets as collarType
     *
     * Style of collar, as expressed by shape or design.
     *
     * @return string
     */
    public function getCollarType()
    {
        return $this->collarType;
    }

    /**
     * Sets a new collarType
     *
     * Style of collar, as expressed by shape or design.
     *
     * @param string $collarType
     * @return self
     */
    public function setCollarType($collarType)
    {
        $this->collarType = $collarType;
        return $this;
    }

    /**
     * Gets as jacketStyle
     *
     * Styles specific to coats and jackets.
     *
     * @return \WalmartDSV\JacketStyleType
     */
    public function getJacketStyle()
    {
        return $this->jacketStyle;
    }

    /**
     * Sets a new jacketStyle
     *
     * Styles specific to coats and jackets.
     *
     * @param \WalmartDSV\JacketStyleType $jacketStyle
     * @return self
     */
    public function setJacketStyle(\WalmartDSV\JacketStyleType $jacketStyle)
    {
        $this->jacketStyle = $jacketStyle;
        return $this;
    }

    /**
     * Gets as suitBreastingStyle
     *
     * The closure style of a suit jacket or coat; double-breasted suits have more fabric where they overlap in front and commonly have 2 rows of buttons.
     *
     * @return string
     */
    public function getSuitBreastingStyle()
    {
        return $this->suitBreastingStyle;
    }

    /**
     * Sets a new suitBreastingStyle
     *
     * The closure style of a suit jacket or coat; double-breasted suits have more fabric where they overlap in front and commonly have 2 rows of buttons.
     *
     * @param string $suitBreastingStyle
     * @return self
     */
    public function setSuitBreastingStyle($suitBreastingStyle)
    {
        $this->suitBreastingStyle = $suitBreastingStyle;
        return $this;
    }

    /**
     * Gets as sweaterStyle
     *
     * Styles specific to sweaters.
     *
     * @return \WalmartDSV\SweaterStyleType
     */
    public function getSweaterStyle()
    {
        return $this->sweaterStyle;
    }

    /**
     * Sets a new sweaterStyle
     *
     * Styles specific to sweaters.
     *
     * @param \WalmartDSV\SweaterStyleType $sweaterStyle
     * @return self
     */
    public function setSweaterStyle(\WalmartDSV\SweaterStyleType $sweaterStyle)
    {
        $this->sweaterStyle = $sweaterStyle;
        return $this;
    }

    /**
     * Gets as scarfStyle
     *
     * Styles specific to scarves.
     *
     * @return \WalmartDSV\ScarfStyleType
     */
    public function getScarfStyle()
    {
        return $this->scarfStyle;
    }

    /**
     * Sets a new scarfStyle
     *
     * Styles specific to scarves.
     *
     * @param \WalmartDSV\ScarfStyleType $scarfStyle
     * @return self
     */
    public function setScarfStyle(\WalmartDSV\ScarfStyleType $scarfStyle)
    {
        $this->scarfStyle = $scarfStyle;
        return $this;
    }

    /**
     * Gets as upperBodyStrapConfiguration
     *
     * Strap configuration style for bras, swimsuit tops, leotards and other clothing tops.
     *
     * @return \WalmartDSV\UpperBodyStrapConfigurationType
     */
    public function getUpperBodyStrapConfiguration()
    {
        return $this->upperBodyStrapConfiguration;
    }

    /**
     * Sets a new upperBodyStrapConfiguration
     *
     * Strap configuration style for bras, swimsuit tops, leotards and other clothing tops.
     *
     * @param \WalmartDSV\UpperBodyStrapConfigurationType $upperBodyStrapConfiguration
     * @return self
     */
    public function setUpperBodyStrapConfiguration(\WalmartDSV\UpperBodyStrapConfigurationType $upperBodyStrapConfiguration)
    {
        $this->upperBodyStrapConfiguration = $upperBodyStrapConfiguration;
        return $this;
    }

    /**
     * Gets as hatSize
     *
     * The sizing information specific to hats, ranging from 6 1/4 to 8 1/8. Generic sized (small/med/large) hats may not have this information (generic s/m/l size information should be entered under the "Clothing Size" attribute).
     *
     * @return string
     */
    public function getHatSize()
    {
        return $this->hatSize;
    }

    /**
     * Sets a new hatSize
     *
     * The sizing information specific to hats, ranging from 6 1/4 to 8 1/8. Generic sized (small/med/large) hats may not have this information (generic s/m/l size information should be entered under the "Clothing Size" attribute).
     *
     * @param string $hatSize
     * @return self
     */
    public function setHatSize($hatSize)
    {
        $this->hatSize = $hatSize;
        return $this;
    }

    /**
     * Gets as hatStyle
     *
     * Styles specific to hats.
     *
     * @return \WalmartDSV\HatStyleType
     */
    public function getHatStyle()
    {
        return $this->hatStyle;
    }

    /**
     * Sets a new hatStyle
     *
     * Styles specific to hats.
     *
     * @param \WalmartDSV\HatStyleType $hatStyle
     * @return self
     */
    public function setHatStyle(\WalmartDSV\HatStyleType $hatStyle)
    {
        $this->hatStyle = $hatStyle;
        return $this;
    }

    /**
     * Gets as braStyle
     *
     * Brassiere styles/defining features. Enter as many as are relevant to the bra. Do not enter strap styles here. To enter descriptive terms for strap styles, please use the "Upper Body Strap Configuration" attribute.
     *
     * @return \WalmartDSV\BraStyleType
     */
    public function getBraStyle()
    {
        return $this->braStyle;
    }

    /**
     * Sets a new braStyle
     *
     * Brassiere styles/defining features. Enter as many as are relevant to the bra. Do not enter strap styles here. To enter descriptive terms for strap styles, please use the "Upper Body Strap Configuration" attribute.
     *
     * @param \WalmartDSV\BraStyleType $braStyle
     * @return self
     */
    public function setBraStyle(\WalmartDSV\BraStyleType $braStyle)
    {
        $this->braStyle = $braStyle;
        return $this;
    }

    /**
     * Gets as braSize
     *
     * Bra size, consisting of band and cup. Use this attribute if the garment has both measurements. If the bra has standard clothing sizes (e.g. S, M, L), enter the size under "Clothing Size". NOTE: for garments that have chest size (measured as the largest part of the chest, i.e. some women's slips), use "Chest Size".
     *
     * @return string
     */
    public function getBraSize()
    {
        return $this->braSize;
    }

    /**
     * Sets a new braSize
     *
     * Bra size, consisting of band and cup. Use this attribute if the garment has both measurements. If the bra has standard clothing sizes (e.g. S, M, L), enter the size under "Clothing Size". NOTE: for garments that have chest size (measured as the largest part of the chest, i.e. some women's slips), use "Chest Size".
     *
     * @param string $braSize
     * @return self
     */
    public function setBraSize($braSize)
    {
        $this->braSize = $braSize;
        return $this;
    }

    /**
     * Gets as chestSize
     *
     * Chest measurement in inches, around the largest part of the chest. Often used for men's jackets and women's full slips.
     *
     * @return \WalmartDSV\ClothingType\ChestSizeAType
     */
    public function getChestSize()
    {
        return $this->chestSize;
    }

    /**
     * Sets a new chestSize
     *
     * Chest measurement in inches, around the largest part of the chest. Often used for men's jackets and women's full slips.
     *
     * @param \WalmartDSV\ClothingType\ChestSizeAType $chestSize
     * @return self
     */
    public function setChestSize(\WalmartDSV\ClothingType\ChestSizeAType $chestSize)
    {
        $this->chestSize = $chestSize;
        return $this;
    }

    /**
     * Gets as pantRise
     *
     * The height at which the waistline rests on the body.
     *
     * @return string
     */
    public function getPantRise()
    {
        return $this->pantRise;
    }

    /**
     * Sets a new pantRise
     *
     * The height at which the waistline rests on the body.
     *
     * @param string $pantRise
     * @return self
     */
    public function setPantRise($pantRise)
    {
        $this->pantRise = $pantRise;
        return $this;
    }

    /**
     * Gets as waistStyle
     *
     * Waist styles/defining features.
     *
     * @return \WalmartDSV\WaistStyleType
     */
    public function getWaistStyle()
    {
        return $this->waistStyle;
    }

    /**
     * Sets a new waistStyle
     *
     * Waist styles/defining features.
     *
     * @param \WalmartDSV\WaistStyleType $waistStyle
     * @return self
     */
    public function setWaistStyle(\WalmartDSV\WaistStyleType $waistStyle)
    {
        $this->waistStyle = $waistStyle;
        return $this;
    }

    /**
     * Gets as waistSize
     *
     * Waist measurement in inches, around the smallest section of the natural waist (typically located just above the belly button).
     *
     * @return \WalmartDSV\ClothingType\WaistSizeAType
     */
    public function getWaistSize()
    {
        return $this->waistSize;
    }

    /**
     * Sets a new waistSize
     *
     * Waist measurement in inches, around the smallest section of the natural waist (typically located just above the belly button).
     *
     * @param \WalmartDSV\ClothingType\WaistSizeAType $waistSize
     * @return self
     */
    public function setWaistSize(\WalmartDSV\ClothingType\WaistSizeAType $waistSize)
    {
        $this->waistSize = $waistSize;
        return $this;
    }

    /**
     * Gets as pantySize
     *
     * Panty size. Generic sized (small/med/large) panties may not have this information (generic s/m/l size information should be entered under the "Clothing Size" attribute). Clothing size style values (Plus Size, Petite, Juniors, etc) should be entered under the "Clothing Size Style" attribute.
     *
     * @return string
     */
    public function getPantySize()
    {
        return $this->pantySize;
    }

    /**
     * Sets a new pantySize
     *
     * Panty size. Generic sized (small/med/large) panties may not have this information (generic s/m/l size information should be entered under the "Clothing Size" attribute). Clothing size style values (Plus Size, Petite, Juniors, etc) should be entered under the "Clothing Size Style" attribute.
     *
     * @param string $pantySize
     * @return self
     */
    public function setPantySize($pantySize)
    {
        $this->pantySize = $pantySize;
        return $this;
    }

    /**
     * Gets as skirtLength
     *
     * The measurement of the skirt, from waist to bottom hem, in inches.
     *
     * @return \WalmartDSV\ClothingType\SkirtLengthAType
     */
    public function getSkirtLength()
    {
        return $this->skirtLength;
    }

    /**
     * Sets a new skirtLength
     *
     * The measurement of the skirt, from waist to bottom hem, in inches.
     *
     * @param \WalmartDSV\ClothingType\SkirtLengthAType $skirtLength
     * @return self
     */
    public function setSkirtLength(\WalmartDSV\ClothingType\SkirtLengthAType $skirtLength)
    {
        $this->skirtLength = $skirtLength;
        return $this;
    }

    /**
     * Gets as legOpeningCut
     *
     * How high on the body the leg opening sits. Commonly referenced for leotards, swimwear, underwear or other legless garments.
     *
     * @return string
     */
    public function getLegOpeningCut()
    {
        return $this->legOpeningCut;
    }

    /**
     * Sets a new legOpeningCut
     *
     * How high on the body the leg opening sits. Commonly referenced for leotards, swimwear, underwear or other legless garments.
     *
     * @param string $legOpeningCut
     * @return self
     */
    public function setLegOpeningCut($legOpeningCut)
    {
        $this->legOpeningCut = $legOpeningCut;
        return $this;
    }

    /**
     * Gets as pantLegCut
     *
     * Common style terms describing how the garment leg has been cut to make a specific sillhoutte/shape.
     *
     * @return string
     */
    public function getPantLegCut()
    {
        return $this->pantLegCut;
    }

    /**
     * Sets a new pantLegCut
     *
     * Common style terms describing how the garment leg has been cut to make a specific sillhoutte/shape.
     *
     * @param string $pantLegCut
     * @return self
     */
    public function setPantLegCut($pantLegCut)
    {
        $this->pantLegCut = $pantLegCut;
        return $this;
    }

    /**
     * Gets as jeanStyle
     *
     * Style terms specific to jeans.
     *
     * @return \WalmartDSV\JeanStyleType
     */
    public function getJeanStyle()
    {
        return $this->jeanStyle;
    }

    /**
     * Sets a new jeanStyle
     *
     * Style terms specific to jeans.
     *
     * @param \WalmartDSV\JeanStyleType $jeanStyle
     * @return self
     */
    public function setJeanStyle(\WalmartDSV\JeanStyleType $jeanStyle)
    {
        $this->jeanStyle = $jeanStyle;
        return $this;
    }

    /**
     * Gets as jeanWash
     *
     * Description of post-process wash treatment effecting the appearance of dye and fabric texture, specific to jeans.
     *
     * @return string
     */
    public function getJeanWash()
    {
        return $this->jeanWash;
    }

    /**
     * Sets a new jeanWash
     *
     * Description of post-process wash treatment effecting the appearance of dye and fabric texture, specific to jeans.
     *
     * @param string $jeanWash
     * @return self
     */
    public function setJeanWash($jeanWash)
    {
        $this->jeanWash = $jeanWash;
        return $this;
    }

    /**
     * Gets as jeanFinish
     *
     * Fabric finishes specific to jeans.
     *
     * @return \WalmartDSV\JeanFinishType
     */
    public function getJeanFinish()
    {
        return $this->jeanFinish;
    }

    /**
     * Sets a new jeanFinish
     *
     * Fabric finishes specific to jeans.
     *
     * @param \WalmartDSV\JeanFinishType $jeanFinish
     * @return self
     */
    public function setJeanFinish(\WalmartDSV\JeanFinishType $jeanFinish)
    {
        $this->jeanFinish = $jeanFinish;
        return $this;
    }

    /**
     * Gets as pantSize
     *
     * A composite of Waist Size and Inseam. Do not fill in this attribute if you have separately filled in both "Waist Size" and "Inseam". If the pant size is in numbers (e.g. 12) or standard sizes (e.g. Large), enter the information under "Clothing Size".
     *
     * @return string
     */
    public function getPantSize()
    {
        return $this->pantSize;
    }

    /**
     * Sets a new pantSize
     *
     * A composite of Waist Size and Inseam. Do not fill in this attribute if you have separately filled in both "Waist Size" and "Inseam". If the pant size is in numbers (e.g. 12) or standard sizes (e.g. Large), enter the information under "Clothing Size".
     *
     * @param string $pantSize
     * @return self
     */
    public function setPantSize($pantSize)
    {
        $this->pantSize = $pantSize;
        return $this;
    }

    /**
     * Gets as pantFit
     *
     * Terms that describe the way pants will fit when worn.
     *
     * @return \WalmartDSV\PantFitType
     */
    public function getPantFit()
    {
        return $this->pantFit;
    }

    /**
     * Sets a new pantFit
     *
     * Terms that describe the way pants will fit when worn.
     *
     * @param \WalmartDSV\PantFitType $pantFit
     * @return self
     */
    public function setPantFit(\WalmartDSV\PantFitType $pantFit)
    {
        $this->pantFit = $pantFit;
        return $this;
    }

    /**
     * Gets as pantStyle
     *
     * Style terms specific to pants.
     *
     * @return string
     */
    public function getPantStyle()
    {
        return $this->pantStyle;
    }

    /**
     * Sets a new pantStyle
     *
     * Style terms specific to pants.
     *
     * @param string $pantStyle
     * @return self
     */
    public function setPantStyle($pantStyle)
    {
        $this->pantStyle = $pantStyle;
        return $this;
    }

    /**
     * Gets as beltStyle
     *
     * Styles specific to belts.
     *
     * @return \WalmartDSV\BeltStyleType
     */
    public function getBeltStyle()
    {
        return $this->beltStyle;
    }

    /**
     * Sets a new beltStyle
     *
     * Styles specific to belts.
     *
     * @param \WalmartDSV\BeltStyleType $beltStyle
     * @return self
     */
    public function setBeltStyle(\WalmartDSV\BeltStyleType $beltStyle)
    {
        $this->beltStyle = $beltStyle;
        return $this;
    }

    /**
     * Gets as beltBuckleStyle
     *
     * Belt buckle configurations.
     *
     * @return string
     */
    public function getBeltBuckleStyle()
    {
        return $this->beltBuckleStyle;
    }

    /**
     * Sets a new beltBuckleStyle
     *
     * Belt buckle configurations.
     *
     * @param string $beltBuckleStyle
     * @return self
     */
    public function setBeltBuckleStyle($beltBuckleStyle)
    {
        $this->beltBuckleStyle = $beltBuckleStyle;
        return $this;
    }

    /**
     * Gets as pantyStyle
     *
     * Descriptive styles common to underwear bottoms and lower body elements of swimwear and leotards.
     *
     * @return string
     */
    public function getPantyStyle()
    {
        return $this->pantyStyle;
    }

    /**
     * Sets a new pantyStyle
     *
     * Descriptive styles common to underwear bottoms and lower body elements of swimwear and leotards.
     *
     * @param string $pantyStyle
     * @return self
     */
    public function setPantyStyle($pantyStyle)
    {
        $this->pantyStyle = $pantyStyle;
        return $this;
    }

    /**
     * Gets as shortsStyle
     *
     * Styles specific to shorts.
     *
     * @return \WalmartDSV\ShortsStyleType
     */
    public function getShortsStyle()
    {
        return $this->shortsStyle;
    }

    /**
     * Sets a new shortsStyle
     *
     * Styles specific to shorts.
     *
     * @param \WalmartDSV\ShortsStyleType $shortsStyle
     * @return self
     */
    public function setShortsStyle(\WalmartDSV\ShortsStyleType $shortsStyle)
    {
        $this->shortsStyle = $shortsStyle;
        return $this;
    }

    /**
     * Gets as skirtAndDressCut
     *
     * Terms describing a garment's skirt style, applicable to dresses and skirt products.
     *
     * @return \WalmartDSV\SkirtStyleType
     */
    public function getSkirtAndDressCut()
    {
        return $this->skirtAndDressCut;
    }

    /**
     * Sets a new skirtAndDressCut
     *
     * Terms describing a garment's skirt style, applicable to dresses and skirt products.
     *
     * @param \WalmartDSV\SkirtStyleType $skirtAndDressCut
     * @return self
     */
    public function setSkirtAndDressCut(\WalmartDSV\SkirtStyleType $skirtAndDressCut)
    {
        $this->skirtAndDressCut = $skirtAndDressCut;
        return $this;
    }

    /**
     * Gets as skirtLengthStyle
     *
     * Descriptive terms for length specific to skirts and dresses.
     *
     * @return string
     */
    public function getSkirtLengthStyle()
    {
        return $this->skirtLengthStyle;
    }

    /**
     * Sets a new skirtLengthStyle
     *
     * Descriptive terms for length specific to skirts and dresses.
     *
     * @param string $skirtLengthStyle
     * @return self
     */
    public function setSkirtLengthStyle($skirtLengthStyle)
    {
        $this->skirtLengthStyle = $skirtLengthStyle;
        return $this;
    }

    /**
     * Gets as hosieryStyle
     *
     * Styles/features specific to tights and hosiery.
     *
     * @return \WalmartDSV\HosieryStyleType
     */
    public function getHosieryStyle()
    {
        return $this->hosieryStyle;
    }

    /**
     * Sets a new hosieryStyle
     *
     * Styles/features specific to tights and hosiery.
     *
     * @param \WalmartDSV\HosieryStyleType $hosieryStyle
     * @return self
     */
    public function setHosieryStyle(\WalmartDSV\HosieryStyleType $hosieryStyle)
    {
        $this->hosieryStyle = $hosieryStyle;
        return $this;
    }

    /**
     * Gets as tightsSheerness
     *
     * Sheerness/opacities specific to tights and hosiery.
     *
     * @return string
     */
    public function getTightsSheerness()
    {
        return $this->tightsSheerness;
    }

    /**
     * Sets a new tightsSheerness
     *
     * Sheerness/opacities specific to tights and hosiery.
     *
     * @param string $tightsSheerness
     * @return self
     */
    public function setTightsSheerness($tightsSheerness)
    {
        $this->tightsSheerness = $tightsSheerness;
        return $this;
    }

    /**
     * Gets as underwearStyle
     *
     * Descriptive styles specific to underwear.
     *
     * @return \WalmartDSV\UnderwearStyleType
     */
    public function getUnderwearStyle()
    {
        return $this->underwearStyle;
    }

    /**
     * Sets a new underwearStyle
     *
     * Descriptive styles specific to underwear.
     *
     * @param \WalmartDSV\UnderwearStyleType $underwearStyle
     * @return self
     */
    public function setUnderwearStyle(\WalmartDSV\UnderwearStyleType $underwearStyle)
    {
        $this->underwearStyle = $underwearStyle;
        return $this;
    }

    /**
     * Gets as sockSize
     *
     * Sock size. Generic sized (small/med/large) socks may not have this information (generic s/m/l size information should be entered under the clothing size attribute).
     *
     * @return string
     */
    public function getSockSize()
    {
        return $this->sockSize;
    }

    /**
     * Sets a new sockSize
     *
     * Sock size. Generic sized (small/med/large) socks may not have this information (generic s/m/l size information should be entered under the clothing size attribute).
     *
     * @param string $sockSize
     * @return self
     */
    public function setSockSize($sockSize)
    {
        $this->sockSize = $sockSize;
        return $this;
    }

    /**
     * Gets as sockStyle
     *
     * Styles specific to socks. Does not include descriptive terms for sock height - those are in the Sock Rise attribute.
     *
     * @return string
     */
    public function getSockStyle()
    {
        return $this->sockStyle;
    }

    /**
     * Sets a new sockStyle
     *
     * Styles specific to socks. Does not include descriptive terms for sock height - those are in the Sock Rise attribute.
     *
     * @param string $sockStyle
     * @return self
     */
    public function setSockStyle($sockStyle)
    {
        $this->sockStyle = $sockStyle;
        return $this;
    }

    /**
     * Gets as sockRise
     *
     * Descriptive terms for sock height.
     *
     * @return string
     */
    public function getSockRise()
    {
        return $this->sockRise;
    }

    /**
     * Sets a new sockRise
     *
     * Descriptive terms for sock height.
     *
     * @param string $sockRise
     * @return self
     */
    public function setSockRise($sockRise)
    {
        $this->sockRise = $sockRise;
        return $this;
    }

    /**
     * Adds as fabricContentValue
     *
     * Material makeup of the item.
     *
     * @param \WalmartDSV\FabricContentValueType $fabricContentValue
     *@return self
     */
    public function addToFabricContent(\WalmartDSV\FabricContentValueType $fabricContentValue)
    {
        $this->fabricContent[] = $fabricContentValue;
        return $this;
    }

    /**
     * isset fabricContent
     *
     * Material makeup of the item.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFabricContent($index)
    {
        return isset($this->fabricContent[$index]);
    }

    /**
     * unset fabricContent
     *
     * Material makeup of the item.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFabricContent($index)
    {
        unset($this->fabricContent[$index]);
    }

    /**
     * Gets as fabricContent
     *
     * Material makeup of the item.
     *
     * @return \WalmartDSV\FabricContentValueType[]
     */
    public function getFabricContent()
    {
        return $this->fabricContent;
    }

    /**
     * Sets a new fabricContent
     *
     * Material makeup of the item.
     *
     * @param \WalmartDSV\FabricContentValueType[] $fabricContent
     * @return self
     */
    public function setFabricContent(array $fabricContent)
    {
        $this->fabricContent = $fabricContent;
        return $this;
    }

    /**
     * Adds as fabricCareInstruction
     *
     * Describes how the fabric should be cleaned. Enter details of the fabric care label found on the item. (For garments, typically located inside on the top of the back or the lower left side.)
     *
     * @return self
     * @param string $fabricCareInstruction
     */
    public function addToFabricCareInstructions($fabricCareInstruction)
    {
        $this->fabricCareInstructions[] = $fabricCareInstruction;
        return $this;
    }

    /**
     * isset fabricCareInstructions
     *
     * Describes how the fabric should be cleaned. Enter details of the fabric care label found on the item. (For garments, typically located inside on the top of the back or the lower left side.)
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFabricCareInstructions($index)
    {
        return isset($this->fabricCareInstructions[$index]);
    }

    /**
     * unset fabricCareInstructions
     *
     * Describes how the fabric should be cleaned. Enter details of the fabric care label found on the item. (For garments, typically located inside on the top of the back or the lower left side.)
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFabricCareInstructions($index)
    {
        unset($this->fabricCareInstructions[$index]);
    }

    /**
     * Gets as fabricCareInstructions
     *
     * Describes how the fabric should be cleaned. Enter details of the fabric care label found on the item. (For garments, typically located inside on the top of the back or the lower left side.)
     *
     * @return string[]
     */
    public function getFabricCareInstructions()
    {
        return $this->fabricCareInstructions;
    }

    /**
     * Sets a new fabricCareInstructions
     *
     * Describes how the fabric should be cleaned. Enter details of the fabric care label found on the item. (For garments, typically located inside on the top of the back or the lower left side.)
     *
     * @param string $fabricCareInstructions
     * @return self
     */
    public function setFabricCareInstructions(array $fabricCareInstructions)
    {
        $this->fabricCareInstructions = $fabricCareInstructions;
        return $this;
    }

    /**
     * Gets as accentColor
     *
     * A secondary product color.
     *
     * @return string
     */
    public function getAccentColor()
    {
        return $this->accentColor;
    }

    /**
     * Sets a new accentColor
     *
     * A secondary product color.
     *
     * @param string $accentColor
     * @return self
     */
    public function setAccentColor($accentColor)
    {
        $this->accentColor = $accentColor;
        return $this;
    }

    /**
     * Gets as clothingWeight
     *
     * Commonly used in retail clothing, these terms describe the density or weight of fabric material.
     *
     * @return string
     */
    public function getClothingWeight()
    {
        return $this->clothingWeight;
    }

    /**
     * Sets a new clothingWeight
     *
     * Commonly used in retail clothing, these terms describe the density or weight of fabric material.
     *
     * @param string $clothingWeight
     * @return self
     */
    public function setClothingWeight($clothingWeight)
    {
        $this->clothingWeight = $clothingWeight;
        return $this;
    }

    /**
     * Gets as clothingStyle
     *
     * Styles and designations that apply generally to various types of clothing.
     *
     * @return \WalmartDSV\ClothingStyleType
     */
    public function getClothingStyle()
    {
        return $this->clothingStyle;
    }

    /**
     * Sets a new clothingStyle
     *
     * Styles and designations that apply generally to various types of clothing.
     *
     * @param \WalmartDSV\ClothingStyleType $clothingStyle
     * @return self
     */
    public function setClothingStyle(\WalmartDSV\ClothingStyleType $clothingStyle)
    {
        $this->clothingStyle = $clothingStyle;
        return $this;
    }

    /**
     * Gets as clothingFit
     *
     * Terms that describe the way a garment will fit when worn. Does not include fit values specific to pants.
     *
     * @return string
     */
    public function getClothingFit()
    {
        return $this->clothingFit;
    }

    /**
     * Sets a new clothingFit
     *
     * Terms that describe the way a garment will fit when worn. Does not include fit values specific to pants.
     *
     * @param string $clothingFit
     * @return self
     */
    public function setClothingFit($clothingFit)
    {
        $this->clothingFit = $clothingFit;
        return $this;
    }

    /**
     * Gets as clothingCut
     *
     * Common clothing cut styles.
     *
     * @return \WalmartDSV\ClothingCutType
     */
    public function getClothingCut()
    {
        return $this->clothingCut;
    }

    /**
     * Sets a new clothingCut
     *
     * Common clothing cut styles.
     *
     * @param \WalmartDSV\ClothingCutType $clothingCut
     * @return self
     */
    public function setClothingCut(\WalmartDSV\ClothingCutType $clothingCut)
    {
        $this->clothingCut = $clothingCut;
        return $this;
    }

    /**
     * Gets as clothingLengthStyle
     *
     * Descriptive terms for where on the lower-body a garment ends.
     *
     * @return string
     */
    public function getClothingLengthStyle()
    {
        return $this->clothingLengthStyle;
    }

    /**
     * Sets a new clothingLengthStyle
     *
     * Descriptive terms for where on the lower-body a garment ends.
     *
     * @param string $clothingLengthStyle
     * @return self
     */
    public function setClothingLengthStyle($clothingLengthStyle)
    {
        $this->clothingLengthStyle = $clothingLengthStyle;
        return $this;
    }

    /**
     * Gets as fastenerType
     *
     * The type of fastener used to keep a garment closed on the wearer and to facilitate putting on the garment.
     *
     * @return string
     */
    public function getFastenerType()
    {
        return $this->fastenerType;
    }

    /**
     * Sets a new fastenerType
     *
     * The type of fastener used to keep a garment closed on the wearer and to facilitate putting on the garment.
     *
     * @param string $fastenerType
     * @return self
     */
    public function setFastenerType($fastenerType)
    {
        $this->fastenerType = $fastenerType;
        return $this;
    }

    /**
     * Gets as swimsuitStyle
     *
     * Descriptive styles common to swimwear. Style terms specific to the bottom half of swimwear appear under the Underpant/Swim Bottom Style attribute.
     *
     * @return string
     */
    public function getSwimsuitStyle()
    {
        return $this->swimsuitStyle;
    }

    /**
     * Sets a new swimsuitStyle
     *
     * Descriptive styles common to swimwear. Style terms specific to the bottom half of swimwear appear under the Underpant/Swim Bottom Style attribute.
     *
     * @param string $swimsuitStyle
     * @return self
     */
    public function setSwimsuitStyle($swimsuitStyle)
    {
        $this->swimsuitStyle = $swimsuitStyle;
        return $this;
    }

    /**
     * Gets as dressStyle
     *
     * Style terms specific to dresses. Other style terms that specifically describe a dress's skirt may be found under the Skirt Style attribute. Some values are shared between skirts and dresses: both "mini" dresses and "mini" skirts exist as separate products.
     *
     * @return string
     */
    public function getDressStyle()
    {
        return $this->dressStyle;
    }

    /**
     * Sets a new dressStyle
     *
     * Style terms specific to dresses. Other style terms that specifically describe a dress's skirt may be found under the Skirt Style attribute. Some values are shared between skirts and dresses: both "mini" dresses and "mini" skirts exist as separate products.
     *
     * @param string $dressStyle
     * @return self
     */
    public function setDressStyle($dressStyle)
    {
        $this->dressStyle = $dressStyle;
        return $this;
    }

    /**
     * Gets as gotsCertification
     *
     * Y indicates the product is certified under requirements of the Global Organic Textile Standard.
     *
     * @return string
     */
    public function getGotsCertification()
    {
        return $this->gotsCertification;
    }

    /**
     * Sets a new gotsCertification
     *
     * Y indicates the product is certified under requirements of the Global Organic Textile Standard.
     *
     * @param string $gotsCertification
     * @return self
     */
    public function setGotsCertification($gotsCertification)
    {
        $this->gotsCertification = $gotsCertification;
        return $this;
    }

    /**
     * Gets as theme
     *
     * A dominant idea, meaning, or setting applied to an item. Used in a wide range of products including decorative objects, clothing, toys, and furniture. Can be an important selection criteria for consumers who want to achieve a particular ambiance for room décor or for a special occasion.
     *
     * @return \WalmartDSV\ThemeType
     */
    public function getTheme()
    {
        return $this->theme;
    }

    /**
     * Sets a new theme
     *
     * A dominant idea, meaning, or setting applied to an item. Used in a wide range of products including decorative objects, clothing, toys, and furniture. Can be an important selection criteria for consumers who want to achieve a particular ambiance for room décor or for a special occasion.
     *
     * @param \WalmartDSV\ThemeType $theme
     * @return self
     */
    public function setTheme(\WalmartDSV\ThemeType $theme)
    {
        $this->theme = $theme;
        return $this;
    }

    /**
     * Gets as character
     *
     * A person or entity portrayed in print or visual media. A character might be a fictional personality or an actual living person.
     *
     * @return \WalmartDSV\CharacterType
     */
    public function getCharacter()
    {
        return $this->character;
    }

    /**
     * Sets a new character
     *
     * A person or entity portrayed in print or visual media. A character might be a fictional personality or an actual living person.
     *
     * @param \WalmartDSV\CharacterType $character
     * @return self
     */
    public function setCharacter(\WalmartDSV\CharacterType $character)
    {
        $this->character = $character;
        return $this;
    }

    /**
     * Adds as globalBrandLicenseValue
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @return self
     * @param string $globalBrandLicenseValue
     */
    public function addToGlobalBrandLicense($globalBrandLicenseValue)
    {
        $this->globalBrandLicense[] = $globalBrandLicenseValue;
        return $this;
    }

    /**
     * isset globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @param int|string $index
     * @return bool
     */
    public function issetGlobalBrandLicense($index)
    {
        return isset($this->globalBrandLicense[$index]);
    }

    /**
     * unset globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @param int|string $index
     * @return void
     */
    public function unsetGlobalBrandLicense($index)
    {
        unset($this->globalBrandLicense[$index]);
    }

    /**
     * Gets as globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @return string[]
     */
    public function getGlobalBrandLicense()
    {
        return $this->globalBrandLicense;
    }

    /**
     * Sets a new globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @param string $globalBrandLicense
     * @return self
     */
    public function setGlobalBrandLicense(array $globalBrandLicense)
    {
        $this->globalBrandLicense = $globalBrandLicense;
        return $this;
    }

    /**
     * Gets as sportsLeague
     *
     * If your item has any association with a specific sports league, enter the league name. Abbreviations are fine. NOTE: This attribute flags an item for inclusion in the online fan shop.
     *
     * @return \WalmartDSV\SportsLeagueType
     */
    public function getSportsLeague()
    {
        return $this->sportsLeague;
    }

    /**
     * Sets a new sportsLeague
     *
     * If your item has any association with a specific sports league, enter the league name. Abbreviations are fine. NOTE: This attribute flags an item for inclusion in the online fan shop.
     *
     * @param \WalmartDSV\SportsLeagueType $sportsLeague
     * @return self
     */
    public function setSportsLeague(\WalmartDSV\SportsLeagueType $sportsLeague)
    {
        $this->sportsLeague = $sportsLeague;
        return $this;
    }

    /**
     * Gets as sportsTeam
     *
     * If your item has any association with a specific sports team, enter the team name. NOTE: This attribute flags an item for inclusion in the online fan shop.
     *
     * @return \WalmartDSV\SportsTeamType
     */
    public function getSportsTeam()
    {
        return $this->sportsTeam;
    }

    /**
     * Sets a new sportsTeam
     *
     * If your item has any association with a specific sports team, enter the team name. NOTE: This attribute flags an item for inclusion in the online fan shop.
     *
     * @param \WalmartDSV\SportsTeamType $sportsTeam
     * @return self
     */
    public function setSportsTeam(\WalmartDSV\SportsTeamType $sportsTeam)
    {
        $this->sportsTeam = $sportsTeam;
        return $this;
    }

    /**
     * Gets as occasion
     *
     * The particular target time, event, or holiday for the product.
     *
     * @return \WalmartDSV\OccasionType
     */
    public function getOccasion()
    {
        return $this->occasion;
    }

    /**
     * Sets a new occasion
     *
     * The particular target time, event, or holiday for the product.
     *
     * @param \WalmartDSV\OccasionType $occasion
     * @return self
     */
    public function setOccasion(\WalmartDSV\OccasionType $occasion)
    {
        $this->occasion = $occasion;
        return $this;
    }

    /**
     * Gets as activity
     *
     * The general type of activity one might perform while wearing this garment.
     *
     * @return \WalmartDSV\ActivityType
     */
    public function getActivity()
    {
        return $this->activity;
    }

    /**
     * Sets a new activity
     *
     * The general type of activity one might perform while wearing this garment.
     *
     * @param \WalmartDSV\ActivityType $activity
     * @return self
     */
    public function setActivity(\WalmartDSV\ActivityType $activity)
    {
        $this->activity = $activity;
        return $this;
    }

    /**
     * Gets as sport
     *
     * If the product is sports-related, the name of the specific sport depicted on the product, or the target sport for the product use
     *
     * @return \WalmartDSV\SportType
     */
    public function getSport()
    {
        return $this->sport;
    }

    /**
     * Sets a new sport
     *
     * If the product is sports-related, the name of the specific sport depicted on the product, or the target sport for the product use
     *
     * @param \WalmartDSV\SportType $sport
     * @return self
     */
    public function setSport(\WalmartDSV\SportType $sport)
    {
        $this->sport = $sport;
        return $this;
    }

    /**
     * Gets as season
     *
     * If designed to be used during a specific type of year, the appropriate season this item may be used.
     *
     * @return \WalmartDSV\SeasonType
     */
    public function getSeason()
    {
        return $this->season;
    }

    /**
     * Sets a new season
     *
     * If designed to be used during a specific type of year, the appropriate season this item may be used.
     *
     * @param \WalmartDSV\SeasonType $season
     * @return self
     */
    public function setSeason(\WalmartDSV\SeasonType $season)
    {
        $this->season = $season;
        return $this;
    }

    /**
     * Gets as weather
     *
     * The type of weather the clothing or fashion accessory was designed for.
     *
     * @return \WalmartDSV\WeatherType
     */
    public function getWeather()
    {
        return $this->weather;
    }

    /**
     * Sets a new weather
     *
     * The type of weather the clothing or fashion accessory was designed for.
     *
     * @param \WalmartDSV\WeatherType $weather
     * @return self
     */
    public function setWeather(\WalmartDSV\WeatherType $weather)
    {
        $this->weather = $weather;
        return $this;
    }

    /**
     * Gets as isMaternity
     *
     * Is this item marketed specifically to women who are pregnant?
     *
     * @return string
     */
    public function getIsMaternity()
    {
        return $this->isMaternity;
    }

    /**
     * Sets a new isMaternity
     *
     * Is this item marketed specifically to women who are pregnant?
     *
     * @param string $isMaternity
     * @return self
     */
    public function setIsMaternity($isMaternity)
    {
        $this->isMaternity = $isMaternity;
        return $this;
    }

    /**
     * Gets as academicInstitution
     *
     * Name of College or other school. This is to distinguish the school from its sports team.
     *
     * @return string
     */
    public function getAcademicInstitution()
    {
        return $this->academicInstitution;
    }

    /**
     * Sets a new academicInstitution
     *
     * Name of College or other school. This is to distinguish the school from its sports team.
     *
     * @param string $academicInstitution
     * @return self
     */
    public function setAcademicInstitution($academicInstitution)
    {
        $this->academicInstitution = $academicInstitution;
        return $this;
    }

    /**
     * Gets as athlete
     *
     * A well-known athlete associated with a product, if applicable. This is used to group items in Fan Shop, not to describe a line of clothing.
     *
     * @return \WalmartDSV\AthleteType
     */
    public function getAthlete()
    {
        return $this->athlete;
    }

    /**
     * Sets a new athlete
     *
     * A well-known athlete associated with a product, if applicable. This is used to group items in Fan Shop, not to describe a line of clothing.
     *
     * @param \WalmartDSV\AthleteType $athlete
     * @return self
     */
    public function setAthlete(\WalmartDSV\AthleteType $athlete)
    {
        $this->athlete = $athlete;
        return $this;
    }

    /**
     * Gets as autographedBy
     *
     * The full name of the person who has autographed this copy.
     *
     * @return string
     */
    public function getAutographedBy()
    {
        return $this->autographedBy;
    }

    /**
     * Sets a new autographedBy
     *
     * The full name of the person who has autographed this copy.
     *
     * @param string $autographedBy
     * @return self
     */
    public function setAutographedBy($autographedBy)
    {
        $this->autographedBy = $autographedBy;
        return $this;
    }

    /**
     * Gets as braBandSize
     *
     * A measurement in inches of the band of the bra, which fits around the ribcage just under the bust. Use this if the garment has a band size only or to separate band and cup size for search discovery.
     *
     * @return \WalmartDSV\ClothingType\BraBandSizeAType
     */
    public function getBraBandSize()
    {
        return $this->braBandSize;
    }

    /**
     * Sets a new braBandSize
     *
     * A measurement in inches of the band of the bra, which fits around the ribcage just under the bust. Use this if the garment has a band size only or to separate band and cup size for search discovery.
     *
     * @param \WalmartDSV\ClothingType\BraBandSizeAType $braBandSize
     * @return self
     */
    public function setBraBandSize(\WalmartDSV\ClothingType\BraBandSizeAType $braBandSize)
    {
        $this->braBandSize = $braBandSize;
        return $this;
    }

    /**
     * Gets as braCupSize
     *
     * A letter size to optimize bra fit, calculated by measuring the chest at its widest point and subtracting the bra band size. 1 inch corresponds to 1 cup letter (e.g. if the difference is 5, the bra cup size is DD or E). Use this to record separate cup sizes for search discovery.
     *
     * @return string
     */
    public function getBraCupSize()
    {
        return $this->braCupSize;
    }

    /**
     * Sets a new braCupSize
     *
     * A letter size to optimize bra fit, calculated by measuring the chest at its widest point and subtracting the bra band size. 1 inch corresponds to 1 cup letter (e.g. if the difference is 5, the bra cup size is DD or E). Use this to record separate cup sizes for search discovery.
     *
     * @param string $braCupSize
     * @return self
     */
    public function setBraCupSize($braCupSize)
    {
        $this->braCupSize = $braCupSize;
        return $this;
    }

    /**
     * Gets as neckSize
     *
     * Neck size in inches.
     *
     * @return \WalmartDSV\ClothingType\NeckSizeAType
     */
    public function getNeckSize()
    {
        return $this->neckSize;
    }

    /**
     * Sets a new neckSize
     *
     * Neck size in inches.
     *
     * @param \WalmartDSV\ClothingType\NeckSizeAType $neckSize
     * @return self
     */
    public function setNeckSize(\WalmartDSV\ClothingType\NeckSizeAType $neckSize)
    {
        $this->neckSize = $neckSize;
        return $this;
    }

    /**
     * Gets as sleeveLength
     *
     * The distance from the shoulder to the bottom hem of the sleeve, measured in inches.
     *
     * @return \WalmartDSV\ClothingType\SleeveLengthAType
     */
    public function getSleeveLength()
    {
        return $this->sleeveLength;
    }

    /**
     * Sets a new sleeveLength
     *
     * The distance from the shoulder to the bottom hem of the sleeve, measured in inches.
     *
     * @param \WalmartDSV\ClothingType\SleeveLengthAType $sleeveLength
     * @return self
     */
    public function setSleeveLength(\WalmartDSV\ClothingType\SleeveLengthAType $sleeveLength)
    {
        $this->sleeveLength = $sleeveLength;
        return $this;
    }

    /**
     * Gets as inseam
     *
     * For pants, the distance from the bottom of the leg to the seam in the crotch, measured in inches.
     *
     * @return \WalmartDSV\ClothingType\InseamAType
     */
    public function getInseam()
    {
        return $this->inseam;
    }

    /**
     * Sets a new inseam
     *
     * For pants, the distance from the bottom of the leg to the seam in the crotch, measured in inches.
     *
     * @param \WalmartDSV\ClothingType\InseamAType $inseam
     * @return self
     */
    public function setInseam(\WalmartDSV\ClothingType\InseamAType $inseam)
    {
        $this->inseam = $inseam;
        return $this;
    }

    /**
     * Gets as isMadeFromRecycledMaterial
     *
     * Indicates that the item is made from recycled materials.
     *
     * @return string
     */
    public function getIsMadeFromRecycledMaterial()
    {
        return $this->isMadeFromRecycledMaterial;
    }

    /**
     * Sets a new isMadeFromRecycledMaterial
     *
     * Indicates that the item is made from recycled materials.
     *
     * @param string $isMadeFromRecycledMaterial
     * @return self
     */
    public function setIsMadeFromRecycledMaterial($isMadeFromRecycledMaterial)
    {
        $this->isMadeFromRecycledMaterial = $isMadeFromRecycledMaterial;
        return $this;
    }

    /**
     * Adds as recycledMaterialContentValue
     *
     * If item contains reused/recycled material, the percentage of all recycled material used to produce the item. This can also include specific material composition; cushions made from 30% recycled cotton fabric. Used to highlight sustainability to the customer.
     *
     * @param \WalmartDSV\RecycledMaterialContentValueType $recycledMaterialContentValue
     *@return self
     */
    public function addToRecycledMaterialContent(\WalmartDSV\RecycledMaterialContentValueType $recycledMaterialContentValue)
    {
        $this->recycledMaterialContent[] = $recycledMaterialContentValue;
        return $this;
    }

    /**
     * isset recycledMaterialContent
     *
     * If item contains reused/recycled material, the percentage of all recycled material used to produce the item. This can also include specific material composition; cushions made from 30% recycled cotton fabric. Used to highlight sustainability to the customer.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetRecycledMaterialContent($index)
    {
        return isset($this->recycledMaterialContent[$index]);
    }

    /**
     * unset recycledMaterialContent
     *
     * If item contains reused/recycled material, the percentage of all recycled material used to produce the item. This can also include specific material composition; cushions made from 30% recycled cotton fabric. Used to highlight sustainability to the customer.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetRecycledMaterialContent($index)
    {
        unset($this->recycledMaterialContent[$index]);
    }

    /**
     * Gets as recycledMaterialContent
     *
     * If item contains reused/recycled material, the percentage of all recycled material used to produce the item. This can also include specific material composition; cushions made from 30% recycled cotton fabric. Used to highlight sustainability to the customer.
     *
     * @return \WalmartDSV\RecycledMaterialContentValueType[]
     */
    public function getRecycledMaterialContent()
    {
        return $this->recycledMaterialContent;
    }

    /**
     * Sets a new recycledMaterialContent
     *
     * If item contains reused/recycled material, the percentage of all recycled material used to produce the item. This can also include specific material composition; cushions made from 30% recycled cotton fabric. Used to highlight sustainability to the customer.
     *
     * @param \WalmartDSV\RecycledMaterialContentValueType[] $recycledMaterialContent
     * @return self
     */
    public function setRecycledMaterialContent(array $recycledMaterialContent)
    {
        $this->recycledMaterialContent = $recycledMaterialContent;
        return $this;
    }

    /**
     * Adds as feature
     *
     * List notable features of the item.
     *
     * @return self
     * @param string $feature
     */
    public function addToFeatures($feature)
    {
        $this->features[] = $feature;
        return $this;
    }

    /**
     * isset features
     *
     * List notable features of the item.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFeatures($index)
    {
        return isset($this->features[$index]);
    }

    /**
     * unset features
     *
     * List notable features of the item.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFeatures($index)
    {
        unset($this->features[$index]);
    }

    /**
     * Gets as features
     *
     * List notable features of the item.
     *
     * @return string[]
     */
    public function getFeatures()
    {
        return $this->features;
    }

    /**
     * Sets a new features
     *
     * List notable features of the item.
     *
     * @param string $features
     * @return self
     */
    public function setFeatures(array $features)
    {
        $this->features = $features;
        return $this;
    }

    /**
     * Gets as keywords
     *
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @return string
     */
    public function getKeywords()
    {
        return $this->keywords;
    }

    /**
     * Sets a new keywords
     *
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @param string $keywords
     * @return self
     */
    public function setKeywords($keywords)
    {
        $this->keywords = $keywords;
        return $this;
    }

    /**
     * Adds as swatchImage
     *
     * @param \WalmartDSV\ClothingType\SwatchImagesAType\SwatchImageAType $swatchImage
     *@return self
     */
    public function addToSwatchImages(\WalmartDSV\ClothingType\SwatchImagesAType\SwatchImageAType $swatchImage)
    {
        $this->swatchImages[] = $swatchImage;
        return $this;
    }

    /**
     * isset swatchImages
     *
     * @param int|string $index
     * @return bool
     */
    public function issetSwatchImages($index)
    {
        return isset($this->swatchImages[$index]);
    }

    /**
     * unset swatchImages
     *
     * @param int|string $index
     * @return void
     */
    public function unsetSwatchImages($index)
    {
        unset($this->swatchImages[$index]);
    }

    /**
     * Gets as swatchImages
     *
     * @return \WalmartDSV\ClothingType\SwatchImagesAType\SwatchImageAType[]
     */
    public function getSwatchImages()
    {
        return $this->swatchImages;
    }

    /**
     * Sets a new swatchImages
     *
     * @param \WalmartDSV\ClothingType\SwatchImagesAType\SwatchImageAType[] $swatchImages
     * @return self
     */
    public function setSwatchImages(array $swatchImages)
    {
        $this->swatchImages = $swatchImages;
        return $this;
    }


}

