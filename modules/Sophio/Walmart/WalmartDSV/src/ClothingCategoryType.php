<?php

namespace WalmartDSV;

/**
 * Class representing ClothingCategoryType
 *
 *
 * XSD Type: ClothingCategory
 */
class ClothingCategoryType
{

    /**
     * @var \WalmartDSV\ClothingType $clothing
     */
    private $clothing = null;

    /**
     * Gets as clothing
     *
     * @return \WalmartDSV\ClothingType
     */
    public function getClothing()
    {
        return $this->clothing;
    }

    /**
     * Sets a new clothing
     *
     * @param \WalmartDSV\ClothingType $clothing
     * @return self
     */
    public function setClothing(\WalmartDSV\ClothingType $clothing)
    {
        $this->clothing = $clothing;
        return $this;
    }


}

