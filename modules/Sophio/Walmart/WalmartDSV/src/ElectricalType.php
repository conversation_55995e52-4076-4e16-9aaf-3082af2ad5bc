<?php

namespace WalmartDSV;

/**
 * Class representing ElectricalType
 *
 *
 * XSD Type: Electrical
 */
class ElectricalType
{

    /**
     * Common industry recognized terms describing shapes of light bulbs.
     *
     * @var string $lightBulbShape
     */
    private $lightBulbShape = null;

    /**
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @var string[] $additionalVariantAttributeNames
     */
    private $additionalVariantAttributeNames = null;

    /**
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @var string $shortDescription
     */
    private $shortDescription = null;

    /**
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @var string[] $keyFeatures
     */
    private $keyFeatures = null;

    /**
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @var string $brand
     */
    private $brand = null;

    /**
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @var string $manufacturer
     */
    private $manufacturer = null;

    /**
     * MPN uniquely identifies the product to its manufacturer. For many products this will be identical to the model number. Some manufacturers distinguish part number from model number. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @var string $manufacturerPartNumber
     */
    private $manufacturerPartNumber = null;

    /**
     * Model numbers allow manufacturers to keep track of each hardware device and identify or replace the proper part when needed. Model numbers are often found on the bottom, back, or side of a product. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @var string $modelNumber
     */
    private $modelNumber = null;

    /**
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @var int $multipackQuantity
     */
    private $multipackQuantity = null;

    /**
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @var int $countPerPack
     */
    private $countPerPack = null;

    /**
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @var string $count
     */
    private $count = null;

    /**
     * The number of small pieces, slices, or different items within the product. Piece Count applies to things such as puzzles, building block sets, and products that contain multiple different items (such as tool sets, dinnerware sets, gift baskets, art sets, makeup kits, or shaving kits). EXAMPLE: (1) A 500-piece puzzle has a "Piece Count" of 500. (2) A 105-Piece Socket Wrench set has a piece count of "105." (3) A gift basket of 5 different items has a "Piece Count" of 5.
     *
     * @var int $pieceCount
     */
    private $pieceCount = null;

    /**
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @var string $mainImageUrl
     */
    private $mainImageUrl = null;

    /**
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @var string[] $productSecondaryImageURL
     */
    private $productSecondaryImageURL = null;

    /**
     * The main material(s) that a product is made of. This does not need to be an exhaustive list, but should contain the predominant or functionally important material/materials. Fabric material specifics should be entered using the "Fabric Content" attribute.
     *
     * @var \WalmartDSV\MaterialType $material
     */
    private $material = null;

    /**
     * Physical shape of the item. Used in a wide variety of products including rugs, toys and large appliances.
     *
     * @var string $shape
     */
    private $shape = null;

    /**
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @var string[] $globalBrandLicense
     */
    private $globalBrandLicense = null;

    /**
     * Describes home furnishings and decorations according to various themes, styles and tastes.
     *
     * @var string $homeDecorStyle
     */
    private $homeDecorStyle = null;

    /**
     * Brightness per bulb measured in lumens.
     *
     * @var \WalmartDSV\ElectricalType\BrightnessAType $brightness
     */
    private $brightness = null;

    /**
     * Provides information on the exact type of power used by the item.
     *
     * @var string $powerType
     */
    private $powerType = null;

    /**
     * Overall dimensions of an item. Used only for products that do not already have a more specific 'x size' attribute, such as ring size or clothing size.
     *
     * @var string $size
     */
    private $size = null;

    /**
     * The length of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @var \WalmartDSV\ElectricalType\AssembledProductLengthAType $assembledProductLength
     */
    private $assembledProductLength = null;

    /**
     * The width of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @var \WalmartDSV\ElectricalType\AssembledProductWidthAType $assembledProductWidth
     */
    private $assembledProductWidth = null;

    /**
     * The height of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @var \WalmartDSV\ElectricalType\AssembledProductHeightAType $assembledProductHeight
     */
    private $assembledProductHeight = null;

    /**
     * The weight of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @var \WalmartDSV\ElectricalType\AssembledProductWeightAType $assembledProductWeight
     */
    private $assembledProductWeight = null;

    /**
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @var string $variantGroupId
     */
    private $variantGroupId = null;

    /**
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @var string[] $variantAttributeNames
     */
    private $variantAttributeNames = null;

    /**
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @var string $isPrimaryVariant
     */
    private $isPrimaryVariant = null;

    /**
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @var string $isPrivateLabelOrUnbranded
     */
    private $isPrivateLabelOrUnbranded = null;

    /**
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @var string $isProp65WarningRequired
     */
    private $isProp65WarningRequired = null;

    /**
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @var string $prop65WarningText
     */
    private $prop65WarningText = null;

    /**
     * "Battery or battery containing product" is defined by Company to include any item of Merchandise that is a battery or any component of Merchandise, including reusable packaging intended to stay in use with the item, containing a battery of any chemistry/ type. Mark Y if this definition applies to your product.
     *
     * @var string $hasBatteries
     */
    private $hasBatteries = null;

    /**
     * Please select the Battery Technology Type from the list provided. NOTE: If battery type is lead acid, lead acid (nonspillable), lithium ion, or lithium metal, please ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @var string $batteryTechnologyType
     */
    private $batteryTechnologyType = null;

    /**
     * Y indicates the item comes with a warranty. If an item has a warranty, then enter EITHER the warranty URL or the warranty text in the appropriate field.
     *
     * @var string $hasWarranty
     */
    private $hasWarranty = null;

    /**
     * If you indicated that your item has a warranty, provide either the Warranty URL or Warranty Text. The Warranty URL is the web location of the image, PDF, or link to the manufacturer's warranty page, showing the warranty and its terms, including the duration of the warranty. URLs must begin with http:// or https:// NOTE: Please remember to update the link and/or text of the warranty as the warranty changes. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @var string $warrantyURL
     */
    private $warrantyURL = null;

    /**
     * If you marked Y for "Has Warranty" provide the Warranty URL or Warranty Text (the full text of the warranty terms, including what is covered by the warranty and the duration of the warranty). NOTE: please remember to update the text of your warranty as your warranty changes.
     *
     * @var string $warrantyText
     */
    private $warrantyText = null;

    /**
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @var string $hasStateRestrictions
     */
    private $hasStateRestrictions = null;

    /**
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @var \WalmartDSV\StateRestrictionType[] $stateRestrictions
     */
    private $stateRestrictions = null;

    /**
     * ‘Aerosol’ is defined by Walmart to include any item of Merchandise that contains a compressed gas or propellant (including bag-on-valve and other pressurized designs). If your product meets this definition, Mark Y and ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @var string $isAerosol
     */
    private $isAerosol = null;

    /**
     * ‘Chemical’ is defined by Walmart to include any item of Merchandise that contains a powder, gel, paste, or liquid that is not intended for human consumption. ‘Chemical’ also includes the following types items that ARE intended for human consumption, inhalation, or absorption, or labeled with drug facts: All over-the-counter medications, including: Lozenges, pills or capsules (e.g. pain relievers; allergy medications; as well as vitamins and supplements that contain metals); Medicated swabs and wipes, acne medication, and sunscreen; Medicated patches (such as nicotine patches); Liquids (e.g. cough medicine, medicated drops, nasal spray and inhalers); Medicated shampoos, gums, ointments and creams; Medicated lip balm, lip creams and petroleum jelly; Contraceptive foam, films, and spermicides; and Product/Equipment sold with chemicals (e.g. vaporizer sold with medication) and electronic cigarettes. If your product meets this definition, Mark Y and ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @var string $isChemical
     */
    private $isChemical = null;

    /**
     * Denotes any item with an empty container that may be filled with fluids, such as fuel, CO2, propane, etc.
     *
     * @var string $hasFuelContainer
     */
    private $hasFuelContainer = null;

    /**
     * Terms describing the overall external treatment applied to the item. Typically finishes give a distinct appearance, texture or additional performance to the item. This attribute is used in a wide variety products and materials including wood, metal and fabric.
     *
     * @var string $finish
     */
    private $finish = null;

    /**
     * Decorative design or visual ornamentation, often with a thematic, recurring motif.
     *
     * @var \WalmartDSV\PatternType $pattern
     */
    private $pattern = null;

    /**
     * Indicate Y if the product has been certified by the EPA Energy Star program.
     *
     * @var string $isEnergyStarCertified
     */
    private $isEnergyStarCertified = null;

    /**
     * The maximum amount of energy that a device such as a surge protector can absorb before it fails; measured in joules.
     *
     * @var \WalmartDSV\PowerUnitType $maximumEnergySurgeRating
     */
    private $maximumEnergySurgeRating = null;

    /**
     * The estimated cost per year, based on 3 hrs/day, 11 cents/kWh.
     *
     * @var \WalmartDSV\ElectricalType\EstimatedEnergyCostPerYearAType $estimatedEnergyCostPerYear
     */
    private $estimatedEnergyCostPerYear = null;

    /**
     * @var \WalmartDSV\ElectricalType\CompatibleConduitSizesAType\CompatibleConduitSizeAType[] $compatibleConduitSizes
     */
    private $compatibleConduitSizes = null;

    /**
     * @var \WalmartDSV\ElectricalType\VoltsAType $volts
     */
    private $volts = null;

    /**
     * Number of amps as a measure of electrical current draw. For products such as appliances, amps are usually specified as a peak value to help consumers select items that not overload household circuits. Also used as a measure of capacity (trip level) for electrical products such as circuit breakers and fuses
     *
     * @var \WalmartDSV\ElectricalType\AmpsAType $amps
     */
    private $amps = null;

    /**
     * Number of watts (wattage) of electrical power the product produces or consumes. This attribute is used in a wide variety of products including appliances, light bulbs, electronic equipment and electrical components.
     *
     * @var \WalmartDSV\ElectricalType\WattsAType $watts
     */
    private $watts = null;

    /**
     * Color of the bulb, if it needs to be differentiated from a standard light bulb.
     *
     * @var string $lightBulbColor
     */
    private $lightBulbColor = null;

    /**
     * Number of lights included in an electrical, home decor, or seasonal decoration.
     *
     * @var int $numberOfLights
     */
    private $numberOfLights = null;

    /**
     * The material of a lampshade, to distinguish it from the rest of the lamp or other lighting product
     *
     * @var string $shadeMaterial
     */
    private $shadeMaterial = null;

    /**
     * Descriptive term for the shape of a lamp or other lighting product. Many terms are industry standard.
     *
     * @var string $shadeStyle
     */
    private $shadeStyle = null;

    /**
     * Listing of any supplementary items that come with the product. Important information for consumers because accessories typically provide additional convenience, utility, attractiveness or safety to or for a product.
     *
     * @var string[] $accessoriesIncluded
     */
    private $accessoriesIncluded = null;

    /**
     * Color as described by the manufacturer.
     *
     * @var \WalmartDSV\ColorType $color
     */
    private $color = null;

    /**
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @var string[] $colorCategory
     */
    private $colorCategory = null;

    /**
     * Color of the base portion of the item, if it needs to be distinguished from other components.
     *
     * @var string $baseColor
     */
    private $baseColor = null;

    /**
     * The finish of a base component of an item, if it needs to be distinguished from another component.
     *
     * @var string $baseFinish
     */
    private $baseFinish = null;

    /**
     * Y indicates that the item has been especially made to be resistant to water, to some degree.
     *
     * @var string $isWaterproof
     */
    private $isWaterproof = null;

    /**
     * Y indicates that the item has been especially made to resist burning under certain conditions, as specified by the manufacturer. An important feature for products such as fire blankets, building materials, and safes.
     *
     * @var string $isFireResistant
     */
    private $isFireResistant = null;

    /**
     * Description of how the item should be cleaned and maintained.
     *
     * @var string $cleaningCareAndMaintenance
     */
    private $cleaningCareAndMaintenance = null;

    /**
     * Further clarification of what the item may be used for. This improves searchability when customers search for general terms like "birthday party" that do not include the names of specific items.
     *
     * @var string[] $recommendedUses
     */
    private $recommendedUses = null;

    /**
     * Measurement in Ohms, of the electrical resistance a circuit presents to a current when a voltage is applied. Important characteristic for products such as transmitters, speakers, microphones, and headphones because it restricts ("impedes") the flow of power from the receiver or amplifier.
     *
     * @var \WalmartDSV\ElectricalType\ImpedanceAType $impedance
     */
    private $impedance = null;

    /**
     * A substance that conducts electrical current very easily.
     *
     * @var string $conductorMaterial
     */
    private $conductorMaterial = null;

    /**
     * List notable features of the item.
     *
     * @var string[] $features
     */
    private $features = null;

    /**
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @var string $keywords
     */
    private $keywords = null;

    /**
     * Designation that corresponds to the method and size of attachment used to connect the light bulb to the lamp. Important criteria to help consumers select bulbs that will fit their lighting fixtures and devices. For example, the E12 is an Edison screw-mount “candelabra” base, with a 12 mm diameter base and is used for many night lights.
     *
     * @var string $lightBulbBaseType
     */
    private $lightBulbBaseType = null;

    /**
     * The ratio of the lumen output of a lamp to the lumen output of the same lamp connected to a reference ballast as per ANSI test procedures and as specified by the manufacturer of the lamp. Measures the relationship between the ballast, which is designed to limit the amount of current in the electrical circuit, and the lamp.
     *
     * @var \WalmartDSV\BrightnessUnitType $electricalBallastFactor
     */
    private $electricalBallastFactor = null;

    /**
     * The angle that corresponds to point at which the intensity of a source drops to 50% of maximum (center reading) measured in degrees of the full angle. Used as a component of light performance in products such as headlights and security lights.
     *
     * @var \WalmartDSV\ElectricalType\BeamAngleAType $beamAngle
     */
    private $beamAngle = null;

    /**
     * Measurement of the spread of light from a reflectorized light source. The width of the beam spread is typically specified by the manufacturer for a certain beam angle, as measured from a given distance. For example, a light with a beam angle of 20 degrees that has a 1.8 foot beam spread from 5 feet away. Feature used by consumers as an indicator of performance for products such as headlights and security lights.
     *
     * @var \WalmartDSV\ElectricalType\BeamSpreadAType $beamSpread
     */
    private $beamSpread = null;

    /**
     * Measurement of the power of the device. Significance of horsepower varies with type of product. For example, for products with engines, horsepower is a general indicator of an engine's size and power (along with other engine specifications of engine displacement and torque). Horsepower is also used as common rating on electric motors and products that contain them.
     *
     * @var \WalmartDSV\ElectricalType\HorsepowerAType $horsepower
     */
    private $horsepower = null;

    /**
     * Y indicates that the item has been certified by the International Dark-Sky Association and incorporates features to reduce light pollution (which reduces the number of stars visible at night), and energy consumption. For example, dark sky compliant lights minimize glare and direct light down. For more information, go to http://darksky.org/fsa/.
     *
     * @var string $isDarkSkyCompliant
     */
    private $isDarkSkyCompliant = null;

    /**
     * Light color is measured on a temperature scale referred to as Kelvin (K).
     *
     * @var \WalmartDSV\ElectricalType\ColorTemperatureAType $colorTemperature
     */
    private $colorTemperature = null;

    /**
     * Measurement, expressed as decibels, of the intensity of sound volume a device produces. Important selection criteria for consumers concerned with effects of products that generate loud noise levels. Attribute applied to such products as power tools and security alarms.
     *
     * @var \WalmartDSV\ElectricalType\DecibelRatingAType $decibelRating
     */
    private $decibelRating = null;

    /**
     * Measurement of the maximum area a device can accommodate. Attribute applied to products such as light, motion, and sound sensors.
     *
     * @var \WalmartDSV\AreaUnitType $maximumRange
     */
    private $maximumRange = null;

    /**
     * Number of individual electrical components that can fit in this product. If product is a light switch, refers to the number of rows of switches on the same base-plate.
     *
     * @var int $numberOfGangs
     */
    private $numberOfGangs = null;

    /**
     * The number of poles contained in electrical distribution equipment. For example, residential circuit breakers can be single or double-pole
     *
     * @var int $numberOfPoles
     */
    private $numberOfPoles = null;

    /**
     * The amount of time the pixels in the display take to change from one state to another. Measured in milliseconds.
     *
     * @var \WalmartDSV\ElectricalType\ResponseTimeAType $responseTime
     */
    private $responseTime = null;

    /**
     * Measurement of how large a wire is, either in diameter or cross sectional area. Applicable to both electrical non-electrical wires, and to structural cable. Used to indicate electrical characteristics or strength. Measurement is expressed using American wire gauge (AWG) standard. As the wire gauge number increases, the wire diameter decreases. AWG of 12 is a smaller diameter than AWG of 6.
     *
     * @var \WalmartDSV\LengthUnitType $americanWireGauge
     */
    private $americanWireGauge = null;

    /**
     * How the item is attached. Used for products such as shelving and fixture hardware.
     *
     * @var \WalmartDSV\MountType $mountType
     */
    private $mountType = null;

    /**
     * Y indicates the item has been specifically rated by the manufacture for outdoor use. Used for products that also have indoor versions. For electrical equipment, suitability for outdoor use is often related to its IP (Ingress Protection) rating.
     *
     * @var string $isRatedForOutdoorUse
     */
    private $isRatedForOutdoorUse = null;

    /**
     * Amount of time the product is expected to last, as specified by the manufacturer. Important selection criteria for comparing products such as light bulbs.
     *
     * @var \WalmartDSV\TimeUnitType $lifespan
     */
    private $lifespan = null;

    /**
     * A person or entity portrayed in print or visual media. A character might be a fictional personality or an actual living person.
     *
     * @var \WalmartDSV\CharacterType $character
     */
    private $character = null;

    /**
     * If your item has any association with a specific sports league, enter the league name. Abbreviations are fine. NOTE: This attribute flags an item for inclusion in the online fan shop.
     *
     * @var \WalmartDSV\SportsLeagueType $sportsLeague
     */
    private $sportsLeague = null;

    /**
     * If your item has any association with a specific sports team, enter the team name. NOTE: This attribute flags an item for inclusion in the online fan shop.
     *
     * @var \WalmartDSV\SportsTeamType $sportsTeam
     */
    private $sportsTeam = null;

    /**
     * A well-known athlete associated with a product, if applicable. This is used to group items in Fan Shop, not to describe a line of clothing.
     *
     * @var \WalmartDSV\AthleteType $athlete
     */
    private $athlete = null;

    /**
     * @var \WalmartDSV\ElectricalType\SwatchImagesAType\SwatchImageAType[] $swatchImages
     */
    private $swatchImages = null;

    /**
     * Gets as lightBulbShape
     *
     * Common industry recognized terms describing shapes of light bulbs.
     *
     * @return string
     */
    public function getLightBulbShape()
    {
        return $this->lightBulbShape;
    }

    /**
     * Sets a new lightBulbShape
     *
     * Common industry recognized terms describing shapes of light bulbs.
     *
     * @param string $lightBulbShape
     * @return self
     */
    public function setLightBulbShape($lightBulbShape)
    {
        $this->lightBulbShape = $lightBulbShape;
        return $this;
    }

    /**
     * Adds as additionalVariantAttributeName
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @return self
     * @param string $additionalVariantAttributeName
     */
    public function addToAdditionalVariantAttributeNames($additionalVariantAttributeName)
    {
        $this->additionalVariantAttributeNames[] = $additionalVariantAttributeName;
        return $this;
    }

    /**
     * isset additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAdditionalVariantAttributeNames($index)
    {
        return isset($this->additionalVariantAttributeNames[$index]);
    }

    /**
     * unset additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAdditionalVariantAttributeNames($index)
    {
        unset($this->additionalVariantAttributeNames[$index]);
    }

    /**
     * Gets as additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @return string[]
     */
    public function getAdditionalVariantAttributeNames()
    {
        return $this->additionalVariantAttributeNames;
    }

    /**
     * Sets a new additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param string $additionalVariantAttributeNames
     * @return self
     */
    public function setAdditionalVariantAttributeNames(array $additionalVariantAttributeNames)
    {
        $this->additionalVariantAttributeNames = $additionalVariantAttributeNames;
        return $this;
    }

    /**
     * Gets as shortDescription
     *
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @return string
     */
    public function getShortDescription()
    {
        return $this->shortDescription;
    }

    /**
     * Sets a new shortDescription
     *
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @param string $shortDescription
     * @return self
     */
    public function setShortDescription($shortDescription)
    {
        $this->shortDescription = $shortDescription;
        return $this;
    }

    /**
     * Adds as keyFeaturesValue
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @return self
     * @param string $keyFeaturesValue
     */
    public function addToKeyFeatures($keyFeaturesValue)
    {
        $this->keyFeatures[] = $keyFeaturesValue;
        return $this;
    }

    /**
     * isset keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetKeyFeatures($index)
    {
        return isset($this->keyFeatures[$index]);
    }

    /**
     * unset keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetKeyFeatures($index)
    {
        unset($this->keyFeatures[$index]);
    }

    /**
     * Gets as keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @return string[]
     */
    public function getKeyFeatures()
    {
        return $this->keyFeatures;
    }

    /**
     * Sets a new keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param string $keyFeatures
     * @return self
     */
    public function setKeyFeatures(array $keyFeatures)
    {
        $this->keyFeatures = $keyFeatures;
        return $this;
    }

    /**
     * Gets as brand
     *
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @return string
     */
    public function getBrand()
    {
        return $this->brand;
    }

    /**
     * Sets a new brand
     *
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @param string $brand
     * @return self
     */
    public function setBrand($brand)
    {
        $this->brand = $brand;
        return $this;
    }

    /**
     * Gets as manufacturer
     *
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @return string
     */
    public function getManufacturer()
    {
        return $this->manufacturer;
    }

    /**
     * Sets a new manufacturer
     *
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @param string $manufacturer
     * @return self
     */
    public function setManufacturer($manufacturer)
    {
        $this->manufacturer = $manufacturer;
        return $this;
    }

    /**
     * Gets as manufacturerPartNumber
     *
     * MPN uniquely identifies the product to its manufacturer. For many products this will be identical to the model number. Some manufacturers distinguish part number from model number. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @return string
     */
    public function getManufacturerPartNumber()
    {
        return $this->manufacturerPartNumber;
    }

    /**
     * Sets a new manufacturerPartNumber
     *
     * MPN uniquely identifies the product to its manufacturer. For many products this will be identical to the model number. Some manufacturers distinguish part number from model number. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @param string $manufacturerPartNumber
     * @return self
     */
    public function setManufacturerPartNumber($manufacturerPartNumber)
    {
        $this->manufacturerPartNumber = $manufacturerPartNumber;
        return $this;
    }

    /**
     * Gets as modelNumber
     *
     * Model numbers allow manufacturers to keep track of each hardware device and identify or replace the proper part when needed. Model numbers are often found on the bottom, back, or side of a product. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @return string
     */
    public function getModelNumber()
    {
        return $this->modelNumber;
    }

    /**
     * Sets a new modelNumber
     *
     * Model numbers allow manufacturers to keep track of each hardware device and identify or replace the proper part when needed. Model numbers are often found on the bottom, back, or side of a product. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @param string $modelNumber
     * @return self
     */
    public function setModelNumber($modelNumber)
    {
        $this->modelNumber = $modelNumber;
        return $this;
    }

    /**
     * Gets as multipackQuantity
     *
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @return int
     */
    public function getMultipackQuantity()
    {
        return $this->multipackQuantity;
    }

    /**
     * Sets a new multipackQuantity
     *
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @param int $multipackQuantity
     * @return self
     */
    public function setMultipackQuantity($multipackQuantity)
    {
        $this->multipackQuantity = $multipackQuantity;
        return $this;
    }

    /**
     * Gets as countPerPack
     *
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @return int
     */
    public function getCountPerPack()
    {
        return $this->countPerPack;
    }

    /**
     * Sets a new countPerPack
     *
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @param int $countPerPack
     * @return self
     */
    public function setCountPerPack($countPerPack)
    {
        $this->countPerPack = $countPerPack;
        return $this;
    }

    /**
     * Gets as count
     *
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @return string
     */
    public function getCount()
    {
        return $this->count;
    }

    /**
     * Sets a new count
     *
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @param string $count
     * @return self
     */
    public function setCount($count)
    {
        $this->count = $count;
        return $this;
    }

    /**
     * Gets as pieceCount
     *
     * The number of small pieces, slices, or different items within the product. Piece Count applies to things such as puzzles, building block sets, and products that contain multiple different items (such as tool sets, dinnerware sets, gift baskets, art sets, makeup kits, or shaving kits). EXAMPLE: (1) A 500-piece puzzle has a "Piece Count" of 500. (2) A 105-Piece Socket Wrench set has a piece count of "105." (3) A gift basket of 5 different items has a "Piece Count" of 5.
     *
     * @return int
     */
    public function getPieceCount()
    {
        return $this->pieceCount;
    }

    /**
     * Sets a new pieceCount
     *
     * The number of small pieces, slices, or different items within the product. Piece Count applies to things such as puzzles, building block sets, and products that contain multiple different items (such as tool sets, dinnerware sets, gift baskets, art sets, makeup kits, or shaving kits). EXAMPLE: (1) A 500-piece puzzle has a "Piece Count" of 500. (2) A 105-Piece Socket Wrench set has a piece count of "105." (3) A gift basket of 5 different items has a "Piece Count" of 5.
     *
     * @param int $pieceCount
     * @return self
     */
    public function setPieceCount($pieceCount)
    {
        $this->pieceCount = $pieceCount;
        return $this;
    }

    /**
     * Gets as mainImageUrl
     *
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return string
     */
    public function getMainImageUrl()
    {
        return $this->mainImageUrl;
    }

    /**
     * Sets a new mainImageUrl
     *
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param string $mainImageUrl
     * @return self
     */
    public function setMainImageUrl($mainImageUrl)
    {
        $this->mainImageUrl = $mainImageUrl;
        return $this;
    }

    /**
     * Adds as productSecondaryImageURLValue
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return self
     * @param string $productSecondaryImageURLValue
     */
    public function addToProductSecondaryImageURL($productSecondaryImageURLValue)
    {
        $this->productSecondaryImageURL[] = $productSecondaryImageURLValue;
        return $this;
    }

    /**
     * isset productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetProductSecondaryImageURL($index)
    {
        return isset($this->productSecondaryImageURL[$index]);
    }

    /**
     * unset productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetProductSecondaryImageURL($index)
    {
        unset($this->productSecondaryImageURL[$index]);
    }

    /**
     * Gets as productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return string[]
     */
    public function getProductSecondaryImageURL()
    {
        return $this->productSecondaryImageURL;
    }

    /**
     * Sets a new productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param string $productSecondaryImageURL
     * @return self
     */
    public function setProductSecondaryImageURL(array $productSecondaryImageURL)
    {
        $this->productSecondaryImageURL = $productSecondaryImageURL;
        return $this;
    }

    /**
     * Gets as material
     *
     * The main material(s) that a product is made of. This does not need to be an exhaustive list, but should contain the predominant or functionally important material/materials. Fabric material specifics should be entered using the "Fabric Content" attribute.
     *
     * @return \WalmartDSV\MaterialType
     */
    public function getMaterial()
    {
        return $this->material;
    }

    /**
     * Sets a new material
     *
     * The main material(s) that a product is made of. This does not need to be an exhaustive list, but should contain the predominant or functionally important material/materials. Fabric material specifics should be entered using the "Fabric Content" attribute.
     *
     * @param \WalmartDSV\MaterialType $material
     * @return self
     */
    public function setMaterial(\WalmartDSV\MaterialType $material)
    {
        $this->material = $material;
        return $this;
    }

    /**
     * Gets as shape
     *
     * Physical shape of the item. Used in a wide variety of products including rugs, toys and large appliances.
     *
     * @return string
     */
    public function getShape()
    {
        return $this->shape;
    }

    /**
     * Sets a new shape
     *
     * Physical shape of the item. Used in a wide variety of products including rugs, toys and large appliances.
     *
     * @param string $shape
     * @return self
     */
    public function setShape($shape)
    {
        $this->shape = $shape;
        return $this;
    }

    /**
     * Adds as globalBrandLicenseValue
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @return self
     * @param string $globalBrandLicenseValue
     */
    public function addToGlobalBrandLicense($globalBrandLicenseValue)
    {
        $this->globalBrandLicense[] = $globalBrandLicenseValue;
        return $this;
    }

    /**
     * isset globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @param int|string $index
     * @return bool
     */
    public function issetGlobalBrandLicense($index)
    {
        return isset($this->globalBrandLicense[$index]);
    }

    /**
     * unset globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @param int|string $index
     * @return void
     */
    public function unsetGlobalBrandLicense($index)
    {
        unset($this->globalBrandLicense[$index]);
    }

    /**
     * Gets as globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @return string[]
     */
    public function getGlobalBrandLicense()
    {
        return $this->globalBrandLicense;
    }

    /**
     * Sets a new globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @param string $globalBrandLicense
     * @return self
     */
    public function setGlobalBrandLicense(array $globalBrandLicense)
    {
        $this->globalBrandLicense = $globalBrandLicense;
        return $this;
    }

    /**
     * Gets as homeDecorStyle
     *
     * Describes home furnishings and decorations according to various themes, styles and tastes.
     *
     * @return string
     */
    public function getHomeDecorStyle()
    {
        return $this->homeDecorStyle;
    }

    /**
     * Sets a new homeDecorStyle
     *
     * Describes home furnishings and decorations according to various themes, styles and tastes.
     *
     * @param string $homeDecorStyle
     * @return self
     */
    public function setHomeDecorStyle($homeDecorStyle)
    {
        $this->homeDecorStyle = $homeDecorStyle;
        return $this;
    }

    /**
     * Gets as brightness
     *
     * Brightness per bulb measured in lumens.
     *
     * @return \WalmartDSV\ElectricalType\BrightnessAType
     */
    public function getBrightness()
    {
        return $this->brightness;
    }

    /**
     * Sets a new brightness
     *
     * Brightness per bulb measured in lumens.
     *
     * @param \WalmartDSV\ElectricalType\BrightnessAType $brightness
     * @return self
     */
    public function setBrightness(\WalmartDSV\ElectricalType\BrightnessAType $brightness)
    {
        $this->brightness = $brightness;
        return $this;
    }

    /**
     * Gets as powerType
     *
     * Provides information on the exact type of power used by the item.
     *
     * @return string
     */
    public function getPowerType()
    {
        return $this->powerType;
    }

    /**
     * Sets a new powerType
     *
     * Provides information on the exact type of power used by the item.
     *
     * @param string $powerType
     * @return self
     */
    public function setPowerType($powerType)
    {
        $this->powerType = $powerType;
        return $this;
    }

    /**
     * Gets as size
     *
     * Overall dimensions of an item. Used only for products that do not already have a more specific 'x size' attribute, such as ring size or clothing size.
     *
     * @return string
     */
    public function getSize()
    {
        return $this->size;
    }

    /**
     * Sets a new size
     *
     * Overall dimensions of an item. Used only for products that do not already have a more specific 'x size' attribute, such as ring size or clothing size.
     *
     * @param string $size
     * @return self
     */
    public function setSize($size)
    {
        $this->size = $size;
        return $this;
    }

    /**
     * Gets as assembledProductLength
     *
     * The length of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @return \WalmartDSV\ElectricalType\AssembledProductLengthAType
     */
    public function getAssembledProductLength()
    {
        return $this->assembledProductLength;
    }

    /**
     * Sets a new assembledProductLength
     *
     * The length of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @param \WalmartDSV\ElectricalType\AssembledProductLengthAType $assembledProductLength
     * @return self
     */
    public function setAssembledProductLength(\WalmartDSV\ElectricalType\AssembledProductLengthAType $assembledProductLength)
    {
        $this->assembledProductLength = $assembledProductLength;
        return $this;
    }

    /**
     * Gets as assembledProductWidth
     *
     * The width of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @return \WalmartDSV\ElectricalType\AssembledProductWidthAType
     */
    public function getAssembledProductWidth()
    {
        return $this->assembledProductWidth;
    }

    /**
     * Sets a new assembledProductWidth
     *
     * The width of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @param \WalmartDSV\ElectricalType\AssembledProductWidthAType $assembledProductWidth
     * @return self
     */
    public function setAssembledProductWidth(\WalmartDSV\ElectricalType\AssembledProductWidthAType $assembledProductWidth)
    {
        $this->assembledProductWidth = $assembledProductWidth;
        return $this;
    }

    /**
     * Gets as assembledProductHeight
     *
     * The height of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @return \WalmartDSV\ElectricalType\AssembledProductHeightAType
     */
    public function getAssembledProductHeight()
    {
        return $this->assembledProductHeight;
    }

    /**
     * Sets a new assembledProductHeight
     *
     * The height of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @param \WalmartDSV\ElectricalType\AssembledProductHeightAType $assembledProductHeight
     * @return self
     */
    public function setAssembledProductHeight(\WalmartDSV\ElectricalType\AssembledProductHeightAType $assembledProductHeight)
    {
        $this->assembledProductHeight = $assembledProductHeight;
        return $this;
    }

    /**
     * Gets as assembledProductWeight
     *
     * The weight of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @return \WalmartDSV\ElectricalType\AssembledProductWeightAType
     */
    public function getAssembledProductWeight()
    {
        return $this->assembledProductWeight;
    }

    /**
     * Sets a new assembledProductWeight
     *
     * The weight of the fully assembled product. NOTE: This information is shown on the item page.
     *
     * @param \WalmartDSV\ElectricalType\AssembledProductWeightAType $assembledProductWeight
     * @return self
     */
    public function setAssembledProductWeight(\WalmartDSV\ElectricalType\AssembledProductWeightAType $assembledProductWeight)
    {
        $this->assembledProductWeight = $assembledProductWeight;
        return $this;
    }

    /**
     * Gets as variantGroupId
     *
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @return string
     */
    public function getVariantGroupId()
    {
        return $this->variantGroupId;
    }

    /**
     * Sets a new variantGroupId
     *
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @param string $variantGroupId
     * @return self
     */
    public function setVariantGroupId($variantGroupId)
    {
        $this->variantGroupId = $variantGroupId;
        return $this;
    }

    /**
     * Adds as variantAttributeName
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @return self
     * @param string $variantAttributeName
     */
    public function addToVariantAttributeNames($variantAttributeName)
    {
        $this->variantAttributeNames[] = $variantAttributeName;
        return $this;
    }

    /**
     * isset variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetVariantAttributeNames($index)
    {
        return isset($this->variantAttributeNames[$index]);
    }

    /**
     * unset variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetVariantAttributeNames($index)
    {
        unset($this->variantAttributeNames[$index]);
    }

    /**
     * Gets as variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @return string[]
     */
    public function getVariantAttributeNames()
    {
        return $this->variantAttributeNames;
    }

    /**
     * Sets a new variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param string $variantAttributeNames
     * @return self
     */
    public function setVariantAttributeNames(array $variantAttributeNames)
    {
        $this->variantAttributeNames = $variantAttributeNames;
        return $this;
    }

    /**
     * Gets as isPrimaryVariant
     *
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @return string
     */
    public function getIsPrimaryVariant()
    {
        return $this->isPrimaryVariant;
    }

    /**
     * Sets a new isPrimaryVariant
     *
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @param string $isPrimaryVariant
     * @return self
     */
    public function setIsPrimaryVariant($isPrimaryVariant)
    {
        $this->isPrimaryVariant = $isPrimaryVariant;
        return $this;
    }

    /**
     * Gets as isPrivateLabelOrUnbranded
     *
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @return string
     */
    public function getIsPrivateLabelOrUnbranded()
    {
        return $this->isPrivateLabelOrUnbranded;
    }

    /**
     * Sets a new isPrivateLabelOrUnbranded
     *
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @param string $isPrivateLabelOrUnbranded
     * @return self
     */
    public function setIsPrivateLabelOrUnbranded($isPrivateLabelOrUnbranded)
    {
        $this->isPrivateLabelOrUnbranded = $isPrivateLabelOrUnbranded;
        return $this;
    }

    /**
     * Gets as isProp65WarningRequired
     *
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @return string
     */
    public function getIsProp65WarningRequired()
    {
        return $this->isProp65WarningRequired;
    }

    /**
     * Sets a new isProp65WarningRequired
     *
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @param string $isProp65WarningRequired
     * @return self
     */
    public function setIsProp65WarningRequired($isProp65WarningRequired)
    {
        $this->isProp65WarningRequired = $isProp65WarningRequired;
        return $this;
    }

    /**
     * Gets as prop65WarningText
     *
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @return string
     */
    public function getProp65WarningText()
    {
        return $this->prop65WarningText;
    }

    /**
     * Sets a new prop65WarningText
     *
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @param string $prop65WarningText
     * @return self
     */
    public function setProp65WarningText($prop65WarningText)
    {
        $this->prop65WarningText = $prop65WarningText;
        return $this;
    }

    /**
     * Gets as hasBatteries
     *
     * "Battery or battery containing product" is defined by Company to include any item of Merchandise that is a battery or any component of Merchandise, including reusable packaging intended to stay in use with the item, containing a battery of any chemistry/ type. Mark Y if this definition applies to your product.
     *
     * @return string
     */
    public function getHasBatteries()
    {
        return $this->hasBatteries;
    }

    /**
     * Sets a new hasBatteries
     *
     * "Battery or battery containing product" is defined by Company to include any item of Merchandise that is a battery or any component of Merchandise, including reusable packaging intended to stay in use with the item, containing a battery of any chemistry/ type. Mark Y if this definition applies to your product.
     *
     * @param string $hasBatteries
     * @return self
     */
    public function setHasBatteries($hasBatteries)
    {
        $this->hasBatteries = $hasBatteries;
        return $this;
    }

    /**
     * Gets as batteryTechnologyType
     *
     * Please select the Battery Technology Type from the list provided. NOTE: If battery type is lead acid, lead acid (nonspillable), lithium ion, or lithium metal, please ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @return string
     */
    public function getBatteryTechnologyType()
    {
        return $this->batteryTechnologyType;
    }

    /**
     * Sets a new batteryTechnologyType
     *
     * Please select the Battery Technology Type from the list provided. NOTE: If battery type is lead acid, lead acid (nonspillable), lithium ion, or lithium metal, please ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @param string $batteryTechnologyType
     * @return self
     */
    public function setBatteryTechnologyType($batteryTechnologyType)
    {
        $this->batteryTechnologyType = $batteryTechnologyType;
        return $this;
    }

    /**
     * Gets as hasWarranty
     *
     * Y indicates the item comes with a warranty. If an item has a warranty, then enter EITHER the warranty URL or the warranty text in the appropriate field.
     *
     * @return string
     */
    public function getHasWarranty()
    {
        return $this->hasWarranty;
    }

    /**
     * Sets a new hasWarranty
     *
     * Y indicates the item comes with a warranty. If an item has a warranty, then enter EITHER the warranty URL or the warranty text in the appropriate field.
     *
     * @param string $hasWarranty
     * @return self
     */
    public function setHasWarranty($hasWarranty)
    {
        $this->hasWarranty = $hasWarranty;
        return $this;
    }

    /**
     * Gets as warrantyURL
     *
     * If you indicated that your item has a warranty, provide either the Warranty URL or Warranty Text. The Warranty URL is the web location of the image, PDF, or link to the manufacturer's warranty page, showing the warranty and its terms, including the duration of the warranty. URLs must begin with http:// or https:// NOTE: Please remember to update the link and/or text of the warranty as the warranty changes. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @return string
     */
    public function getWarrantyURL()
    {
        return $this->warrantyURL;
    }

    /**
     * Sets a new warrantyURL
     *
     * If you indicated that your item has a warranty, provide either the Warranty URL or Warranty Text. The Warranty URL is the web location of the image, PDF, or link to the manufacturer's warranty page, showing the warranty and its terms, including the duration of the warranty. URLs must begin with http:// or https:// NOTE: Please remember to update the link and/or text of the warranty as the warranty changes. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @param string $warrantyURL
     * @return self
     */
    public function setWarrantyURL($warrantyURL)
    {
        $this->warrantyURL = $warrantyURL;
        return $this;
    }

    /**
     * Gets as warrantyText
     *
     * If you marked Y for "Has Warranty" provide the Warranty URL or Warranty Text (the full text of the warranty terms, including what is covered by the warranty and the duration of the warranty). NOTE: please remember to update the text of your warranty as your warranty changes.
     *
     * @return string
     */
    public function getWarrantyText()
    {
        return $this->warrantyText;
    }

    /**
     * Sets a new warrantyText
     *
     * If you marked Y for "Has Warranty" provide the Warranty URL or Warranty Text (the full text of the warranty terms, including what is covered by the warranty and the duration of the warranty). NOTE: please remember to update the text of your warranty as your warranty changes.
     *
     * @param string $warrantyText
     * @return self
     */
    public function setWarrantyText($warrantyText)
    {
        $this->warrantyText = $warrantyText;
        return $this;
    }

    /**
     * Gets as hasStateRestrictions
     *
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @return string
     */
    public function getHasStateRestrictions()
    {
        return $this->hasStateRestrictions;
    }

    /**
     * Sets a new hasStateRestrictions
     *
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @param string $hasStateRestrictions
     * @return self
     */
    public function setHasStateRestrictions($hasStateRestrictions)
    {
        $this->hasStateRestrictions = $hasStateRestrictions;
        return $this;
    }

    /**
     * Adds as stateRestriction
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param \WalmartDSV\StateRestrictionType $stateRestriction
     *@return self
     */
    public function addToStateRestrictions(\WalmartDSV\StateRestrictionType $stateRestriction)
    {
        $this->stateRestrictions[] = $stateRestriction;
        return $this;
    }

    /**
     * isset stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetStateRestrictions($index)
    {
        return isset($this->stateRestrictions[$index]);
    }

    /**
     * unset stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetStateRestrictions($index)
    {
        unset($this->stateRestrictions[$index]);
    }

    /**
     * Gets as stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @return \WalmartDSV\StateRestrictionType[]
     */
    public function getStateRestrictions()
    {
        return $this->stateRestrictions;
    }

    /**
     * Sets a new stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param \WalmartDSV\StateRestrictionType[] $stateRestrictions
     * @return self
     */
    public function setStateRestrictions(array $stateRestrictions)
    {
        $this->stateRestrictions = $stateRestrictions;
        return $this;
    }

    /**
     * Gets as isAerosol
     *
     * ‘Aerosol’ is defined by Walmart to include any item of Merchandise that contains a compressed gas or propellant (including bag-on-valve and other pressurized designs). If your product meets this definition, Mark Y and ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @return string
     */
    public function getIsAerosol()
    {
        return $this->isAerosol;
    }

    /**
     * Sets a new isAerosol
     *
     * ‘Aerosol’ is defined by Walmart to include any item of Merchandise that contains a compressed gas or propellant (including bag-on-valve and other pressurized designs). If your product meets this definition, Mark Y and ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @param string $isAerosol
     * @return self
     */
    public function setIsAerosol($isAerosol)
    {
        $this->isAerosol = $isAerosol;
        return $this;
    }

    /**
     * Gets as isChemical
     *
     * ‘Chemical’ is defined by Walmart to include any item of Merchandise that contains a powder, gel, paste, or liquid that is not intended for human consumption. ‘Chemical’ also includes the following types items that ARE intended for human consumption, inhalation, or absorption, or labeled with drug facts: All over-the-counter medications, including: Lozenges, pills or capsules (e.g. pain relievers; allergy medications; as well as vitamins and supplements that contain metals); Medicated swabs and wipes, acne medication, and sunscreen; Medicated patches (such as nicotine patches); Liquids (e.g. cough medicine, medicated drops, nasal spray and inhalers); Medicated shampoos, gums, ointments and creams; Medicated lip balm, lip creams and petroleum jelly; Contraceptive foam, films, and spermicides; and Product/Equipment sold with chemicals (e.g. vaporizer sold with medication) and electronic cigarettes. If your product meets this definition, Mark Y and ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @return string
     */
    public function getIsChemical()
    {
        return $this->isChemical;
    }

    /**
     * Sets a new isChemical
     *
     * ‘Chemical’ is defined by Walmart to include any item of Merchandise that contains a powder, gel, paste, or liquid that is not intended for human consumption. ‘Chemical’ also includes the following types items that ARE intended for human consumption, inhalation, or absorption, or labeled with drug facts: All over-the-counter medications, including: Lozenges, pills or capsules (e.g. pain relievers; allergy medications; as well as vitamins and supplements that contain metals); Medicated swabs and wipes, acne medication, and sunscreen; Medicated patches (such as nicotine patches); Liquids (e.g. cough medicine, medicated drops, nasal spray and inhalers); Medicated shampoos, gums, ointments and creams; Medicated lip balm, lip creams and petroleum jelly; Contraceptive foam, films, and spermicides; and Product/Equipment sold with chemicals (e.g. vaporizer sold with medication) and electronic cigarettes. If your product meets this definition, Mark Y and ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @param string $isChemical
     * @return self
     */
    public function setIsChemical($isChemical)
    {
        $this->isChemical = $isChemical;
        return $this;
    }

    /**
     * Gets as hasFuelContainer
     *
     * Denotes any item with an empty container that may be filled with fluids, such as fuel, CO2, propane, etc.
     *
     * @return string
     */
    public function getHasFuelContainer()
    {
        return $this->hasFuelContainer;
    }

    /**
     * Sets a new hasFuelContainer
     *
     * Denotes any item with an empty container that may be filled with fluids, such as fuel, CO2, propane, etc.
     *
     * @param string $hasFuelContainer
     * @return self
     */
    public function setHasFuelContainer($hasFuelContainer)
    {
        $this->hasFuelContainer = $hasFuelContainer;
        return $this;
    }

    /**
     * Gets as finish
     *
     * Terms describing the overall external treatment applied to the item. Typically finishes give a distinct appearance, texture or additional performance to the item. This attribute is used in a wide variety products and materials including wood, metal and fabric.
     *
     * @return string
     */
    public function getFinish()
    {
        return $this->finish;
    }

    /**
     * Sets a new finish
     *
     * Terms describing the overall external treatment applied to the item. Typically finishes give a distinct appearance, texture or additional performance to the item. This attribute is used in a wide variety products and materials including wood, metal and fabric.
     *
     * @param string $finish
     * @return self
     */
    public function setFinish($finish)
    {
        $this->finish = $finish;
        return $this;
    }

    /**
     * Gets as pattern
     *
     * Decorative design or visual ornamentation, often with a thematic, recurring motif.
     *
     * @return \WalmartDSV\PatternType
     */
    public function getPattern()
    {
        return $this->pattern;
    }

    /**
     * Sets a new pattern
     *
     * Decorative design or visual ornamentation, often with a thematic, recurring motif.
     *
     * @param \WalmartDSV\PatternType $pattern
     * @return self
     */
    public function setPattern(\WalmartDSV\PatternType $pattern)
    {
        $this->pattern = $pattern;
        return $this;
    }

    /**
     * Gets as isEnergyStarCertified
     *
     * Indicate Y if the product has been certified by the EPA Energy Star program.
     *
     * @return string
     */
    public function getIsEnergyStarCertified()
    {
        return $this->isEnergyStarCertified;
    }

    /**
     * Sets a new isEnergyStarCertified
     *
     * Indicate Y if the product has been certified by the EPA Energy Star program.
     *
     * @param string $isEnergyStarCertified
     * @return self
     */
    public function setIsEnergyStarCertified($isEnergyStarCertified)
    {
        $this->isEnergyStarCertified = $isEnergyStarCertified;
        return $this;
    }

    /**
     * Gets as maximumEnergySurgeRating
     *
     * The maximum amount of energy that a device such as a surge protector can absorb before it fails; measured in joules.
     *
     * @return \WalmartDSV\PowerUnitType
     */
    public function getMaximumEnergySurgeRating()
    {
        return $this->maximumEnergySurgeRating;
    }

    /**
     * Sets a new maximumEnergySurgeRating
     *
     * The maximum amount of energy that a device such as a surge protector can absorb before it fails; measured in joules.
     *
     * @param \WalmartDSV\PowerUnitType $maximumEnergySurgeRating
     * @return self
     */
    public function setMaximumEnergySurgeRating(\WalmartDSV\PowerUnitType $maximumEnergySurgeRating)
    {
        $this->maximumEnergySurgeRating = $maximumEnergySurgeRating;
        return $this;
    }

    /**
     * Gets as estimatedEnergyCostPerYear
     *
     * The estimated cost per year, based on 3 hrs/day, 11 cents/kWh.
     *
     * @return \WalmartDSV\ElectricalType\EstimatedEnergyCostPerYearAType
     */
    public function getEstimatedEnergyCostPerYear()
    {
        return $this->estimatedEnergyCostPerYear;
    }

    /**
     * Sets a new estimatedEnergyCostPerYear
     *
     * The estimated cost per year, based on 3 hrs/day, 11 cents/kWh.
     *
     * @param \WalmartDSV\ElectricalType\EstimatedEnergyCostPerYearAType $estimatedEnergyCostPerYear
     * @return self
     */
    public function setEstimatedEnergyCostPerYear(\WalmartDSV\ElectricalType\EstimatedEnergyCostPerYearAType $estimatedEnergyCostPerYear)
    {
        $this->estimatedEnergyCostPerYear = $estimatedEnergyCostPerYear;
        return $this;
    }

    /**
     * Adds as compatibleConduitSize
     *
     * @param \WalmartDSV\ElectricalType\CompatibleConduitSizesAType\CompatibleConduitSizeAType $compatibleConduitSize
     *@return self
     */
    public function addToCompatibleConduitSizes(\WalmartDSV\ElectricalType\CompatibleConduitSizesAType\CompatibleConduitSizeAType $compatibleConduitSize)
    {
        $this->compatibleConduitSizes[] = $compatibleConduitSize;
        return $this;
    }

    /**
     * isset compatibleConduitSizes
     *
     * @param int|string $index
     * @return bool
     */
    public function issetCompatibleConduitSizes($index)
    {
        return isset($this->compatibleConduitSizes[$index]);
    }

    /**
     * unset compatibleConduitSizes
     *
     * @param int|string $index
     * @return void
     */
    public function unsetCompatibleConduitSizes($index)
    {
        unset($this->compatibleConduitSizes[$index]);
    }

    /**
     * Gets as compatibleConduitSizes
     *
     * @return \WalmartDSV\ElectricalType\CompatibleConduitSizesAType\CompatibleConduitSizeAType[]
     */
    public function getCompatibleConduitSizes()
    {
        return $this->compatibleConduitSizes;
    }

    /**
     * Sets a new compatibleConduitSizes
     *
     * @param \WalmartDSV\ElectricalType\CompatibleConduitSizesAType\CompatibleConduitSizeAType[] $compatibleConduitSizes
     * @return self
     */
    public function setCompatibleConduitSizes(array $compatibleConduitSizes)
    {
        $this->compatibleConduitSizes = $compatibleConduitSizes;
        return $this;
    }

    /**
     * Gets as volts
     *
     * @return \WalmartDSV\ElectricalType\VoltsAType
     */
    public function getVolts()
    {
        return $this->volts;
    }

    /**
     * Sets a new volts
     *
     * @param \WalmartDSV\ElectricalType\VoltsAType $volts
     * @return self
     */
    public function setVolts(\WalmartDSV\ElectricalType\VoltsAType $volts)
    {
        $this->volts = $volts;
        return $this;
    }

    /**
     * Gets as amps
     *
     * Number of amps as a measure of electrical current draw. For products such as appliances, amps are usually specified as a peak value to help consumers select items that not overload household circuits. Also used as a measure of capacity (trip level) for electrical products such as circuit breakers and fuses
     *
     * @return \WalmartDSV\ElectricalType\AmpsAType
     */
    public function getAmps()
    {
        return $this->amps;
    }

    /**
     * Sets a new amps
     *
     * Number of amps as a measure of electrical current draw. For products such as appliances, amps are usually specified as a peak value to help consumers select items that not overload household circuits. Also used as a measure of capacity (trip level) for electrical products such as circuit breakers and fuses
     *
     * @param \WalmartDSV\ElectricalType\AmpsAType $amps
     * @return self
     */
    public function setAmps(\WalmartDSV\ElectricalType\AmpsAType $amps)
    {
        $this->amps = $amps;
        return $this;
    }

    /**
     * Gets as watts
     *
     * Number of watts (wattage) of electrical power the product produces or consumes. This attribute is used in a wide variety of products including appliances, light bulbs, electronic equipment and electrical components.
     *
     * @return \WalmartDSV\ElectricalType\WattsAType
     */
    public function getWatts()
    {
        return $this->watts;
    }

    /**
     * Sets a new watts
     *
     * Number of watts (wattage) of electrical power the product produces or consumes. This attribute is used in a wide variety of products including appliances, light bulbs, electronic equipment and electrical components.
     *
     * @param \WalmartDSV\ElectricalType\WattsAType $watts
     * @return self
     */
    public function setWatts(\WalmartDSV\ElectricalType\WattsAType $watts)
    {
        $this->watts = $watts;
        return $this;
    }

    /**
     * Gets as lightBulbColor
     *
     * Color of the bulb, if it needs to be differentiated from a standard light bulb.
     *
     * @return string
     */
    public function getLightBulbColor()
    {
        return $this->lightBulbColor;
    }

    /**
     * Sets a new lightBulbColor
     *
     * Color of the bulb, if it needs to be differentiated from a standard light bulb.
     *
     * @param string $lightBulbColor
     * @return self
     */
    public function setLightBulbColor($lightBulbColor)
    {
        $this->lightBulbColor = $lightBulbColor;
        return $this;
    }

    /**
     * Gets as numberOfLights
     *
     * Number of lights included in an electrical, home decor, or seasonal decoration.
     *
     * @return int
     */
    public function getNumberOfLights()
    {
        return $this->numberOfLights;
    }

    /**
     * Sets a new numberOfLights
     *
     * Number of lights included in an electrical, home decor, or seasonal decoration.
     *
     * @param int $numberOfLights
     * @return self
     */
    public function setNumberOfLights($numberOfLights)
    {
        $this->numberOfLights = $numberOfLights;
        return $this;
    }

    /**
     * Gets as shadeMaterial
     *
     * The material of a lampshade, to distinguish it from the rest of the lamp or other lighting product
     *
     * @return string
     */
    public function getShadeMaterial()
    {
        return $this->shadeMaterial;
    }

    /**
     * Sets a new shadeMaterial
     *
     * The material of a lampshade, to distinguish it from the rest of the lamp or other lighting product
     *
     * @param string $shadeMaterial
     * @return self
     */
    public function setShadeMaterial($shadeMaterial)
    {
        $this->shadeMaterial = $shadeMaterial;
        return $this;
    }

    /**
     * Gets as shadeStyle
     *
     * Descriptive term for the shape of a lamp or other lighting product. Many terms are industry standard.
     *
     * @return string
     */
    public function getShadeStyle()
    {
        return $this->shadeStyle;
    }

    /**
     * Sets a new shadeStyle
     *
     * Descriptive term for the shape of a lamp or other lighting product. Many terms are industry standard.
     *
     * @param string $shadeStyle
     * @return self
     */
    public function setShadeStyle($shadeStyle)
    {
        $this->shadeStyle = $shadeStyle;
        return $this;
    }

    /**
     * Adds as accessoriesIncludedValue
     *
     * Listing of any supplementary items that come with the product. Important information for consumers because accessories typically provide additional convenience, utility, attractiveness or safety to or for a product.
     *
     * @return self
     * @param string $accessoriesIncludedValue
     */
    public function addToAccessoriesIncluded($accessoriesIncludedValue)
    {
        $this->accessoriesIncluded[] = $accessoriesIncludedValue;
        return $this;
    }

    /**
     * isset accessoriesIncluded
     *
     * Listing of any supplementary items that come with the product. Important information for consumers because accessories typically provide additional convenience, utility, attractiveness or safety to or for a product.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAccessoriesIncluded($index)
    {
        return isset($this->accessoriesIncluded[$index]);
    }

    /**
     * unset accessoriesIncluded
     *
     * Listing of any supplementary items that come with the product. Important information for consumers because accessories typically provide additional convenience, utility, attractiveness or safety to or for a product.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAccessoriesIncluded($index)
    {
        unset($this->accessoriesIncluded[$index]);
    }

    /**
     * Gets as accessoriesIncluded
     *
     * Listing of any supplementary items that come with the product. Important information for consumers because accessories typically provide additional convenience, utility, attractiveness or safety to or for a product.
     *
     * @return string[]
     */
    public function getAccessoriesIncluded()
    {
        return $this->accessoriesIncluded;
    }

    /**
     * Sets a new accessoriesIncluded
     *
     * Listing of any supplementary items that come with the product. Important information for consumers because accessories typically provide additional convenience, utility, attractiveness or safety to or for a product.
     *
     * @param string $accessoriesIncluded
     * @return self
     */
    public function setAccessoriesIncluded(array $accessoriesIncluded)
    {
        $this->accessoriesIncluded = $accessoriesIncluded;
        return $this;
    }

    /**
     * Gets as color
     *
     * Color as described by the manufacturer.
     *
     * @return \WalmartDSV\ColorType
     */
    public function getColor()
    {
        return $this->color;
    }

    /**
     * Sets a new color
     *
     * Color as described by the manufacturer.
     *
     * @param \WalmartDSV\ColorType $color
     * @return self
     */
    public function setColor(\WalmartDSV\ColorType $color)
    {
        $this->color = $color;
        return $this;
    }

    /**
     * Adds as colorCategoryValue
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @return self
     * @param string $colorCategoryValue
     */
    public function addToColorCategory($colorCategoryValue)
    {
        $this->colorCategory[] = $colorCategoryValue;
        return $this;
    }

    /**
     * isset colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetColorCategory($index)
    {
        return isset($this->colorCategory[$index]);
    }

    /**
     * unset colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetColorCategory($index)
    {
        unset($this->colorCategory[$index]);
    }

    /**
     * Gets as colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @return string[]
     */
    public function getColorCategory()
    {
        return $this->colorCategory;
    }

    /**
     * Sets a new colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @param string $colorCategory
     * @return self
     */
    public function setColorCategory(array $colorCategory)
    {
        $this->colorCategory = $colorCategory;
        return $this;
    }

    /**
     * Gets as baseColor
     *
     * Color of the base portion of the item, if it needs to be distinguished from other components.
     *
     * @return string
     */
    public function getBaseColor()
    {
        return $this->baseColor;
    }

    /**
     * Sets a new baseColor
     *
     * Color of the base portion of the item, if it needs to be distinguished from other components.
     *
     * @param string $baseColor
     * @return self
     */
    public function setBaseColor($baseColor)
    {
        $this->baseColor = $baseColor;
        return $this;
    }

    /**
     * Gets as baseFinish
     *
     * The finish of a base component of an item, if it needs to be distinguished from another component.
     *
     * @return string
     */
    public function getBaseFinish()
    {
        return $this->baseFinish;
    }

    /**
     * Sets a new baseFinish
     *
     * The finish of a base component of an item, if it needs to be distinguished from another component.
     *
     * @param string $baseFinish
     * @return self
     */
    public function setBaseFinish($baseFinish)
    {
        $this->baseFinish = $baseFinish;
        return $this;
    }

    /**
     * Gets as isWaterproof
     *
     * Y indicates that the item has been especially made to be resistant to water, to some degree.
     *
     * @return string
     */
    public function getIsWaterproof()
    {
        return $this->isWaterproof;
    }

    /**
     * Sets a new isWaterproof
     *
     * Y indicates that the item has been especially made to be resistant to water, to some degree.
     *
     * @param string $isWaterproof
     * @return self
     */
    public function setIsWaterproof($isWaterproof)
    {
        $this->isWaterproof = $isWaterproof;
        return $this;
    }

    /**
     * Gets as isFireResistant
     *
     * Y indicates that the item has been especially made to resist burning under certain conditions, as specified by the manufacturer. An important feature for products such as fire blankets, building materials, and safes.
     *
     * @return string
     */
    public function getIsFireResistant()
    {
        return $this->isFireResistant;
    }

    /**
     * Sets a new isFireResistant
     *
     * Y indicates that the item has been especially made to resist burning under certain conditions, as specified by the manufacturer. An important feature for products such as fire blankets, building materials, and safes.
     *
     * @param string $isFireResistant
     * @return self
     */
    public function setIsFireResistant($isFireResistant)
    {
        $this->isFireResistant = $isFireResistant;
        return $this;
    }

    /**
     * Gets as cleaningCareAndMaintenance
     *
     * Description of how the item should be cleaned and maintained.
     *
     * @return string
     */
    public function getCleaningCareAndMaintenance()
    {
        return $this->cleaningCareAndMaintenance;
    }

    /**
     * Sets a new cleaningCareAndMaintenance
     *
     * Description of how the item should be cleaned and maintained.
     *
     * @param string $cleaningCareAndMaintenance
     * @return self
     */
    public function setCleaningCareAndMaintenance($cleaningCareAndMaintenance)
    {
        $this->cleaningCareAndMaintenance = $cleaningCareAndMaintenance;
        return $this;
    }

    /**
     * Adds as recommendedUse
     *
     * Further clarification of what the item may be used for. This improves searchability when customers search for general terms like "birthday party" that do not include the names of specific items.
     *
     * @return self
     * @param string $recommendedUse
     */
    public function addToRecommendedUses($recommendedUse)
    {
        $this->recommendedUses[] = $recommendedUse;
        return $this;
    }

    /**
     * isset recommendedUses
     *
     * Further clarification of what the item may be used for. This improves searchability when customers search for general terms like "birthday party" that do not include the names of specific items.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetRecommendedUses($index)
    {
        return isset($this->recommendedUses[$index]);
    }

    /**
     * unset recommendedUses
     *
     * Further clarification of what the item may be used for. This improves searchability when customers search for general terms like "birthday party" that do not include the names of specific items.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetRecommendedUses($index)
    {
        unset($this->recommendedUses[$index]);
    }

    /**
     * Gets as recommendedUses
     *
     * Further clarification of what the item may be used for. This improves searchability when customers search for general terms like "birthday party" that do not include the names of specific items.
     *
     * @return string[]
     */
    public function getRecommendedUses()
    {
        return $this->recommendedUses;
    }

    /**
     * Sets a new recommendedUses
     *
     * Further clarification of what the item may be used for. This improves searchability when customers search for general terms like "birthday party" that do not include the names of specific items.
     *
     * @param string $recommendedUses
     * @return self
     */
    public function setRecommendedUses(array $recommendedUses)
    {
        $this->recommendedUses = $recommendedUses;
        return $this;
    }

    /**
     * Gets as impedance
     *
     * Measurement in Ohms, of the electrical resistance a circuit presents to a current when a voltage is applied. Important characteristic for products such as transmitters, speakers, microphones, and headphones because it restricts ("impedes") the flow of power from the receiver or amplifier.
     *
     * @return \WalmartDSV\ElectricalType\ImpedanceAType
     */
    public function getImpedance()
    {
        return $this->impedance;
    }

    /**
     * Sets a new impedance
     *
     * Measurement in Ohms, of the electrical resistance a circuit presents to a current when a voltage is applied. Important characteristic for products such as transmitters, speakers, microphones, and headphones because it restricts ("impedes") the flow of power from the receiver or amplifier.
     *
     * @param \WalmartDSV\ElectricalType\ImpedanceAType $impedance
     * @return self
     */
    public function setImpedance(\WalmartDSV\ElectricalType\ImpedanceAType $impedance)
    {
        $this->impedance = $impedance;
        return $this;
    }

    /**
     * Gets as conductorMaterial
     *
     * A substance that conducts electrical current very easily.
     *
     * @return string
     */
    public function getConductorMaterial()
    {
        return $this->conductorMaterial;
    }

    /**
     * Sets a new conductorMaterial
     *
     * A substance that conducts electrical current very easily.
     *
     * @param string $conductorMaterial
     * @return self
     */
    public function setConductorMaterial($conductorMaterial)
    {
        $this->conductorMaterial = $conductorMaterial;
        return $this;
    }

    /**
     * Adds as feature
     *
     * List notable features of the item.
     *
     * @return self
     * @param string $feature
     */
    public function addToFeatures($feature)
    {
        $this->features[] = $feature;
        return $this;
    }

    /**
     * isset features
     *
     * List notable features of the item.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFeatures($index)
    {
        return isset($this->features[$index]);
    }

    /**
     * unset features
     *
     * List notable features of the item.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFeatures($index)
    {
        unset($this->features[$index]);
    }

    /**
     * Gets as features
     *
     * List notable features of the item.
     *
     * @return string[]
     */
    public function getFeatures()
    {
        return $this->features;
    }

    /**
     * Sets a new features
     *
     * List notable features of the item.
     *
     * @param string $features
     * @return self
     */
    public function setFeatures(array $features)
    {
        $this->features = $features;
        return $this;
    }

    /**
     * Gets as keywords
     *
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @return string
     */
    public function getKeywords()
    {
        return $this->keywords;
    }

    /**
     * Sets a new keywords
     *
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @param string $keywords
     * @return self
     */
    public function setKeywords($keywords)
    {
        $this->keywords = $keywords;
        return $this;
    }

    /**
     * Gets as lightBulbBaseType
     *
     * Designation that corresponds to the method and size of attachment used to connect the light bulb to the lamp. Important criteria to help consumers select bulbs that will fit their lighting fixtures and devices. For example, the E12 is an Edison screw-mount “candelabra” base, with a 12 mm diameter base and is used for many night lights.
     *
     * @return string
     */
    public function getLightBulbBaseType()
    {
        return $this->lightBulbBaseType;
    }

    /**
     * Sets a new lightBulbBaseType
     *
     * Designation that corresponds to the method and size of attachment used to connect the light bulb to the lamp. Important criteria to help consumers select bulbs that will fit their lighting fixtures and devices. For example, the E12 is an Edison screw-mount “candelabra” base, with a 12 mm diameter base and is used for many night lights.
     *
     * @param string $lightBulbBaseType
     * @return self
     */
    public function setLightBulbBaseType($lightBulbBaseType)
    {
        $this->lightBulbBaseType = $lightBulbBaseType;
        return $this;
    }

    /**
     * Gets as electricalBallastFactor
     *
     * The ratio of the lumen output of a lamp to the lumen output of the same lamp connected to a reference ballast as per ANSI test procedures and as specified by the manufacturer of the lamp. Measures the relationship between the ballast, which is designed to limit the amount of current in the electrical circuit, and the lamp.
     *
     * @return \WalmartDSV\BrightnessUnitType
     */
    public function getElectricalBallastFactor()
    {
        return $this->electricalBallastFactor;
    }

    /**
     * Sets a new electricalBallastFactor
     *
     * The ratio of the lumen output of a lamp to the lumen output of the same lamp connected to a reference ballast as per ANSI test procedures and as specified by the manufacturer of the lamp. Measures the relationship between the ballast, which is designed to limit the amount of current in the electrical circuit, and the lamp.
     *
     * @param \WalmartDSV\BrightnessUnitType $electricalBallastFactor
     * @return self
     */
    public function setElectricalBallastFactor(\WalmartDSV\BrightnessUnitType $electricalBallastFactor)
    {
        $this->electricalBallastFactor = $electricalBallastFactor;
        return $this;
    }

    /**
     * Gets as beamAngle
     *
     * The angle that corresponds to point at which the intensity of a source drops to 50% of maximum (center reading) measured in degrees of the full angle. Used as a component of light performance in products such as headlights and security lights.
     *
     * @return \WalmartDSV\ElectricalType\BeamAngleAType
     */
    public function getBeamAngle()
    {
        return $this->beamAngle;
    }

    /**
     * Sets a new beamAngle
     *
     * The angle that corresponds to point at which the intensity of a source drops to 50% of maximum (center reading) measured in degrees of the full angle. Used as a component of light performance in products such as headlights and security lights.
     *
     * @param \WalmartDSV\ElectricalType\BeamAngleAType $beamAngle
     * @return self
     */
    public function setBeamAngle(\WalmartDSV\ElectricalType\BeamAngleAType $beamAngle)
    {
        $this->beamAngle = $beamAngle;
        return $this;
    }

    /**
     * Gets as beamSpread
     *
     * Measurement of the spread of light from a reflectorized light source. The width of the beam spread is typically specified by the manufacturer for a certain beam angle, as measured from a given distance. For example, a light with a beam angle of 20 degrees that has a 1.8 foot beam spread from 5 feet away. Feature used by consumers as an indicator of performance for products such as headlights and security lights.
     *
     * @return \WalmartDSV\ElectricalType\BeamSpreadAType
     */
    public function getBeamSpread()
    {
        return $this->beamSpread;
    }

    /**
     * Sets a new beamSpread
     *
     * Measurement of the spread of light from a reflectorized light source. The width of the beam spread is typically specified by the manufacturer for a certain beam angle, as measured from a given distance. For example, a light with a beam angle of 20 degrees that has a 1.8 foot beam spread from 5 feet away. Feature used by consumers as an indicator of performance for products such as headlights and security lights.
     *
     * @param \WalmartDSV\ElectricalType\BeamSpreadAType $beamSpread
     * @return self
     */
    public function setBeamSpread(\WalmartDSV\ElectricalType\BeamSpreadAType $beamSpread)
    {
        $this->beamSpread = $beamSpread;
        return $this;
    }

    /**
     * Gets as horsepower
     *
     * Measurement of the power of the device. Significance of horsepower varies with type of product. For example, for products with engines, horsepower is a general indicator of an engine's size and power (along with other engine specifications of engine displacement and torque). Horsepower is also used as common rating on electric motors and products that contain them.
     *
     * @return \WalmartDSV\ElectricalType\HorsepowerAType
     */
    public function getHorsepower()
    {
        return $this->horsepower;
    }

    /**
     * Sets a new horsepower
     *
     * Measurement of the power of the device. Significance of horsepower varies with type of product. For example, for products with engines, horsepower is a general indicator of an engine's size and power (along with other engine specifications of engine displacement and torque). Horsepower is also used as common rating on electric motors and products that contain them.
     *
     * @param \WalmartDSV\ElectricalType\HorsepowerAType $horsepower
     * @return self
     */
    public function setHorsepower(\WalmartDSV\ElectricalType\HorsepowerAType $horsepower)
    {
        $this->horsepower = $horsepower;
        return $this;
    }

    /**
     * Gets as isDarkSkyCompliant
     *
     * Y indicates that the item has been certified by the International Dark-Sky Association and incorporates features to reduce light pollution (which reduces the number of stars visible at night), and energy consumption. For example, dark sky compliant lights minimize glare and direct light down. For more information, go to http://darksky.org/fsa/.
     *
     * @return string
     */
    public function getIsDarkSkyCompliant()
    {
        return $this->isDarkSkyCompliant;
    }

    /**
     * Sets a new isDarkSkyCompliant
     *
     * Y indicates that the item has been certified by the International Dark-Sky Association and incorporates features to reduce light pollution (which reduces the number of stars visible at night), and energy consumption. For example, dark sky compliant lights minimize glare and direct light down. For more information, go to http://darksky.org/fsa/.
     *
     * @param string $isDarkSkyCompliant
     * @return self
     */
    public function setIsDarkSkyCompliant($isDarkSkyCompliant)
    {
        $this->isDarkSkyCompliant = $isDarkSkyCompliant;
        return $this;
    }

    /**
     * Gets as colorTemperature
     *
     * Light color is measured on a temperature scale referred to as Kelvin (K).
     *
     * @return \WalmartDSV\ElectricalType\ColorTemperatureAType
     */
    public function getColorTemperature()
    {
        return $this->colorTemperature;
    }

    /**
     * Sets a new colorTemperature
     *
     * Light color is measured on a temperature scale referred to as Kelvin (K).
     *
     * @param \WalmartDSV\ElectricalType\ColorTemperatureAType $colorTemperature
     * @return self
     */
    public function setColorTemperature(\WalmartDSV\ElectricalType\ColorTemperatureAType $colorTemperature)
    {
        $this->colorTemperature = $colorTemperature;
        return $this;
    }

    /**
     * Gets as decibelRating
     *
     * Measurement, expressed as decibels, of the intensity of sound volume a device produces. Important selection criteria for consumers concerned with effects of products that generate loud noise levels. Attribute applied to such products as power tools and security alarms.
     *
     * @return \WalmartDSV\ElectricalType\DecibelRatingAType
     */
    public function getDecibelRating()
    {
        return $this->decibelRating;
    }

    /**
     * Sets a new decibelRating
     *
     * Measurement, expressed as decibels, of the intensity of sound volume a device produces. Important selection criteria for consumers concerned with effects of products that generate loud noise levels. Attribute applied to such products as power tools and security alarms.
     *
     * @param \WalmartDSV\ElectricalType\DecibelRatingAType $decibelRating
     * @return self
     */
    public function setDecibelRating(\WalmartDSV\ElectricalType\DecibelRatingAType $decibelRating)
    {
        $this->decibelRating = $decibelRating;
        return $this;
    }

    /**
     * Gets as maximumRange
     *
     * Measurement of the maximum area a device can accommodate. Attribute applied to products such as light, motion, and sound sensors.
     *
     * @return \WalmartDSV\AreaUnitType
     */
    public function getMaximumRange()
    {
        return $this->maximumRange;
    }

    /**
     * Sets a new maximumRange
     *
     * Measurement of the maximum area a device can accommodate. Attribute applied to products such as light, motion, and sound sensors.
     *
     * @param \WalmartDSV\AreaUnitType $maximumRange
     * @return self
     */
    public function setMaximumRange(\WalmartDSV\AreaUnitType $maximumRange)
    {
        $this->maximumRange = $maximumRange;
        return $this;
    }

    /**
     * Gets as numberOfGangs
     *
     * Number of individual electrical components that can fit in this product. If product is a light switch, refers to the number of rows of switches on the same base-plate.
     *
     * @return int
     */
    public function getNumberOfGangs()
    {
        return $this->numberOfGangs;
    }

    /**
     * Sets a new numberOfGangs
     *
     * Number of individual electrical components that can fit in this product. If product is a light switch, refers to the number of rows of switches on the same base-plate.
     *
     * @param int $numberOfGangs
     * @return self
     */
    public function setNumberOfGangs($numberOfGangs)
    {
        $this->numberOfGangs = $numberOfGangs;
        return $this;
    }

    /**
     * Gets as numberOfPoles
     *
     * The number of poles contained in electrical distribution equipment. For example, residential circuit breakers can be single or double-pole
     *
     * @return int
     */
    public function getNumberOfPoles()
    {
        return $this->numberOfPoles;
    }

    /**
     * Sets a new numberOfPoles
     *
     * The number of poles contained in electrical distribution equipment. For example, residential circuit breakers can be single or double-pole
     *
     * @param int $numberOfPoles
     * @return self
     */
    public function setNumberOfPoles($numberOfPoles)
    {
        $this->numberOfPoles = $numberOfPoles;
        return $this;
    }

    /**
     * Gets as responseTime
     *
     * The amount of time the pixels in the display take to change from one state to another. Measured in milliseconds.
     *
     * @return \WalmartDSV\ElectricalType\ResponseTimeAType
     */
    public function getResponseTime()
    {
        return $this->responseTime;
    }

    /**
     * Sets a new responseTime
     *
     * The amount of time the pixels in the display take to change from one state to another. Measured in milliseconds.
     *
     * @param \WalmartDSV\ElectricalType\ResponseTimeAType $responseTime
     * @return self
     */
    public function setResponseTime(\WalmartDSV\ElectricalType\ResponseTimeAType $responseTime)
    {
        $this->responseTime = $responseTime;
        return $this;
    }

    /**
     * Gets as americanWireGauge
     *
     * Measurement of how large a wire is, either in diameter or cross sectional area. Applicable to both electrical non-electrical wires, and to structural cable. Used to indicate electrical characteristics or strength. Measurement is expressed using American wire gauge (AWG) standard. As the wire gauge number increases, the wire diameter decreases. AWG of 12 is a smaller diameter than AWG of 6.
     *
     * @return \WalmartDSV\LengthUnitType
     */
    public function getAmericanWireGauge()
    {
        return $this->americanWireGauge;
    }

    /**
     * Sets a new americanWireGauge
     *
     * Measurement of how large a wire is, either in diameter or cross sectional area. Applicable to both electrical non-electrical wires, and to structural cable. Used to indicate electrical characteristics or strength. Measurement is expressed using American wire gauge (AWG) standard. As the wire gauge number increases, the wire diameter decreases. AWG of 12 is a smaller diameter than AWG of 6.
     *
     * @param \WalmartDSV\LengthUnitType $americanWireGauge
     * @return self
     */
    public function setAmericanWireGauge(\WalmartDSV\LengthUnitType $americanWireGauge)
    {
        $this->americanWireGauge = $americanWireGauge;
        return $this;
    }

    /**
     * Gets as mountType
     *
     * How the item is attached. Used for products such as shelving and fixture hardware.
     *
     * @return \WalmartDSV\MountType
     */
    public function getMountType()
    {
        return $this->mountType;
    }

    /**
     * Sets a new mountType
     *
     * How the item is attached. Used for products such as shelving and fixture hardware.
     *
     * @param \WalmartDSV\MountType $mountType
     * @return self
     */
    public function setMountType(\WalmartDSV\MountType $mountType)
    {
        $this->mountType = $mountType;
        return $this;
    }

    /**
     * Gets as isRatedForOutdoorUse
     *
     * Y indicates the item has been specifically rated by the manufacture for outdoor use. Used for products that also have indoor versions. For electrical equipment, suitability for outdoor use is often related to its IP (Ingress Protection) rating.
     *
     * @return string
     */
    public function getIsRatedForOutdoorUse()
    {
        return $this->isRatedForOutdoorUse;
    }

    /**
     * Sets a new isRatedForOutdoorUse
     *
     * Y indicates the item has been specifically rated by the manufacture for outdoor use. Used for products that also have indoor versions. For electrical equipment, suitability for outdoor use is often related to its IP (Ingress Protection) rating.
     *
     * @param string $isRatedForOutdoorUse
     * @return self
     */
    public function setIsRatedForOutdoorUse($isRatedForOutdoorUse)
    {
        $this->isRatedForOutdoorUse = $isRatedForOutdoorUse;
        return $this;
    }

    /**
     * Gets as lifespan
     *
     * Amount of time the product is expected to last, as specified by the manufacturer. Important selection criteria for comparing products such as light bulbs.
     *
     * @return \WalmartDSV\TimeUnitType
     */
    public function getLifespan()
    {
        return $this->lifespan;
    }

    /**
     * Sets a new lifespan
     *
     * Amount of time the product is expected to last, as specified by the manufacturer. Important selection criteria for comparing products such as light bulbs.
     *
     * @param \WalmartDSV\TimeUnitType $lifespan
     * @return self
     */
    public function setLifespan(\WalmartDSV\TimeUnitType $lifespan)
    {
        $this->lifespan = $lifespan;
        return $this;
    }

    /**
     * Gets as character
     *
     * A person or entity portrayed in print or visual media. A character might be a fictional personality or an actual living person.
     *
     * @return \WalmartDSV\CharacterType
     */
    public function getCharacter()
    {
        return $this->character;
    }

    /**
     * Sets a new character
     *
     * A person or entity portrayed in print or visual media. A character might be a fictional personality or an actual living person.
     *
     * @param \WalmartDSV\CharacterType $character
     * @return self
     */
    public function setCharacter(\WalmartDSV\CharacterType $character)
    {
        $this->character = $character;
        return $this;
    }

    /**
     * Gets as sportsLeague
     *
     * If your item has any association with a specific sports league, enter the league name. Abbreviations are fine. NOTE: This attribute flags an item for inclusion in the online fan shop.
     *
     * @return \WalmartDSV\SportsLeagueType
     */
    public function getSportsLeague()
    {
        return $this->sportsLeague;
    }

    /**
     * Sets a new sportsLeague
     *
     * If your item has any association with a specific sports league, enter the league name. Abbreviations are fine. NOTE: This attribute flags an item for inclusion in the online fan shop.
     *
     * @param \WalmartDSV\SportsLeagueType $sportsLeague
     * @return self
     */
    public function setSportsLeague(\WalmartDSV\SportsLeagueType $sportsLeague)
    {
        $this->sportsLeague = $sportsLeague;
        return $this;
    }

    /**
     * Gets as sportsTeam
     *
     * If your item has any association with a specific sports team, enter the team name. NOTE: This attribute flags an item for inclusion in the online fan shop.
     *
     * @return \WalmartDSV\SportsTeamType
     */
    public function getSportsTeam()
    {
        return $this->sportsTeam;
    }

    /**
     * Sets a new sportsTeam
     *
     * If your item has any association with a specific sports team, enter the team name. NOTE: This attribute flags an item for inclusion in the online fan shop.
     *
     * @param \WalmartDSV\SportsTeamType $sportsTeam
     * @return self
     */
    public function setSportsTeam(\WalmartDSV\SportsTeamType $sportsTeam)
    {
        $this->sportsTeam = $sportsTeam;
        return $this;
    }

    /**
     * Gets as athlete
     *
     * A well-known athlete associated with a product, if applicable. This is used to group items in Fan Shop, not to describe a line of clothing.
     *
     * @return \WalmartDSV\AthleteType
     */
    public function getAthlete()
    {
        return $this->athlete;
    }

    /**
     * Sets a new athlete
     *
     * A well-known athlete associated with a product, if applicable. This is used to group items in Fan Shop, not to describe a line of clothing.
     *
     * @param \WalmartDSV\AthleteType $athlete
     * @return self
     */
    public function setAthlete(\WalmartDSV\AthleteType $athlete)
    {
        $this->athlete = $athlete;
        return $this;
    }

    /**
     * Adds as swatchImage
     *
     * @param \WalmartDSV\ElectricalType\SwatchImagesAType\SwatchImageAType $swatchImage
     *@return self
     */
    public function addToSwatchImages(\WalmartDSV\ElectricalType\SwatchImagesAType\SwatchImageAType $swatchImage)
    {
        $this->swatchImages[] = $swatchImage;
        return $this;
    }

    /**
     * isset swatchImages
     *
     * @param int|string $index
     * @return bool
     */
    public function issetSwatchImages($index)
    {
        return isset($this->swatchImages[$index]);
    }

    /**
     * unset swatchImages
     *
     * @param int|string $index
     * @return void
     */
    public function unsetSwatchImages($index)
    {
        unset($this->swatchImages[$index]);
    }

    /**
     * Gets as swatchImages
     *
     * @return \WalmartDSV\ElectricalType\SwatchImagesAType\SwatchImageAType[]
     */
    public function getSwatchImages()
    {
        return $this->swatchImages;
    }

    /**
     * Sets a new swatchImages
     *
     * @param \WalmartDSV\ElectricalType\SwatchImagesAType\SwatchImageAType[] $swatchImages
     * @return self
     */
    public function setSwatchImages(array $swatchImages)
    {
        $this->swatchImages = $swatchImages;
        return $this;
    }


}

