<?php

namespace WalmartDSV;

/**
 * Class representing BraStyleType
 *
 * Brassiere styles/defining features. Enter as many as are relevant to the bra. Do not enter strap styles here. To enter descriptive terms for strap styles, please use the "Upper Body Strap Configuration" attribute.
 * XSD Type: BraStyle
 */
class BraStyleType
{

    /**
     * @var string $braStyleValue
     */
    private $braStyleValue = null;

    /**
     * Gets as braStyleValue
     *
     * @return string
     */
    public function getBraStyleValue()
    {
        return $this->braStyleValue;
    }

    /**
     * Sets a new braStyleValue
     *
     * @param string $braStyleValue
     * @return self
     */
    public function setBraStyleValue($braStyleValue)
    {
        $this->braStyleValue = $braStyleValue;
        return $this;
    }


}

