<?php

namespace WalmartDSV;

/**
 * Class representing ChainPatternType
 *
 * If pertinent, choose a pattern from the example values; if your value does not exist, then enter one of your own.
 * XSD Type: ChainPattern
 */
class ChainPatternType
{

    /**
     * @var string[] $chainPatternValue
     */
    private $chainPatternValue = [
        
    ];

    /**
     * Adds as chainPatternValue
     *
     * @return self
     * @param string $chainPatternValue
     */
    public function addToChainPatternValue($chainPatternValue)
    {
        $this->chainPatternValue[] = $chainPatternValue;
        return $this;
    }

    /**
     * isset chainPatternValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetChainPatternValue($index)
    {
        return isset($this->chainPatternValue[$index]);
    }

    /**
     * unset chainPatternValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetChainPatternValue($index)
    {
        unset($this->chainPatternValue[$index]);
    }

    /**
     * Gets as chainPatternValue
     *
     * @return string[]
     */
    public function getChainPatternValue()
    {
        return $this->chainPatternValue;
    }

    /**
     * Sets a new chainPatternValue
     *
     * @param string $chainPatternValue
     * @return self
     */
    public function setChainPatternValue(array $chainPatternValue)
    {
        $this->chainPatternValue = $chainPatternValue;
        return $this;
    }


}

