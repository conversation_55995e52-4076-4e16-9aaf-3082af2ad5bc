<?php

namespace WalmartDSV;

/**
 * Class representing SportType
 *
 * If the product is sports-related, the name of the specific sport depicted on the product, or the target sport for the product use
 * XSD Type: Sport
 */
class SportType
{

    /**
     * @var string $sportValue
     */
    private $sportValue = null;

    /**
     * Gets as sportValue
     *
     * @return string
     */
    public function getSportValue()
    {
        return $this->sportValue;
    }

    /**
     * Sets a new sportValue
     *
     * @param string $sportValue
     * @return self
     */
    public function setSportValue($sportValue)
    {
        $this->sportValue = $sportValue;
        return $this;
    }


}

