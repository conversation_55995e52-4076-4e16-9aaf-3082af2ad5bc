<?php

namespace WalmartDSV;

/**
 * Class representing FocusType
 *
 * Terms that describe modes, mechanisms, or control arrangements that adjust optical focus on the device. For example, if item is a pair of binoculars, focus type would describe weather the lenses on binoculars can be adjusted independently of one another.
 * XSD Type: FocusType
 */
class FocusType
{

    /**
     * @var string[] $focusTypeValue
     */
    private $focusTypeValue = [
        
    ];

    /**
     * Adds as focusTypeValue
     *
     * @return self
     * @param string $focusTypeValue
     */
    public function addToFocusTypeValue($focusTypeValue)
    {
        $this->focusTypeValue[] = $focusTypeValue;
        return $this;
    }

    /**
     * isset focusTypeValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFocusTypeValue($index)
    {
        return isset($this->focusTypeValue[$index]);
    }

    /**
     * unset focusTypeValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFocusTypeValue($index)
    {
        unset($this->focusTypeValue[$index]);
    }

    /**
     * Gets as focusTypeValue
     *
     * @return string[]
     */
    public function getFocusTypeValue()
    {
        return $this->focusTypeValue;
    }

    /**
     * Sets a new focusTypeValue
     *
     * @param string $focusTypeValue
     * @return self
     */
    public function setFocusTypeValue(array $focusTypeValue)
    {
        $this->focusTypeValue = $focusTypeValue;
        return $this;
    }


}

