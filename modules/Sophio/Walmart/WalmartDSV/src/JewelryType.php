<?php

namespace WalmartDSV;

/**
 * Class representing JewelryType
 *
 *
 * XSD Type: Jewelry
 */
class JewelryType
{

    /**
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @var string[] $additionalVariantAttributeNames
     */
    private $additionalVariantAttributeNames = null;

    /**
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @var string $shortDescription
     */
    private $shortDescription = null;

    /**
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @var string[] $keyFeatures
     */
    private $keyFeatures = null;

    /**
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @var string $brand
     */
    private $brand = null;

    /**
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @var string $manufacturer
     */
    private $manufacturer = null;

    /**
     * MPN uniquely identifies the product to its manufacturer. For many products this will be identical to the model number. Some manufacturers distinguish part number from model number. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @var string $manufacturerPartNumber
     */
    private $manufacturerPartNumber = null;

    /**
     * Model numbers allow manufacturers to keep track of each hardware device and identify or replace the proper part when needed. Model numbers are often found on the bottom, back, or side of a product. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @var string $modelNumber
     */
    private $modelNumber = null;

    /**
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @var int $multipackQuantity
     */
    private $multipackQuantity = null;

    /**
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @var int $countPerPack
     */
    private $countPerPack = null;

    /**
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @var string $count
     */
    private $count = null;

    /**
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @var string $mainImageUrl
     */
    private $mainImageUrl = null;

    /**
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @var string[] $productSecondaryImageURL
     */
    private $productSecondaryImageURL = null;

    /**
     * Material for general jewelry items. Enter precious metals and purities under "Metal Type" and "Metal Stamp."
     *
     * @var \WalmartDSV\MaterialType $material
     */
    private $material = null;

    /**
     * Fine jewelry indicates a high-quality of material, workmanship, and durability. Fashion (sometimes called "costume" or "cosmetic") jewelry is made from inexpensive materials, is usually mass-produced, and has a generally lower level of quality.
     *
     * @var string $jewelryStyle
     */
    private $jewelryStyle = null;

    /**
     * For items made primarily of metal, or where the metal component plays an important part in the makeup of the item and purchasing decision.
     *
     * @var string $metal
     */
    private $metal = null;

    /**
     * Most jewelry items made of precious metal are stamped with information about the purity level of the metal content. Generally the stamp is placed in an inconspicuous place on the item so it does not detract from the design. Stamps will usually be located on the inside of the band on a ring, on the post or basket setting on a pair of earrings, on the bail (the part that the chain slides through) on a pendant, and on the connecting ring or the clasp on a necklace or bracelet. All jewelry stamps adhere to strict guidelines set by the Federal Trade Commission.
     *
     * @var string[] $metalStamp
     */
    private $metalStamp = null;

    /**
     * Measure of the purity or fineness of gold in an alloy, 100% pure gold being 24 Karats.
     *
     * @var \WalmartDSV\JewelryType\KaratsAType $karats
     */
    private $karats = null;

    /**
     * Metal plating. Use for items where the plating is a central feature, or important to the buying decision.
     *
     * @var string $plating
     */
    private $plating = null;

    /**
     * Indicate whether this item is meant for a particular gender or meant to be gender-agnostic (unisex).
     *
     * @var string $gender
     */
    private $gender = null;

    /**
     * General grouping of ages into commonly used demographic labels.
     *
     * @var string[] $ageGroup
     */
    private $ageGroup = null;

    /**
     * Overall dimensions of an item. Used only for products that do not already have a more specific 'x size' attribute, such as ring size or clothing size.
     *
     * @var string $size
     */
    private $size = null;

    /**
     * Color as described by the manufacturer.
     *
     * @var \WalmartDSV\ColorType $color
     */
    private $color = null;

    /**
     * The particular target time, event, or holiday for the product.
     *
     * @var \WalmartDSV\OccasionType $occasion
     */
    private $occasion = null;

    /**
     * General description of qualities that represent a particular aesthetic.
     *
     * @var string $style
     */
    private $style = null;

    /**
     * Descriptive terms for the roles people have in each other's lives, commonly centered on family or romance.
     *
     * @var string[] $personalRelationship
     */
    private $personalRelationship = null;

    /**
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @var string $variantGroupId
     */
    private $variantGroupId = null;

    /**
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @var string[] $variantAttributeNames
     */
    private $variantAttributeNames = null;

    /**
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @var string $isPrimaryVariant
     */
    private $isPrimaryVariant = null;

    /**
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @var string $isPrivateLabelOrUnbranded
     */
    private $isPrivateLabelOrUnbranded = null;

    /**
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @var string $isProp65WarningRequired
     */
    private $isProp65WarningRequired = null;

    /**
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @var string $prop65WarningText
     */
    private $prop65WarningText = null;

    /**
     * To determine if any choking warnings are applicable, check current product packaging for choking warning message(s). Please indicate the warning number (0-6). 0 - No warning applicable; 1 - Choking hazard is a small ball; 2 - Choking hazard contains small ball; 3 - Choking hazard contains small parts; 4 - Choking hazard balloon; 5 - Choking hazard is a marble; 6 - Choking hazard contains a marble.
     *
     * @var int[] $smallPartsWarnings
     */
    private $smallPartsWarnings = null;

    /**
     * "Battery or battery containing product" is defined by Company to include any item of Merchandise that is a battery or any component of Merchandise, including reusable packaging intended to stay in use with the item, containing a battery of any chemistry/ type. Mark Y if this definition applies to your product.
     *
     * @var string $hasBatteries
     */
    private $hasBatteries = null;

    /**
     * Please select the Battery Technology Type from the list provided. NOTE: If battery type is lead acid, lead acid (nonspillable), lithium ion, or lithium metal, please ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @var string $batteryTechnologyType
     */
    private $batteryTechnologyType = null;

    /**
     * Y indicates the item comes with a warranty. If an item has a warranty, then enter EITHER the warranty URL or the warranty text in the appropriate field.
     *
     * @var string $hasWarranty
     */
    private $hasWarranty = null;

    /**
     * If you indicated that your item has a warranty, provide either the Warranty URL or Warranty Text. The Warranty URL is the web location of the image, PDF, or link to the manufacturer's warranty page, showing the warranty and its terms, including the duration of the warranty. URLs must begin with http:// or https:// NOTE: Please remember to update the link and/or text of the warranty as the warranty changes. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @var string $warrantyURL
     */
    private $warrantyURL = null;

    /**
     * If you marked Y for "Has Warranty" provide the Warranty URL or Warranty Text (the full text of the warranty terms, including what is covered by the warranty and the duration of the warranty). NOTE: please remember to update the text of your warranty as your warranty changes.
     *
     * @var string $warrantyText
     */
    private $warrantyText = null;

    /**
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @var string $hasStateRestrictions
     */
    private $hasStateRestrictions = null;

    /**
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @var \WalmartDSV\StateRestrictionType[] $stateRestrictions
     */
    private $stateRestrictions = null;

    /**
     * If the item has a certificate, enter the certificate number here. Separate multiple values by semicolons.
     *
     * @var string[] $certificateNumber
     */
    private $certificateNumber = null;

    /**
     * Name of month associated with this gemstone as a birthstone. The format used is: Month- Stone
     *
     * @var string $birthstone
     */
    private $birthstone = null;

    /**
     * Decorative design or visual ornamentation, often with a thematic, recurring motif.
     *
     * @var \WalmartDSV\PatternType $pattern
     */
    private $pattern = null;

    /**
     * If your item has a clasp, then choose a clasp type from the example values. If your value does not exist, enter one of your own.
     *
     * @var string[] $claspType
     */
    private $claspType = null;

    /**
     * Kind of hardware used in the "back" of the jewelry, as in the earring back that helps to fasten the earring to the ear. This may be, for example, the earring finding or jewelry backing style.
     *
     * @var string $backFinding
     */
    private $backFinding = null;

    /**
     * Method or style in which stones are attached to a piece of jewelry.
     *
     * @var string[] $jewelrySetting
     */
    private $jewelrySetting = null;

    /**
     * Style of earring, as represented by fashion or form. Select from example values; if your value does not exist, enter your own.
     *
     * @var string $earringStyle
     */
    private $earringStyle = null;

    /**
     * Features distinct to earring jewelry.
     *
     * @var string[] $earringFeature
     */
    private $earringFeature = null;

    /**
     * Style of bracelet as expressed by its fashion or form. Choose a value from the example values; if your value does not exist, then enter your own value.
     *
     * @var string $braceletStyle
     */
    private $braceletStyle = null;

    /**
     * Style of necklace as expressed by fashion or form. Select from the example values; if your value does not exist, enter your own.
     *
     * @var string $necklaceStyle
     */
    private $necklaceStyle = null;

    /**
     * Enter length of jewelry chain, in inches or feet.
     *
     * @var \WalmartDSV\JewelryType\ChainLengthAType $chainLength
     */
    private $chainLength = null;

    /**
     * If pertinent, choose a pattern from the example values; if your value does not exist, then enter one of your own.
     *
     * @var string[] $chainPattern
     */
    private $chainPattern = null;

    /**
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @var string[] $globalBrandLicense
     */
    private $globalBrandLicense = null;

    /**
     * Diamond clarity is a quality of diamonds relating to the existence and visual appearance of internal characteristics of a diamond called inclusions, and surface defects called blemishes.
     *
     * @var string $diamondClarity
     */
    private $diamondClarity = null;

    /**
     * The style or design followed when cutting a diamond. This does not refer to the shape of the diamond, but the pattern of the cut itself.
     *
     * @var string $diamondCut
     */
    private $diamondCut = null;

    /**
     * Measure of the weight (mass) of gemstones and pearls. One carat (ct) is equal to 200 mg.
     *
     * @var \WalmartDSV\JewelryType\CaratsAType $carats
     */
    private $carats = null;

    /**
     * Total diamond weight is the combined carat weight of all the diamonds in a piece of jewelry. Paired earrings would be considered a single piece, therefore if each earring contained a diamond with a carat weight of 1/2, the value entered would be 1 ct (2 x 1/2 ct).
     *
     * @var \WalmartDSV\JewelryType\TotalDiamondWeightAType $totalDiamondWeight
     */
    private $totalDiamondWeight = null;

    /**
     * Total gemstone weight is the combined carat weight of all of the gemstones in a piece of jewelry. Paired earrings would be considered a single piece, therefore if each earring contained a gemstone with a carat weight of 1/2, the value entered would be 1 ct (2 x 1/2 ct). This is a useful indication of gemstone sizes, provided the stones in question are of the same type. Differing types have differing densities so the carat weight will not necessarily provide an accurate indication of size for comparison purposes.
     *
     * @var float $totalGemWeight
     */
    private $totalGemWeight = null;

    /**
     * Type of gemstone cut, as distinct from shape
     *
     * @var string $gemstoneCut
     */
    private $gemstoneCut = null;

    /**
     * The gemstone trade name (i.e., amethyst, not purple quartz).
     *
     * @var \WalmartDSV\GemstoneType $gemstone
     */
    private $gemstone = null;

    /**
     * The color of the gemstone including factors of hue, tone and saturation. Expressed as a color grade, or descriptors (light rose).
     *
     * @var string $gemstoneColor
     */
    private $gemstoneColor = null;

    /**
     * Note that while the clarity rating scale (VVS, VS, etc.) is the same for colored gems as it is for diamonds, the meaning is different: Rating definitions vary based on the type of colored gem. Please refer to the specific ratings definitions for Type 1, Type 2, or Type 3 gems.
     *
     * @var string $gemstoneClarity
     */
    private $gemstoneClarity = null;

    /**
     * Whether the stone was created by nature or humans
     *
     * @var string $stoneCreationMethod
     */
    private $stoneCreationMethod = null;

    /**
     * Describes the process a gemstone has undergone to produce changes in the durability, color, and/or clarity that are beyond the typical gemstone conditioning.
     *
     * @var string $stoneTreatment
     */
    private $stoneTreatment = null;

    /**
     * The measurement of the height of the stone (vs. the setting) as measured the from the table (top of the stone) to the culet (bottom of the stone).
     *
     * @var \WalmartDSV\JewelryType\StoneHeightAType $stoneHeight
     */
    private $stoneHeight = null;

    /**
     * The measure of the largest dimensional side of a gemstone with the face pointing towards the observer.
     *
     * @var \WalmartDSV\JewelryType\StoneLengthAType $stoneLength
     */
    private $stoneLength = null;

    /**
     * The measure of the narrower dimensional side of a gemstone with the face pointing towards the observer.
     *
     * @var \WalmartDSV\JewelryType\StoneWidthAType $stoneWidth
     */
    private $stoneWidth = null;

    /**
     * This is calculated by the following formula: depth / width.
     *
     * @var float $stoneDepthPercentage
     */
    private $stoneDepthPercentage = null;

    /**
     * In a grading report, table percentage is calculated based on the size of the table divided by the average girdle diameter of the gem. So, a 60 percent table means that the table is 60 percent wide as the gem's outline.
     *
     * @var float $stoneTablePercentage
     */
    private $stoneTablePercentage = null;

    /**
     * Symmetry refers to the exactness of the shape and placement of the facets. The GIA grades both polish and symmetry and lists it on the certificate using the scale: Excellent, Very Good, Good, Fair and Poor.
     *
     * @var string $stoneSymmetry
     */
    private $stoneSymmetry = null;

    /**
     * Polish refers to the degree of smoothness of each facet of a gem as measured by a gemologist. When a gem is cut and polished, microscopic surface defects may be created by the polishing wheel as it drags tiny dislodged crystals across the gem's surface.
     *
     * @var string $stonePolish
     */
    private $stonePolish = null;

    /**
     * The girdle is the thin perimeter of a stone, dividing the crown above from the pavilion below. When viewing a stone in its setting or from a profile view, the girdle is the widest part (or the circumference) of the polished stone - the portion of the stone that makes contact with the setting itself.
     *
     * @var string $stoneGirdle
     */
    private $stoneGirdle = null;

    /**
     * The culet (pronounced cue-let) is the small area at the bottom of a diamond's pavilion. The culet can be a point or a very small facet sitting parallel to the table.
     *
     * @var string $stoneCulet
     */
    private $stoneCulet = null;

    /**
     * Used to describe the color and strength of the UV light reflected off some diamonds.
     *
     * @var string $stoneFluoresence
     */
    private $stoneFluoresence = null;

    /**
     * Describes the method for growing pearls and information about the origin of the pearl.
     *
     * @var string[] $pearlType
     */
    private $pearlType = null;

    /**
     * The color of the pearl's body, vs. the overtone.
     *
     * @var string[] $pearlBodyColor
     */
    private $pearlBodyColor = null;

    /**
     * Measure of the quality and quantity of light that reflects off a pearl.
     *
     * @var string[] $pearlLustre
     */
    private $pearlLustre = null;

    /**
     * The shape of the pearl, defined in part by how regular and smooth the surface of the pearl is.
     *
     * @var string[] $pearlShape
     */
    private $pearlShape = null;

    /**
     * Describes how well the individual pearls have been matched within a piece of pearl jewelry. Selection factor for quality and appearance, typically applied to pearl-strand necklaces. Values range from Excellent to Poor, depending on the degree of noticeable variations.
     *
     * @var string[] $pearlUniformity
     */
    private $pearlUniformity = null;

    /**
     * Evaluation of pearls based on the blemishes or irregularities in the pearl’s surface, based on size, number, nature, location, visibility and type of surface characteristics. Values range from Clean to Heavily Spotted.
     *
     * @var string[] $pearlSurfaceBlemishes
     */
    private $pearlSurfaceBlemishes = null;

    /**
     * Thickness of the nacre, or iridescent layers which are produced by the mollusk to coat the nucleus of the pearl.
     *
     * @var \WalmartDSV\JewelryType\PearlNacreThicknessAType $pearlNacreThickness
     */
    private $pearlNacreThickness = null;

    /**
     * Describes the knotting method and material used in constructing the string of pearls.
     *
     * @var string[] $pearlStringingMethod
     */
    private $pearlStringingMethod = null;

    /**
     * Diameter of each pearl in millimeters.
     *
     * @var \WalmartDSV\JewelryType\SizePerPearlAType $sizePerPearl
     */
    private $sizePerPearl = null;

    /**
     * Please provide a count of individual pearls on a single item.
     *
     * @var int $numberOfPearls
     */
    private $numberOfPearls = null;

    /**
     * If the item is engraved with an inscription, put the inscription text here.
     *
     * @var string $inscription
     */
    private $inscription = null;

    /**
     * Indicates that the item is resizable.
     *
     * @var string $isResizable
     */
    private $isResizable = null;

    /**
     * Indicates the smallest size this ring can become if resized. Used with Ring Sizing Upper Range provides total size range if ring is resizable.
     *
     * @var float $ringSizingLowerRange
     */
    private $ringSizingLowerRange = null;

    /**
     * Indicates the largest size this ring can become if resized. Used with Ring Sizing Lower Range provides total size range if ring is resizable.
     *
     * @var float $ringSizingUpperRange
     */
    private $ringSizingUpperRange = null;

    /**
     * Form or design of a ring
     *
     * @var string[] $ringStyle
     */
    private $ringStyle = null;

    /**
     * Standardized numerical sizing of a jewelry ring (based on circumference), as utilized in the United States, Canada and Mexico. Can include quarter and half sizes, depending on which standard is used.
     *
     * @var float $ringSize
     */
    private $ringSize = null;

    /**
     * Measurement of a circular object around its perimeter. NOTE: for rings, measurement is taken from the inside of the ring.
     *
     * @var \WalmartDSV\JewelryType\CircumferenceAType $circumference
     */
    private $circumference = null;

    /**
     * The measurement from one side of a circle to the other, through the middle. NOTE: For rings, the measurement is taken from the inside of the ring.
     *
     * @var \WalmartDSV\JewelryType\DiameterAType $diameter
     */
    private $diameter = null;

    /**
     * One of five major ring-sizing standards.
     *
     * @var string $ringSizeStandard
     */
    private $ringSizeStandard = null;

    /**
     * If your item has any association with a specific sports league, enter the league name. Abbreviations are fine. NOTE: This attribute flags an item for inclusion in the online fan shop.
     *
     * @var \WalmartDSV\SportsLeagueType $sportsLeague
     */
    private $sportsLeague = null;

    /**
     * If your item has any association with a specific sports team, enter the team name. NOTE: This attribute flags an item for inclusion in the online fan shop.
     *
     * @var \WalmartDSV\SportsTeamType $sportsTeam
     */
    private $sportsTeam = null;

    /**
     * A dominant idea, meaning, or setting applied to an item. Used in a wide range of products including decorative objects, clothing, toys, and furniture. Can be an important selection criteria for consumers who want to achieve a particular ambiance for room décor or for a special occasion.
     *
     * @var \WalmartDSV\ThemeType $theme
     */
    private $theme = null;

    /**
     * AWG indicates wire diameter. Both electrical wiring and jewelry piercing industries use AWG to describe product diameters. In the jewelry industry, AWG is used to describe both metallic and non-metallic products.
     *
     * @var int $americanWireGuage
     */
    private $americanWireGuage = null;

    /**
     * A well-known athlete associated with a product, if applicable. This is used to group items in Fan Shop, not to describe a line of clothing.
     *
     * @var \WalmartDSV\AthleteType $athlete
     */
    private $athlete = null;

    /**
     * List notable features of the item.
     *
     * @var string[] $features
     */
    private $features = null;

    /**
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @var string $keywords
     */
    private $keywords = null;

    /**
     * Indicates that the item is made from recycled materials.
     *
     * @var string $isMadeFromRecycledMaterial
     */
    private $isMadeFromRecycledMaterial = null;

    /**
     * If item contains reused/recycled material, the percentage of all recycled material used to produce the item. This can also include specific material composition; cushions made from 30% recycled cotton fabric. Used to highlight sustainability to the customer.
     *
     * @var \WalmartDSV\RecycledMaterialContentValueType[] $recycledMaterialContent
     */
    private $recycledMaterialContent = null;

    /**
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @var string[] $colorCategory
     */
    private $colorCategory = null;

    /**
     * A person or entity portrayed in print or visual media. A character might be a fictional personality or an actual living person.
     *
     * @var \WalmartDSV\CharacterType $character
     */
    private $character = null;

    /**
     * The body part/s for which the item is intended.
     *
     * @var string[] $bodyParts
     */
    private $bodyParts = null;

    /**
     * Name of the person who planned the form, look, or workings of the product to be made or built.
     *
     * @var string $designer
     */
    private $designer = null;

    /**
     * @var \WalmartDSV\JewelryType\SwatchImagesAType\SwatchImageAType[] $swatchImages
     */
    private $swatchImages = null;

    /**
     * Adds as additionalVariantAttributeName
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @return self
     * @param string $additionalVariantAttributeName
     */
    public function addToAdditionalVariantAttributeNames($additionalVariantAttributeName)
    {
        $this->additionalVariantAttributeNames[] = $additionalVariantAttributeName;
        return $this;
    }

    /**
     * isset additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAdditionalVariantAttributeNames($index)
    {
        return isset($this->additionalVariantAttributeNames[$index]);
    }

    /**
     * unset additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAdditionalVariantAttributeNames($index)
    {
        unset($this->additionalVariantAttributeNames[$index]);
    }

    /**
     * Gets as additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @return string[]
     */
    public function getAdditionalVariantAttributeNames()
    {
        return $this->additionalVariantAttributeNames;
    }

    /**
     * Sets a new additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param string $additionalVariantAttributeNames
     * @return self
     */
    public function setAdditionalVariantAttributeNames(array $additionalVariantAttributeNames)
    {
        $this->additionalVariantAttributeNames = $additionalVariantAttributeNames;
        return $this;
    }

    /**
     * Gets as shortDescription
     *
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @return string
     */
    public function getShortDescription()
    {
        return $this->shortDescription;
    }

    /**
     * Sets a new shortDescription
     *
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @param string $shortDescription
     * @return self
     */
    public function setShortDescription($shortDescription)
    {
        $this->shortDescription = $shortDescription;
        return $this;
    }

    /**
     * Adds as keyFeaturesValue
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @return self
     * @param string $keyFeaturesValue
     */
    public function addToKeyFeatures($keyFeaturesValue)
    {
        $this->keyFeatures[] = $keyFeaturesValue;
        return $this;
    }

    /**
     * isset keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetKeyFeatures($index)
    {
        return isset($this->keyFeatures[$index]);
    }

    /**
     * unset keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetKeyFeatures($index)
    {
        unset($this->keyFeatures[$index]);
    }

    /**
     * Gets as keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @return string[]
     */
    public function getKeyFeatures()
    {
        return $this->keyFeatures;
    }

    /**
     * Sets a new keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param string $keyFeatures
     * @return self
     */
    public function setKeyFeatures(array $keyFeatures)
    {
        $this->keyFeatures = $keyFeatures;
        return $this;
    }

    /**
     * Gets as brand
     *
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @return string
     */
    public function getBrand()
    {
        return $this->brand;
    }

    /**
     * Sets a new brand
     *
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @param string $brand
     * @return self
     */
    public function setBrand($brand)
    {
        $this->brand = $brand;
        return $this;
    }

    /**
     * Gets as manufacturer
     *
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @return string
     */
    public function getManufacturer()
    {
        return $this->manufacturer;
    }

    /**
     * Sets a new manufacturer
     *
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @param string $manufacturer
     * @return self
     */
    public function setManufacturer($manufacturer)
    {
        $this->manufacturer = $manufacturer;
        return $this;
    }

    /**
     * Gets as manufacturerPartNumber
     *
     * MPN uniquely identifies the product to its manufacturer. For many products this will be identical to the model number. Some manufacturers distinguish part number from model number. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @return string
     */
    public function getManufacturerPartNumber()
    {
        return $this->manufacturerPartNumber;
    }

    /**
     * Sets a new manufacturerPartNumber
     *
     * MPN uniquely identifies the product to its manufacturer. For many products this will be identical to the model number. Some manufacturers distinguish part number from model number. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @param string $manufacturerPartNumber
     * @return self
     */
    public function setManufacturerPartNumber($manufacturerPartNumber)
    {
        $this->manufacturerPartNumber = $manufacturerPartNumber;
        return $this;
    }

    /**
     * Gets as modelNumber
     *
     * Model numbers allow manufacturers to keep track of each hardware device and identify or replace the proper part when needed. Model numbers are often found on the bottom, back, or side of a product. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @return string
     */
    public function getModelNumber()
    {
        return $this->modelNumber;
    }

    /**
     * Sets a new modelNumber
     *
     * Model numbers allow manufacturers to keep track of each hardware device and identify or replace the proper part when needed. Model numbers are often found on the bottom, back, or side of a product. Having this information allows customers to search for items on the site and informs product matching.
     *
     * @param string $modelNumber
     * @return self
     */
    public function setModelNumber($modelNumber)
    {
        $this->modelNumber = $modelNumber;
        return $this;
    }

    /**
     * Gets as multipackQuantity
     *
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @return int
     */
    public function getMultipackQuantity()
    {
        return $this->multipackQuantity;
    }

    /**
     * Sets a new multipackQuantity
     *
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @param int $multipackQuantity
     * @return self
     */
    public function setMultipackQuantity($multipackQuantity)
    {
        $this->multipackQuantity = $multipackQuantity;
        return $this;
    }

    /**
     * Gets as countPerPack
     *
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @return int
     */
    public function getCountPerPack()
    {
        return $this->countPerPack;
    }

    /**
     * Sets a new countPerPack
     *
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @param int $countPerPack
     * @return self
     */
    public function setCountPerPack($countPerPack)
    {
        $this->countPerPack = $countPerPack;
        return $this;
    }

    /**
     * Gets as count
     *
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @return string
     */
    public function getCount()
    {
        return $this->count;
    }

    /**
     * Sets a new count
     *
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @param string $count
     * @return self
     */
    public function setCount($count)
    {
        $this->count = $count;
        return $this;
    }

    /**
     * Gets as mainImageUrl
     *
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return string
     */
    public function getMainImageUrl()
    {
        return $this->mainImageUrl;
    }

    /**
     * Sets a new mainImageUrl
     *
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param string $mainImageUrl
     * @return self
     */
    public function setMainImageUrl($mainImageUrl)
    {
        $this->mainImageUrl = $mainImageUrl;
        return $this;
    }

    /**
     * Adds as productSecondaryImageURLValue
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return self
     * @param string $productSecondaryImageURLValue
     */
    public function addToProductSecondaryImageURL($productSecondaryImageURLValue)
    {
        $this->productSecondaryImageURL[] = $productSecondaryImageURLValue;
        return $this;
    }

    /**
     * isset productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetProductSecondaryImageURL($index)
    {
        return isset($this->productSecondaryImageURL[$index]);
    }

    /**
     * unset productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetProductSecondaryImageURL($index)
    {
        unset($this->productSecondaryImageURL[$index]);
    }

    /**
     * Gets as productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return string[]
     */
    public function getProductSecondaryImageURL()
    {
        return $this->productSecondaryImageURL;
    }

    /**
     * Sets a new productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param string $productSecondaryImageURL
     * @return self
     */
    public function setProductSecondaryImageURL(array $productSecondaryImageURL)
    {
        $this->productSecondaryImageURL = $productSecondaryImageURL;
        return $this;
    }

    /**
     * Gets as material
     *
     * Material for general jewelry items. Enter precious metals and purities under "Metal Type" and "Metal Stamp."
     *
     * @return \WalmartDSV\MaterialType
     */
    public function getMaterial()
    {
        return $this->material;
    }

    /**
     * Sets a new material
     *
     * Material for general jewelry items. Enter precious metals and purities under "Metal Type" and "Metal Stamp."
     *
     * @param \WalmartDSV\MaterialType $material
     * @return self
     */
    public function setMaterial(\WalmartDSV\MaterialType $material)
    {
        $this->material = $material;
        return $this;
    }

    /**
     * Gets as jewelryStyle
     *
     * Fine jewelry indicates a high-quality of material, workmanship, and durability. Fashion (sometimes called "costume" or "cosmetic") jewelry is made from inexpensive materials, is usually mass-produced, and has a generally lower level of quality.
     *
     * @return string
     */
    public function getJewelryStyle()
    {
        return $this->jewelryStyle;
    }

    /**
     * Sets a new jewelryStyle
     *
     * Fine jewelry indicates a high-quality of material, workmanship, and durability. Fashion (sometimes called "costume" or "cosmetic") jewelry is made from inexpensive materials, is usually mass-produced, and has a generally lower level of quality.
     *
     * @param string $jewelryStyle
     * @return self
     */
    public function setJewelryStyle($jewelryStyle)
    {
        $this->jewelryStyle = $jewelryStyle;
        return $this;
    }

    /**
     * Gets as metal
     *
     * For items made primarily of metal, or where the metal component plays an important part in the makeup of the item and purchasing decision.
     *
     * @return string
     */
    public function getMetal()
    {
        return $this->metal;
    }

    /**
     * Sets a new metal
     *
     * For items made primarily of metal, or where the metal component plays an important part in the makeup of the item and purchasing decision.
     *
     * @param string $metal
     * @return self
     */
    public function setMetal($metal)
    {
        $this->metal = $metal;
        return $this;
    }

    /**
     * Adds as metalStampValue
     *
     * Most jewelry items made of precious metal are stamped with information about the purity level of the metal content. Generally the stamp is placed in an inconspicuous place on the item so it does not detract from the design. Stamps will usually be located on the inside of the band on a ring, on the post or basket setting on a pair of earrings, on the bail (the part that the chain slides through) on a pendant, and on the connecting ring or the clasp on a necklace or bracelet. All jewelry stamps adhere to strict guidelines set by the Federal Trade Commission.
     *
     * @return self
     * @param string $metalStampValue
     */
    public function addToMetalStamp($metalStampValue)
    {
        $this->metalStamp[] = $metalStampValue;
        return $this;
    }

    /**
     * isset metalStamp
     *
     * Most jewelry items made of precious metal are stamped with information about the purity level of the metal content. Generally the stamp is placed in an inconspicuous place on the item so it does not detract from the design. Stamps will usually be located on the inside of the band on a ring, on the post or basket setting on a pair of earrings, on the bail (the part that the chain slides through) on a pendant, and on the connecting ring or the clasp on a necklace or bracelet. All jewelry stamps adhere to strict guidelines set by the Federal Trade Commission.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetMetalStamp($index)
    {
        return isset($this->metalStamp[$index]);
    }

    /**
     * unset metalStamp
     *
     * Most jewelry items made of precious metal are stamped with information about the purity level of the metal content. Generally the stamp is placed in an inconspicuous place on the item so it does not detract from the design. Stamps will usually be located on the inside of the band on a ring, on the post or basket setting on a pair of earrings, on the bail (the part that the chain slides through) on a pendant, and on the connecting ring or the clasp on a necklace or bracelet. All jewelry stamps adhere to strict guidelines set by the Federal Trade Commission.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetMetalStamp($index)
    {
        unset($this->metalStamp[$index]);
    }

    /**
     * Gets as metalStamp
     *
     * Most jewelry items made of precious metal are stamped with information about the purity level of the metal content. Generally the stamp is placed in an inconspicuous place on the item so it does not detract from the design. Stamps will usually be located on the inside of the band on a ring, on the post or basket setting on a pair of earrings, on the bail (the part that the chain slides through) on a pendant, and on the connecting ring or the clasp on a necklace or bracelet. All jewelry stamps adhere to strict guidelines set by the Federal Trade Commission.
     *
     * @return string[]
     */
    public function getMetalStamp()
    {
        return $this->metalStamp;
    }

    /**
     * Sets a new metalStamp
     *
     * Most jewelry items made of precious metal are stamped with information about the purity level of the metal content. Generally the stamp is placed in an inconspicuous place on the item so it does not detract from the design. Stamps will usually be located on the inside of the band on a ring, on the post or basket setting on a pair of earrings, on the bail (the part that the chain slides through) on a pendant, and on the connecting ring or the clasp on a necklace or bracelet. All jewelry stamps adhere to strict guidelines set by the Federal Trade Commission.
     *
     * @param string $metalStamp
     * @return self
     */
    public function setMetalStamp(array $metalStamp)
    {
        $this->metalStamp = $metalStamp;
        return $this;
    }

    /**
     * Gets as karats
     *
     * Measure of the purity or fineness of gold in an alloy, 100% pure gold being 24 Karats.
     *
     * @return \WalmartDSV\JewelryType\KaratsAType
     */
    public function getKarats()
    {
        return $this->karats;
    }

    /**
     * Sets a new karats
     *
     * Measure of the purity or fineness of gold in an alloy, 100% pure gold being 24 Karats.
     *
     * @param \WalmartDSV\JewelryType\KaratsAType $karats
     * @return self
     */
    public function setKarats(\WalmartDSV\JewelryType\KaratsAType $karats)
    {
        $this->karats = $karats;
        return $this;
    }

    /**
     * Gets as plating
     *
     * Metal plating. Use for items where the plating is a central feature, or important to the buying decision.
     *
     * @return string
     */
    public function getPlating()
    {
        return $this->plating;
    }

    /**
     * Sets a new plating
     *
     * Metal plating. Use for items where the plating is a central feature, or important to the buying decision.
     *
     * @param string $plating
     * @return self
     */
    public function setPlating($plating)
    {
        $this->plating = $plating;
        return $this;
    }

    /**
     * Gets as gender
     *
     * Indicate whether this item is meant for a particular gender or meant to be gender-agnostic (unisex).
     *
     * @return string
     */
    public function getGender()
    {
        return $this->gender;
    }

    /**
     * Sets a new gender
     *
     * Indicate whether this item is meant for a particular gender or meant to be gender-agnostic (unisex).
     *
     * @param string $gender
     * @return self
     */
    public function setGender($gender)
    {
        $this->gender = $gender;
        return $this;
    }

    /**
     * Adds as ageGroupValue
     *
     * General grouping of ages into commonly used demographic labels.
     *
     * @return self
     * @param string $ageGroupValue
     */
    public function addToAgeGroup($ageGroupValue)
    {
        $this->ageGroup[] = $ageGroupValue;
        return $this;
    }

    /**
     * isset ageGroup
     *
     * General grouping of ages into commonly used demographic labels.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAgeGroup($index)
    {
        return isset($this->ageGroup[$index]);
    }

    /**
     * unset ageGroup
     *
     * General grouping of ages into commonly used demographic labels.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAgeGroup($index)
    {
        unset($this->ageGroup[$index]);
    }

    /**
     * Gets as ageGroup
     *
     * General grouping of ages into commonly used demographic labels.
     *
     * @return string[]
     */
    public function getAgeGroup()
    {
        return $this->ageGroup;
    }

    /**
     * Sets a new ageGroup
     *
     * General grouping of ages into commonly used demographic labels.
     *
     * @param string $ageGroup
     * @return self
     */
    public function setAgeGroup(array $ageGroup)
    {
        $this->ageGroup = $ageGroup;
        return $this;
    }

    /**
     * Gets as size
     *
     * Overall dimensions of an item. Used only for products that do not already have a more specific 'x size' attribute, such as ring size or clothing size.
     *
     * @return string
     */
    public function getSize()
    {
        return $this->size;
    }

    /**
     * Sets a new size
     *
     * Overall dimensions of an item. Used only for products that do not already have a more specific 'x size' attribute, such as ring size or clothing size.
     *
     * @param string $size
     * @return self
     */
    public function setSize($size)
    {
        $this->size = $size;
        return $this;
    }

    /**
     * Gets as color
     *
     * Color as described by the manufacturer.
     *
     * @return \WalmartDSV\ColorType
     */
    public function getColor()
    {
        return $this->color;
    }

    /**
     * Sets a new color
     *
     * Color as described by the manufacturer.
     *
     * @param \WalmartDSV\ColorType $color
     * @return self
     */
    public function setColor(\WalmartDSV\ColorType $color)
    {
        $this->color = $color;
        return $this;
    }

    /**
     * Gets as occasion
     *
     * The particular target time, event, or holiday for the product.
     *
     * @return \WalmartDSV\OccasionType
     */
    public function getOccasion()
    {
        return $this->occasion;
    }

    /**
     * Sets a new occasion
     *
     * The particular target time, event, or holiday for the product.
     *
     * @param \WalmartDSV\OccasionType $occasion
     * @return self
     */
    public function setOccasion(\WalmartDSV\OccasionType $occasion)
    {
        $this->occasion = $occasion;
        return $this;
    }

    /**
     * Gets as style
     *
     * General description of qualities that represent a particular aesthetic.
     *
     * @return string
     */
    public function getStyle()
    {
        return $this->style;
    }

    /**
     * Sets a new style
     *
     * General description of qualities that represent a particular aesthetic.
     *
     * @param string $style
     * @return self
     */
    public function setStyle($style)
    {
        $this->style = $style;
        return $this;
    }

    /**
     * Adds as personalRelationshipValue
     *
     * Descriptive terms for the roles people have in each other's lives, commonly centered on family or romance.
     *
     * @return self
     * @param string $personalRelationshipValue
     */
    public function addToPersonalRelationship($personalRelationshipValue)
    {
        $this->personalRelationship[] = $personalRelationshipValue;
        return $this;
    }

    /**
     * isset personalRelationship
     *
     * Descriptive terms for the roles people have in each other's lives, commonly centered on family or romance.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetPersonalRelationship($index)
    {
        return isset($this->personalRelationship[$index]);
    }

    /**
     * unset personalRelationship
     *
     * Descriptive terms for the roles people have in each other's lives, commonly centered on family or romance.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetPersonalRelationship($index)
    {
        unset($this->personalRelationship[$index]);
    }

    /**
     * Gets as personalRelationship
     *
     * Descriptive terms for the roles people have in each other's lives, commonly centered on family or romance.
     *
     * @return string[]
     */
    public function getPersonalRelationship()
    {
        return $this->personalRelationship;
    }

    /**
     * Sets a new personalRelationship
     *
     * Descriptive terms for the roles people have in each other's lives, commonly centered on family or romance.
     *
     * @param string $personalRelationship
     * @return self
     */
    public function setPersonalRelationship(array $personalRelationship)
    {
        $this->personalRelationship = $personalRelationship;
        return $this;
    }

    /**
     * Gets as variantGroupId
     *
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @return string
     */
    public function getVariantGroupId()
    {
        return $this->variantGroupId;
    }

    /**
     * Sets a new variantGroupId
     *
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @param string $variantGroupId
     * @return self
     */
    public function setVariantGroupId($variantGroupId)
    {
        $this->variantGroupId = $variantGroupId;
        return $this;
    }

    /**
     * Adds as variantAttributeName
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @return self
     * @param string $variantAttributeName
     */
    public function addToVariantAttributeNames($variantAttributeName)
    {
        $this->variantAttributeNames[] = $variantAttributeName;
        return $this;
    }

    /**
     * isset variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetVariantAttributeNames($index)
    {
        return isset($this->variantAttributeNames[$index]);
    }

    /**
     * unset variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetVariantAttributeNames($index)
    {
        unset($this->variantAttributeNames[$index]);
    }

    /**
     * Gets as variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @return string[]
     */
    public function getVariantAttributeNames()
    {
        return $this->variantAttributeNames;
    }

    /**
     * Sets a new variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param string $variantAttributeNames
     * @return self
     */
    public function setVariantAttributeNames(array $variantAttributeNames)
    {
        $this->variantAttributeNames = $variantAttributeNames;
        return $this;
    }

    /**
     * Gets as isPrimaryVariant
     *
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @return string
     */
    public function getIsPrimaryVariant()
    {
        return $this->isPrimaryVariant;
    }

    /**
     * Sets a new isPrimaryVariant
     *
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @param string $isPrimaryVariant
     * @return self
     */
    public function setIsPrimaryVariant($isPrimaryVariant)
    {
        $this->isPrimaryVariant = $isPrimaryVariant;
        return $this;
    }

    /**
     * Gets as isPrivateLabelOrUnbranded
     *
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @return string
     */
    public function getIsPrivateLabelOrUnbranded()
    {
        return $this->isPrivateLabelOrUnbranded;
    }

    /**
     * Sets a new isPrivateLabelOrUnbranded
     *
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @param string $isPrivateLabelOrUnbranded
     * @return self
     */
    public function setIsPrivateLabelOrUnbranded($isPrivateLabelOrUnbranded)
    {
        $this->isPrivateLabelOrUnbranded = $isPrivateLabelOrUnbranded;
        return $this;
    }

    /**
     * Gets as isProp65WarningRequired
     *
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @return string
     */
    public function getIsProp65WarningRequired()
    {
        return $this->isProp65WarningRequired;
    }

    /**
     * Sets a new isProp65WarningRequired
     *
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @param string $isProp65WarningRequired
     * @return self
     */
    public function setIsProp65WarningRequired($isProp65WarningRequired)
    {
        $this->isProp65WarningRequired = $isProp65WarningRequired;
        return $this;
    }

    /**
     * Gets as prop65WarningText
     *
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @return string
     */
    public function getProp65WarningText()
    {
        return $this->prop65WarningText;
    }

    /**
     * Sets a new prop65WarningText
     *
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @param string $prop65WarningText
     * @return self
     */
    public function setProp65WarningText($prop65WarningText)
    {
        $this->prop65WarningText = $prop65WarningText;
        return $this;
    }

    /**
     * Adds as smallPartsWarning
     *
     * To determine if any choking warnings are applicable, check current product packaging for choking warning message(s). Please indicate the warning number (0-6). 0 - No warning applicable; 1 - Choking hazard is a small ball; 2 - Choking hazard contains small ball; 3 - Choking hazard contains small parts; 4 - Choking hazard balloon; 5 - Choking hazard is a marble; 6 - Choking hazard contains a marble.
     *
     * @return self
     * @param int $smallPartsWarning
     */
    public function addToSmallPartsWarnings($smallPartsWarning)
    {
        $this->smallPartsWarnings[] = $smallPartsWarning;
        return $this;
    }

    /**
     * isset smallPartsWarnings
     *
     * To determine if any choking warnings are applicable, check current product packaging for choking warning message(s). Please indicate the warning number (0-6). 0 - No warning applicable; 1 - Choking hazard is a small ball; 2 - Choking hazard contains small ball; 3 - Choking hazard contains small parts; 4 - Choking hazard balloon; 5 - Choking hazard is a marble; 6 - Choking hazard contains a marble.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetSmallPartsWarnings($index)
    {
        return isset($this->smallPartsWarnings[$index]);
    }

    /**
     * unset smallPartsWarnings
     *
     * To determine if any choking warnings are applicable, check current product packaging for choking warning message(s). Please indicate the warning number (0-6). 0 - No warning applicable; 1 - Choking hazard is a small ball; 2 - Choking hazard contains small ball; 3 - Choking hazard contains small parts; 4 - Choking hazard balloon; 5 - Choking hazard is a marble; 6 - Choking hazard contains a marble.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetSmallPartsWarnings($index)
    {
        unset($this->smallPartsWarnings[$index]);
    }

    /**
     * Gets as smallPartsWarnings
     *
     * To determine if any choking warnings are applicable, check current product packaging for choking warning message(s). Please indicate the warning number (0-6). 0 - No warning applicable; 1 - Choking hazard is a small ball; 2 - Choking hazard contains small ball; 3 - Choking hazard contains small parts; 4 - Choking hazard balloon; 5 - Choking hazard is a marble; 6 - Choking hazard contains a marble.
     *
     * @return int[]
     */
    public function getSmallPartsWarnings()
    {
        return $this->smallPartsWarnings;
    }

    /**
     * Sets a new smallPartsWarnings
     *
     * To determine if any choking warnings are applicable, check current product packaging for choking warning message(s). Please indicate the warning number (0-6). 0 - No warning applicable; 1 - Choking hazard is a small ball; 2 - Choking hazard contains small ball; 3 - Choking hazard contains small parts; 4 - Choking hazard balloon; 5 - Choking hazard is a marble; 6 - Choking hazard contains a marble.
     *
     * @param int $smallPartsWarnings
     * @return self
     */
    public function setSmallPartsWarnings(array $smallPartsWarnings)
    {
        $this->smallPartsWarnings = $smallPartsWarnings;
        return $this;
    }

    /**
     * Gets as hasBatteries
     *
     * "Battery or battery containing product" is defined by Company to include any item of Merchandise that is a battery or any component of Merchandise, including reusable packaging intended to stay in use with the item, containing a battery of any chemistry/ type. Mark Y if this definition applies to your product.
     *
     * @return string
     */
    public function getHasBatteries()
    {
        return $this->hasBatteries;
    }

    /**
     * Sets a new hasBatteries
     *
     * "Battery or battery containing product" is defined by Company to include any item of Merchandise that is a battery or any component of Merchandise, including reusable packaging intended to stay in use with the item, containing a battery of any chemistry/ type. Mark Y if this definition applies to your product.
     *
     * @param string $hasBatteries
     * @return self
     */
    public function setHasBatteries($hasBatteries)
    {
        $this->hasBatteries = $hasBatteries;
        return $this;
    }

    /**
     * Gets as batteryTechnologyType
     *
     * Please select the Battery Technology Type from the list provided. NOTE: If battery type is lead acid, lead acid (nonspillable), lithium ion, or lithium metal, please ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @return string
     */
    public function getBatteryTechnologyType()
    {
        return $this->batteryTechnologyType;
    }

    /**
     * Sets a new batteryTechnologyType
     *
     * Please select the Battery Technology Type from the list provided. NOTE: If battery type is lead acid, lead acid (nonspillable), lithium ion, or lithium metal, please ensure that you have obtained a hazardous materials risk assessment through WERCS before setting up your item.
     *
     * @param string $batteryTechnologyType
     * @return self
     */
    public function setBatteryTechnologyType($batteryTechnologyType)
    {
        $this->batteryTechnologyType = $batteryTechnologyType;
        return $this;
    }

    /**
     * Gets as hasWarranty
     *
     * Y indicates the item comes with a warranty. If an item has a warranty, then enter EITHER the warranty URL or the warranty text in the appropriate field.
     *
     * @return string
     */
    public function getHasWarranty()
    {
        return $this->hasWarranty;
    }

    /**
     * Sets a new hasWarranty
     *
     * Y indicates the item comes with a warranty. If an item has a warranty, then enter EITHER the warranty URL or the warranty text in the appropriate field.
     *
     * @param string $hasWarranty
     * @return self
     */
    public function setHasWarranty($hasWarranty)
    {
        $this->hasWarranty = $hasWarranty;
        return $this;
    }

    /**
     * Gets as warrantyURL
     *
     * If you indicated that your item has a warranty, provide either the Warranty URL or Warranty Text. The Warranty URL is the web location of the image, PDF, or link to the manufacturer's warranty page, showing the warranty and its terms, including the duration of the warranty. URLs must begin with http:// or https:// NOTE: Please remember to update the link and/or text of the warranty as the warranty changes. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @return string
     */
    public function getWarrantyURL()
    {
        return $this->warrantyURL;
    }

    /**
     * Sets a new warrantyURL
     *
     * If you indicated that your item has a warranty, provide either the Warranty URL or Warranty Text. The Warranty URL is the web location of the image, PDF, or link to the manufacturer's warranty page, showing the warranty and its terms, including the duration of the warranty. URLs must begin with http:// or https:// NOTE: Please remember to update the link and/or text of the warranty as the warranty changes. If supplying an image, provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @param string $warrantyURL
     * @return self
     */
    public function setWarrantyURL($warrantyURL)
    {
        $this->warrantyURL = $warrantyURL;
        return $this;
    }

    /**
     * Gets as warrantyText
     *
     * If you marked Y for "Has Warranty" provide the Warranty URL or Warranty Text (the full text of the warranty terms, including what is covered by the warranty and the duration of the warranty). NOTE: please remember to update the text of your warranty as your warranty changes.
     *
     * @return string
     */
    public function getWarrantyText()
    {
        return $this->warrantyText;
    }

    /**
     * Sets a new warrantyText
     *
     * If you marked Y for "Has Warranty" provide the Warranty URL or Warranty Text (the full text of the warranty terms, including what is covered by the warranty and the duration of the warranty). NOTE: please remember to update the text of your warranty as your warranty changes.
     *
     * @param string $warrantyText
     * @return self
     */
    public function setWarrantyText($warrantyText)
    {
        $this->warrantyText = $warrantyText;
        return $this;
    }

    /**
     * Gets as hasStateRestrictions
     *
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @return string
     */
    public function getHasStateRestrictions()
    {
        return $this->hasStateRestrictions;
    }

    /**
     * Sets a new hasStateRestrictions
     *
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @param string $hasStateRestrictions
     * @return self
     */
    public function setHasStateRestrictions($hasStateRestrictions)
    {
        $this->hasStateRestrictions = $hasStateRestrictions;
        return $this;
    }

    /**
     * Adds as stateRestriction
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param \WalmartDSV\StateRestrictionType $stateRestriction
     *@return self
     */
    public function addToStateRestrictions(\WalmartDSV\StateRestrictionType $stateRestriction)
    {
        $this->stateRestrictions[] = $stateRestriction;
        return $this;
    }

    /**
     * isset stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetStateRestrictions($index)
    {
        return isset($this->stateRestrictions[$index]);
    }

    /**
     * unset stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetStateRestrictions($index)
    {
        unset($this->stateRestrictions[$index]);
    }

    /**
     * Gets as stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @return \WalmartDSV\StateRestrictionType[]
     */
    public function getStateRestrictions()
    {
        return $this->stateRestrictions;
    }

    /**
     * Sets a new stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param \WalmartDSV\StateRestrictionType[] $stateRestrictions
     * @return self
     */
    public function setStateRestrictions(array $stateRestrictions)
    {
        $this->stateRestrictions = $stateRestrictions;
        return $this;
    }

    /**
     * Adds as certificateNumberValue
     *
     * If the item has a certificate, enter the certificate number here. Separate multiple values by semicolons.
     *
     * @return self
     * @param string $certificateNumberValue
     */
    public function addToCertificateNumber($certificateNumberValue)
    {
        $this->certificateNumber[] = $certificateNumberValue;
        return $this;
    }

    /**
     * isset certificateNumber
     *
     * If the item has a certificate, enter the certificate number here. Separate multiple values by semicolons.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetCertificateNumber($index)
    {
        return isset($this->certificateNumber[$index]);
    }

    /**
     * unset certificateNumber
     *
     * If the item has a certificate, enter the certificate number here. Separate multiple values by semicolons.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetCertificateNumber($index)
    {
        unset($this->certificateNumber[$index]);
    }

    /**
     * Gets as certificateNumber
     *
     * If the item has a certificate, enter the certificate number here. Separate multiple values by semicolons.
     *
     * @return string[]
     */
    public function getCertificateNumber()
    {
        return $this->certificateNumber;
    }

    /**
     * Sets a new certificateNumber
     *
     * If the item has a certificate, enter the certificate number here. Separate multiple values by semicolons.
     *
     * @param string $certificateNumber
     * @return self
     */
    public function setCertificateNumber(array $certificateNumber)
    {
        $this->certificateNumber = $certificateNumber;
        return $this;
    }

    /**
     * Gets as birthstone
     *
     * Name of month associated with this gemstone as a birthstone. The format used is: Month- Stone
     *
     * @return string
     */
    public function getBirthstone()
    {
        return $this->birthstone;
    }

    /**
     * Sets a new birthstone
     *
     * Name of month associated with this gemstone as a birthstone. The format used is: Month- Stone
     *
     * @param string $birthstone
     * @return self
     */
    public function setBirthstone($birthstone)
    {
        $this->birthstone = $birthstone;
        return $this;
    }

    /**
     * Gets as pattern
     *
     * Decorative design or visual ornamentation, often with a thematic, recurring motif.
     *
     * @return \WalmartDSV\PatternType
     */
    public function getPattern()
    {
        return $this->pattern;
    }

    /**
     * Sets a new pattern
     *
     * Decorative design or visual ornamentation, often with a thematic, recurring motif.
     *
     * @param \WalmartDSV\PatternType $pattern
     * @return self
     */
    public function setPattern(\WalmartDSV\PatternType $pattern)
    {
        $this->pattern = $pattern;
        return $this;
    }

    /**
     * Adds as claspTypeValue
     *
     * If your item has a clasp, then choose a clasp type from the example values. If your value does not exist, enter one of your own.
     *
     * @return self
     * @param string $claspTypeValue
     */
    public function addToClaspType($claspTypeValue)
    {
        $this->claspType[] = $claspTypeValue;
        return $this;
    }

    /**
     * isset claspType
     *
     * If your item has a clasp, then choose a clasp type from the example values. If your value does not exist, enter one of your own.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetClaspType($index)
    {
        return isset($this->claspType[$index]);
    }

    /**
     * unset claspType
     *
     * If your item has a clasp, then choose a clasp type from the example values. If your value does not exist, enter one of your own.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetClaspType($index)
    {
        unset($this->claspType[$index]);
    }

    /**
     * Gets as claspType
     *
     * If your item has a clasp, then choose a clasp type from the example values. If your value does not exist, enter one of your own.
     *
     * @return string[]
     */
    public function getClaspType()
    {
        return $this->claspType;
    }

    /**
     * Sets a new claspType
     *
     * If your item has a clasp, then choose a clasp type from the example values. If your value does not exist, enter one of your own.
     *
     * @param string $claspType
     * @return self
     */
    public function setClaspType(array $claspType)
    {
        $this->claspType = $claspType;
        return $this;
    }

    /**
     * Gets as backFinding
     *
     * Kind of hardware used in the "back" of the jewelry, as in the earring back that helps to fasten the earring to the ear. This may be, for example, the earring finding or jewelry backing style.
     *
     * @return string
     */
    public function getBackFinding()
    {
        return $this->backFinding;
    }

    /**
     * Sets a new backFinding
     *
     * Kind of hardware used in the "back" of the jewelry, as in the earring back that helps to fasten the earring to the ear. This may be, for example, the earring finding or jewelry backing style.
     *
     * @param string $backFinding
     * @return self
     */
    public function setBackFinding($backFinding)
    {
        $this->backFinding = $backFinding;
        return $this;
    }

    /**
     * Adds as jewelrySettingValue
     *
     * Method or style in which stones are attached to a piece of jewelry.
     *
     * @return self
     * @param string $jewelrySettingValue
     */
    public function addToJewelrySetting($jewelrySettingValue)
    {
        $this->jewelrySetting[] = $jewelrySettingValue;
        return $this;
    }

    /**
     * isset jewelrySetting
     *
     * Method or style in which stones are attached to a piece of jewelry.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetJewelrySetting($index)
    {
        return isset($this->jewelrySetting[$index]);
    }

    /**
     * unset jewelrySetting
     *
     * Method or style in which stones are attached to a piece of jewelry.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetJewelrySetting($index)
    {
        unset($this->jewelrySetting[$index]);
    }

    /**
     * Gets as jewelrySetting
     *
     * Method or style in which stones are attached to a piece of jewelry.
     *
     * @return string[]
     */
    public function getJewelrySetting()
    {
        return $this->jewelrySetting;
    }

    /**
     * Sets a new jewelrySetting
     *
     * Method or style in which stones are attached to a piece of jewelry.
     *
     * @param string $jewelrySetting
     * @return self
     */
    public function setJewelrySetting(array $jewelrySetting)
    {
        $this->jewelrySetting = $jewelrySetting;
        return $this;
    }

    /**
     * Gets as earringStyle
     *
     * Style of earring, as represented by fashion or form. Select from example values; if your value does not exist, enter your own.
     *
     * @return string
     */
    public function getEarringStyle()
    {
        return $this->earringStyle;
    }

    /**
     * Sets a new earringStyle
     *
     * Style of earring, as represented by fashion or form. Select from example values; if your value does not exist, enter your own.
     *
     * @param string $earringStyle
     * @return self
     */
    public function setEarringStyle($earringStyle)
    {
        $this->earringStyle = $earringStyle;
        return $this;
    }

    /**
     * Adds as earringFeatureValue
     *
     * Features distinct to earring jewelry.
     *
     * @return self
     * @param string $earringFeatureValue
     */
    public function addToEarringFeature($earringFeatureValue)
    {
        $this->earringFeature[] = $earringFeatureValue;
        return $this;
    }

    /**
     * isset earringFeature
     *
     * Features distinct to earring jewelry.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetEarringFeature($index)
    {
        return isset($this->earringFeature[$index]);
    }

    /**
     * unset earringFeature
     *
     * Features distinct to earring jewelry.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetEarringFeature($index)
    {
        unset($this->earringFeature[$index]);
    }

    /**
     * Gets as earringFeature
     *
     * Features distinct to earring jewelry.
     *
     * @return string[]
     */
    public function getEarringFeature()
    {
        return $this->earringFeature;
    }

    /**
     * Sets a new earringFeature
     *
     * Features distinct to earring jewelry.
     *
     * @param string $earringFeature
     * @return self
     */
    public function setEarringFeature(array $earringFeature)
    {
        $this->earringFeature = $earringFeature;
        return $this;
    }

    /**
     * Gets as braceletStyle
     *
     * Style of bracelet as expressed by its fashion or form. Choose a value from the example values; if your value does not exist, then enter your own value.
     *
     * @return string
     */
    public function getBraceletStyle()
    {
        return $this->braceletStyle;
    }

    /**
     * Sets a new braceletStyle
     *
     * Style of bracelet as expressed by its fashion or form. Choose a value from the example values; if your value does not exist, then enter your own value.
     *
     * @param string $braceletStyle
     * @return self
     */
    public function setBraceletStyle($braceletStyle)
    {
        $this->braceletStyle = $braceletStyle;
        return $this;
    }

    /**
     * Gets as necklaceStyle
     *
     * Style of necklace as expressed by fashion or form. Select from the example values; if your value does not exist, enter your own.
     *
     * @return string
     */
    public function getNecklaceStyle()
    {
        return $this->necklaceStyle;
    }

    /**
     * Sets a new necklaceStyle
     *
     * Style of necklace as expressed by fashion or form. Select from the example values; if your value does not exist, enter your own.
     *
     * @param string $necklaceStyle
     * @return self
     */
    public function setNecklaceStyle($necklaceStyle)
    {
        $this->necklaceStyle = $necklaceStyle;
        return $this;
    }

    /**
     * Gets as chainLength
     *
     * Enter length of jewelry chain, in inches or feet.
     *
     * @return \WalmartDSV\JewelryType\ChainLengthAType
     */
    public function getChainLength()
    {
        return $this->chainLength;
    }

    /**
     * Sets a new chainLength
     *
     * Enter length of jewelry chain, in inches or feet.
     *
     * @param \WalmartDSV\JewelryType\ChainLengthAType $chainLength
     * @return self
     */
    public function setChainLength(\WalmartDSV\JewelryType\ChainLengthAType $chainLength)
    {
        $this->chainLength = $chainLength;
        return $this;
    }

    /**
     * Adds as chainPatternValue
     *
     * If pertinent, choose a pattern from the example values; if your value does not exist, then enter one of your own.
     *
     * @return self
     * @param string $chainPatternValue
     */
    public function addToChainPattern($chainPatternValue)
    {
        $this->chainPattern[] = $chainPatternValue;
        return $this;
    }

    /**
     * isset chainPattern
     *
     * If pertinent, choose a pattern from the example values; if your value does not exist, then enter one of your own.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetChainPattern($index)
    {
        return isset($this->chainPattern[$index]);
    }

    /**
     * unset chainPattern
     *
     * If pertinent, choose a pattern from the example values; if your value does not exist, then enter one of your own.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetChainPattern($index)
    {
        unset($this->chainPattern[$index]);
    }

    /**
     * Gets as chainPattern
     *
     * If pertinent, choose a pattern from the example values; if your value does not exist, then enter one of your own.
     *
     * @return string[]
     */
    public function getChainPattern()
    {
        return $this->chainPattern;
    }

    /**
     * Sets a new chainPattern
     *
     * If pertinent, choose a pattern from the example values; if your value does not exist, then enter one of your own.
     *
     * @param string $chainPattern
     * @return self
     */
    public function setChainPattern(array $chainPattern)
    {
        $this->chainPattern = $chainPattern;
        return $this;
    }

    /**
     * Adds as globalBrandLicenseValue
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @return self
     * @param string $globalBrandLicenseValue
     */
    public function addToGlobalBrandLicense($globalBrandLicenseValue)
    {
        $this->globalBrandLicense[] = $globalBrandLicenseValue;
        return $this;
    }

    /**
     * isset globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @param int|string $index
     * @return bool
     */
    public function issetGlobalBrandLicense($index)
    {
        return isset($this->globalBrandLicense[$index]);
    }

    /**
     * unset globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @param int|string $index
     * @return void
     */
    public function unsetGlobalBrandLicense($index)
    {
        unset($this->globalBrandLicense[$index]);
    }

    /**
     * Gets as globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @return string[]
     */
    public function getGlobalBrandLicense()
    {
        return $this->globalBrandLicense;
    }

    /**
     * Sets a new globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @param string $globalBrandLicense
     * @return self
     */
    public function setGlobalBrandLicense(array $globalBrandLicense)
    {
        $this->globalBrandLicense = $globalBrandLicense;
        return $this;
    }

    /**
     * Gets as diamondClarity
     *
     * Diamond clarity is a quality of diamonds relating to the existence and visual appearance of internal characteristics of a diamond called inclusions, and surface defects called blemishes.
     *
     * @return string
     */
    public function getDiamondClarity()
    {
        return $this->diamondClarity;
    }

    /**
     * Sets a new diamondClarity
     *
     * Diamond clarity is a quality of diamonds relating to the existence and visual appearance of internal characteristics of a diamond called inclusions, and surface defects called blemishes.
     *
     * @param string $diamondClarity
     * @return self
     */
    public function setDiamondClarity($diamondClarity)
    {
        $this->diamondClarity = $diamondClarity;
        return $this;
    }

    /**
     * Gets as diamondCut
     *
     * The style or design followed when cutting a diamond. This does not refer to the shape of the diamond, but the pattern of the cut itself.
     *
     * @return string
     */
    public function getDiamondCut()
    {
        return $this->diamondCut;
    }

    /**
     * Sets a new diamondCut
     *
     * The style or design followed when cutting a diamond. This does not refer to the shape of the diamond, but the pattern of the cut itself.
     *
     * @param string $diamondCut
     * @return self
     */
    public function setDiamondCut($diamondCut)
    {
        $this->diamondCut = $diamondCut;
        return $this;
    }

    /**
     * Gets as carats
     *
     * Measure of the weight (mass) of gemstones and pearls. One carat (ct) is equal to 200 mg.
     *
     * @return \WalmartDSV\JewelryType\CaratsAType
     */
    public function getCarats()
    {
        return $this->carats;
    }

    /**
     * Sets a new carats
     *
     * Measure of the weight (mass) of gemstones and pearls. One carat (ct) is equal to 200 mg.
     *
     * @param \WalmartDSV\JewelryType\CaratsAType $carats
     * @return self
     */
    public function setCarats(\WalmartDSV\JewelryType\CaratsAType $carats)
    {
        $this->carats = $carats;
        return $this;
    }

    /**
     * Gets as totalDiamondWeight
     *
     * Total diamond weight is the combined carat weight of all the diamonds in a piece of jewelry. Paired earrings would be considered a single piece, therefore if each earring contained a diamond with a carat weight of 1/2, the value entered would be 1 ct (2 x 1/2 ct).
     *
     * @return \WalmartDSV\JewelryType\TotalDiamondWeightAType
     */
    public function getTotalDiamondWeight()
    {
        return $this->totalDiamondWeight;
    }

    /**
     * Sets a new totalDiamondWeight
     *
     * Total diamond weight is the combined carat weight of all the diamonds in a piece of jewelry. Paired earrings would be considered a single piece, therefore if each earring contained a diamond with a carat weight of 1/2, the value entered would be 1 ct (2 x 1/2 ct).
     *
     * @param \WalmartDSV\JewelryType\TotalDiamondWeightAType $totalDiamondWeight
     * @return self
     */
    public function setTotalDiamondWeight(\WalmartDSV\JewelryType\TotalDiamondWeightAType $totalDiamondWeight)
    {
        $this->totalDiamondWeight = $totalDiamondWeight;
        return $this;
    }

    /**
     * Gets as totalGemWeight
     *
     * Total gemstone weight is the combined carat weight of all of the gemstones in a piece of jewelry. Paired earrings would be considered a single piece, therefore if each earring contained a gemstone with a carat weight of 1/2, the value entered would be 1 ct (2 x 1/2 ct). This is a useful indication of gemstone sizes, provided the stones in question are of the same type. Differing types have differing densities so the carat weight will not necessarily provide an accurate indication of size for comparison purposes.
     *
     * @return float
     */
    public function getTotalGemWeight()
    {
        return $this->totalGemWeight;
    }

    /**
     * Sets a new totalGemWeight
     *
     * Total gemstone weight is the combined carat weight of all of the gemstones in a piece of jewelry. Paired earrings would be considered a single piece, therefore if each earring contained a gemstone with a carat weight of 1/2, the value entered would be 1 ct (2 x 1/2 ct). This is a useful indication of gemstone sizes, provided the stones in question are of the same type. Differing types have differing densities so the carat weight will not necessarily provide an accurate indication of size for comparison purposes.
     *
     * @param float $totalGemWeight
     * @return self
     */
    public function setTotalGemWeight($totalGemWeight)
    {
        $this->totalGemWeight = $totalGemWeight;
        return $this;
    }

    /**
     * Gets as gemstoneCut
     *
     * Type of gemstone cut, as distinct from shape
     *
     * @return string
     */
    public function getGemstoneCut()
    {
        return $this->gemstoneCut;
    }

    /**
     * Sets a new gemstoneCut
     *
     * Type of gemstone cut, as distinct from shape
     *
     * @param string $gemstoneCut
     * @return self
     */
    public function setGemstoneCut($gemstoneCut)
    {
        $this->gemstoneCut = $gemstoneCut;
        return $this;
    }

    /**
     * Gets as gemstone
     *
     * The gemstone trade name (i.e., amethyst, not purple quartz).
     *
     * @return \WalmartDSV\GemstoneType
     */
    public function getGemstone()
    {
        return $this->gemstone;
    }

    /**
     * Sets a new gemstone
     *
     * The gemstone trade name (i.e., amethyst, not purple quartz).
     *
     * @param \WalmartDSV\GemstoneType $gemstone
     * @return self
     */
    public function setGemstone(\WalmartDSV\GemstoneType $gemstone)
    {
        $this->gemstone = $gemstone;
        return $this;
    }

    /**
     * Gets as gemstoneColor
     *
     * The color of the gemstone including factors of hue, tone and saturation. Expressed as a color grade, or descriptors (light rose).
     *
     * @return string
     */
    public function getGemstoneColor()
    {
        return $this->gemstoneColor;
    }

    /**
     * Sets a new gemstoneColor
     *
     * The color of the gemstone including factors of hue, tone and saturation. Expressed as a color grade, or descriptors (light rose).
     *
     * @param string $gemstoneColor
     * @return self
     */
    public function setGemstoneColor($gemstoneColor)
    {
        $this->gemstoneColor = $gemstoneColor;
        return $this;
    }

    /**
     * Gets as gemstoneClarity
     *
     * Note that while the clarity rating scale (VVS, VS, etc.) is the same for colored gems as it is for diamonds, the meaning is different: Rating definitions vary based on the type of colored gem. Please refer to the specific ratings definitions for Type 1, Type 2, or Type 3 gems.
     *
     * @return string
     */
    public function getGemstoneClarity()
    {
        return $this->gemstoneClarity;
    }

    /**
     * Sets a new gemstoneClarity
     *
     * Note that while the clarity rating scale (VVS, VS, etc.) is the same for colored gems as it is for diamonds, the meaning is different: Rating definitions vary based on the type of colored gem. Please refer to the specific ratings definitions for Type 1, Type 2, or Type 3 gems.
     *
     * @param string $gemstoneClarity
     * @return self
     */
    public function setGemstoneClarity($gemstoneClarity)
    {
        $this->gemstoneClarity = $gemstoneClarity;
        return $this;
    }

    /**
     * Gets as stoneCreationMethod
     *
     * Whether the stone was created by nature or humans
     *
     * @return string
     */
    public function getStoneCreationMethod()
    {
        return $this->stoneCreationMethod;
    }

    /**
     * Sets a new stoneCreationMethod
     *
     * Whether the stone was created by nature or humans
     *
     * @param string $stoneCreationMethod
     * @return self
     */
    public function setStoneCreationMethod($stoneCreationMethod)
    {
        $this->stoneCreationMethod = $stoneCreationMethod;
        return $this;
    }

    /**
     * Gets as stoneTreatment
     *
     * Describes the process a gemstone has undergone to produce changes in the durability, color, and/or clarity that are beyond the typical gemstone conditioning.
     *
     * @return string
     */
    public function getStoneTreatment()
    {
        return $this->stoneTreatment;
    }

    /**
     * Sets a new stoneTreatment
     *
     * Describes the process a gemstone has undergone to produce changes in the durability, color, and/or clarity that are beyond the typical gemstone conditioning.
     *
     * @param string $stoneTreatment
     * @return self
     */
    public function setStoneTreatment($stoneTreatment)
    {
        $this->stoneTreatment = $stoneTreatment;
        return $this;
    }

    /**
     * Gets as stoneHeight
     *
     * The measurement of the height of the stone (vs. the setting) as measured the from the table (top of the stone) to the culet (bottom of the stone).
     *
     * @return \WalmartDSV\JewelryType\StoneHeightAType
     */
    public function getStoneHeight()
    {
        return $this->stoneHeight;
    }

    /**
     * Sets a new stoneHeight
     *
     * The measurement of the height of the stone (vs. the setting) as measured the from the table (top of the stone) to the culet (bottom of the stone).
     *
     * @param \WalmartDSV\JewelryType\StoneHeightAType $stoneHeight
     * @return self
     */
    public function setStoneHeight(\WalmartDSV\JewelryType\StoneHeightAType $stoneHeight)
    {
        $this->stoneHeight = $stoneHeight;
        return $this;
    }

    /**
     * Gets as stoneLength
     *
     * The measure of the largest dimensional side of a gemstone with the face pointing towards the observer.
     *
     * @return \WalmartDSV\JewelryType\StoneLengthAType
     */
    public function getStoneLength()
    {
        return $this->stoneLength;
    }

    /**
     * Sets a new stoneLength
     *
     * The measure of the largest dimensional side of a gemstone with the face pointing towards the observer.
     *
     * @param \WalmartDSV\JewelryType\StoneLengthAType $stoneLength
     * @return self
     */
    public function setStoneLength(\WalmartDSV\JewelryType\StoneLengthAType $stoneLength)
    {
        $this->stoneLength = $stoneLength;
        return $this;
    }

    /**
     * Gets as stoneWidth
     *
     * The measure of the narrower dimensional side of a gemstone with the face pointing towards the observer.
     *
     * @return \WalmartDSV\JewelryType\StoneWidthAType
     */
    public function getStoneWidth()
    {
        return $this->stoneWidth;
    }

    /**
     * Sets a new stoneWidth
     *
     * The measure of the narrower dimensional side of a gemstone with the face pointing towards the observer.
     *
     * @param \WalmartDSV\JewelryType\StoneWidthAType $stoneWidth
     * @return self
     */
    public function setStoneWidth(\WalmartDSV\JewelryType\StoneWidthAType $stoneWidth)
    {
        $this->stoneWidth = $stoneWidth;
        return $this;
    }

    /**
     * Gets as stoneDepthPercentage
     *
     * This is calculated by the following formula: depth / width.
     *
     * @return float
     */
    public function getStoneDepthPercentage()
    {
        return $this->stoneDepthPercentage;
    }

    /**
     * Sets a new stoneDepthPercentage
     *
     * This is calculated by the following formula: depth / width.
     *
     * @param float $stoneDepthPercentage
     * @return self
     */
    public function setStoneDepthPercentage($stoneDepthPercentage)
    {
        $this->stoneDepthPercentage = $stoneDepthPercentage;
        return $this;
    }

    /**
     * Gets as stoneTablePercentage
     *
     * In a grading report, table percentage is calculated based on the size of the table divided by the average girdle diameter of the gem. So, a 60 percent table means that the table is 60 percent wide as the gem's outline.
     *
     * @return float
     */
    public function getStoneTablePercentage()
    {
        return $this->stoneTablePercentage;
    }

    /**
     * Sets a new stoneTablePercentage
     *
     * In a grading report, table percentage is calculated based on the size of the table divided by the average girdle diameter of the gem. So, a 60 percent table means that the table is 60 percent wide as the gem's outline.
     *
     * @param float $stoneTablePercentage
     * @return self
     */
    public function setStoneTablePercentage($stoneTablePercentage)
    {
        $this->stoneTablePercentage = $stoneTablePercentage;
        return $this;
    }

    /**
     * Gets as stoneSymmetry
     *
     * Symmetry refers to the exactness of the shape and placement of the facets. The GIA grades both polish and symmetry and lists it on the certificate using the scale: Excellent, Very Good, Good, Fair and Poor.
     *
     * @return string
     */
    public function getStoneSymmetry()
    {
        return $this->stoneSymmetry;
    }

    /**
     * Sets a new stoneSymmetry
     *
     * Symmetry refers to the exactness of the shape and placement of the facets. The GIA grades both polish and symmetry and lists it on the certificate using the scale: Excellent, Very Good, Good, Fair and Poor.
     *
     * @param string $stoneSymmetry
     * @return self
     */
    public function setStoneSymmetry($stoneSymmetry)
    {
        $this->stoneSymmetry = $stoneSymmetry;
        return $this;
    }

    /**
     * Gets as stonePolish
     *
     * Polish refers to the degree of smoothness of each facet of a gem as measured by a gemologist. When a gem is cut and polished, microscopic surface defects may be created by the polishing wheel as it drags tiny dislodged crystals across the gem's surface.
     *
     * @return string
     */
    public function getStonePolish()
    {
        return $this->stonePolish;
    }

    /**
     * Sets a new stonePolish
     *
     * Polish refers to the degree of smoothness of each facet of a gem as measured by a gemologist. When a gem is cut and polished, microscopic surface defects may be created by the polishing wheel as it drags tiny dislodged crystals across the gem's surface.
     *
     * @param string $stonePolish
     * @return self
     */
    public function setStonePolish($stonePolish)
    {
        $this->stonePolish = $stonePolish;
        return $this;
    }

    /**
     * Gets as stoneGirdle
     *
     * The girdle is the thin perimeter of a stone, dividing the crown above from the pavilion below. When viewing a stone in its setting or from a profile view, the girdle is the widest part (or the circumference) of the polished stone - the portion of the stone that makes contact with the setting itself.
     *
     * @return string
     */
    public function getStoneGirdle()
    {
        return $this->stoneGirdle;
    }

    /**
     * Sets a new stoneGirdle
     *
     * The girdle is the thin perimeter of a stone, dividing the crown above from the pavilion below. When viewing a stone in its setting or from a profile view, the girdle is the widest part (or the circumference) of the polished stone - the portion of the stone that makes contact with the setting itself.
     *
     * @param string $stoneGirdle
     * @return self
     */
    public function setStoneGirdle($stoneGirdle)
    {
        $this->stoneGirdle = $stoneGirdle;
        return $this;
    }

    /**
     * Gets as stoneCulet
     *
     * The culet (pronounced cue-let) is the small area at the bottom of a diamond's pavilion. The culet can be a point or a very small facet sitting parallel to the table.
     *
     * @return string
     */
    public function getStoneCulet()
    {
        return $this->stoneCulet;
    }

    /**
     * Sets a new stoneCulet
     *
     * The culet (pronounced cue-let) is the small area at the bottom of a diamond's pavilion. The culet can be a point or a very small facet sitting parallel to the table.
     *
     * @param string $stoneCulet
     * @return self
     */
    public function setStoneCulet($stoneCulet)
    {
        $this->stoneCulet = $stoneCulet;
        return $this;
    }

    /**
     * Gets as stoneFluoresence
     *
     * Used to describe the color and strength of the UV light reflected off some diamonds.
     *
     * @return string
     */
    public function getStoneFluoresence()
    {
        return $this->stoneFluoresence;
    }

    /**
     * Sets a new stoneFluoresence
     *
     * Used to describe the color and strength of the UV light reflected off some diamonds.
     *
     * @param string $stoneFluoresence
     * @return self
     */
    public function setStoneFluoresence($stoneFluoresence)
    {
        $this->stoneFluoresence = $stoneFluoresence;
        return $this;
    }

    /**
     * Adds as pearlTypeValue
     *
     * Describes the method for growing pearls and information about the origin of the pearl.
     *
     * @return self
     * @param string $pearlTypeValue
     */
    public function addToPearlType($pearlTypeValue)
    {
        $this->pearlType[] = $pearlTypeValue;
        return $this;
    }

    /**
     * isset pearlType
     *
     * Describes the method for growing pearls and information about the origin of the pearl.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetPearlType($index)
    {
        return isset($this->pearlType[$index]);
    }

    /**
     * unset pearlType
     *
     * Describes the method for growing pearls and information about the origin of the pearl.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetPearlType($index)
    {
        unset($this->pearlType[$index]);
    }

    /**
     * Gets as pearlType
     *
     * Describes the method for growing pearls and information about the origin of the pearl.
     *
     * @return string[]
     */
    public function getPearlType()
    {
        return $this->pearlType;
    }

    /**
     * Sets a new pearlType
     *
     * Describes the method for growing pearls and information about the origin of the pearl.
     *
     * @param string $pearlType
     * @return self
     */
    public function setPearlType(array $pearlType)
    {
        $this->pearlType = $pearlType;
        return $this;
    }

    /**
     * Adds as pearlBodyColorValue
     *
     * The color of the pearl's body, vs. the overtone.
     *
     * @return self
     * @param string $pearlBodyColorValue
     */
    public function addToPearlBodyColor($pearlBodyColorValue)
    {
        $this->pearlBodyColor[] = $pearlBodyColorValue;
        return $this;
    }

    /**
     * isset pearlBodyColor
     *
     * The color of the pearl's body, vs. the overtone.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetPearlBodyColor($index)
    {
        return isset($this->pearlBodyColor[$index]);
    }

    /**
     * unset pearlBodyColor
     *
     * The color of the pearl's body, vs. the overtone.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetPearlBodyColor($index)
    {
        unset($this->pearlBodyColor[$index]);
    }

    /**
     * Gets as pearlBodyColor
     *
     * The color of the pearl's body, vs. the overtone.
     *
     * @return string[]
     */
    public function getPearlBodyColor()
    {
        return $this->pearlBodyColor;
    }

    /**
     * Sets a new pearlBodyColor
     *
     * The color of the pearl's body, vs. the overtone.
     *
     * @param string $pearlBodyColor
     * @return self
     */
    public function setPearlBodyColor(array $pearlBodyColor)
    {
        $this->pearlBodyColor = $pearlBodyColor;
        return $this;
    }

    /**
     * Adds as pearlLustreValue
     *
     * Measure of the quality and quantity of light that reflects off a pearl.
     *
     * @return self
     * @param string $pearlLustreValue
     */
    public function addToPearlLustre($pearlLustreValue)
    {
        $this->pearlLustre[] = $pearlLustreValue;
        return $this;
    }

    /**
     * isset pearlLustre
     *
     * Measure of the quality and quantity of light that reflects off a pearl.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetPearlLustre($index)
    {
        return isset($this->pearlLustre[$index]);
    }

    /**
     * unset pearlLustre
     *
     * Measure of the quality and quantity of light that reflects off a pearl.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetPearlLustre($index)
    {
        unset($this->pearlLustre[$index]);
    }

    /**
     * Gets as pearlLustre
     *
     * Measure of the quality and quantity of light that reflects off a pearl.
     *
     * @return string[]
     */
    public function getPearlLustre()
    {
        return $this->pearlLustre;
    }

    /**
     * Sets a new pearlLustre
     *
     * Measure of the quality and quantity of light that reflects off a pearl.
     *
     * @param string $pearlLustre
     * @return self
     */
    public function setPearlLustre(array $pearlLustre)
    {
        $this->pearlLustre = $pearlLustre;
        return $this;
    }

    /**
     * Adds as pearlShapeValue
     *
     * The shape of the pearl, defined in part by how regular and smooth the surface of the pearl is.
     *
     * @return self
     * @param string $pearlShapeValue
     */
    public function addToPearlShape($pearlShapeValue)
    {
        $this->pearlShape[] = $pearlShapeValue;
        return $this;
    }

    /**
     * isset pearlShape
     *
     * The shape of the pearl, defined in part by how regular and smooth the surface of the pearl is.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetPearlShape($index)
    {
        return isset($this->pearlShape[$index]);
    }

    /**
     * unset pearlShape
     *
     * The shape of the pearl, defined in part by how regular and smooth the surface of the pearl is.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetPearlShape($index)
    {
        unset($this->pearlShape[$index]);
    }

    /**
     * Gets as pearlShape
     *
     * The shape of the pearl, defined in part by how regular and smooth the surface of the pearl is.
     *
     * @return string[]
     */
    public function getPearlShape()
    {
        return $this->pearlShape;
    }

    /**
     * Sets a new pearlShape
     *
     * The shape of the pearl, defined in part by how regular and smooth the surface of the pearl is.
     *
     * @param string $pearlShape
     * @return self
     */
    public function setPearlShape(array $pearlShape)
    {
        $this->pearlShape = $pearlShape;
        return $this;
    }

    /**
     * Adds as pearlUniformityValue
     *
     * Describes how well the individual pearls have been matched within a piece of pearl jewelry. Selection factor for quality and appearance, typically applied to pearl-strand necklaces. Values range from Excellent to Poor, depending on the degree of noticeable variations.
     *
     * @return self
     * @param string $pearlUniformityValue
     */
    public function addToPearlUniformity($pearlUniformityValue)
    {
        $this->pearlUniformity[] = $pearlUniformityValue;
        return $this;
    }

    /**
     * isset pearlUniformity
     *
     * Describes how well the individual pearls have been matched within a piece of pearl jewelry. Selection factor for quality and appearance, typically applied to pearl-strand necklaces. Values range from Excellent to Poor, depending on the degree of noticeable variations.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetPearlUniformity($index)
    {
        return isset($this->pearlUniformity[$index]);
    }

    /**
     * unset pearlUniformity
     *
     * Describes how well the individual pearls have been matched within a piece of pearl jewelry. Selection factor for quality and appearance, typically applied to pearl-strand necklaces. Values range from Excellent to Poor, depending on the degree of noticeable variations.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetPearlUniformity($index)
    {
        unset($this->pearlUniformity[$index]);
    }

    /**
     * Gets as pearlUniformity
     *
     * Describes how well the individual pearls have been matched within a piece of pearl jewelry. Selection factor for quality and appearance, typically applied to pearl-strand necklaces. Values range from Excellent to Poor, depending on the degree of noticeable variations.
     *
     * @return string[]
     */
    public function getPearlUniformity()
    {
        return $this->pearlUniformity;
    }

    /**
     * Sets a new pearlUniformity
     *
     * Describes how well the individual pearls have been matched within a piece of pearl jewelry. Selection factor for quality and appearance, typically applied to pearl-strand necklaces. Values range from Excellent to Poor, depending on the degree of noticeable variations.
     *
     * @param string $pearlUniformity
     * @return self
     */
    public function setPearlUniformity(array $pearlUniformity)
    {
        $this->pearlUniformity = $pearlUniformity;
        return $this;
    }

    /**
     * Adds as pearlSurfaceBlemishesValue
     *
     * Evaluation of pearls based on the blemishes or irregularities in the pearl’s surface, based on size, number, nature, location, visibility and type of surface characteristics. Values range from Clean to Heavily Spotted.
     *
     * @return self
     * @param string $pearlSurfaceBlemishesValue
     */
    public function addToPearlSurfaceBlemishes($pearlSurfaceBlemishesValue)
    {
        $this->pearlSurfaceBlemishes[] = $pearlSurfaceBlemishesValue;
        return $this;
    }

    /**
     * isset pearlSurfaceBlemishes
     *
     * Evaluation of pearls based on the blemishes or irregularities in the pearl’s surface, based on size, number, nature, location, visibility and type of surface characteristics. Values range from Clean to Heavily Spotted.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetPearlSurfaceBlemishes($index)
    {
        return isset($this->pearlSurfaceBlemishes[$index]);
    }

    /**
     * unset pearlSurfaceBlemishes
     *
     * Evaluation of pearls based on the blemishes or irregularities in the pearl’s surface, based on size, number, nature, location, visibility and type of surface characteristics. Values range from Clean to Heavily Spotted.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetPearlSurfaceBlemishes($index)
    {
        unset($this->pearlSurfaceBlemishes[$index]);
    }

    /**
     * Gets as pearlSurfaceBlemishes
     *
     * Evaluation of pearls based on the blemishes or irregularities in the pearl’s surface, based on size, number, nature, location, visibility and type of surface characteristics. Values range from Clean to Heavily Spotted.
     *
     * @return string[]
     */
    public function getPearlSurfaceBlemishes()
    {
        return $this->pearlSurfaceBlemishes;
    }

    /**
     * Sets a new pearlSurfaceBlemishes
     *
     * Evaluation of pearls based on the blemishes or irregularities in the pearl’s surface, based on size, number, nature, location, visibility and type of surface characteristics. Values range from Clean to Heavily Spotted.
     *
     * @param string $pearlSurfaceBlemishes
     * @return self
     */
    public function setPearlSurfaceBlemishes(array $pearlSurfaceBlemishes)
    {
        $this->pearlSurfaceBlemishes = $pearlSurfaceBlemishes;
        return $this;
    }

    /**
     * Gets as pearlNacreThickness
     *
     * Thickness of the nacre, or iridescent layers which are produced by the mollusk to coat the nucleus of the pearl.
     *
     * @return \WalmartDSV\JewelryType\PearlNacreThicknessAType
     */
    public function getPearlNacreThickness()
    {
        return $this->pearlNacreThickness;
    }

    /**
     * Sets a new pearlNacreThickness
     *
     * Thickness of the nacre, or iridescent layers which are produced by the mollusk to coat the nucleus of the pearl.
     *
     * @param \WalmartDSV\JewelryType\PearlNacreThicknessAType $pearlNacreThickness
     * @return self
     */
    public function setPearlNacreThickness(\WalmartDSV\JewelryType\PearlNacreThicknessAType $pearlNacreThickness)
    {
        $this->pearlNacreThickness = $pearlNacreThickness;
        return $this;
    }

    /**
     * Adds as pearlStringingMethodValue
     *
     * Describes the knotting method and material used in constructing the string of pearls.
     *
     * @return self
     * @param string $pearlStringingMethodValue
     */
    public function addToPearlStringingMethod($pearlStringingMethodValue)
    {
        $this->pearlStringingMethod[] = $pearlStringingMethodValue;
        return $this;
    }

    /**
     * isset pearlStringingMethod
     *
     * Describes the knotting method and material used in constructing the string of pearls.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetPearlStringingMethod($index)
    {
        return isset($this->pearlStringingMethod[$index]);
    }

    /**
     * unset pearlStringingMethod
     *
     * Describes the knotting method and material used in constructing the string of pearls.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetPearlStringingMethod($index)
    {
        unset($this->pearlStringingMethod[$index]);
    }

    /**
     * Gets as pearlStringingMethod
     *
     * Describes the knotting method and material used in constructing the string of pearls.
     *
     * @return string[]
     */
    public function getPearlStringingMethod()
    {
        return $this->pearlStringingMethod;
    }

    /**
     * Sets a new pearlStringingMethod
     *
     * Describes the knotting method and material used in constructing the string of pearls.
     *
     * @param string $pearlStringingMethod
     * @return self
     */
    public function setPearlStringingMethod(array $pearlStringingMethod)
    {
        $this->pearlStringingMethod = $pearlStringingMethod;
        return $this;
    }

    /**
     * Gets as sizePerPearl
     *
     * Diameter of each pearl in millimeters.
     *
     * @return \WalmartDSV\JewelryType\SizePerPearlAType
     */
    public function getSizePerPearl()
    {
        return $this->sizePerPearl;
    }

    /**
     * Sets a new sizePerPearl
     *
     * Diameter of each pearl in millimeters.
     *
     * @param \WalmartDSV\JewelryType\SizePerPearlAType $sizePerPearl
     * @return self
     */
    public function setSizePerPearl(\WalmartDSV\JewelryType\SizePerPearlAType $sizePerPearl)
    {
        $this->sizePerPearl = $sizePerPearl;
        return $this;
    }

    /**
     * Gets as numberOfPearls
     *
     * Please provide a count of individual pearls on a single item.
     *
     * @return int
     */
    public function getNumberOfPearls()
    {
        return $this->numberOfPearls;
    }

    /**
     * Sets a new numberOfPearls
     *
     * Please provide a count of individual pearls on a single item.
     *
     * @param int $numberOfPearls
     * @return self
     */
    public function setNumberOfPearls($numberOfPearls)
    {
        $this->numberOfPearls = $numberOfPearls;
        return $this;
    }

    /**
     * Gets as inscription
     *
     * If the item is engraved with an inscription, put the inscription text here.
     *
     * @return string
     */
    public function getInscription()
    {
        return $this->inscription;
    }

    /**
     * Sets a new inscription
     *
     * If the item is engraved with an inscription, put the inscription text here.
     *
     * @param string $inscription
     * @return self
     */
    public function setInscription($inscription)
    {
        $this->inscription = $inscription;
        return $this;
    }

    /**
     * Gets as isResizable
     *
     * Indicates that the item is resizable.
     *
     * @return string
     */
    public function getIsResizable()
    {
        return $this->isResizable;
    }

    /**
     * Sets a new isResizable
     *
     * Indicates that the item is resizable.
     *
     * @param string $isResizable
     * @return self
     */
    public function setIsResizable($isResizable)
    {
        $this->isResizable = $isResizable;
        return $this;
    }

    /**
     * Gets as ringSizingLowerRange
     *
     * Indicates the smallest size this ring can become if resized. Used with Ring Sizing Upper Range provides total size range if ring is resizable.
     *
     * @return float
     */
    public function getRingSizingLowerRange()
    {
        return $this->ringSizingLowerRange;
    }

    /**
     * Sets a new ringSizingLowerRange
     *
     * Indicates the smallest size this ring can become if resized. Used with Ring Sizing Upper Range provides total size range if ring is resizable.
     *
     * @param float $ringSizingLowerRange
     * @return self
     */
    public function setRingSizingLowerRange($ringSizingLowerRange)
    {
        $this->ringSizingLowerRange = $ringSizingLowerRange;
        return $this;
    }

    /**
     * Gets as ringSizingUpperRange
     *
     * Indicates the largest size this ring can become if resized. Used with Ring Sizing Lower Range provides total size range if ring is resizable.
     *
     * @return float
     */
    public function getRingSizingUpperRange()
    {
        return $this->ringSizingUpperRange;
    }

    /**
     * Sets a new ringSizingUpperRange
     *
     * Indicates the largest size this ring can become if resized. Used with Ring Sizing Lower Range provides total size range if ring is resizable.
     *
     * @param float $ringSizingUpperRange
     * @return self
     */
    public function setRingSizingUpperRange($ringSizingUpperRange)
    {
        $this->ringSizingUpperRange = $ringSizingUpperRange;
        return $this;
    }

    /**
     * Adds as ringStyleValue
     *
     * Form or design of a ring
     *
     * @return self
     * @param string $ringStyleValue
     */
    public function addToRingStyle($ringStyleValue)
    {
        $this->ringStyle[] = $ringStyleValue;
        return $this;
    }

    /**
     * isset ringStyle
     *
     * Form or design of a ring
     *
     * @param int|string $index
     * @return bool
     */
    public function issetRingStyle($index)
    {
        return isset($this->ringStyle[$index]);
    }

    /**
     * unset ringStyle
     *
     * Form or design of a ring
     *
     * @param int|string $index
     * @return void
     */
    public function unsetRingStyle($index)
    {
        unset($this->ringStyle[$index]);
    }

    /**
     * Gets as ringStyle
     *
     * Form or design of a ring
     *
     * @return string[]
     */
    public function getRingStyle()
    {
        return $this->ringStyle;
    }

    /**
     * Sets a new ringStyle
     *
     * Form or design of a ring
     *
     * @param string $ringStyle
     * @return self
     */
    public function setRingStyle(array $ringStyle)
    {
        $this->ringStyle = $ringStyle;
        return $this;
    }

    /**
     * Gets as ringSize
     *
     * Standardized numerical sizing of a jewelry ring (based on circumference), as utilized in the United States, Canada and Mexico. Can include quarter and half sizes, depending on which standard is used.
     *
     * @return float
     */
    public function getRingSize()
    {
        return $this->ringSize;
    }

    /**
     * Sets a new ringSize
     *
     * Standardized numerical sizing of a jewelry ring (based on circumference), as utilized in the United States, Canada and Mexico. Can include quarter and half sizes, depending on which standard is used.
     *
     * @param float $ringSize
     * @return self
     */
    public function setRingSize($ringSize)
    {
        $this->ringSize = $ringSize;
        return $this;
    }

    /**
     * Gets as circumference
     *
     * Measurement of a circular object around its perimeter. NOTE: for rings, measurement is taken from the inside of the ring.
     *
     * @return \WalmartDSV\JewelryType\CircumferenceAType
     */
    public function getCircumference()
    {
        return $this->circumference;
    }

    /**
     * Sets a new circumference
     *
     * Measurement of a circular object around its perimeter. NOTE: for rings, measurement is taken from the inside of the ring.
     *
     * @param \WalmartDSV\JewelryType\CircumferenceAType $circumference
     * @return self
     */
    public function setCircumference(\WalmartDSV\JewelryType\CircumferenceAType $circumference)
    {
        $this->circumference = $circumference;
        return $this;
    }

    /**
     * Gets as diameter
     *
     * The measurement from one side of a circle to the other, through the middle. NOTE: For rings, the measurement is taken from the inside of the ring.
     *
     * @return \WalmartDSV\JewelryType\DiameterAType
     */
    public function getDiameter()
    {
        return $this->diameter;
    }

    /**
     * Sets a new diameter
     *
     * The measurement from one side of a circle to the other, through the middle. NOTE: For rings, the measurement is taken from the inside of the ring.
     *
     * @param \WalmartDSV\JewelryType\DiameterAType $diameter
     * @return self
     */
    public function setDiameter(\WalmartDSV\JewelryType\DiameterAType $diameter)
    {
        $this->diameter = $diameter;
        return $this;
    }

    /**
     * Gets as ringSizeStandard
     *
     * One of five major ring-sizing standards.
     *
     * @return string
     */
    public function getRingSizeStandard()
    {
        return $this->ringSizeStandard;
    }

    /**
     * Sets a new ringSizeStandard
     *
     * One of five major ring-sizing standards.
     *
     * @param string $ringSizeStandard
     * @return self
     */
    public function setRingSizeStandard($ringSizeStandard)
    {
        $this->ringSizeStandard = $ringSizeStandard;
        return $this;
    }

    /**
     * Gets as sportsLeague
     *
     * If your item has any association with a specific sports league, enter the league name. Abbreviations are fine. NOTE: This attribute flags an item for inclusion in the online fan shop.
     *
     * @return \WalmartDSV\SportsLeagueType
     */
    public function getSportsLeague()
    {
        return $this->sportsLeague;
    }

    /**
     * Sets a new sportsLeague
     *
     * If your item has any association with a specific sports league, enter the league name. Abbreviations are fine. NOTE: This attribute flags an item for inclusion in the online fan shop.
     *
     * @param \WalmartDSV\SportsLeagueType $sportsLeague
     * @return self
     */
    public function setSportsLeague(\WalmartDSV\SportsLeagueType $sportsLeague)
    {
        $this->sportsLeague = $sportsLeague;
        return $this;
    }

    /**
     * Gets as sportsTeam
     *
     * If your item has any association with a specific sports team, enter the team name. NOTE: This attribute flags an item for inclusion in the online fan shop.
     *
     * @return \WalmartDSV\SportsTeamType
     */
    public function getSportsTeam()
    {
        return $this->sportsTeam;
    }

    /**
     * Sets a new sportsTeam
     *
     * If your item has any association with a specific sports team, enter the team name. NOTE: This attribute flags an item for inclusion in the online fan shop.
     *
     * @param \WalmartDSV\SportsTeamType $sportsTeam
     * @return self
     */
    public function setSportsTeam(\WalmartDSV\SportsTeamType $sportsTeam)
    {
        $this->sportsTeam = $sportsTeam;
        return $this;
    }

    /**
     * Gets as theme
     *
     * A dominant idea, meaning, or setting applied to an item. Used in a wide range of products including decorative objects, clothing, toys, and furniture. Can be an important selection criteria for consumers who want to achieve a particular ambiance for room décor or for a special occasion.
     *
     * @return \WalmartDSV\ThemeType
     */
    public function getTheme()
    {
        return $this->theme;
    }

    /**
     * Sets a new theme
     *
     * A dominant idea, meaning, or setting applied to an item. Used in a wide range of products including decorative objects, clothing, toys, and furniture. Can be an important selection criteria for consumers who want to achieve a particular ambiance for room décor or for a special occasion.
     *
     * @param \WalmartDSV\ThemeType $theme
     * @return self
     */
    public function setTheme(\WalmartDSV\ThemeType $theme)
    {
        $this->theme = $theme;
        return $this;
    }

    /**
     * Gets as americanWireGuage
     *
     * AWG indicates wire diameter. Both electrical wiring and jewelry piercing industries use AWG to describe product diameters. In the jewelry industry, AWG is used to describe both metallic and non-metallic products.
     *
     * @return int
     */
    public function getAmericanWireGuage()
    {
        return $this->americanWireGuage;
    }

    /**
     * Sets a new americanWireGuage
     *
     * AWG indicates wire diameter. Both electrical wiring and jewelry piercing industries use AWG to describe product diameters. In the jewelry industry, AWG is used to describe both metallic and non-metallic products.
     *
     * @param int $americanWireGuage
     * @return self
     */
    public function setAmericanWireGuage($americanWireGuage)
    {
        $this->americanWireGuage = $americanWireGuage;
        return $this;
    }

    /**
     * Gets as athlete
     *
     * A well-known athlete associated with a product, if applicable. This is used to group items in Fan Shop, not to describe a line of clothing.
     *
     * @return \WalmartDSV\AthleteType
     */
    public function getAthlete()
    {
        return $this->athlete;
    }

    /**
     * Sets a new athlete
     *
     * A well-known athlete associated with a product, if applicable. This is used to group items in Fan Shop, not to describe a line of clothing.
     *
     * @param \WalmartDSV\AthleteType $athlete
     * @return self
     */
    public function setAthlete(\WalmartDSV\AthleteType $athlete)
    {
        $this->athlete = $athlete;
        return $this;
    }

    /**
     * Adds as feature
     *
     * List notable features of the item.
     *
     * @return self
     * @param string $feature
     */
    public function addToFeatures($feature)
    {
        $this->features[] = $feature;
        return $this;
    }

    /**
     * isset features
     *
     * List notable features of the item.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFeatures($index)
    {
        return isset($this->features[$index]);
    }

    /**
     * unset features
     *
     * List notable features of the item.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFeatures($index)
    {
        unset($this->features[$index]);
    }

    /**
     * Gets as features
     *
     * List notable features of the item.
     *
     * @return string[]
     */
    public function getFeatures()
    {
        return $this->features;
    }

    /**
     * Sets a new features
     *
     * List notable features of the item.
     *
     * @param string $features
     * @return self
     */
    public function setFeatures(array $features)
    {
        $this->features = $features;
        return $this;
    }

    /**
     * Gets as keywords
     *
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @return string
     */
    public function getKeywords()
    {
        return $this->keywords;
    }

    /**
     * Sets a new keywords
     *
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @param string $keywords
     * @return self
     */
    public function setKeywords($keywords)
    {
        $this->keywords = $keywords;
        return $this;
    }

    /**
     * Gets as isMadeFromRecycledMaterial
     *
     * Indicates that the item is made from recycled materials.
     *
     * @return string
     */
    public function getIsMadeFromRecycledMaterial()
    {
        return $this->isMadeFromRecycledMaterial;
    }

    /**
     * Sets a new isMadeFromRecycledMaterial
     *
     * Indicates that the item is made from recycled materials.
     *
     * @param string $isMadeFromRecycledMaterial
     * @return self
     */
    public function setIsMadeFromRecycledMaterial($isMadeFromRecycledMaterial)
    {
        $this->isMadeFromRecycledMaterial = $isMadeFromRecycledMaterial;
        return $this;
    }

    /**
     * Adds as recycledMaterialContentValue
     *
     * If item contains reused/recycled material, the percentage of all recycled material used to produce the item. This can also include specific material composition; cushions made from 30% recycled cotton fabric. Used to highlight sustainability to the customer.
     *
     * @param \WalmartDSV\RecycledMaterialContentValueType $recycledMaterialContentValue
     *@return self
     */
    public function addToRecycledMaterialContent(\WalmartDSV\RecycledMaterialContentValueType $recycledMaterialContentValue)
    {
        $this->recycledMaterialContent[] = $recycledMaterialContentValue;
        return $this;
    }

    /**
     * isset recycledMaterialContent
     *
     * If item contains reused/recycled material, the percentage of all recycled material used to produce the item. This can also include specific material composition; cushions made from 30% recycled cotton fabric. Used to highlight sustainability to the customer.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetRecycledMaterialContent($index)
    {
        return isset($this->recycledMaterialContent[$index]);
    }

    /**
     * unset recycledMaterialContent
     *
     * If item contains reused/recycled material, the percentage of all recycled material used to produce the item. This can also include specific material composition; cushions made from 30% recycled cotton fabric. Used to highlight sustainability to the customer.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetRecycledMaterialContent($index)
    {
        unset($this->recycledMaterialContent[$index]);
    }

    /**
     * Gets as recycledMaterialContent
     *
     * If item contains reused/recycled material, the percentage of all recycled material used to produce the item. This can also include specific material composition; cushions made from 30% recycled cotton fabric. Used to highlight sustainability to the customer.
     *
     * @return \WalmartDSV\RecycledMaterialContentValueType[]
     */
    public function getRecycledMaterialContent()
    {
        return $this->recycledMaterialContent;
    }

    /**
     * Sets a new recycledMaterialContent
     *
     * If item contains reused/recycled material, the percentage of all recycled material used to produce the item. This can also include specific material composition; cushions made from 30% recycled cotton fabric. Used to highlight sustainability to the customer.
     *
     * @param \WalmartDSV\RecycledMaterialContentValueType[] $recycledMaterialContent
     * @return self
     */
    public function setRecycledMaterialContent(array $recycledMaterialContent)
    {
        $this->recycledMaterialContent = $recycledMaterialContent;
        return $this;
    }

    /**
     * Adds as colorCategoryValue
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @return self
     * @param string $colorCategoryValue
     */
    public function addToColorCategory($colorCategoryValue)
    {
        $this->colorCategory[] = $colorCategoryValue;
        return $this;
    }

    /**
     * isset colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetColorCategory($index)
    {
        return isset($this->colorCategory[$index]);
    }

    /**
     * unset colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetColorCategory($index)
    {
        unset($this->colorCategory[$index]);
    }

    /**
     * Gets as colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @return string[]
     */
    public function getColorCategory()
    {
        return $this->colorCategory;
    }

    /**
     * Sets a new colorCategory
     *
     * Select the color from a short list that best describes the general color of the item. This improves searchability as it allows customers to view items by color from the left navigation when they perform a search.
     *
     * @param string $colorCategory
     * @return self
     */
    public function setColorCategory(array $colorCategory)
    {
        $this->colorCategory = $colorCategory;
        return $this;
    }

    /**
     * Gets as character
     *
     * A person or entity portrayed in print or visual media. A character might be a fictional personality or an actual living person.
     *
     * @return \WalmartDSV\CharacterType
     */
    public function getCharacter()
    {
        return $this->character;
    }

    /**
     * Sets a new character
     *
     * A person or entity portrayed in print or visual media. A character might be a fictional personality or an actual living person.
     *
     * @param \WalmartDSV\CharacterType $character
     * @return self
     */
    public function setCharacter(\WalmartDSV\CharacterType $character)
    {
        $this->character = $character;
        return $this;
    }

    /**
     * Adds as bodyPart
     *
     * The body part/s for which the item is intended.
     *
     * @return self
     * @param string $bodyPart
     */
    public function addToBodyParts($bodyPart)
    {
        $this->bodyParts[] = $bodyPart;
        return $this;
    }

    /**
     * isset bodyParts
     *
     * The body part/s for which the item is intended.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetBodyParts($index)
    {
        return isset($this->bodyParts[$index]);
    }

    /**
     * unset bodyParts
     *
     * The body part/s for which the item is intended.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetBodyParts($index)
    {
        unset($this->bodyParts[$index]);
    }

    /**
     * Gets as bodyParts
     *
     * The body part/s for which the item is intended.
     *
     * @return string[]
     */
    public function getBodyParts()
    {
        return $this->bodyParts;
    }

    /**
     * Sets a new bodyParts
     *
     * The body part/s for which the item is intended.
     *
     * @param string $bodyParts
     * @return self
     */
    public function setBodyParts(array $bodyParts)
    {
        $this->bodyParts = $bodyParts;
        return $this;
    }

    /**
     * Gets as designer
     *
     * Name of the person who planned the form, look, or workings of the product to be made or built.
     *
     * @return string
     */
    public function getDesigner()
    {
        return $this->designer;
    }

    /**
     * Sets a new designer
     *
     * Name of the person who planned the form, look, or workings of the product to be made or built.
     *
     * @param string $designer
     * @return self
     */
    public function setDesigner($designer)
    {
        $this->designer = $designer;
        return $this;
    }

    /**
     * Adds as swatchImage
     *
     * @param \WalmartDSV\JewelryType\SwatchImagesAType\SwatchImageAType $swatchImage
     *@return self
     */
    public function addToSwatchImages(\WalmartDSV\JewelryType\SwatchImagesAType\SwatchImageAType $swatchImage)
    {
        $this->swatchImages[] = $swatchImage;
        return $this;
    }

    /**
     * isset swatchImages
     *
     * @param int|string $index
     * @return bool
     */
    public function issetSwatchImages($index)
    {
        return isset($this->swatchImages[$index]);
    }

    /**
     * unset swatchImages
     *
     * @param int|string $index
     * @return void
     */
    public function unsetSwatchImages($index)
    {
        unset($this->swatchImages[$index]);
    }

    /**
     * Gets as swatchImages
     *
     * @return \WalmartDSV\JewelryType\SwatchImagesAType\SwatchImageAType[]
     */
    public function getSwatchImages()
    {
        return $this->swatchImages;
    }

    /**
     * Sets a new swatchImages
     *
     * @param \WalmartDSV\JewelryType\SwatchImagesAType\SwatchImageAType[] $swatchImages
     * @return self
     */
    public function setSwatchImages(array $swatchImages)
    {
        $this->swatchImages = $swatchImages;
        return $this;
    }


}

