<?php

namespace WalmartDSV;

/**
 * Class representing InkColorType
 *
 * The ink color of pens, markers, ink pads, and other writing implements.
 * XSD Type: InkColor
 */
class InkColorType
{

    /**
     * @var string[] $inkColorValue
     */
    private $inkColorValue = [
        
    ];

    /**
     * Adds as inkColorValue
     *
     * @return self
     * @param string $inkColorValue
     */
    public function addToInkColorValue($inkColorValue)
    {
        $this->inkColorValue[] = $inkColorValue;
        return $this;
    }

    /**
     * isset inkColorValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetInkColorValue($index)
    {
        return isset($this->inkColorValue[$index]);
    }

    /**
     * unset inkColorValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetInkColorValue($index)
    {
        unset($this->inkColorValue[$index]);
    }

    /**
     * Gets as inkColorValue
     *
     * @return string[]
     */
    public function getInkColorValue()
    {
        return $this->inkColorValue;
    }

    /**
     * Sets a new inkColorValue
     *
     * @param string $inkColorValue
     * @return self
     */
    public function setInkColorValue(array $inkColorValue)
    {
        $this->inkColorValue = $inkColorValue;
        return $this;
    }


}

