<?php

namespace WalmartDSV;

/**
 * Class representing InterfaceType
 *
 * Industry term describing the electronic connection enabling devices to communicate with each other. Used in a wide variety of electronic products including cables, computers, and cameras. Important selection factor for compatibility. For example, a cable that allows transfer of photos from a digital camera to a computer using a USB interface.
 * XSD Type: InterfaceType
 */
class InterfaceType
{

    /**
     * @var string[] $interfaceTypeValue
     */
    private $interfaceTypeValue = [
        
    ];

    /**
     * Adds as interfaceTypeValue
     *
     * @return self
     * @param string $interfaceTypeValue
     */
    public function addToInterfaceTypeValue($interfaceTypeValue)
    {
        $this->interfaceTypeValue[] = $interfaceTypeValue;
        return $this;
    }

    /**
     * isset interfaceTypeValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetInterfaceTypeValue($index)
    {
        return isset($this->interfaceTypeValue[$index]);
    }

    /**
     * unset interfaceTypeValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetInterfaceTypeValue($index)
    {
        unset($this->interfaceTypeValue[$index]);
    }

    /**
     * Gets as interfaceTypeValue
     *
     * @return string[]
     */
    public function getInterfaceTypeValue()
    {
        return $this->interfaceTypeValue;
    }

    /**
     * Sets a new interfaceTypeValue
     *
     * @param string $interfaceTypeValue
     * @return self
     */
    public function setInterfaceTypeValue(array $interfaceTypeValue)
    {
        $this->interfaceTypeValue = $interfaceTypeValue;
        return $this;
    }


}

