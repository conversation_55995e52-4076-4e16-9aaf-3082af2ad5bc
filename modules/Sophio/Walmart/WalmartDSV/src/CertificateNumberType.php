<?php

namespace WalmartDSV;

/**
 * Class representing CertificateNumberType
 *
 * If the item has a certificate, enter the certificate number here. Separate multiple values by semicolons.
 * XSD Type: CertificateNumber
 */
class CertificateNumberType
{

    /**
     * @var string[] $certificateNumberValue
     */
    private $certificateNumberValue = [
        
    ];

    /**
     * Adds as certificateNumberValue
     *
     * @return self
     * @param string $certificateNumberValue
     */
    public function addToCertificateNumberValue($certificateNumberValue)
    {
        $this->certificateNumberValue[] = $certificateNumberValue;
        return $this;
    }

    /**
     * isset certificateNumberValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetCertificateNumberValue($index)
    {
        return isset($this->certificateNumberValue[$index]);
    }

    /**
     * unset certificateNumberValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetCertificateNumberValue($index)
    {
        unset($this->certificateNumberValue[$index]);
    }

    /**
     * Gets as certificateNumberValue
     *
     * @return string[]
     */
    public function getCertificateNumberValue()
    {
        return $this->certificateNumberValue;
    }

    /**
     * Sets a new certificateNumberValue
     *
     * @param string $certificateNumberValue
     * @return self
     */
    public function setCertificateNumberValue(array $certificateNumberValue)
    {
        $this->certificateNumberValue = $certificateNumberValue;
        return $this;
    }


}

