<?php

namespace WalmartDSV;

/**
 * Class representing NutrientType
 *
 *
 * XSD Type: nutrient
 */
class NutrientType
{

    /**
     * Name of additional nutrient. This attribute is used in conjunction with Nutrient Amount and Nutrient Percentage Daily Value.
     *
     * @var string $nutrientName
     */
    private $nutrientName = null;

    /**
     * Amount of the nutrient present in one serving. This attribute is used in conjunction with Nutrient Name and Nutrient Percentage Daily Value.
     *
     * @var string $nutrientAmount
     */
    private $nutrientAmount = null;

    /**
     * Percent daily value of the nutrient present in one serving. This attribute is used in conjunction with Nutrient Name and Nutrient Amount.
     *
     * @var string $nutrientPercentageDailyValue
     */
    private $nutrientPercentageDailyValue = null;

    /**
     * Additional footnote text referenced from the Percentage Daily Value in the nutrition or supplement facts.
     *
     * @var string $nutrientFootnote
     */
    private $nutrientFootnote = null;

    /**
     * Gets as nutrientName
     *
     * Name of additional nutrient. This attribute is used in conjunction with Nutrient Amount and Nutrient Percentage Daily Value.
     *
     * @return string
     */
    public function getNutrientName()
    {
        return $this->nutrientName;
    }

    /**
     * Sets a new nutrientName
     *
     * Name of additional nutrient. This attribute is used in conjunction with Nutrient Amount and Nutrient Percentage Daily Value.
     *
     * @param string $nutrientName
     * @return self
     */
    public function setNutrientName($nutrientName)
    {
        $this->nutrientName = $nutrientName;
        return $this;
    }

    /**
     * Gets as nutrientAmount
     *
     * Amount of the nutrient present in one serving. This attribute is used in conjunction with Nutrient Name and Nutrient Percentage Daily Value.
     *
     * @return string
     */
    public function getNutrientAmount()
    {
        return $this->nutrientAmount;
    }

    /**
     * Sets a new nutrientAmount
     *
     * Amount of the nutrient present in one serving. This attribute is used in conjunction with Nutrient Name and Nutrient Percentage Daily Value.
     *
     * @param string $nutrientAmount
     * @return self
     */
    public function setNutrientAmount($nutrientAmount)
    {
        $this->nutrientAmount = $nutrientAmount;
        return $this;
    }

    /**
     * Gets as nutrientPercentageDailyValue
     *
     * Percent daily value of the nutrient present in one serving. This attribute is used in conjunction with Nutrient Name and Nutrient Amount.
     *
     * @return string
     */
    public function getNutrientPercentageDailyValue()
    {
        return $this->nutrientPercentageDailyValue;
    }

    /**
     * Sets a new nutrientPercentageDailyValue
     *
     * Percent daily value of the nutrient present in one serving. This attribute is used in conjunction with Nutrient Name and Nutrient Amount.
     *
     * @param string $nutrientPercentageDailyValue
     * @return self
     */
    public function setNutrientPercentageDailyValue($nutrientPercentageDailyValue)
    {
        $this->nutrientPercentageDailyValue = $nutrientPercentageDailyValue;
        return $this;
    }

    /**
     * Gets as nutrientFootnote
     *
     * Additional footnote text referenced from the Percentage Daily Value in the nutrition or supplement facts.
     *
     * @return string
     */
    public function getNutrientFootnote()
    {
        return $this->nutrientFootnote;
    }

    /**
     * Sets a new nutrientFootnote
     *
     * Additional footnote text referenced from the Percentage Daily Value in the nutrition or supplement facts.
     *
     * @param string $nutrientFootnote
     * @return self
     */
    public function setNutrientFootnote($nutrientFootnote)
    {
        $this->nutrientFootnote = $nutrientFootnote;
        return $this;
    }


}

