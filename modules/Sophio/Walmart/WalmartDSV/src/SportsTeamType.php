<?php

namespace WalmartDSV;

/**
 * Class representing SportsTeamType
 *
 * If your item has any association with a specific sports team, enter the team name. NOTE: This attribute flags an item for inclusion in the online fan shop.
 * XSD Type: SportsTeam
 */
class SportsTeamType
{

    /**
     * @var string $sportsTeamValue
     */
    private $sportsTeamValue = null;

    /**
     * Gets as sportsTeamValue
     *
     * @return string
     */
    public function getSportsTeamValue()
    {
        return $this->sportsTeamValue;
    }

    /**
     * Sets a new sportsTeamValue
     *
     * @param string $sportsTeamValue
     * @return self
     */
    public function setSportsTeamValue($sportsTeamValue)
    {
        $this->sportsTeamValue = $sportsTeamValue;
        return $this;
    }


}

