<?php

namespace WalmartDSV;

/**
 * Class representing PearlLustreType
 *
 * Measure of the quality and quantity of light that reflects off a pearl.
 * XSD Type: PearlLustre
 */
class PearlLustreType
{

    /**
     * @var string[] $pearlLustreValue
     */
    private $pearlLustreValue = [
        
    ];

    /**
     * Adds as pearlLustreValue
     *
     * @return self
     * @param string $pearlLustreValue
     */
    public function addToPearlLustreValue($pearlLustreValue)
    {
        $this->pearlLustreValue[] = $pearlLustreValue;
        return $this;
    }

    /**
     * isset pearlLustreValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetPearlLustreValue($index)
    {
        return isset($this->pearlLustreValue[$index]);
    }

    /**
     * unset pearlLustreValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetPearlLustreValue($index)
    {
        unset($this->pearlLustreValue[$index]);
    }

    /**
     * Gets as pearlLustreValue
     *
     * @return string[]
     */
    public function getPearlLustreValue()
    {
        return $this->pearlLustreValue;
    }

    /**
     * Sets a new pearlLustreValue
     *
     * @param string $pearlLustreValue
     * @return self
     */
    public function setPearlLustreValue(array $pearlLustreValue)
    {
        $this->pearlLustreValue = $pearlLustreValue;
        return $this;
    }


}

