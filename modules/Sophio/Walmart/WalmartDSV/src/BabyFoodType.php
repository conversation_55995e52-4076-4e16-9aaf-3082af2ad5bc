<?php

namespace WalmartDSV;

/**
 * Class representing BabyFoodType
 *
 *
 * XSD Type: BabyFood
 */
class BabyFoodType
{

    /**
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @var string[] $additionalVariantAttributeNames
     */
    private $additionalVariantAttributeNames = null;

    /**
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @var string $shortDescription
     */
    private $shortDescription = null;

    /**
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @var string[] $keyFeatures
     */
    private $keyFeatures = null;

    /**
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @var string $brand
     */
    private $brand = null;

    /**
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @var string $manufacturer
     */
    private $manufacturer = null;

    /**
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @var int $multipackQuantity
     */
    private $multipackQuantity = null;

    /**
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @var int $countPerPack
     */
    private $countPerPack = null;

    /**
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @var string $count
     */
    private $count = null;

    /**
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @var string $mainImageUrl
     */
    private $mainImageUrl = null;

    /**
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @var string[] $productSecondaryImageURL
     */
    private $productSecondaryImageURL = null;

    /**
     * The distinctive taste or flavor of the item, as provided by manufacturer. This is used for a wide variety of products, including food and beverages for both animals and humans. This may also apply to non-food items that come in flavors, including dental products, cigars and smoker wood chips.
     *
     * @var string $flavor
     */
    private $flavor = null;

    /**
     * Food that can be consumed without further preparation, according to FDA guidelines. For more information see FDA Food Code 2009: Chapter 1-201.10 Definitions.
     *
     * @var string $isReadyToEat
     */
    private $isReadyToEat = null;

    /**
     * Overall dimensions of an item. Used only for products that do not already have a more specific 'x size' attribute, such as ring size or clothing size.
     *
     * @var string $size
     */
    private $size = null;

    /**
     * General grouping of ages into commonly used demographic labels.
     *
     * @var string[] $ageGroup
     */
    private $ageGroup = null;

    /**
     * Minimum and Maximum Ages for a product. Note: Both Min. and Max. attributes will be the same Unit of Measure: Months, or Years.
     *
     * @var \WalmartDSV\AgeRangeType $ageRange
     */
    private $ageRange = null;

    /**
     * A person or entity portrayed in print or visual media. A character might be a fictional personality or an actual living person.
     *
     * @var \WalmartDSV\CharacterType $character
     */
    private $character = null;

    /**
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @var string[] $globalBrandLicense
     */
    private $globalBrandLicense = null;

    /**
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @var string $variantGroupId
     */
    private $variantGroupId = null;

    /**
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @var string[] $variantAttributeNames
     */
    private $variantAttributeNames = null;

    /**
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @var string $isPrimaryVariant
     */
    private $isPrimaryVariant = null;

    /**
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @var string $isPrivateLabelOrUnbranded
     */
    private $isPrivateLabelOrUnbranded = null;

    /**
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @var string $isProp65WarningRequired
     */
    private $isProp65WarningRequired = null;

    /**
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @var string $prop65WarningText
     */
    private $prop65WarningText = null;

    /**
     * Mark as "Y" if your item is any of the following: food used for human or domestic animal consumption; ingredients added to food; napkins; tissues; toilet paper; foil, plastic wrap, wax paper, parchment paper; paper towels; disposable plates, bowls, and cutlery; detergents, soaps, waxes, and other cleansing agents; non-prescription drugs, female hygeine products, and toiletries; automotive fluids and cleaners; rock salt; diapers, pullups and swimmers; fertilizer; kitty litter.
     *
     * @var string $hasPricePerUnit
     */
    private $hasPricePerUnit = null;

    /**
     * Enter the quantity of units for the item, based on the "PPU Unit of Measure" you selected. For example, a gallon of milk should be 128. NOTE: Do not enter the price.
     *
     * @var float $pricePerUnitQuantity
     */
    private $pricePerUnitQuantity = null;

    /**
     * The units that will be used to calculate the "Price Per Unit" for your product. For example, a gallon of milk has a "PPU Unit of Measure" of Fluid Ounces. NOTE: This may not be the Unit of Measure on the label.
     *
     * @var string $pricePerUnitUom
     */
    private $pricePerUnitUom = null;

    /**
     * Select Yes if product is labeled with any type of expiration or code date that indicates when product should no longer be consumed or no longer at best quality (e.g. Best If Used By, Best By, Use By, etc. ). Some examples of items with expiration dates include food, cleaning supplies, beauty products, etc.
     *
     * @var string $hasExpiration
     */
    private $hasExpiration = null;

    /**
     * The length of time that the product can be stored without becoming unfit for consumption or after which the product is no longer at best quality, measured in days.
     *
     * @var \WalmartDSV\BabyFoodType\ShelfLifeAType $shelfLife
     */
    private $shelfLife = null;

    /**
     * Indicates if item requires nutritional facts labeling per FDA guidelines. If yes, please provide the following elements in one or more images 1) The Nutrition Facts and 2) Ingredients. Both attributes are required. If both elements are contained in one image, you may repeat the URL in both attributes.
     *
     * @var string $isNutritionFactsLabelRequired
     */
    private $isNutritionFactsLabelRequired = null;

    /**
     * Image URL of the nutritional facts label. Provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB.
     *
     * @var string $nutritionFactsLabel
     */
    private $nutritionFactsLabel = null;

    /**
     * URL of image. Provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @var string $nutritionIngredientsImage
     */
    private $nutritionIngredientsImage = null;

    /**
     * Does your product have a list of ingredients OTHER than that provided with Drug Facts, Nutrition Facts, or Supplement Facts? If so, please provide EITHER the ingredients text or the URL to the image.
     *
     * @var string $hasIngredientList
     */
    private $hasIngredientList = null;

    /**
     * If your product contains a list of ingredients OTHER than that required with drug, supplement, or nutrition info, provide the URL of image. Provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB.
     *
     * @var string $ingredientListImage
     */
    private $ingredientListImage = null;

    /**
     * The list of all ingredients contained in an item, as found on the product label mandated by FDA guidelines. The ingredients should be listed in descending order by weight. The label must list the names of any FDA-certified color additives, but some ingredients can be listed collectively as flavors, spices, artificial flavoring or artificial colors. Refer to the FDA Food Labeling Guide for more guidelines.
     *
     * @var string $ingredients
     */
    private $ingredients = null;

    /**
     * Indicates that the item is prone to freezing or melting that may adversely affect it.
     *
     * @var string $isTemperatureSensitive
     */
    private $isTemperatureSensitive = null;

    /**
     * Does your product contain Genetically Modified Organisms (GMOS) whose DNA has been altered using genetic engineering techniques?
     *
     * @var string $hasGMOs
     */
    private $hasGMOs = null;

    /**
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @var string $hasStateRestrictions
     */
    private $hasStateRestrictions = null;

    /**
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @var \WalmartDSV\StateRestrictionType[] $stateRestrictions
     */
    private $stateRestrictions = null;

    /**
     * Measurement value specifying the amount of the item typically used as a reference on the label of that item to list per serving information (nutrients, calories, total fat). Applicable for a wide variety of products including food, beverages, and nutritional supplements.
     *
     * @var string $servingSize
     */
    private $servingSize = null;

    /**
     * Number of servings contained in the item's package (box, bottle, bag).
     *
     * @var float $servingsPerContainer
     */
    private $servingsPerContainer = null;

    /**
     * Number of calories contained in one serving, as found on the food label.
     *
     * @var \WalmartDSV\BabyFoodType\CaloriesAType $calories
     */
    private $calories = null;

    /**
     * Number of calories derived from fat, as found on the food label.
     *
     * @var \WalmartDSV\BabyFoodType\CaloriesFromFatAType $caloriesFromFat
     */
    private $caloriesFromFat = null;

    /**
     * Total number of fat calories per serving, expressed in grams, milligrams, or less than a certain number of grams.
     *
     * @var \WalmartDSV\BabyFoodType\TotalFatAType $totalFat
     */
    private $totalFat = null;

    /**
     * Percent daily value of fat per serving, according to the food label.
     *
     * @var float $totalFatPercentageDailyValue
     */
    private $totalFatPercentageDailyValue = null;

    /**
     * The number of calories in one gram of fat, as may be provided in Nutritional Facts according to FDA guidelines.
     *
     * @var \WalmartDSV\BabyFoodType\FatCaloriesPerGramAType $fatCaloriesPerGram
     */
    private $fatCaloriesPerGram = null;

    /**
     * Total number of carbohydrates per serving, expressed in grams or less than a certain number of grams.
     *
     * @var \WalmartDSV\BabyFoodType\TotalCarbohydrateAType $totalCarbohydrate
     */
    private $totalCarbohydrate = null;

    /**
     * Percent daily value of carbohydrates per serving.
     *
     * @var float $totalCarbohydratePercentageDailyValue
     */
    private $totalCarbohydratePercentageDailyValue = null;

    /**
     * The number of calories in one gram of carbohydrates, as may be provided in Nutritional Facts according to FDA guidelines.
     *
     * @var \WalmartDSV\BabyFoodType\CarbohydrateCaloriesPerGramAType $carbohydrateCaloriesPerGram
     */
    private $carbohydrateCaloriesPerGram = null;

    /**
     * Additional nutrients, not including total fat or total carbohydrates, which should be entered in "Total Fat" and "Total Carbohydrate" respectively.
     *
     * @var \WalmartDSV\NutrientType[] $nutrients
     */
    private $nutrients = null;

    /**
     * The number of calories in one gram of protein, as may be provided in Nutritional Facts according to FDA guidelines.
     *
     * @var \WalmartDSV\BabyFoodType\ProteinCaloriesPerGramAType $proteinCaloriesPerGram
     */
    private $proteinCaloriesPerGram = null;

    /**
     * Percent daily value of protein per serving.
     *
     * @var float $totalProteinPercentageDailyValue
     */
    private $totalProteinPercentageDailyValue = null;

    /**
     * Total protein per serving, expressed in grams, milligrams, or less than.
     *
     * @var \WalmartDSV\BabyFoodType\TotalProteinAType $totalProtein
     */
    private $totalProtein = null;

    /**
     * Describes the form of the food if the food is sold in a variety of forms, such as sliced and unsliced, whole or halves, etc.
     *
     * @var string $foodForm
     */
    private $foodForm = null;

    /**
     * The kind of physical package or receptacle that contains the product as presented to the consumer. Also used to describe storage items. Consumers may select different products based on their container preferences. For example, a parent may select juice packaged in boxes rather than bottles for use in childrens' lunch boxes.
     *
     * @var string[] $containerType
     */
    private $containerType = null;

    /**
     * Generally a new food that resembles a traditional food and is a substitute for the traditional food must be labeled as an imitation, especially if the new food contains less protein or a lesser amount of any essential vitamin or mineral.
     *
     * @var string $isImitation
     */
    private $isImitation = null;

    /**
     * Has your product been inspected by the United States Department of Agriculture? There are a number of food products that are under the jurisdiction of the Food Safety and Inspection Service (FSIS), and are thus subject to inspection, including: egg products (liquid, frozen or dried), meat, and poultry. Refer to the FDA / USDA Food Standards and Labeling Policy Book for exceptions and more detailed guidelines.
     *
     * @var string $usdaInspected
     */
    private $usdaInspected = null;

    /**
     * Select "Y" if High Fructose Corn Syrup is in the ingredient list.
     *
     * @var string $hasHighFructoseCornSyrup
     */
    private $hasHighFructoseCornSyrup = null;

    /**
     * Statement of the number of fluid ounces required to supply 100 calories, for use with baby formula only.
     *
     * @var \WalmartDSV\BabyFoodType\FluidOuncesSupplying100CaloriesAType $fluidOuncesSupplying100Calories
     */
    private $fluidOuncesSupplying100Calories = null;

    /**
     * Statement regarding any ingredients that may be food allergens, often written as "Contains X" or "Manufactured in a facility which processes Y."
     *
     * @var string[] $foodAllergenStatements
     */
    private $foodAllergenStatements = null;

    /**
     * Description of the type of packaging for baby food product. This is used as a sorting category when visitors search for baby food on Walmart.com. It is recommended to use example values for searchability. If your product falls outside those values, then you may enter another appropriate value.
     *
     * @var string $babyFoodPackaging
     */
    private $babyFoodPackaging = null;

    /**
     * If the baby formula states that it is for a particular life stage, enter it here.
     *
     * @var string[] $babyFormulaStage
     */
    private $babyFormulaStage = null;

    /**
     * Attribute designed to capture the generalized age grouping (commonly referred to as "stage") of a given baby food, as specified by manufacturer. Specific description of each stage varies with baby food producer. Example descriptions: Stage 1 - Foods have a single ingredient and are pureed and generally contain about 2.5 oz of fruits, veggies or meats. Stage 2 – Foods are strained instead of pureed and have a combination of fruits or veggies instead of single ingredients, contain larger portions. Stage 3 – Foods are mashed and have more texture than the pureed foods and may have bits and chunks of meats or veggies.
     *
     * @var string[] $babyFoodStage
     */
    private $babyFoodStage = null;

    /**
     * Detailed information telling how the product should be operated or assembled.
     *
     * @var string $instructions
     */
    private $instructions = null;

    /**
     * List notable features of the item.
     *
     * @var string[] $features
     */
    private $features = null;

    /**
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @var string $keywords
     */
    private $keywords = null;

    /**
     * Instructions for storage or preparation of potentially hazardous fresh food. NOTE: Required for raw meat products.
     *
     * @var string $safeHandlingInstructions
     */
    private $safeHandlingInstructions = null;

    /**
     * Indicates the cooking style, especially regional or cultural cuisines, you might use the food to prepare.
     *
     * @var string[] $cuisine
     */
    private $cuisine = null;

    /**
     * Helpful information related to food preparation.
     *
     * @var string[] $foodPreparationTips
     */
    private $foodPreparationTips = null;

    /**
     * Helpful information related to food storage.
     *
     * @var string[] $foodStorageTips
     */
    private $foodStorageTips = null;

    /**
     * @var \WalmartDSV\BabyFoodType\SwatchImagesAType\SwatchImageAType[] $swatchImages
     */
    private $swatchImages = null;

    /**
     * Adds as additionalVariantAttributeName
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @return self
     * @param string $additionalVariantAttributeName
     */
    public function addToAdditionalVariantAttributeNames($additionalVariantAttributeName)
    {
        $this->additionalVariantAttributeNames[] = $additionalVariantAttributeName;
        return $this;
    }

    /**
     * isset additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAdditionalVariantAttributeNames($index)
    {
        return isset($this->additionalVariantAttributeNames[$index]);
    }

    /**
     * unset additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAdditionalVariantAttributeNames($index)
    {
        unset($this->additionalVariantAttributeNames[$index]);
    }

    /**
     * Gets as additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @return string[]
     */
    public function getAdditionalVariantAttributeNames()
    {
        return $this->additionalVariantAttributeNames;
    }

    /**
     * Sets a new additionalVariantAttributeNames
     *
     * Holder for migrating legacy items with variant xml names, that are not part of V3 variant attributes
     *
     * @param string $additionalVariantAttributeNames
     * @return self
     */
    public function setAdditionalVariantAttributeNames(array $additionalVariantAttributeNames)
    {
        $this->additionalVariantAttributeNames = $additionalVariantAttributeNames;
        return $this;
    }

    /**
     * Gets as shortDescription
     *
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @return string
     */
    public function getShortDescription()
    {
        return $this->shortDescription;
    }

    /**
     * Sets a new shortDescription
     *
     * Overview of the key selling points of the item, marketing content, and highlights in paragraph form. For SEO purposes, repeat the product name and relevant keywords here.
     *
     * @param string $shortDescription
     * @return self
     */
    public function setShortDescription($shortDescription)
    {
        $this->shortDescription = $shortDescription;
        return $this;
    }

    /**
     * Adds as keyFeaturesValue
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @return self
     * @param string $keyFeaturesValue
     */
    public function addToKeyFeatures($keyFeaturesValue)
    {
        $this->keyFeatures[] = $keyFeaturesValue;
        return $this;
    }

    /**
     * isset keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetKeyFeatures($index)
    {
        return isset($this->keyFeatures[$index]);
    }

    /**
     * unset keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetKeyFeatures($index)
    {
        unset($this->keyFeatures[$index]);
    }

    /**
     * Gets as keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @return string[]
     */
    public function getKeyFeatures()
    {
        return $this->keyFeatures;
    }

    /**
     * Sets a new keyFeatures
     *
     * Key features will appear as bulleted text on the item page and in search results. Key features help the user understand the benefits of the product with a simple and clean format. We highly recommended using three or more key features.
     *
     * @param string $keyFeatures
     * @return self
     */
    public function setKeyFeatures(array $keyFeatures)
    {
        $this->keyFeatures = $keyFeatures;
        return $this;
    }

    /**
     * Gets as brand
     *
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @return string
     */
    public function getBrand()
    {
        return $this->brand;
    }

    /**
     * Sets a new brand
     *
     * Name, term, design or other feature that distinguishes one seller's product from those of others. This can be the name of the company associated with the product, but not always. If item does not have a brand, use "Unbranded".
     *
     * @param string $brand
     * @return self
     */
    public function setBrand($brand)
    {
        $this->brand = $brand;
        return $this;
    }

    /**
     * Gets as manufacturer
     *
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @return string
     */
    public function getManufacturer()
    {
        return $this->manufacturer;
    }

    /**
     * Sets a new manufacturer
     *
     * Manufacturer is the maker of the product. This is the name of the company that produces the product, not necessarily the brand name of the item. For some products, the manufacturer and the brand may be the same.
     *
     * @param string $manufacturer
     * @return self
     */
    public function setManufacturer($manufacturer)
    {
        $this->manufacturer = $manufacturer;
        return $this;
    }

    /**
     * Gets as multipackQuantity
     *
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @return int
     */
    public function getMultipackQuantity()
    {
        return $this->multipackQuantity;
    }

    /**
     * Sets a new multipackQuantity
     *
     * The number of identical, individually packaged-for-sale items. If an item does not contain other items, does not contain identical items, or if the items contained within cannot be sold individually, the value for this attribute should be "1." Examples: (1) A single bottle of 50 pills has a "Multipack Quantity" of "1." (2) A package containing two identical bottles of 50 pills has a "Multipack Quantity" of 2. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Multipack Quantity" of "6." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Multipack Quantity" of "1." (5) A gift basket of 5 different items has a "Multipack Quantity" of "1."
     *
     * @param int $multipackQuantity
     * @return self
     */
    public function setMultipackQuantity($multipackQuantity)
    {
        $this->multipackQuantity = $multipackQuantity;
        return $this;
    }

    /**
     * Gets as countPerPack
     *
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @return int
     */
    public function getCountPerPack()
    {
        return $this->countPerPack;
    }

    /**
     * Sets a new countPerPack
     *
     * The number of identical items inside each individual pack given by the "Multipack Quantity" attribute. Examples: (1) A single bottle of 50 pills has a "Count Per Pack" of "50." (2) A package containing two identical bottles of 50 pills has a "Count Per Pack" of 50. (3) A 6-pack of soda labelled for individual sale connected by plastic rings has a "Count Per Pack" of "1." (4) A 6-pack of soda in a box whose cans are not marked for individual sale has a "Count Per Pack" of "6." (5) A gift basket of 5 different items has a "Count Per Pack" of "1."
     *
     * @param int $countPerPack
     * @return self
     */
    public function setCountPerPack($countPerPack)
    {
        $this->countPerPack = $countPerPack;
        return $this;
    }

    /**
     * Gets as count
     *
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @return string
     */
    public function getCount()
    {
        return $this->count;
    }

    /**
     * Sets a new count
     *
     * The total number of identical items in the package or box; a result of the multiplication of Multipack Quantity by Count Per Pack. Examples: (1) A single bottle of 50 pills has a "Total Count" of 50. (2) A package containing two identical bottles of 50 pills has a "Total Count" of 100. (3) A gift basket of 5 different items has a "Total Count" of 1.
     *
     * @param string $count
     * @return self
     */
    public function setCount($count)
    {
        $this->count = $count;
        return $this;
    }

    /**
     * Gets as mainImageUrl
     *
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return string
     */
    public function getMainImageUrl()
    {
        return $this->mainImageUrl;
    }

    /**
     * Sets a new mainImageUrl
     *
     * Main image of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param string $mainImageUrl
     * @return self
     */
    public function setMainImageUrl($mainImageUrl)
    {
        $this->mainImageUrl = $mainImageUrl;
        return $this;
    }

    /**
     * Adds as productSecondaryImageURLValue
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return self
     * @param string $productSecondaryImageURLValue
     */
    public function addToProductSecondaryImageURL($productSecondaryImageURLValue)
    {
        $this->productSecondaryImageURL[] = $productSecondaryImageURLValue;
        return $this;
    }

    /**
     * isset productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetProductSecondaryImageURL($index)
    {
        return isset($this->productSecondaryImageURL[$index]);
    }

    /**
     * unset productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetProductSecondaryImageURL($index)
    {
        unset($this->productSecondaryImageURL[$index]);
    }

    /**
     * Gets as productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @return string[]
     */
    public function getProductSecondaryImageURL()
    {
        return $this->productSecondaryImageURL;
    }

    /**
     * Sets a new productSecondaryImageURL
     *
     * Secondary images of the item. Image URL must end in the file name. Image URL must be static and have no redirections. Preferred file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Recommended image resolution: 3000 x 3000 pixels at 300 ppi. Minimum image size requirements: Zoom capability: 2000 x 2000 pixels at 300 ppi. Non-zoom minimum: 500 x 500 pixels at 72 ppi. Maximum file size: 2 MB.
     *
     * @param string $productSecondaryImageURL
     * @return self
     */
    public function setProductSecondaryImageURL(array $productSecondaryImageURL)
    {
        $this->productSecondaryImageURL = $productSecondaryImageURL;
        return $this;
    }

    /**
     * Gets as flavor
     *
     * The distinctive taste or flavor of the item, as provided by manufacturer. This is used for a wide variety of products, including food and beverages for both animals and humans. This may also apply to non-food items that come in flavors, including dental products, cigars and smoker wood chips.
     *
     * @return string
     */
    public function getFlavor()
    {
        return $this->flavor;
    }

    /**
     * Sets a new flavor
     *
     * The distinctive taste or flavor of the item, as provided by manufacturer. This is used for a wide variety of products, including food and beverages for both animals and humans. This may also apply to non-food items that come in flavors, including dental products, cigars and smoker wood chips.
     *
     * @param string $flavor
     * @return self
     */
    public function setFlavor($flavor)
    {
        $this->flavor = $flavor;
        return $this;
    }

    /**
     * Gets as isReadyToEat
     *
     * Food that can be consumed without further preparation, according to FDA guidelines. For more information see FDA Food Code 2009: Chapter 1-201.10 Definitions.
     *
     * @return string
     */
    public function getIsReadyToEat()
    {
        return $this->isReadyToEat;
    }

    /**
     * Sets a new isReadyToEat
     *
     * Food that can be consumed without further preparation, according to FDA guidelines. For more information see FDA Food Code 2009: Chapter 1-201.10 Definitions.
     *
     * @param string $isReadyToEat
     * @return self
     */
    public function setIsReadyToEat($isReadyToEat)
    {
        $this->isReadyToEat = $isReadyToEat;
        return $this;
    }

    /**
     * Gets as size
     *
     * Overall dimensions of an item. Used only for products that do not already have a more specific 'x size' attribute, such as ring size or clothing size.
     *
     * @return string
     */
    public function getSize()
    {
        return $this->size;
    }

    /**
     * Sets a new size
     *
     * Overall dimensions of an item. Used only for products that do not already have a more specific 'x size' attribute, such as ring size or clothing size.
     *
     * @param string $size
     * @return self
     */
    public function setSize($size)
    {
        $this->size = $size;
        return $this;
    }

    /**
     * Adds as ageGroupValue
     *
     * General grouping of ages into commonly used demographic labels.
     *
     * @return self
     * @param string $ageGroupValue
     */
    public function addToAgeGroup($ageGroupValue)
    {
        $this->ageGroup[] = $ageGroupValue;
        return $this;
    }

    /**
     * isset ageGroup
     *
     * General grouping of ages into commonly used demographic labels.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAgeGroup($index)
    {
        return isset($this->ageGroup[$index]);
    }

    /**
     * unset ageGroup
     *
     * General grouping of ages into commonly used demographic labels.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAgeGroup($index)
    {
        unset($this->ageGroup[$index]);
    }

    /**
     * Gets as ageGroup
     *
     * General grouping of ages into commonly used demographic labels.
     *
     * @return string[]
     */
    public function getAgeGroup()
    {
        return $this->ageGroup;
    }

    /**
     * Sets a new ageGroup
     *
     * General grouping of ages into commonly used demographic labels.
     *
     * @param string $ageGroup
     * @return self
     */
    public function setAgeGroup(array $ageGroup)
    {
        $this->ageGroup = $ageGroup;
        return $this;
    }

    /**
     * Gets as ageRange
     *
     * Minimum and Maximum Ages for a product. Note: Both Min. and Max. attributes will be the same Unit of Measure: Months, or Years.
     *
     * @return \WalmartDSV\AgeRangeType
     */
    public function getAgeRange()
    {
        return $this->ageRange;
    }

    /**
     * Sets a new ageRange
     *
     * Minimum and Maximum Ages for a product. Note: Both Min. and Max. attributes will be the same Unit of Measure: Months, or Years.
     *
     * @param \WalmartDSV\AgeRangeType $ageRange
     * @return self
     */
    public function setAgeRange(\WalmartDSV\AgeRangeType $ageRange)
    {
        $this->ageRange = $ageRange;
        return $this;
    }

    /**
     * Gets as character
     *
     * A person or entity portrayed in print or visual media. A character might be a fictional personality or an actual living person.
     *
     * @return \WalmartDSV\CharacterType
     */
    public function getCharacter()
    {
        return $this->character;
    }

    /**
     * Sets a new character
     *
     * A person or entity portrayed in print or visual media. A character might be a fictional personality or an actual living person.
     *
     * @param \WalmartDSV\CharacterType $character
     * @return self
     */
    public function setCharacter(\WalmartDSV\CharacterType $character)
    {
        $this->character = $character;
        return $this;
    }

    /**
     * Adds as globalBrandLicenseValue
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @return self
     * @param string $globalBrandLicenseValue
     */
    public function addToGlobalBrandLicense($globalBrandLicenseValue)
    {
        $this->globalBrandLicense[] = $globalBrandLicenseValue;
        return $this;
    }

    /**
     * isset globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @param int|string $index
     * @return bool
     */
    public function issetGlobalBrandLicense($index)
    {
        return isset($this->globalBrandLicense[$index]);
    }

    /**
     * unset globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @param int|string $index
     * @return void
     */
    public function unsetGlobalBrandLicense($index)
    {
        unset($this->globalBrandLicense[$index]);
    }

    /**
     * Gets as globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @return string[]
     */
    public function getGlobalBrandLicense()
    {
        return $this->globalBrandLicense;
    }

    /**
     * Sets a new globalBrandLicense
     *
     * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
     *
     * @param string $globalBrandLicense
     * @return self
     */
    public function setGlobalBrandLicense(array $globalBrandLicense)
    {
        $this->globalBrandLicense = $globalBrandLicense;
        return $this;
    }

    /**
     * Gets as variantGroupId
     *
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @return string
     */
    public function getVariantGroupId()
    {
        return $this->variantGroupId;
    }

    /**
     * Sets a new variantGroupId
     *
     * Required if item is a variant. Make up a number and/or letter code for “Variant Group ID” and add this to all variations of the same product. Partners must ensure uniqueness of their Variant Group IDs.
     *
     * @param string $variantGroupId
     * @return self
     */
    public function setVariantGroupId($variantGroupId)
    {
        $this->variantGroupId = $variantGroupId;
        return $this;
    }

    /**
     * Adds as variantAttributeName
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @return self
     * @param string $variantAttributeName
     */
    public function addToVariantAttributeNames($variantAttributeName)
    {
        $this->variantAttributeNames[] = $variantAttributeName;
        return $this;
    }

    /**
     * isset variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetVariantAttributeNames($index)
    {
        return isset($this->variantAttributeNames[$index]);
    }

    /**
     * unset variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetVariantAttributeNames($index)
    {
        unset($this->variantAttributeNames[$index]);
    }

    /**
     * Gets as variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @return string[]
     */
    public function getVariantAttributeNames()
    {
        return $this->variantAttributeNames;
    }

    /**
     * Sets a new variantAttributeNames
     *
     * Designate all attributes by which the item is varying. Variant attributes are limited to those designated for each category.
     *
     * @param string $variantAttributeNames
     * @return self
     */
    public function setVariantAttributeNames(array $variantAttributeNames)
    {
        $this->variantAttributeNames = $variantAttributeNames;
        return $this;
    }

    /**
     * Gets as isPrimaryVariant
     *
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @return string
     */
    public function getIsPrimaryVariant()
    {
        return $this->isPrimaryVariant;
    }

    /**
     * Sets a new isPrimaryVariant
     *
     * Note whether item is intended as the main variant in a variant grouping. The primary variant will appear as the image when customers search and the first image displayed on the item page. This should be set as "Yes" for only one item in a group of variants.
     *
     * @param string $isPrimaryVariant
     * @return self
     */
    public function setIsPrimaryVariant($isPrimaryVariant)
    {
        $this->isPrimaryVariant = $isPrimaryVariant;
        return $this;
    }

    /**
     * Gets as isPrivateLabelOrUnbranded
     *
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @return string
     */
    public function getIsPrivateLabelOrUnbranded()
    {
        return $this->isPrivateLabelOrUnbranded;
    }

    /**
     * Sets a new isPrivateLabelOrUnbranded
     *
     * Products with brand names solely owned, registered, or sold exclusively at Walmart. This includes derivations of national brands.
     *
     * @param string $isPrivateLabelOrUnbranded
     * @return self
     */
    public function setIsPrivateLabelOrUnbranded($isPrivateLabelOrUnbranded)
    {
        $this->isPrivateLabelOrUnbranded = $isPrivateLabelOrUnbranded;
        return $this;
    }

    /**
     * Gets as isProp65WarningRequired
     *
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @return string
     */
    public function getIsProp65WarningRequired()
    {
        return $this->isProp65WarningRequired;
    }

    /**
     * Sets a new isProp65WarningRequired
     *
     * Selecting "Y" indicates the product requires California's Proposition 65 special warning. Proposition 65 entitles California consumers to special warnings for products that contain chemicals known to the state of California to cause cancer and birth defects or other reproductive harm if certain criteria are met (such as quantity of chemical contained in the product). See the portions of the California Health and Safety Code related to Proposition 65 for more information.
     *
     * @param string $isProp65WarningRequired
     * @return self
     */
    public function setIsProp65WarningRequired($isProp65WarningRequired)
    {
        $this->isProp65WarningRequired = $isProp65WarningRequired;
        return $this;
    }

    /**
     * Gets as prop65WarningText
     *
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @return string
     */
    public function getProp65WarningText()
    {
        return $this->prop65WarningText;
    }

    /**
     * Sets a new prop65WarningText
     *
     * This is a particular statement legally required by the State of California for certain products to warn consumers about potential health dangers. See the portions of the California Health and Safety Code related to Proposition 65 to see what products require labels and to verify the text of your warning label.
     *
     * @param string $prop65WarningText
     * @return self
     */
    public function setProp65WarningText($prop65WarningText)
    {
        $this->prop65WarningText = $prop65WarningText;
        return $this;
    }

    /**
     * Gets as hasPricePerUnit
     *
     * Mark as "Y" if your item is any of the following: food used for human or domestic animal consumption; ingredients added to food; napkins; tissues; toilet paper; foil, plastic wrap, wax paper, parchment paper; paper towels; disposable plates, bowls, and cutlery; detergents, soaps, waxes, and other cleansing agents; non-prescription drugs, female hygeine products, and toiletries; automotive fluids and cleaners; rock salt; diapers, pullups and swimmers; fertilizer; kitty litter.
     *
     * @return string
     */
    public function getHasPricePerUnit()
    {
        return $this->hasPricePerUnit;
    }

    /**
     * Sets a new hasPricePerUnit
     *
     * Mark as "Y" if your item is any of the following: food used for human or domestic animal consumption; ingredients added to food; napkins; tissues; toilet paper; foil, plastic wrap, wax paper, parchment paper; paper towels; disposable plates, bowls, and cutlery; detergents, soaps, waxes, and other cleansing agents; non-prescription drugs, female hygeine products, and toiletries; automotive fluids and cleaners; rock salt; diapers, pullups and swimmers; fertilizer; kitty litter.
     *
     * @param string $hasPricePerUnit
     * @return self
     */
    public function setHasPricePerUnit($hasPricePerUnit)
    {
        $this->hasPricePerUnit = $hasPricePerUnit;
        return $this;
    }

    /**
     * Gets as pricePerUnitQuantity
     *
     * Enter the quantity of units for the item, based on the "PPU Unit of Measure" you selected. For example, a gallon of milk should be 128. NOTE: Do not enter the price.
     *
     * @return float
     */
    public function getPricePerUnitQuantity()
    {
        return $this->pricePerUnitQuantity;
    }

    /**
     * Sets a new pricePerUnitQuantity
     *
     * Enter the quantity of units for the item, based on the "PPU Unit of Measure" you selected. For example, a gallon of milk should be 128. NOTE: Do not enter the price.
     *
     * @param float $pricePerUnitQuantity
     * @return self
     */
    public function setPricePerUnitQuantity($pricePerUnitQuantity)
    {
        $this->pricePerUnitQuantity = $pricePerUnitQuantity;
        return $this;
    }

    /**
     * Gets as pricePerUnitUom
     *
     * The units that will be used to calculate the "Price Per Unit" for your product. For example, a gallon of milk has a "PPU Unit of Measure" of Fluid Ounces. NOTE: This may not be the Unit of Measure on the label.
     *
     * @return string
     */
    public function getPricePerUnitUom()
    {
        return $this->pricePerUnitUom;
    }

    /**
     * Sets a new pricePerUnitUom
     *
     * The units that will be used to calculate the "Price Per Unit" for your product. For example, a gallon of milk has a "PPU Unit of Measure" of Fluid Ounces. NOTE: This may not be the Unit of Measure on the label.
     *
     * @param string $pricePerUnitUom
     * @return self
     */
    public function setPricePerUnitUom($pricePerUnitUom)
    {
        $this->pricePerUnitUom = $pricePerUnitUom;
        return $this;
    }

    /**
     * Gets as hasExpiration
     *
     * Select Yes if product is labeled with any type of expiration or code date that indicates when product should no longer be consumed or no longer at best quality (e.g. Best If Used By, Best By, Use By, etc. ). Some examples of items with expiration dates include food, cleaning supplies, beauty products, etc.
     *
     * @return string
     */
    public function getHasExpiration()
    {
        return $this->hasExpiration;
    }

    /**
     * Sets a new hasExpiration
     *
     * Select Yes if product is labeled with any type of expiration or code date that indicates when product should no longer be consumed or no longer at best quality (e.g. Best If Used By, Best By, Use By, etc. ). Some examples of items with expiration dates include food, cleaning supplies, beauty products, etc.
     *
     * @param string $hasExpiration
     * @return self
     */
    public function setHasExpiration($hasExpiration)
    {
        $this->hasExpiration = $hasExpiration;
        return $this;
    }

    /**
     * Gets as shelfLife
     *
     * The length of time that the product can be stored without becoming unfit for consumption or after which the product is no longer at best quality, measured in days.
     *
     * @return \WalmartDSV\BabyFoodType\ShelfLifeAType
     */
    public function getShelfLife()
    {
        return $this->shelfLife;
    }

    /**
     * Sets a new shelfLife
     *
     * The length of time that the product can be stored without becoming unfit for consumption or after which the product is no longer at best quality, measured in days.
     *
     * @param \WalmartDSV\BabyFoodType\ShelfLifeAType $shelfLife
     * @return self
     */
    public function setShelfLife(\WalmartDSV\BabyFoodType\ShelfLifeAType $shelfLife)
    {
        $this->shelfLife = $shelfLife;
        return $this;
    }

    /**
     * Gets as isNutritionFactsLabelRequired
     *
     * Indicates if item requires nutritional facts labeling per FDA guidelines. If yes, please provide the following elements in one or more images 1) The Nutrition Facts and 2) Ingredients. Both attributes are required. If both elements are contained in one image, you may repeat the URL in both attributes.
     *
     * @return string
     */
    public function getIsNutritionFactsLabelRequired()
    {
        return $this->isNutritionFactsLabelRequired;
    }

    /**
     * Sets a new isNutritionFactsLabelRequired
     *
     * Indicates if item requires nutritional facts labeling per FDA guidelines. If yes, please provide the following elements in one or more images 1) The Nutrition Facts and 2) Ingredients. Both attributes are required. If both elements are contained in one image, you may repeat the URL in both attributes.
     *
     * @param string $isNutritionFactsLabelRequired
     * @return self
     */
    public function setIsNutritionFactsLabelRequired($isNutritionFactsLabelRequired)
    {
        $this->isNutritionFactsLabelRequired = $isNutritionFactsLabelRequired;
        return $this;
    }

    /**
     * Gets as nutritionFactsLabel
     *
     * Image URL of the nutritional facts label. Provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB.
     *
     * @return string
     */
    public function getNutritionFactsLabel()
    {
        return $this->nutritionFactsLabel;
    }

    /**
     * Sets a new nutritionFactsLabel
     *
     * Image URL of the nutritional facts label. Provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB.
     *
     * @param string $nutritionFactsLabel
     * @return self
     */
    public function setNutritionFactsLabel($nutritionFactsLabel)
    {
        $this->nutritionFactsLabel = $nutritionFactsLabel;
        return $this;
    }

    /**
     * Gets as nutritionIngredientsImage
     *
     * URL of image. Provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @return string
     */
    public function getNutritionIngredientsImage()
    {
        return $this->nutritionIngredientsImage;
    }

    /**
     * Sets a new nutritionIngredientsImage
     *
     * URL of image. Provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB. If the Ingredients have been included in another image, you may repeat the URL here.
     *
     * @param string $nutritionIngredientsImage
     * @return self
     */
    public function setNutritionIngredientsImage($nutritionIngredientsImage)
    {
        $this->nutritionIngredientsImage = $nutritionIngredientsImage;
        return $this;
    }

    /**
     * Gets as hasIngredientList
     *
     * Does your product have a list of ingredients OTHER than that provided with Drug Facts, Nutrition Facts, or Supplement Facts? If so, please provide EITHER the ingredients text or the URL to the image.
     *
     * @return string
     */
    public function getHasIngredientList()
    {
        return $this->hasIngredientList;
    }

    /**
     * Sets a new hasIngredientList
     *
     * Does your product have a list of ingredients OTHER than that provided with Drug Facts, Nutrition Facts, or Supplement Facts? If so, please provide EITHER the ingredients text or the URL to the image.
     *
     * @param string $hasIngredientList
     * @return self
     */
    public function setHasIngredientList($hasIngredientList)
    {
        $this->hasIngredientList = $hasIngredientList;
        return $this;
    }

    /**
     * Gets as ingredientListImage
     *
     * If your product contains a list of ingredients OTHER than that required with drug, supplement, or nutrition info, provide the URL of image. Provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB.
     *
     * @return string
     */
    public function getIngredientListImage()
    {
        return $this->ingredientListImage;
    }

    /**
     * Sets a new ingredientListImage
     *
     * If your product contains a list of ingredients OTHER than that required with drug, supplement, or nutrition info, provide the URL of image. Provide the final destination image URL (no-redirects) that is publicly accessible (no username/password required) and ends in a proper image extension. Recommended file type: JPEG Accepted file types: JPG, PNG, GIF, BMP Maximum file size: 2 GB.
     *
     * @param string $ingredientListImage
     * @return self
     */
    public function setIngredientListImage($ingredientListImage)
    {
        $this->ingredientListImage = $ingredientListImage;
        return $this;
    }

    /**
     * Gets as ingredients
     *
     * The list of all ingredients contained in an item, as found on the product label mandated by FDA guidelines. The ingredients should be listed in descending order by weight. The label must list the names of any FDA-certified color additives, but some ingredients can be listed collectively as flavors, spices, artificial flavoring or artificial colors. Refer to the FDA Food Labeling Guide for more guidelines.
     *
     * @return string
     */
    public function getIngredients()
    {
        return $this->ingredients;
    }

    /**
     * Sets a new ingredients
     *
     * The list of all ingredients contained in an item, as found on the product label mandated by FDA guidelines. The ingredients should be listed in descending order by weight. The label must list the names of any FDA-certified color additives, but some ingredients can be listed collectively as flavors, spices, artificial flavoring or artificial colors. Refer to the FDA Food Labeling Guide for more guidelines.
     *
     * @param string $ingredients
     * @return self
     */
    public function setIngredients($ingredients)
    {
        $this->ingredients = $ingredients;
        return $this;
    }

    /**
     * Gets as isTemperatureSensitive
     *
     * Indicates that the item is prone to freezing or melting that may adversely affect it.
     *
     * @return string
     */
    public function getIsTemperatureSensitive()
    {
        return $this->isTemperatureSensitive;
    }

    /**
     * Sets a new isTemperatureSensitive
     *
     * Indicates that the item is prone to freezing or melting that may adversely affect it.
     *
     * @param string $isTemperatureSensitive
     * @return self
     */
    public function setIsTemperatureSensitive($isTemperatureSensitive)
    {
        $this->isTemperatureSensitive = $isTemperatureSensitive;
        return $this;
    }

    /**
     * Gets as hasGMOs
     *
     * Does your product contain Genetically Modified Organisms (GMOS) whose DNA has been altered using genetic engineering techniques?
     *
     * @return string
     */
    public function getHasGMOs()
    {
        return $this->hasGMOs;
    }

    /**
     * Sets a new hasGMOs
     *
     * Does your product contain Genetically Modified Organisms (GMOS) whose DNA has been altered using genetic engineering techniques?
     *
     * @param string $hasGMOs
     * @return self
     */
    public function setHasGMOs($hasGMOs)
    {
        $this->hasGMOs = $hasGMOs;
        return $this;
    }

    /**
     * Gets as hasStateRestrictions
     *
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @return string
     */
    public function getHasStateRestrictions()
    {
        return $this->hasStateRestrictions;
    }

    /**
     * Sets a new hasStateRestrictions
     *
     * Select Y if your product needs to be prohibited from sale in any State or Zip Codes. Please note that it is your obligation to understand and inform Walmart of any laws, regulations, ordinances, etc. that would prohibit or restrict your product from being sold in a specific State or Zip Code. Examples: To comply with California energy efficiency requirements, certain lamps sold to California customers must include an energy efficient CFL or LED bulb with the product; if the product is not sold with an energy efficient light bulb, then the product must be restricted for sale in California. Additional State restrictions may be required if the products do not meet California or Colorado’s water efficiency requirements for products such as faucets and shower heads.
     *
     * @param string $hasStateRestrictions
     * @return self
     */
    public function setHasStateRestrictions($hasStateRestrictions)
    {
        $this->hasStateRestrictions = $hasStateRestrictions;
        return $this;
    }

    /**
     * Adds as stateRestriction
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param \WalmartDSV\StateRestrictionType $stateRestriction
     *@return self
     */
    public function addToStateRestrictions(\WalmartDSV\StateRestrictionType $stateRestriction)
    {
        $this->stateRestrictions[] = $stateRestriction;
        return $this;
    }

    /**
     * isset stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetStateRestrictions($index)
    {
        return isset($this->stateRestrictions[$index]);
    }

    /**
     * unset stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetStateRestrictions($index)
    {
        unset($this->stateRestrictions[$index]);
    }

    /**
     * Gets as stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @return \WalmartDSV\StateRestrictionType[]
     */
    public function getStateRestrictions()
    {
        return $this->stateRestrictions;
    }

    /**
     * Sets a new stateRestrictions
     *
     * Group of attributes to capture information when there are restrictions (legal or supplier discretion) prohibiting an item's sale.
     *
     * @param \WalmartDSV\StateRestrictionType[] $stateRestrictions
     * @return self
     */
    public function setStateRestrictions(array $stateRestrictions)
    {
        $this->stateRestrictions = $stateRestrictions;
        return $this;
    }

    /**
     * Gets as servingSize
     *
     * Measurement value specifying the amount of the item typically used as a reference on the label of that item to list per serving information (nutrients, calories, total fat). Applicable for a wide variety of products including food, beverages, and nutritional supplements.
     *
     * @return string
     */
    public function getServingSize()
    {
        return $this->servingSize;
    }

    /**
     * Sets a new servingSize
     *
     * Measurement value specifying the amount of the item typically used as a reference on the label of that item to list per serving information (nutrients, calories, total fat). Applicable for a wide variety of products including food, beverages, and nutritional supplements.
     *
     * @param string $servingSize
     * @return self
     */
    public function setServingSize($servingSize)
    {
        $this->servingSize = $servingSize;
        return $this;
    }

    /**
     * Gets as servingsPerContainer
     *
     * Number of servings contained in the item's package (box, bottle, bag).
     *
     * @return float
     */
    public function getServingsPerContainer()
    {
        return $this->servingsPerContainer;
    }

    /**
     * Sets a new servingsPerContainer
     *
     * Number of servings contained in the item's package (box, bottle, bag).
     *
     * @param float $servingsPerContainer
     * @return self
     */
    public function setServingsPerContainer($servingsPerContainer)
    {
        $this->servingsPerContainer = $servingsPerContainer;
        return $this;
    }

    /**
     * Gets as calories
     *
     * Number of calories contained in one serving, as found on the food label.
     *
     * @return \WalmartDSV\BabyFoodType\CaloriesAType
     */
    public function getCalories()
    {
        return $this->calories;
    }

    /**
     * Sets a new calories
     *
     * Number of calories contained in one serving, as found on the food label.
     *
     * @param \WalmartDSV\BabyFoodType\CaloriesAType $calories
     * @return self
     */
    public function setCalories(\WalmartDSV\BabyFoodType\CaloriesAType $calories)
    {
        $this->calories = $calories;
        return $this;
    }

    /**
     * Gets as caloriesFromFat
     *
     * Number of calories derived from fat, as found on the food label.
     *
     * @return \WalmartDSV\BabyFoodType\CaloriesFromFatAType
     */
    public function getCaloriesFromFat()
    {
        return $this->caloriesFromFat;
    }

    /**
     * Sets a new caloriesFromFat
     *
     * Number of calories derived from fat, as found on the food label.
     *
     * @param \WalmartDSV\BabyFoodType\CaloriesFromFatAType $caloriesFromFat
     * @return self
     */
    public function setCaloriesFromFat(\WalmartDSV\BabyFoodType\CaloriesFromFatAType $caloriesFromFat)
    {
        $this->caloriesFromFat = $caloriesFromFat;
        return $this;
    }

    /**
     * Gets as totalFat
     *
     * Total number of fat calories per serving, expressed in grams, milligrams, or less than a certain number of grams.
     *
     * @return \WalmartDSV\BabyFoodType\TotalFatAType
     */
    public function getTotalFat()
    {
        return $this->totalFat;
    }

    /**
     * Sets a new totalFat
     *
     * Total number of fat calories per serving, expressed in grams, milligrams, or less than a certain number of grams.
     *
     * @param \WalmartDSV\BabyFoodType\TotalFatAType $totalFat
     * @return self
     */
    public function setTotalFat(\WalmartDSV\BabyFoodType\TotalFatAType $totalFat)
    {
        $this->totalFat = $totalFat;
        return $this;
    }

    /**
     * Gets as totalFatPercentageDailyValue
     *
     * Percent daily value of fat per serving, according to the food label.
     *
     * @return float
     */
    public function getTotalFatPercentageDailyValue()
    {
        return $this->totalFatPercentageDailyValue;
    }

    /**
     * Sets a new totalFatPercentageDailyValue
     *
     * Percent daily value of fat per serving, according to the food label.
     *
     * @param float $totalFatPercentageDailyValue
     * @return self
     */
    public function setTotalFatPercentageDailyValue($totalFatPercentageDailyValue)
    {
        $this->totalFatPercentageDailyValue = $totalFatPercentageDailyValue;
        return $this;
    }

    /**
     * Gets as fatCaloriesPerGram
     *
     * The number of calories in one gram of fat, as may be provided in Nutritional Facts according to FDA guidelines.
     *
     * @return \WalmartDSV\BabyFoodType\FatCaloriesPerGramAType
     */
    public function getFatCaloriesPerGram()
    {
        return $this->fatCaloriesPerGram;
    }

    /**
     * Sets a new fatCaloriesPerGram
     *
     * The number of calories in one gram of fat, as may be provided in Nutritional Facts according to FDA guidelines.
     *
     * @param \WalmartDSV\BabyFoodType\FatCaloriesPerGramAType $fatCaloriesPerGram
     * @return self
     */
    public function setFatCaloriesPerGram(\WalmartDSV\BabyFoodType\FatCaloriesPerGramAType $fatCaloriesPerGram)
    {
        $this->fatCaloriesPerGram = $fatCaloriesPerGram;
        return $this;
    }

    /**
     * Gets as totalCarbohydrate
     *
     * Total number of carbohydrates per serving, expressed in grams or less than a certain number of grams.
     *
     * @return \WalmartDSV\BabyFoodType\TotalCarbohydrateAType
     */
    public function getTotalCarbohydrate()
    {
        return $this->totalCarbohydrate;
    }

    /**
     * Sets a new totalCarbohydrate
     *
     * Total number of carbohydrates per serving, expressed in grams or less than a certain number of grams.
     *
     * @param \WalmartDSV\BabyFoodType\TotalCarbohydrateAType $totalCarbohydrate
     * @return self
     */
    public function setTotalCarbohydrate(\WalmartDSV\BabyFoodType\TotalCarbohydrateAType $totalCarbohydrate)
    {
        $this->totalCarbohydrate = $totalCarbohydrate;
        return $this;
    }

    /**
     * Gets as totalCarbohydratePercentageDailyValue
     *
     * Percent daily value of carbohydrates per serving.
     *
     * @return float
     */
    public function getTotalCarbohydratePercentageDailyValue()
    {
        return $this->totalCarbohydratePercentageDailyValue;
    }

    /**
     * Sets a new totalCarbohydratePercentageDailyValue
     *
     * Percent daily value of carbohydrates per serving.
     *
     * @param float $totalCarbohydratePercentageDailyValue
     * @return self
     */
    public function setTotalCarbohydratePercentageDailyValue($totalCarbohydratePercentageDailyValue)
    {
        $this->totalCarbohydratePercentageDailyValue = $totalCarbohydratePercentageDailyValue;
        return $this;
    }

    /**
     * Gets as carbohydrateCaloriesPerGram
     *
     * The number of calories in one gram of carbohydrates, as may be provided in Nutritional Facts according to FDA guidelines.
     *
     * @return \WalmartDSV\BabyFoodType\CarbohydrateCaloriesPerGramAType
     */
    public function getCarbohydrateCaloriesPerGram()
    {
        return $this->carbohydrateCaloriesPerGram;
    }

    /**
     * Sets a new carbohydrateCaloriesPerGram
     *
     * The number of calories in one gram of carbohydrates, as may be provided in Nutritional Facts according to FDA guidelines.
     *
     * @param \WalmartDSV\BabyFoodType\CarbohydrateCaloriesPerGramAType $carbohydrateCaloriesPerGram
     * @return self
     */
    public function setCarbohydrateCaloriesPerGram(\WalmartDSV\BabyFoodType\CarbohydrateCaloriesPerGramAType $carbohydrateCaloriesPerGram)
    {
        $this->carbohydrateCaloriesPerGram = $carbohydrateCaloriesPerGram;
        return $this;
    }

    /**
     * Adds as nutrient
     *
     * Additional nutrients, not including total fat or total carbohydrates, which should be entered in "Total Fat" and "Total Carbohydrate" respectively.
     *
     * @param \WalmartDSV\NutrientType $nutrient
     *@return self
     */
    public function addToNutrients(\WalmartDSV\NutrientType $nutrient)
    {
        $this->nutrients[] = $nutrient;
        return $this;
    }

    /**
     * isset nutrients
     *
     * Additional nutrients, not including total fat or total carbohydrates, which should be entered in "Total Fat" and "Total Carbohydrate" respectively.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetNutrients($index)
    {
        return isset($this->nutrients[$index]);
    }

    /**
     * unset nutrients
     *
     * Additional nutrients, not including total fat or total carbohydrates, which should be entered in "Total Fat" and "Total Carbohydrate" respectively.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetNutrients($index)
    {
        unset($this->nutrients[$index]);
    }

    /**
     * Gets as nutrients
     *
     * Additional nutrients, not including total fat or total carbohydrates, which should be entered in "Total Fat" and "Total Carbohydrate" respectively.
     *
     * @return \WalmartDSV\NutrientType[]
     */
    public function getNutrients()
    {
        return $this->nutrients;
    }

    /**
     * Sets a new nutrients
     *
     * Additional nutrients, not including total fat or total carbohydrates, which should be entered in "Total Fat" and "Total Carbohydrate" respectively.
     *
     * @param \WalmartDSV\NutrientType[] $nutrients
     * @return self
     */
    public function setNutrients(array $nutrients)
    {
        $this->nutrients = $nutrients;
        return $this;
    }

    /**
     * Gets as proteinCaloriesPerGram
     *
     * The number of calories in one gram of protein, as may be provided in Nutritional Facts according to FDA guidelines.
     *
     * @return \WalmartDSV\BabyFoodType\ProteinCaloriesPerGramAType
     */
    public function getProteinCaloriesPerGram()
    {
        return $this->proteinCaloriesPerGram;
    }

    /**
     * Sets a new proteinCaloriesPerGram
     *
     * The number of calories in one gram of protein, as may be provided in Nutritional Facts according to FDA guidelines.
     *
     * @param \WalmartDSV\BabyFoodType\ProteinCaloriesPerGramAType $proteinCaloriesPerGram
     * @return self
     */
    public function setProteinCaloriesPerGram(\WalmartDSV\BabyFoodType\ProteinCaloriesPerGramAType $proteinCaloriesPerGram)
    {
        $this->proteinCaloriesPerGram = $proteinCaloriesPerGram;
        return $this;
    }

    /**
     * Gets as totalProteinPercentageDailyValue
     *
     * Percent daily value of protein per serving.
     *
     * @return float
     */
    public function getTotalProteinPercentageDailyValue()
    {
        return $this->totalProteinPercentageDailyValue;
    }

    /**
     * Sets a new totalProteinPercentageDailyValue
     *
     * Percent daily value of protein per serving.
     *
     * @param float $totalProteinPercentageDailyValue
     * @return self
     */
    public function setTotalProteinPercentageDailyValue($totalProteinPercentageDailyValue)
    {
        $this->totalProteinPercentageDailyValue = $totalProteinPercentageDailyValue;
        return $this;
    }

    /**
     * Gets as totalProtein
     *
     * Total protein per serving, expressed in grams, milligrams, or less than.
     *
     * @return \WalmartDSV\BabyFoodType\TotalProteinAType
     */
    public function getTotalProtein()
    {
        return $this->totalProtein;
    }

    /**
     * Sets a new totalProtein
     *
     * Total protein per serving, expressed in grams, milligrams, or less than.
     *
     * @param \WalmartDSV\BabyFoodType\TotalProteinAType $totalProtein
     * @return self
     */
    public function setTotalProtein(\WalmartDSV\BabyFoodType\TotalProteinAType $totalProtein)
    {
        $this->totalProtein = $totalProtein;
        return $this;
    }

    /**
     * Gets as foodForm
     *
     * Describes the form of the food if the food is sold in a variety of forms, such as sliced and unsliced, whole or halves, etc.
     *
     * @return string
     */
    public function getFoodForm()
    {
        return $this->foodForm;
    }

    /**
     * Sets a new foodForm
     *
     * Describes the form of the food if the food is sold in a variety of forms, such as sliced and unsliced, whole or halves, etc.
     *
     * @param string $foodForm
     * @return self
     */
    public function setFoodForm($foodForm)
    {
        $this->foodForm = $foodForm;
        return $this;
    }

    /**
     * Adds as containerTypeValue
     *
     * The kind of physical package or receptacle that contains the product as presented to the consumer. Also used to describe storage items. Consumers may select different products based on their container preferences. For example, a parent may select juice packaged in boxes rather than bottles for use in childrens' lunch boxes.
     *
     * @return self
     * @param string $containerTypeValue
     */
    public function addToContainerType($containerTypeValue)
    {
        $this->containerType[] = $containerTypeValue;
        return $this;
    }

    /**
     * isset containerType
     *
     * The kind of physical package or receptacle that contains the product as presented to the consumer. Also used to describe storage items. Consumers may select different products based on their container preferences. For example, a parent may select juice packaged in boxes rather than bottles for use in childrens' lunch boxes.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetContainerType($index)
    {
        return isset($this->containerType[$index]);
    }

    /**
     * unset containerType
     *
     * The kind of physical package or receptacle that contains the product as presented to the consumer. Also used to describe storage items. Consumers may select different products based on their container preferences. For example, a parent may select juice packaged in boxes rather than bottles for use in childrens' lunch boxes.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetContainerType($index)
    {
        unset($this->containerType[$index]);
    }

    /**
     * Gets as containerType
     *
     * The kind of physical package or receptacle that contains the product as presented to the consumer. Also used to describe storage items. Consumers may select different products based on their container preferences. For example, a parent may select juice packaged in boxes rather than bottles for use in childrens' lunch boxes.
     *
     * @return string[]
     */
    public function getContainerType()
    {
        return $this->containerType;
    }

    /**
     * Sets a new containerType
     *
     * The kind of physical package or receptacle that contains the product as presented to the consumer. Also used to describe storage items. Consumers may select different products based on their container preferences. For example, a parent may select juice packaged in boxes rather than bottles for use in childrens' lunch boxes.
     *
     * @param string $containerType
     * @return self
     */
    public function setContainerType(array $containerType)
    {
        $this->containerType = $containerType;
        return $this;
    }

    /**
     * Gets as isImitation
     *
     * Generally a new food that resembles a traditional food and is a substitute for the traditional food must be labeled as an imitation, especially if the new food contains less protein or a lesser amount of any essential vitamin or mineral.
     *
     * @return string
     */
    public function getIsImitation()
    {
        return $this->isImitation;
    }

    /**
     * Sets a new isImitation
     *
     * Generally a new food that resembles a traditional food and is a substitute for the traditional food must be labeled as an imitation, especially if the new food contains less protein or a lesser amount of any essential vitamin or mineral.
     *
     * @param string $isImitation
     * @return self
     */
    public function setIsImitation($isImitation)
    {
        $this->isImitation = $isImitation;
        return $this;
    }

    /**
     * Gets as usdaInspected
     *
     * Has your product been inspected by the United States Department of Agriculture? There are a number of food products that are under the jurisdiction of the Food Safety and Inspection Service (FSIS), and are thus subject to inspection, including: egg products (liquid, frozen or dried), meat, and poultry. Refer to the FDA / USDA Food Standards and Labeling Policy Book for exceptions and more detailed guidelines.
     *
     * @return string
     */
    public function getUsdaInspected()
    {
        return $this->usdaInspected;
    }

    /**
     * Sets a new usdaInspected
     *
     * Has your product been inspected by the United States Department of Agriculture? There are a number of food products that are under the jurisdiction of the Food Safety and Inspection Service (FSIS), and are thus subject to inspection, including: egg products (liquid, frozen or dried), meat, and poultry. Refer to the FDA / USDA Food Standards and Labeling Policy Book for exceptions and more detailed guidelines.
     *
     * @param string $usdaInspected
     * @return self
     */
    public function setUsdaInspected($usdaInspected)
    {
        $this->usdaInspected = $usdaInspected;
        return $this;
    }

    /**
     * Gets as hasHighFructoseCornSyrup
     *
     * Select "Y" if High Fructose Corn Syrup is in the ingredient list.
     *
     * @return string
     */
    public function getHasHighFructoseCornSyrup()
    {
        return $this->hasHighFructoseCornSyrup;
    }

    /**
     * Sets a new hasHighFructoseCornSyrup
     *
     * Select "Y" if High Fructose Corn Syrup is in the ingredient list.
     *
     * @param string $hasHighFructoseCornSyrup
     * @return self
     */
    public function setHasHighFructoseCornSyrup($hasHighFructoseCornSyrup)
    {
        $this->hasHighFructoseCornSyrup = $hasHighFructoseCornSyrup;
        return $this;
    }

    /**
     * Gets as fluidOuncesSupplying100Calories
     *
     * Statement of the number of fluid ounces required to supply 100 calories, for use with baby formula only.
     *
     * @return \WalmartDSV\BabyFoodType\FluidOuncesSupplying100CaloriesAType
     */
    public function getFluidOuncesSupplying100Calories()
    {
        return $this->fluidOuncesSupplying100Calories;
    }

    /**
     * Sets a new fluidOuncesSupplying100Calories
     *
     * Statement of the number of fluid ounces required to supply 100 calories, for use with baby formula only.
     *
     * @param \WalmartDSV\BabyFoodType\FluidOuncesSupplying100CaloriesAType $fluidOuncesSupplying100Calories
     * @return self
     */
    public function setFluidOuncesSupplying100Calories(\WalmartDSV\BabyFoodType\FluidOuncesSupplying100CaloriesAType $fluidOuncesSupplying100Calories)
    {
        $this->fluidOuncesSupplying100Calories = $fluidOuncesSupplying100Calories;
        return $this;
    }

    /**
     * Adds as foodAllergenStatement
     *
     * Statement regarding any ingredients that may be food allergens, often written as "Contains X" or "Manufactured in a facility which processes Y."
     *
     * @return self
     * @param string $foodAllergenStatement
     */
    public function addToFoodAllergenStatements($foodAllergenStatement)
    {
        $this->foodAllergenStatements[] = $foodAllergenStatement;
        return $this;
    }

    /**
     * isset foodAllergenStatements
     *
     * Statement regarding any ingredients that may be food allergens, often written as "Contains X" or "Manufactured in a facility which processes Y."
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFoodAllergenStatements($index)
    {
        return isset($this->foodAllergenStatements[$index]);
    }

    /**
     * unset foodAllergenStatements
     *
     * Statement regarding any ingredients that may be food allergens, often written as "Contains X" or "Manufactured in a facility which processes Y."
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFoodAllergenStatements($index)
    {
        unset($this->foodAllergenStatements[$index]);
    }

    /**
     * Gets as foodAllergenStatements
     *
     * Statement regarding any ingredients that may be food allergens, often written as "Contains X" or "Manufactured in a facility which processes Y."
     *
     * @return string[]
     */
    public function getFoodAllergenStatements()
    {
        return $this->foodAllergenStatements;
    }

    /**
     * Sets a new foodAllergenStatements
     *
     * Statement regarding any ingredients that may be food allergens, often written as "Contains X" or "Manufactured in a facility which processes Y."
     *
     * @param string $foodAllergenStatements
     * @return self
     */
    public function setFoodAllergenStatements(array $foodAllergenStatements)
    {
        $this->foodAllergenStatements = $foodAllergenStatements;
        return $this;
    }

    /**
     * Gets as babyFoodPackaging
     *
     * Description of the type of packaging for baby food product. This is used as a sorting category when visitors search for baby food on Walmart.com. It is recommended to use example values for searchability. If your product falls outside those values, then you may enter another appropriate value.
     *
     * @return string
     */
    public function getBabyFoodPackaging()
    {
        return $this->babyFoodPackaging;
    }

    /**
     * Sets a new babyFoodPackaging
     *
     * Description of the type of packaging for baby food product. This is used as a sorting category when visitors search for baby food on Walmart.com. It is recommended to use example values for searchability. If your product falls outside those values, then you may enter another appropriate value.
     *
     * @param string $babyFoodPackaging
     * @return self
     */
    public function setBabyFoodPackaging($babyFoodPackaging)
    {
        $this->babyFoodPackaging = $babyFoodPackaging;
        return $this;
    }

    /**
     * Adds as babyFormulaStageValue
     *
     * If the baby formula states that it is for a particular life stage, enter it here.
     *
     * @return self
     * @param string $babyFormulaStageValue
     */
    public function addToBabyFormulaStage($babyFormulaStageValue)
    {
        $this->babyFormulaStage[] = $babyFormulaStageValue;
        return $this;
    }

    /**
     * isset babyFormulaStage
     *
     * If the baby formula states that it is for a particular life stage, enter it here.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetBabyFormulaStage($index)
    {
        return isset($this->babyFormulaStage[$index]);
    }

    /**
     * unset babyFormulaStage
     *
     * If the baby formula states that it is for a particular life stage, enter it here.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetBabyFormulaStage($index)
    {
        unset($this->babyFormulaStage[$index]);
    }

    /**
     * Gets as babyFormulaStage
     *
     * If the baby formula states that it is for a particular life stage, enter it here.
     *
     * @return string[]
     */
    public function getBabyFormulaStage()
    {
        return $this->babyFormulaStage;
    }

    /**
     * Sets a new babyFormulaStage
     *
     * If the baby formula states that it is for a particular life stage, enter it here.
     *
     * @param string $babyFormulaStage
     * @return self
     */
    public function setBabyFormulaStage(array $babyFormulaStage)
    {
        $this->babyFormulaStage = $babyFormulaStage;
        return $this;
    }

    /**
     * Adds as babyFoodStageValue
     *
     * Attribute designed to capture the generalized age grouping (commonly referred to as "stage") of a given baby food, as specified by manufacturer. Specific description of each stage varies with baby food producer. Example descriptions: Stage 1 - Foods have a single ingredient and are pureed and generally contain about 2.5 oz of fruits, veggies or meats. Stage 2 – Foods are strained instead of pureed and have a combination of fruits or veggies instead of single ingredients, contain larger portions. Stage 3 – Foods are mashed and have more texture than the pureed foods and may have bits and chunks of meats or veggies.
     *
     * @return self
     * @param string $babyFoodStageValue
     */
    public function addToBabyFoodStage($babyFoodStageValue)
    {
        $this->babyFoodStage[] = $babyFoodStageValue;
        return $this;
    }

    /**
     * isset babyFoodStage
     *
     * Attribute designed to capture the generalized age grouping (commonly referred to as "stage") of a given baby food, as specified by manufacturer. Specific description of each stage varies with baby food producer. Example descriptions: Stage 1 - Foods have a single ingredient and are pureed and generally contain about 2.5 oz of fruits, veggies or meats. Stage 2 – Foods are strained instead of pureed and have a combination of fruits or veggies instead of single ingredients, contain larger portions. Stage 3 – Foods are mashed and have more texture than the pureed foods and may have bits and chunks of meats or veggies.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetBabyFoodStage($index)
    {
        return isset($this->babyFoodStage[$index]);
    }

    /**
     * unset babyFoodStage
     *
     * Attribute designed to capture the generalized age grouping (commonly referred to as "stage") of a given baby food, as specified by manufacturer. Specific description of each stage varies with baby food producer. Example descriptions: Stage 1 - Foods have a single ingredient and are pureed and generally contain about 2.5 oz of fruits, veggies or meats. Stage 2 – Foods are strained instead of pureed and have a combination of fruits or veggies instead of single ingredients, contain larger portions. Stage 3 – Foods are mashed and have more texture than the pureed foods and may have bits and chunks of meats or veggies.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetBabyFoodStage($index)
    {
        unset($this->babyFoodStage[$index]);
    }

    /**
     * Gets as babyFoodStage
     *
     * Attribute designed to capture the generalized age grouping (commonly referred to as "stage") of a given baby food, as specified by manufacturer. Specific description of each stage varies with baby food producer. Example descriptions: Stage 1 - Foods have a single ingredient and are pureed and generally contain about 2.5 oz of fruits, veggies or meats. Stage 2 – Foods are strained instead of pureed and have a combination of fruits or veggies instead of single ingredients, contain larger portions. Stage 3 – Foods are mashed and have more texture than the pureed foods and may have bits and chunks of meats or veggies.
     *
     * @return string[]
     */
    public function getBabyFoodStage()
    {
        return $this->babyFoodStage;
    }

    /**
     * Sets a new babyFoodStage
     *
     * Attribute designed to capture the generalized age grouping (commonly referred to as "stage") of a given baby food, as specified by manufacturer. Specific description of each stage varies with baby food producer. Example descriptions: Stage 1 - Foods have a single ingredient and are pureed and generally contain about 2.5 oz of fruits, veggies or meats. Stage 2 – Foods are strained instead of pureed and have a combination of fruits or veggies instead of single ingredients, contain larger portions. Stage 3 – Foods are mashed and have more texture than the pureed foods and may have bits and chunks of meats or veggies.
     *
     * @param string $babyFoodStage
     * @return self
     */
    public function setBabyFoodStage(array $babyFoodStage)
    {
        $this->babyFoodStage = $babyFoodStage;
        return $this;
    }

    /**
     * Gets as instructions
     *
     * Detailed information telling how the product should be operated or assembled.
     *
     * @return string
     */
    public function getInstructions()
    {
        return $this->instructions;
    }

    /**
     * Sets a new instructions
     *
     * Detailed information telling how the product should be operated or assembled.
     *
     * @param string $instructions
     * @return self
     */
    public function setInstructions($instructions)
    {
        $this->instructions = $instructions;
        return $this;
    }

    /**
     * Adds as feature
     *
     * List notable features of the item.
     *
     * @return self
     * @param string $feature
     */
    public function addToFeatures($feature)
    {
        $this->features[] = $feature;
        return $this;
    }

    /**
     * isset features
     *
     * List notable features of the item.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFeatures($index)
    {
        return isset($this->features[$index]);
    }

    /**
     * unset features
     *
     * List notable features of the item.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFeatures($index)
    {
        unset($this->features[$index]);
    }

    /**
     * Gets as features
     *
     * List notable features of the item.
     *
     * @return string[]
     */
    public function getFeatures()
    {
        return $this->features;
    }

    /**
     * Sets a new features
     *
     * List notable features of the item.
     *
     * @param string $features
     * @return self
     */
    public function setFeatures(array $features)
    {
        $this->features = $features;
        return $this;
    }

    /**
     * Gets as keywords
     *
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @return string
     */
    public function getKeywords()
    {
        return $this->keywords;
    }

    /**
     * Sets a new keywords
     *
     * Words that people would use to search for this item. Keywords can include synonyms and related terms.
     *
     * @param string $keywords
     * @return self
     */
    public function setKeywords($keywords)
    {
        $this->keywords = $keywords;
        return $this;
    }

    /**
     * Gets as safeHandlingInstructions
     *
     * Instructions for storage or preparation of potentially hazardous fresh food. NOTE: Required for raw meat products.
     *
     * @return string
     */
    public function getSafeHandlingInstructions()
    {
        return $this->safeHandlingInstructions;
    }

    /**
     * Sets a new safeHandlingInstructions
     *
     * Instructions for storage or preparation of potentially hazardous fresh food. NOTE: Required for raw meat products.
     *
     * @param string $safeHandlingInstructions
     * @return self
     */
    public function setSafeHandlingInstructions($safeHandlingInstructions)
    {
        $this->safeHandlingInstructions = $safeHandlingInstructions;
        return $this;
    }

    /**
     * Adds as cuisineValue
     *
     * Indicates the cooking style, especially regional or cultural cuisines, you might use the food to prepare.
     *
     * @return self
     * @param string $cuisineValue
     */
    public function addToCuisine($cuisineValue)
    {
        $this->cuisine[] = $cuisineValue;
        return $this;
    }

    /**
     * isset cuisine
     *
     * Indicates the cooking style, especially regional or cultural cuisines, you might use the food to prepare.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetCuisine($index)
    {
        return isset($this->cuisine[$index]);
    }

    /**
     * unset cuisine
     *
     * Indicates the cooking style, especially regional or cultural cuisines, you might use the food to prepare.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetCuisine($index)
    {
        unset($this->cuisine[$index]);
    }

    /**
     * Gets as cuisine
     *
     * Indicates the cooking style, especially regional or cultural cuisines, you might use the food to prepare.
     *
     * @return string[]
     */
    public function getCuisine()
    {
        return $this->cuisine;
    }

    /**
     * Sets a new cuisine
     *
     * Indicates the cooking style, especially regional or cultural cuisines, you might use the food to prepare.
     *
     * @param string $cuisine
     * @return self
     */
    public function setCuisine(array $cuisine)
    {
        $this->cuisine = $cuisine;
        return $this;
    }

    /**
     * Adds as foodPreparationTipsValue
     *
     * Helpful information related to food preparation.
     *
     * @return self
     * @param string $foodPreparationTipsValue
     */
    public function addToFoodPreparationTips($foodPreparationTipsValue)
    {
        $this->foodPreparationTips[] = $foodPreparationTipsValue;
        return $this;
    }

    /**
     * isset foodPreparationTips
     *
     * Helpful information related to food preparation.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFoodPreparationTips($index)
    {
        return isset($this->foodPreparationTips[$index]);
    }

    /**
     * unset foodPreparationTips
     *
     * Helpful information related to food preparation.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFoodPreparationTips($index)
    {
        unset($this->foodPreparationTips[$index]);
    }

    /**
     * Gets as foodPreparationTips
     *
     * Helpful information related to food preparation.
     *
     * @return string[]
     */
    public function getFoodPreparationTips()
    {
        return $this->foodPreparationTips;
    }

    /**
     * Sets a new foodPreparationTips
     *
     * Helpful information related to food preparation.
     *
     * @param string $foodPreparationTips
     * @return self
     */
    public function setFoodPreparationTips(array $foodPreparationTips)
    {
        $this->foodPreparationTips = $foodPreparationTips;
        return $this;
    }

    /**
     * Adds as foodStorageTipsValue
     *
     * Helpful information related to food storage.
     *
     * @return self
     * @param string $foodStorageTipsValue
     */
    public function addToFoodStorageTips($foodStorageTipsValue)
    {
        $this->foodStorageTips[] = $foodStorageTipsValue;
        return $this;
    }

    /**
     * isset foodStorageTips
     *
     * Helpful information related to food storage.
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFoodStorageTips($index)
    {
        return isset($this->foodStorageTips[$index]);
    }

    /**
     * unset foodStorageTips
     *
     * Helpful information related to food storage.
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFoodStorageTips($index)
    {
        unset($this->foodStorageTips[$index]);
    }

    /**
     * Gets as foodStorageTips
     *
     * Helpful information related to food storage.
     *
     * @return string[]
     */
    public function getFoodStorageTips()
    {
        return $this->foodStorageTips;
    }

    /**
     * Sets a new foodStorageTips
     *
     * Helpful information related to food storage.
     *
     * @param string $foodStorageTips
     * @return self
     */
    public function setFoodStorageTips(array $foodStorageTips)
    {
        $this->foodStorageTips = $foodStorageTips;
        return $this;
    }

    /**
     * Adds as swatchImage
     *
     * @param \WalmartDSV\BabyFoodType\SwatchImagesAType\SwatchImageAType $swatchImage
     *@return self
     */
    public function addToSwatchImages(\WalmartDSV\BabyFoodType\SwatchImagesAType\SwatchImageAType $swatchImage)
    {
        $this->swatchImages[] = $swatchImage;
        return $this;
    }

    /**
     * isset swatchImages
     *
     * @param int|string $index
     * @return bool
     */
    public function issetSwatchImages($index)
    {
        return isset($this->swatchImages[$index]);
    }

    /**
     * unset swatchImages
     *
     * @param int|string $index
     * @return void
     */
    public function unsetSwatchImages($index)
    {
        unset($this->swatchImages[$index]);
    }

    /**
     * Gets as swatchImages
     *
     * @return \WalmartDSV\BabyFoodType\SwatchImagesAType\SwatchImageAType[]
     */
    public function getSwatchImages()
    {
        return $this->swatchImages;
    }

    /**
     * Sets a new swatchImages
     *
     * @param \WalmartDSV\BabyFoodType\SwatchImagesAType\SwatchImageAType[] $swatchImages
     * @return self
     */
    public function setSwatchImages(array $swatchImages)
    {
        $this->swatchImages = $swatchImages;
        return $this;
    }


}

