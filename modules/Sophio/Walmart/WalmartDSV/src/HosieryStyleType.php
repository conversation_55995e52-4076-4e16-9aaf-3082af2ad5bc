<?php

namespace WalmartDSV;

/**
 * Class representing HosieryStyleType
 *
 * Styles/features specific to tights and hosiery.
 * XSD Type: HosieryStyle
 */
class HosieryStyleType
{

    /**
     * @var string $hosieryStyleValue
     */
    private $hosieryStyleValue = null;

    /**
     * Gets as hosieryStyleValue
     *
     * @return string
     */
    public function getHosieryStyleValue()
    {
        return $this->hosieryStyleValue;
    }

    /**
     * Sets a new hosieryStyleValue
     *
     * @param string $hosieryStyleValue
     * @return self
     */
    public function setHosieryStyleValue($hosieryStyleValue)
    {
        $this->hosieryStyleValue = $hosieryStyleValue;
        return $this;
    }


}

