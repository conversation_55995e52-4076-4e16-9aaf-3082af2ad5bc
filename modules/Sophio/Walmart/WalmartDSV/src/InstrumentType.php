<?php

namespace WalmartDSV;

/**
 * Class representing InstrumentType
 *
 * The name(s) of the musical instrument(s) or equipment this accessory is intended for/compatible with.
 * XSD Type: Instrument
 */
class InstrumentType
{

    /**
     * @var string[] $instrumentValue
     */
    private $instrumentValue = [
        
    ];

    /**
     * Adds as instrumentValue
     *
     * @return self
     * @param string $instrumentValue
     */
    public function addToInstrumentValue($instrumentValue)
    {
        $this->instrumentValue[] = $instrumentValue;
        return $this;
    }

    /**
     * isset instrumentValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetInstrumentValue($index)
    {
        return isset($this->instrumentValue[$index]);
    }

    /**
     * unset instrumentValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetInstrumentValue($index)
    {
        unset($this->instrumentValue[$index]);
    }

    /**
     * Gets as instrumentValue
     *
     * @return string[]
     */
    public function getInstrumentValue()
    {
        return $this->instrumentValue;
    }

    /**
     * Sets a new instrumentValue
     *
     * @param string $instrumentValue
     * @return self
     */
    public function setInstrumentValue(array $instrumentValue)
    {
        $this->instrumentValue = $instrumentValue;
        return $this;
    }


}

