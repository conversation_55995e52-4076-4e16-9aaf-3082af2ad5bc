<?php

namespace WalmartDSV;

/**
 * Class representing AgeRangeType
 *
 * Minimum and Maximum Ages for a product. Note: Both Min. and Max. attributes will be the same Unit of Measure: Months, or Years.
 * XSD Type: AgeRange
 */
class AgeRangeType
{

    /**
     * The minimum age someone should be to use the product - in months or years.
     *
     * @var int $rangeMinimum
     */
    private $rangeMinimum = null;

    /**
     * The maximum age someone can be to use the product, on average - in months or years.
     *
     * @var int $rangeMaximum
     */
    private $rangeMaximum = null;

    /**
     * @var string $unit
     */
    private $unit = null;

    /**
     * Gets as rangeMinimum
     *
     * The minimum age someone should be to use the product - in months or years.
     *
     * @return int
     */
    public function getRangeMinimum()
    {
        return $this->rangeMinimum;
    }

    /**
     * Sets a new rangeMinimum
     *
     * The minimum age someone should be to use the product - in months or years.
     *
     * @param int $rangeMinimum
     * @return self
     */
    public function setRangeMinimum($rangeMinimum)
    {
        $this->rangeMinimum = $rangeMinimum;
        return $this;
    }

    /**
     * Gets as rangeMaximum
     *
     * The maximum age someone can be to use the product, on average - in months or years.
     *
     * @return int
     */
    public function getRangeMaximum()
    {
        return $this->rangeMaximum;
    }

    /**
     * Sets a new rangeMaximum
     *
     * The maximum age someone can be to use the product, on average - in months or years.
     *
     * @param int $rangeMaximum
     * @return self
     */
    public function setRangeMaximum($rangeMaximum)
    {
        $this->rangeMaximum = $rangeMaximum;
        return $this;
    }

    /**
     * Gets as unit
     *
     * @return string
     */
    public function getUnit()
    {
        return $this->unit;
    }

    /**
     * Sets a new unit
     *
     * @param string $unit
     * @return self
     */
    public function setUnit($unit)
    {
        $this->unit = $unit;
        return $this;
    }


}

