<?php

namespace WalmartDSV;

/**
 * Class representing BrandLicenseType
 *
 * A brand name that is not owned by the product brand, but is licensed for the particular product. (Often character and media tie-ins and promotions.)
 * XSD Type: BrandLicense
 */
class BrandLicenseType
{

    /**
     * @var string[] $globalBrandLicenseValue
     */
    private $globalBrandLicenseValue = [
        
    ];

    /**
     * Adds as globalBrandLicenseValue
     *
     * @return self
     * @param string $globalBrandLicenseValue
     */
    public function addToGlobalBrandLicenseValue($globalBrandLicenseValue)
    {
        $this->globalBrandLicenseValue[] = $globalBrandLicenseValue;
        return $this;
    }

    /**
     * isset globalBrandLicenseValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetGlobalBrandLicenseValue($index)
    {
        return isset($this->globalBrandLicenseValue[$index]);
    }

    /**
     * unset globalBrandLicenseValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetGlobalBrandLicenseValue($index)
    {
        unset($this->globalBrandLicenseValue[$index]);
    }

    /**
     * Gets as globalBrandLicenseValue
     *
     * @return string[]
     */
    public function getGlobalBrandLicenseValue()
    {
        return $this->globalBrandLicenseValue;
    }

    /**
     * Sets a new globalBrandLicenseValue
     *
     * @param string $globalBrandLicenseValue
     * @return self
     */
    public function setGlobalBrandLicenseValue(array $globalBrandLicenseValue)
    {
        $this->globalBrandLicenseValue = $globalBrandLicenseValue;
        return $this;
    }


}

