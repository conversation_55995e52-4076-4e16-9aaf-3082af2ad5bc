<?php

namespace WalmartDSV;

/**
 * Class representing AuthorType
 *
 * The name (or pseudonym) of the person who wrote a book, as written on the cover and/or title page.
 * XSD Type: Author
 */
class AuthorType
{

    /**
     * @var string[] $authorValue
     */
    private $authorValue = [
        
    ];

    /**
     * Adds as authorValue
     *
     * @return self
     * @param string $authorValue
     */
    public function addToAuthorValue($authorValue)
    {
        $this->authorValue[] = $authorValue;
        return $this;
    }

    /**
     * isset authorValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAuthorValue($index)
    {
        return isset($this->authorValue[$index]);
    }

    /**
     * unset authorValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAuthorValue($index)
    {
        unset($this->authorValue[$index]);
    }

    /**
     * Gets as authorValue
     *
     * @return string[]
     */
    public function getAuthorValue()
    {
        return $this->authorValue;
    }

    /**
     * Sets a new authorValue
     *
     * @param string $authorValue
     * @return self
     */
    public function setAuthorValue(array $authorValue)
    {
        $this->authorValue = $authorValue;
        return $this;
    }


}

