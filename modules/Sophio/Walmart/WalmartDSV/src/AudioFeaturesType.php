<?php

namespace WalmartDSV;

/**
 * Class representing AudioFeaturesType
 *
 * Features intended to increase sound quality or fidelity.
 * XSD Type: AudioFeatures
 */
class AudioFeaturesType
{

    /**
     * @var string[] $audioFeature
     */
    private $audioFeature = [
        
    ];

    /**
     * Adds as audioFeature
     *
     * @return self
     * @param string $audioFeature
     */
    public function addToAudioFeature($audioFeature)
    {
        $this->audioFeature[] = $audioFeature;
        return $this;
    }

    /**
     * isset audioFeature
     *
     * @param int|string $index
     * @return bool
     */
    public function issetAudioFeature($index)
    {
        return isset($this->audioFeature[$index]);
    }

    /**
     * unset audioFeature
     *
     * @param int|string $index
     * @return void
     */
    public function unsetAudioFeature($index)
    {
        unset($this->audioFeature[$index]);
    }

    /**
     * Gets as audioFeature
     *
     * @return string[]
     */
    public function getAudioFeature()
    {
        return $this->audioFeature;
    }

    /**
     * Sets a new audioFeature
     *
     * @param string $audioFeature
     * @return self
     */
    public function setAudioFeature(array $audioFeature)
    {
        $this->audioFeature = $audioFeature;
        return $this;
    }


}

