<?php

namespace WalmartDSV;

/**
 * Class representing PearlShapeType
 *
 * The shape of the pearl, defined in part by how regular and smooth the surface of the pearl is.
 * XSD Type: PearlShape
 */
class PearlShapeType
{

    /**
     * @var string[] $pearlShapeValue
     */
    private $pearlShapeValue = [
        
    ];

    /**
     * Adds as pearlShapeValue
     *
     * @return self
     * @param string $pearlShapeValue
     */
    public function addToPearlShapeValue($pearlShapeValue)
    {
        $this->pearlShapeValue[] = $pearlShapeValue;
        return $this;
    }

    /**
     * isset pearlShapeValue
     *
     * @param int|string $index
     * @return bool
     */
    public function issetPearlShapeValue($index)
    {
        return isset($this->pearlShapeValue[$index]);
    }

    /**
     * unset pearlShapeValue
     *
     * @param int|string $index
     * @return void
     */
    public function unsetPearlShapeValue($index)
    {
        unset($this->pearlShapeValue[$index]);
    }

    /**
     * Gets as pearlShapeValue
     *
     * @return string[]
     */
    public function getPearlShapeValue()
    {
        return $this->pearlShapeValue;
    }

    /**
     * Sets a new pearlShapeValue
     *
     * @param string $pearlShapeValue
     * @return self
     */
    public function setPearlShapeValue(array $pearlShapeValue)
    {
        $this->pearlShapeValue = $pearlShapeValue;
        return $this;
    }


}

