<?php

namespace WalmartDSV\AnimalEverythingElseType;

/**
 * Class representing SwatchImagesAType
 *
 * Enter the swatch image location in "Swatch Image URL" and its corresponding variant attribute name in "Swatch Variant Attribute". Required for products with visual variations, like color or pattern. List the swatches in the order you recommend they appear on the site.
 */
class SwatchImagesAType
{

    /**
     * @var \WalmartDSV\AnimalEverythingElseType\SwatchImagesAType\SwatchImageAType[] $swatchImage
     */
    private $swatchImage = [
        
    ];

    /**
     * Adds as swatchImage
     *
     * @param \WalmartDSV\AnimalEverythingElseType\SwatchImagesAType\SwatchImageAType $swatchImage
     *@return self
     */
    public function addToSwatchImage(\WalmartDSV\AnimalEverythingElseType\SwatchImagesAType\SwatchImageAType $swatchImage)
    {
        $this->swatchImage[] = $swatchImage;
        return $this;
    }

    /**
     * isset swatchImage
     *
     * @param int|string $index
     * @return bool
     */
    public function issetSwatchImage($index)
    {
        return isset($this->swatchImage[$index]);
    }

    /**
     * unset swatchImage
     *
     * @param int|string $index
     * @return void
     */
    public function unsetSwatchImage($index)
    {
        unset($this->swatchImage[$index]);
    }

    /**
     * Gets as swatchImage
     *
     * @return \WalmartDSV\AnimalEverythingElseType\SwatchImagesAType\SwatchImageAType[]
     */
    public function getSwatchImage()
    {
        return $this->swatchImage;
    }

    /**
     * Sets a new swatchImage
     *
     * @param \WalmartDSV\AnimalEverythingElseType\SwatchImagesAType\SwatchImageAType[] $swatchImage
     * @return self
     */
    public function setSwatchImage(array $swatchImage)
    {
        $this->swatchImage = $swatchImage;
        return $this;
    }


}

