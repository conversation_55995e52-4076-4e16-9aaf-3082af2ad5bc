WalmartDSV\ClothingType:
    properties:
        additionalVariantAttributeNames:
            expose: true
            access_type: public_method
            serialized_name: additionalVariantAttributeNames
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAdditionalVariantAttributeNames
                setter: setAdditionalVariantAttributeNames
            type: array<string>
            xml_list:
                inline: false
                entry_name: additionalVariantAttributeName
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        shortDescription:
            expose: true
            access_type: public_method
            serialized_name: shortDescription
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getShortDescription
                setter: setShortDescription
            type: string
        keyFeatures:
            expose: true
            access_type: public_method
            serialized_name: keyFeatures
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getKeyFeatures
                setter: setKeyFeatures
            type: array<string>
            xml_list:
                inline: false
                entry_name: keyFeaturesValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        brand:
            expose: true
            access_type: public_method
            serialized_name: brand
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBrand
                setter: setBrand
            type: string
        manufacturer:
            expose: true
            access_type: public_method
            serialized_name: manufacturer
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getManufacturer
                setter: setManufacturer
            type: string
        manufacturerPartNumber:
            expose: true
            access_type: public_method
            serialized_name: manufacturerPartNumber
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getManufacturerPartNumber
                setter: setManufacturerPartNumber
            type: string
        multipackQuantity:
            expose: true
            access_type: public_method
            serialized_name: multipackQuantity
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMultipackQuantity
                setter: setMultipackQuantity
            type: int
        countPerPack:
            expose: true
            access_type: public_method
            serialized_name: countPerPack
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCountPerPack
                setter: setCountPerPack
            type: int
        count:
            expose: true
            access_type: public_method
            serialized_name: count
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCount
                setter: setCount
            type: string
        pieceCount:
            expose: true
            access_type: public_method
            serialized_name: pieceCount
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPieceCount
                setter: setPieceCount
            type: int
        modelNumber:
            expose: true
            access_type: public_method
            serialized_name: modelNumber
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getModelNumber
                setter: setModelNumber
            type: string
        seasonCode:
            expose: true
            access_type: public_method
            serialized_name: seasonCode
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSeasonCode
                setter: setSeasonCode
            type: int
        seasonYear:
            expose: true
            access_type: public_method
            serialized_name: seasonYear
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSeasonYear
                setter: setSeasonYear
            type: int
        mainImageUrl:
            expose: true
            access_type: public_method
            serialized_name: mainImageUrl
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMainImageUrl
                setter: setMainImageUrl
            type: string
        productSecondaryImageURL:
            expose: true
            access_type: public_method
            serialized_name: productSecondaryImageURL
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getProductSecondaryImageURL
                setter: setProductSecondaryImageURL
            type: array<string>
            xml_list:
                inline: false
                entry_name: productSecondaryImageURLValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        color:
            expose: true
            access_type: public_method
            serialized_name: color
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getColor
                setter: setColor
            type: WalmartDSV\ColorType
        colorCategory:
            expose: true
            access_type: public_method
            serialized_name: colorCategory
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getColorCategory
                setter: setColorCategory
            type: array<string>
            xml_list:
                inline: false
                entry_name: colorCategoryValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        pattern:
            expose: true
            access_type: public_method
            serialized_name: pattern
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPattern
                setter: setPattern
            type: WalmartDSV\PatternType
        material:
            expose: true
            access_type: public_method
            serialized_name: material
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMaterial
                setter: setMaterial
            type: WalmartDSV\MaterialType
        gender:
            expose: true
            access_type: public_method
            serialized_name: gender
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getGender
                setter: setGender
            type: string
        ageGroup:
            expose: true
            access_type: public_method
            serialized_name: ageGroup
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAgeGroup
                setter: setAgeGroup
            type: array<string>
            xml_list:
                inline: false
                entry_name: ageGroupValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        clothingSizeGroup:
            expose: true
            access_type: public_method
            serialized_name: clothingSizeGroup
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getClothingSizeGroup
                setter: setClothingSizeGroup
            type: string
        clothingSize:
            expose: true
            access_type: public_method
            serialized_name: clothingSize
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getClothingSize
                setter: setClothingSize
            type: string
        isSet:
            expose: true
            access_type: public_method
            serialized_name: isSet
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsSet
                setter: setIsSet
            type: string
        variantGroupId:
            expose: true
            access_type: public_method
            serialized_name: variantGroupId
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVariantGroupId
                setter: setVariantGroupId
            type: string
        variantAttributeNames:
            expose: true
            access_type: public_method
            serialized_name: variantAttributeNames
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVariantAttributeNames
                setter: setVariantAttributeNames
            type: array<string>
            xml_list:
                inline: false
                entry_name: variantAttributeName
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        isPrimaryVariant:
            expose: true
            access_type: public_method
            serialized_name: isPrimaryVariant
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPrimaryVariant
                setter: setIsPrimaryVariant
            type: string
        isPrivateLabelOrUnbranded:
            expose: true
            access_type: public_method
            serialized_name: isPrivateLabelOrUnbranded
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPrivateLabelOrUnbranded
                setter: setIsPrivateLabelOrUnbranded
            type: string
        isProp65WarningRequired:
            expose: true
            access_type: public_method
            serialized_name: isProp65WarningRequired
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsProp65WarningRequired
                setter: setIsProp65WarningRequired
            type: string
        prop65WarningText:
            expose: true
            access_type: public_method
            serialized_name: prop65WarningText
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getProp65WarningText
                setter: setProp65WarningText
            type: string
        smallPartsWarnings:
            expose: true
            access_type: public_method
            serialized_name: smallPartsWarnings
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSmallPartsWarnings
                setter: setSmallPartsWarnings
            type: array<int>
            xml_list:
                inline: false
                entry_name: smallPartsWarning
                skip_when_empty: false
                namespace: 'http://walmart.com/'
        requiresTextileActLabeling:
            expose: true
            access_type: public_method
            serialized_name: requiresTextileActLabeling
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getRequiresTextileActLabeling
                setter: setRequiresTextileActLabeling
            type: string
        countryOfOriginTextiles:
            expose: true
            access_type: public_method
            serialized_name: countryOfOriginTextiles
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCountryOfOriginTextiles
                setter: setCountryOfOriginTextiles
            type: string
        hasBatteries:
            expose: true
            access_type: public_method
            serialized_name: hasBatteries
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasBatteries
                setter: setHasBatteries
            type: string
        batteryTechnologyType:
            expose: true
            access_type: public_method
            serialized_name: batteryTechnologyType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBatteryTechnologyType
                setter: setBatteryTechnologyType
            type: string
        hasWarranty:
            expose: true
            access_type: public_method
            serialized_name: hasWarranty
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasWarranty
                setter: setHasWarranty
            type: string
        warrantyURL:
            expose: true
            access_type: public_method
            serialized_name: warrantyURL
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWarrantyURL
                setter: setWarrantyURL
            type: string
        warrantyText:
            expose: true
            access_type: public_method
            serialized_name: warrantyText
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWarrantyText
                setter: setWarrantyText
            type: string
        hasStateRestrictions:
            expose: true
            access_type: public_method
            serialized_name: hasStateRestrictions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasStateRestrictions
                setter: setHasStateRestrictions
            type: string
        stateRestrictions:
            expose: true
            access_type: public_method
            serialized_name: stateRestrictions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getStateRestrictions
                setter: setStateRestrictions
            type: array<WalmartDSV\StateRestrictionType>
            xml_list:
                inline: false
                entry_name: stateRestriction
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        clothingTopStyle:
            expose: true
            access_type: public_method
            serialized_name: clothingTopStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getClothingTopStyle
                setter: setClothingTopStyle
            type: WalmartDSV\ClothingTopStyleType
        dressShirtSize:
            expose: true
            access_type: public_method
            serialized_name: dressShirtSize
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getDressShirtSize
                setter: setDressShirtSize
            type: string
        sleeveStyle:
            expose: true
            access_type: public_method
            serialized_name: sleeveStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSleeveStyle
                setter: setSleeveStyle
            type: string
        sleeveLengthStyle:
            expose: true
            access_type: public_method
            serialized_name: sleeveLengthStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSleeveLengthStyle
                setter: setSleeveLengthStyle
            type: string
        shirtNeckStyle:
            expose: true
            access_type: public_method
            serialized_name: shirtNeckStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getShirtNeckStyle
                setter: setShirtNeckStyle
            type: string
        collarType:
            expose: true
            access_type: public_method
            serialized_name: collarType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCollarType
                setter: setCollarType
            type: string
        jacketStyle:
            expose: true
            access_type: public_method
            serialized_name: jacketStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getJacketStyle
                setter: setJacketStyle
            type: WalmartDSV\JacketStyleType
        suitBreastingStyle:
            expose: true
            access_type: public_method
            serialized_name: suitBreastingStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSuitBreastingStyle
                setter: setSuitBreastingStyle
            type: string
        sweaterStyle:
            expose: true
            access_type: public_method
            serialized_name: sweaterStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSweaterStyle
                setter: setSweaterStyle
            type: WalmartDSV\SweaterStyleType
        scarfStyle:
            expose: true
            access_type: public_method
            serialized_name: scarfStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getScarfStyle
                setter: setScarfStyle
            type: WalmartDSV\ScarfStyleType
        upperBodyStrapConfiguration:
            expose: true
            access_type: public_method
            serialized_name: upperBodyStrapConfiguration
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getUpperBodyStrapConfiguration
                setter: setUpperBodyStrapConfiguration
            type: WalmartDSV\UpperBodyStrapConfigurationType
        hatSize:
            expose: true
            access_type: public_method
            serialized_name: hatSize
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHatSize
                setter: setHatSize
            type: string
        hatStyle:
            expose: true
            access_type: public_method
            serialized_name: hatStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHatStyle
                setter: setHatStyle
            type: WalmartDSV\HatStyleType
        braStyle:
            expose: true
            access_type: public_method
            serialized_name: braStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBraStyle
                setter: setBraStyle
            type: WalmartDSV\BraStyleType
        braSize:
            expose: true
            access_type: public_method
            serialized_name: braSize
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBraSize
                setter: setBraSize
            type: string
        chestSize:
            expose: true
            access_type: public_method
            serialized_name: chestSize
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getChestSize
                setter: setChestSize
            type: WalmartDSV\ClothingType\ChestSizeAType
        pantRise:
            expose: true
            access_type: public_method
            serialized_name: pantRise
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPantRise
                setter: setPantRise
            type: string
        waistStyle:
            expose: true
            access_type: public_method
            serialized_name: waistStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWaistStyle
                setter: setWaistStyle
            type: WalmartDSV\WaistStyleType
        waistSize:
            expose: true
            access_type: public_method
            serialized_name: waistSize
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWaistSize
                setter: setWaistSize
            type: WalmartDSV\ClothingType\WaistSizeAType
        pantySize:
            expose: true
            access_type: public_method
            serialized_name: pantySize
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPantySize
                setter: setPantySize
            type: string
        skirtLength:
            expose: true
            access_type: public_method
            serialized_name: skirtLength
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSkirtLength
                setter: setSkirtLength
            type: WalmartDSV\ClothingType\SkirtLengthAType
        legOpeningCut:
            expose: true
            access_type: public_method
            serialized_name: legOpeningCut
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getLegOpeningCut
                setter: setLegOpeningCut
            type: string
        pantLegCut:
            expose: true
            access_type: public_method
            serialized_name: pantLegCut
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPantLegCut
                setter: setPantLegCut
            type: string
        jeanStyle:
            expose: true
            access_type: public_method
            serialized_name: jeanStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getJeanStyle
                setter: setJeanStyle
            type: WalmartDSV\JeanStyleType
        jeanWash:
            expose: true
            access_type: public_method
            serialized_name: jeanWash
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getJeanWash
                setter: setJeanWash
            type: string
        jeanFinish:
            expose: true
            access_type: public_method
            serialized_name: jeanFinish
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getJeanFinish
                setter: setJeanFinish
            type: WalmartDSV\JeanFinishType
        pantSize:
            expose: true
            access_type: public_method
            serialized_name: pantSize
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPantSize
                setter: setPantSize
            type: string
        pantFit:
            expose: true
            access_type: public_method
            serialized_name: pantFit
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPantFit
                setter: setPantFit
            type: WalmartDSV\PantFitType
        pantStyle:
            expose: true
            access_type: public_method
            serialized_name: pantStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPantStyle
                setter: setPantStyle
            type: string
        beltStyle:
            expose: true
            access_type: public_method
            serialized_name: beltStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBeltStyle
                setter: setBeltStyle
            type: WalmartDSV\BeltStyleType
        beltBuckleStyle:
            expose: true
            access_type: public_method
            serialized_name: beltBuckleStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBeltBuckleStyle
                setter: setBeltBuckleStyle
            type: string
        pantyStyle:
            expose: true
            access_type: public_method
            serialized_name: pantyStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPantyStyle
                setter: setPantyStyle
            type: string
        shortsStyle:
            expose: true
            access_type: public_method
            serialized_name: shortsStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getShortsStyle
                setter: setShortsStyle
            type: WalmartDSV\ShortsStyleType
        skirtAndDressCut:
            expose: true
            access_type: public_method
            serialized_name: skirtAndDressCut
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSkirtAndDressCut
                setter: setSkirtAndDressCut
            type: WalmartDSV\SkirtStyleType
        skirtLengthStyle:
            expose: true
            access_type: public_method
            serialized_name: skirtLengthStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSkirtLengthStyle
                setter: setSkirtLengthStyle
            type: string
        hosieryStyle:
            expose: true
            access_type: public_method
            serialized_name: hosieryStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHosieryStyle
                setter: setHosieryStyle
            type: WalmartDSV\HosieryStyleType
        tightsSheerness:
            expose: true
            access_type: public_method
            serialized_name: tightsSheerness
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getTightsSheerness
                setter: setTightsSheerness
            type: string
        underwearStyle:
            expose: true
            access_type: public_method
            serialized_name: underwearStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getUnderwearStyle
                setter: setUnderwearStyle
            type: WalmartDSV\UnderwearStyleType
        sockSize:
            expose: true
            access_type: public_method
            serialized_name: sockSize
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSockSize
                setter: setSockSize
            type: string
        sockStyle:
            expose: true
            access_type: public_method
            serialized_name: sockStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSockStyle
                setter: setSockStyle
            type: string
        sockRise:
            expose: true
            access_type: public_method
            serialized_name: sockRise
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSockRise
                setter: setSockRise
            type: string
        fabricContent:
            expose: true
            access_type: public_method
            serialized_name: fabricContent
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFabricContent
                setter: setFabricContent
            type: array<WalmartDSV\FabricContentValueType>
            xml_list:
                inline: false
                entry_name: fabricContentValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        fabricCareInstructions:
            expose: true
            access_type: public_method
            serialized_name: fabricCareInstructions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFabricCareInstructions
                setter: setFabricCareInstructions
            type: array<string>
            xml_list:
                inline: false
                entry_name: fabricCareInstruction
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        accentColor:
            expose: true
            access_type: public_method
            serialized_name: accentColor
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAccentColor
                setter: setAccentColor
            type: string
        clothingWeight:
            expose: true
            access_type: public_method
            serialized_name: clothingWeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getClothingWeight
                setter: setClothingWeight
            type: string
        clothingStyle:
            expose: true
            access_type: public_method
            serialized_name: clothingStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getClothingStyle
                setter: setClothingStyle
            type: WalmartDSV\ClothingStyleType
        clothingFit:
            expose: true
            access_type: public_method
            serialized_name: clothingFit
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getClothingFit
                setter: setClothingFit
            type: string
        clothingCut:
            expose: true
            access_type: public_method
            serialized_name: clothingCut
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getClothingCut
                setter: setClothingCut
            type: WalmartDSV\ClothingCutType
        clothingLengthStyle:
            expose: true
            access_type: public_method
            serialized_name: clothingLengthStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getClothingLengthStyle
                setter: setClothingLengthStyle
            type: string
        fastenerType:
            expose: true
            access_type: public_method
            serialized_name: fastenerType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFastenerType
                setter: setFastenerType
            type: string
        swimsuitStyle:
            expose: true
            access_type: public_method
            serialized_name: swimsuitStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSwimsuitStyle
                setter: setSwimsuitStyle
            type: string
        dressStyle:
            expose: true
            access_type: public_method
            serialized_name: dressStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getDressStyle
                setter: setDressStyle
            type: string
        gotsCertification:
            expose: true
            access_type: public_method
            serialized_name: gotsCertification
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getGotsCertification
                setter: setGotsCertification
            type: string
        theme:
            expose: true
            access_type: public_method
            serialized_name: theme
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getTheme
                setter: setTheme
            type: WalmartDSV\ThemeType
        character:
            expose: true
            access_type: public_method
            serialized_name: character
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCharacter
                setter: setCharacter
            type: WalmartDSV\CharacterType
        globalBrandLicense:
            expose: true
            access_type: public_method
            serialized_name: globalBrandLicense
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getGlobalBrandLicense
                setter: setGlobalBrandLicense
            type: array<string>
            xml_list:
                inline: false
                entry_name: globalBrandLicenseValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        sportsLeague:
            expose: true
            access_type: public_method
            serialized_name: sportsLeague
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSportsLeague
                setter: setSportsLeague
            type: WalmartDSV\SportsLeagueType
        sportsTeam:
            expose: true
            access_type: public_method
            serialized_name: sportsTeam
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSportsTeam
                setter: setSportsTeam
            type: WalmartDSV\SportsTeamType
        occasion:
            expose: true
            access_type: public_method
            serialized_name: occasion
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getOccasion
                setter: setOccasion
            type: WalmartDSV\OccasionType
        activity:
            expose: true
            access_type: public_method
            serialized_name: activity
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getActivity
                setter: setActivity
            type: WalmartDSV\ActivityType
        sport:
            expose: true
            access_type: public_method
            serialized_name: sport
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSport
                setter: setSport
            type: WalmartDSV\SportType
        season:
            expose: true
            access_type: public_method
            serialized_name: season
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSeason
                setter: setSeason
            type: WalmartDSV\SeasonType
        weather:
            expose: true
            access_type: public_method
            serialized_name: weather
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWeather
                setter: setWeather
            type: WalmartDSV\WeatherType
        isMaternity:
            expose: true
            access_type: public_method
            serialized_name: isMaternity
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsMaternity
                setter: setIsMaternity
            type: string
        academicInstitution:
            expose: true
            access_type: public_method
            serialized_name: academicInstitution
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAcademicInstitution
                setter: setAcademicInstitution
            type: string
        athlete:
            expose: true
            access_type: public_method
            serialized_name: athlete
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAthlete
                setter: setAthlete
            type: WalmartDSV\AthleteType
        autographedBy:
            expose: true
            access_type: public_method
            serialized_name: autographedBy
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAutographedBy
                setter: setAutographedBy
            type: string
        braBandSize:
            expose: true
            access_type: public_method
            serialized_name: braBandSize
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBraBandSize
                setter: setBraBandSize
            type: WalmartDSV\ClothingType\BraBandSizeAType
        braCupSize:
            expose: true
            access_type: public_method
            serialized_name: braCupSize
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBraCupSize
                setter: setBraCupSize
            type: string
        neckSize:
            expose: true
            access_type: public_method
            serialized_name: neckSize
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getNeckSize
                setter: setNeckSize
            type: WalmartDSV\ClothingType\NeckSizeAType
        sleeveLength:
            expose: true
            access_type: public_method
            serialized_name: sleeveLength
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSleeveLength
                setter: setSleeveLength
            type: WalmartDSV\ClothingType\SleeveLengthAType
        inseam:
            expose: true
            access_type: public_method
            serialized_name: inseam
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getInseam
                setter: setInseam
            type: WalmartDSV\ClothingType\InseamAType
        isMadeFromRecycledMaterial:
            expose: true
            access_type: public_method
            serialized_name: isMadeFromRecycledMaterial
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsMadeFromRecycledMaterial
                setter: setIsMadeFromRecycledMaterial
            type: string
        recycledMaterialContent:
            expose: true
            access_type: public_method
            serialized_name: recycledMaterialContent
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getRecycledMaterialContent
                setter: setRecycledMaterialContent
            type: array<WalmartDSV\RecycledMaterialContentValueType>
            xml_list:
                inline: false
                entry_name: recycledMaterialContentValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        features:
            expose: true
            access_type: public_method
            serialized_name: features
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFeatures
                setter: setFeatures
            type: array<string>
            xml_list:
                inline: false
                entry_name: feature
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        keywords:
            expose: true
            access_type: public_method
            serialized_name: keywords
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getKeywords
                setter: setKeywords
            type: string
        swatchImages:
            expose: true
            access_type: public_method
            serialized_name: swatchImages
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSwatchImages
                setter: setSwatchImages
            type: array<WalmartDSV\ClothingType\SwatchImagesAType\SwatchImageAType>
            xml_list:
                inline: false
                entry_name: swatchImage
                skip_when_empty: true
                namespace: 'http://walmart.com/'
