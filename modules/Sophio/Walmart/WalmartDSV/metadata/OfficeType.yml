WalmartDSV\OfficeType:
    properties:
        additionalVariantAttributeNames:
            expose: true
            access_type: public_method
            serialized_name: additionalVariantAttributeNames
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAdditionalVariantAttributeNames
                setter: setAdditionalVariantAttributeNames
            type: array<string>
            xml_list:
                inline: false
                entry_name: additionalVariantAttributeName
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        shortDescription:
            expose: true
            access_type: public_method
            serialized_name: shortDescription
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getShortDescription
                setter: setShortDescription
            type: string
        keyFeatures:
            expose: true
            access_type: public_method
            serialized_name: keyFeatures
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getKeyFeatures
                setter: setKeyFeatures
            type: array<string>
            xml_list:
                inline: false
                entry_name: keyFeaturesValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        brand:
            expose: true
            access_type: public_method
            serialized_name: brand
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBrand
                setter: setBrand
            type: string
        manufacturer:
            expose: true
            access_type: public_method
            serialized_name: manufacturer
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getManufacturer
                setter: setManufacturer
            type: string
        modelNumber:
            expose: true
            access_type: public_method
            serialized_name: modelNumber
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getModelNumber
                setter: setModelNumber
            type: string
        manufacturerPartNumber:
            expose: true
            access_type: public_method
            serialized_name: manufacturerPartNumber
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getManufacturerPartNumber
                setter: setManufacturerPartNumber
            type: string
        multipackQuantity:
            expose: true
            access_type: public_method
            serialized_name: multipackQuantity
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMultipackQuantity
                setter: setMultipackQuantity
            type: int
        countPerPack:
            expose: true
            access_type: public_method
            serialized_name: countPerPack
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCountPerPack
                setter: setCountPerPack
            type: int
        count:
            expose: true
            access_type: public_method
            serialized_name: count
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCount
                setter: setCount
            type: string
        pieceCount:
            expose: true
            access_type: public_method
            serialized_name: pieceCount
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPieceCount
                setter: setPieceCount
            type: int
        mainImageUrl:
            expose: true
            access_type: public_method
            serialized_name: mainImageUrl
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMainImageUrl
                setter: setMainImageUrl
            type: string
        productSecondaryImageURL:
            expose: true
            access_type: public_method
            serialized_name: productSecondaryImageURL
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getProductSecondaryImageURL
                setter: setProductSecondaryImageURL
            type: array<string>
            xml_list:
                inline: false
                entry_name: productSecondaryImageURLValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        color:
            expose: true
            access_type: public_method
            serialized_name: color
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getColor
                setter: setColor
            type: WalmartDSV\ColorType
        colorCategory:
            expose: true
            access_type: public_method
            serialized_name: colorCategory
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getColorCategory
                setter: setColorCategory
            type: array<string>
            xml_list:
                inline: false
                entry_name: colorCategoryValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        size:
            expose: true
            access_type: public_method
            serialized_name: size
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSize
                setter: setSize
            type: string
        ageGroup:
            expose: true
            access_type: public_method
            serialized_name: ageGroup
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAgeGroup
                setter: setAgeGroup
            type: array<string>
            xml_list:
                inline: false
                entry_name: ageGroupValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        material:
            expose: true
            access_type: public_method
            serialized_name: material
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMaterial
                setter: setMaterial
            type: WalmartDSV\MaterialType
        capacity:
            expose: true
            access_type: public_method
            serialized_name: capacity
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCapacity
                setter: setCapacity
            type: string
        occasion:
            expose: true
            access_type: public_method
            serialized_name: occasion
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getOccasion
                setter: setOccasion
            type: WalmartDSV\OccasionType
        paperSize:
            expose: true
            access_type: public_method
            serialized_name: paperSize
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPaperSize
                setter: setPaperSize
            type: WalmartDSV\PaperSizeType
        assembledProductLength:
            expose: true
            access_type: public_method
            serialized_name: assembledProductLength
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductLength
                setter: setAssembledProductLength
            type: WalmartDSV\OfficeType\AssembledProductLengthAType
        assembledProductWidth:
            expose: true
            access_type: public_method
            serialized_name: assembledProductWidth
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductWidth
                setter: setAssembledProductWidth
            type: WalmartDSV\OfficeType\AssembledProductWidthAType
        assembledProductHeight:
            expose: true
            access_type: public_method
            serialized_name: assembledProductHeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductHeight
                setter: setAssembledProductHeight
            type: WalmartDSV\OfficeType\AssembledProductHeightAType
        assembledProductWeight:
            expose: true
            access_type: public_method
            serialized_name: assembledProductWeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductWeight
                setter: setAssembledProductWeight
            type: WalmartDSV\OfficeType\AssembledProductWeightAType
        variantGroupId:
            expose: true
            access_type: public_method
            serialized_name: variantGroupId
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVariantGroupId
                setter: setVariantGroupId
            type: string
        variantAttributeNames:
            expose: true
            access_type: public_method
            serialized_name: variantAttributeNames
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVariantAttributeNames
                setter: setVariantAttributeNames
            type: array<string>
            xml_list:
                inline: false
                entry_name: variantAttributeName
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        isPrimaryVariant:
            expose: true
            access_type: public_method
            serialized_name: isPrimaryVariant
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPrimaryVariant
                setter: setIsPrimaryVariant
            type: string
        isPrivateLabelOrUnbranded:
            expose: true
            access_type: public_method
            serialized_name: isPrivateLabelOrUnbranded
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPrivateLabelOrUnbranded
                setter: setIsPrivateLabelOrUnbranded
            type: string
        isProp65WarningRequired:
            expose: true
            access_type: public_method
            serialized_name: isProp65WarningRequired
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsProp65WarningRequired
                setter: setIsProp65WarningRequired
            type: string
        prop65WarningText:
            expose: true
            access_type: public_method
            serialized_name: prop65WarningText
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getProp65WarningText
                setter: setProp65WarningText
            type: string
        smallPartsWarnings:
            expose: true
            access_type: public_method
            serialized_name: smallPartsWarnings
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSmallPartsWarnings
                setter: setSmallPartsWarnings
            type: array<int>
            xml_list:
                inline: false
                entry_name: smallPartsWarning
                skip_when_empty: false
                namespace: 'http://walmart.com/'
        hasBatteries:
            expose: true
            access_type: public_method
            serialized_name: hasBatteries
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasBatteries
                setter: setHasBatteries
            type: string
        batteryTechnologyType:
            expose: true
            access_type: public_method
            serialized_name: batteryTechnologyType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBatteryTechnologyType
                setter: setBatteryTechnologyType
            type: string
        isAerosol:
            expose: true
            access_type: public_method
            serialized_name: isAerosol
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsAerosol
                setter: setIsAerosol
            type: string
        isChemical:
            expose: true
            access_type: public_method
            serialized_name: isChemical
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsChemical
                setter: setIsChemical
            type: string
        compositeWoodCertificationCode:
            expose: true
            access_type: public_method
            serialized_name: compositeWoodCertificationCode
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCompositeWoodCertificationCode
                setter: setCompositeWoodCertificationCode
            type: int
        isPesticide:
            expose: true
            access_type: public_method
            serialized_name: isPesticide
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPesticide
                setter: setIsPesticide
            type: string
        hasExpiration:
            expose: true
            access_type: public_method
            serialized_name: hasExpiration
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasExpiration
                setter: setHasExpiration
            type: string
        shelfLife:
            expose: true
            access_type: public_method
            serialized_name: shelfLife
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getShelfLife
                setter: setShelfLife
            type: WalmartDSV\OfficeType\ShelfLifeAType
        hasWarranty:
            expose: true
            access_type: public_method
            serialized_name: hasWarranty
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasWarranty
                setter: setHasWarranty
            type: string
        warrantyURL:
            expose: true
            access_type: public_method
            serialized_name: warrantyURL
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWarrantyURL
                setter: setWarrantyURL
            type: string
        warrantyText:
            expose: true
            access_type: public_method
            serialized_name: warrantyText
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWarrantyText
                setter: setWarrantyText
            type: string
        hasStateRestrictions:
            expose: true
            access_type: public_method
            serialized_name: hasStateRestrictions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasStateRestrictions
                setter: setHasStateRestrictions
            type: string
        stateRestrictions:
            expose: true
            access_type: public_method
            serialized_name: stateRestrictions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getStateRestrictions
                setter: setStateRestrictions
            type: array<WalmartDSV\StateRestrictionType>
            xml_list:
                inline: false
                entry_name: stateRestriction
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        isLightingFactsLabelRequired:
            expose: true
            access_type: public_method
            serialized_name: isLightingFactsLabelRequired
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsLightingFactsLabelRequired
                setter: setIsLightingFactsLabelRequired
            type: string
        lightingFactsLabel:
            expose: true
            access_type: public_method
            serialized_name: lightingFactsLabel
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getLightingFactsLabel
                setter: setLightingFactsLabel
            type: string
        pattern:
            expose: true
            access_type: public_method
            serialized_name: pattern
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPattern
                setter: setPattern
            type: WalmartDSV\PatternType
        shape:
            expose: true
            access_type: public_method
            serialized_name: shape
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getShape
                setter: setShape
            type: string
        finish:
            expose: true
            access_type: public_method
            serialized_name: finish
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFinish
                setter: setFinish
            type: string
        theme:
            expose: true
            access_type: public_method
            serialized_name: theme
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getTheme
                setter: setTheme
            type: WalmartDSV\ThemeType
        globalBrandLicense:
            expose: true
            access_type: public_method
            serialized_name: globalBrandLicense
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getGlobalBrandLicense
                setter: setGlobalBrandLicense
            type: array<string>
            xml_list:
                inline: false
                entry_name: globalBrandLicenseValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        compatibleDevices:
            expose: true
            access_type: public_method
            serialized_name: compatibleDevices
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCompatibleDevices
                setter: setCompatibleDevices
            type: WalmartDSV\CompatibleDevicesType
        isPowered:
            expose: true
            access_type: public_method
            serialized_name: isPowered
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPowered
                setter: setIsPowered
            type: string
        powerType:
            expose: true
            access_type: public_method
            serialized_name: powerType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPowerType
                setter: setPowerType
            type: string
        brightness:
            expose: true
            access_type: public_method
            serialized_name: brightness
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBrightness
                setter: setBrightness
            type: WalmartDSV\OfficeType\BrightnessAType
        dexterity:
            expose: true
            access_type: public_method
            serialized_name: dexterity
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getDexterity
                setter: setDexterity
            type: string
        systemOfMeasurement:
            expose: true
            access_type: public_method
            serialized_name: systemOfMeasurement
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSystemOfMeasurement
                setter: setSystemOfMeasurement
            type: string
        holeSize:
            expose: true
            access_type: public_method
            serialized_name: holeSize
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHoleSize
                setter: setHoleSize
            type: WalmartDSV\OfficeType\HoleSizeAType
        year:
            expose: true
            access_type: public_method
            serialized_name: year
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getYear
                setter: setYear
            type: int
        calendarFormat:
            expose: true
            access_type: public_method
            serialized_name: calendarFormat
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCalendarFormat
                setter: setCalendarFormat
            type: string
        calendarTerm:
            expose: true
            access_type: public_method
            serialized_name: calendarTerm
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCalendarTerm
                setter: setCalendarTerm
            type: WalmartDSV\OfficeType\CalendarTermAType
        numberOfSheets:
            expose: true
            access_type: public_method
            serialized_name: numberOfSheets
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getNumberOfSheets
                setter: setNumberOfSheets
            type: int
        envelopeSize:
            expose: true
            access_type: public_method
            serialized_name: envelopeSize
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getEnvelopeSize
                setter: setEnvelopeSize
            type: float
        inkColor:
            expose: true
            access_type: public_method
            serialized_name: inkColor
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getInkColor
                setter: setInkColor
            type: array<string>
            xml_list:
                inline: false
                entry_name: inkColorValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        isRefillable:
            expose: true
            access_type: public_method
            serialized_name: isRefillable
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsRefillable
                setter: setIsRefillable
            type: string
        isRetractable:
            expose: true
            access_type: public_method
            serialized_name: isRetractable
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsRetractable
                setter: setIsRetractable
            type: string
        isIndustrial:
            expose: true
            access_type: public_method
            serialized_name: isIndustrial
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsIndustrial
                setter: setIsIndustrial
            type: string
        isAntiglare:
            expose: true
            access_type: public_method
            serialized_name: isAntiglare
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsAntiglare
                setter: setIsAntiglare
            type: string
        isMagnetic:
            expose: true
            access_type: public_method
            serialized_name: isMagnetic
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsMagnetic
                setter: setIsMagnetic
            type: string
        isTearResistant:
            expose: true
            access_type: public_method
            serialized_name: isTearResistant
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsTearResistant
                setter: setIsTearResistant
            type: string
        recommendedUses:
            expose: true
            access_type: public_method
            serialized_name: recommendedUses
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getRecommendedUses
                setter: setRecommendedUses
            type: array<string>
            xml_list:
                inline: false
                entry_name: recommendedUse
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        hpprintercartridgeNumber:
            expose: true
            access_type: public_method
            serialized_name: hpprintercartridgeNumber
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHpprintercartridgeNumber
                setter: setHpprintercartridgeNumber
            type: array<string>
            xml_list:
                inline: false
                entry_name: hpprintercartridgeNumberValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        pencilLeadDiameter:
            expose: true
            access_type: public_method
            serialized_name: pencilLeadDiameter
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPencilLeadDiameter
                setter: setPencilLeadDiameter
            type: string
        tabCut:
            expose: true
            access_type: public_method
            serialized_name: tabCut
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getTabCut
                setter: setTabCut
            type: string
        tabColor:
            expose: true
            access_type: public_method
            serialized_name: tabColor
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getTabColor
                setter: setTabColor
            type: string
        shredderCutStyles:
            expose: true
            access_type: public_method
            serialized_name: shredderCutStyles
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getShredderCutStyles
                setter: setShredderCutStyles
            type: string
        isMadeFromRecycledMaterial:
            expose: true
            access_type: public_method
            serialized_name: isMadeFromRecycledMaterial
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsMadeFromRecycledMaterial
                setter: setIsMadeFromRecycledMaterial
            type: string
        isLined:
            expose: true
            access_type: public_method
            serialized_name: isLined
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsLined
                setter: setIsLined
            type: string
        recycledMaterialContent:
            expose: true
            access_type: public_method
            serialized_name: recycledMaterialContent
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getRecycledMaterialContent
                setter: setRecycledMaterialContent
            type: array<WalmartDSV\RecycledMaterialContentValueType>
            xml_list:
                inline: false
                entry_name: recycledMaterialContentValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        overallExpansion:
            expose: true
            access_type: public_method
            serialized_name: overallExpansion
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getOverallExpansion
                setter: setOverallExpansion
            type: WalmartDSV\OfficeType\OverallExpansionAType
        paperClipSize:
            expose: true
            access_type: public_method
            serialized_name: paperClipSize
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPaperClipSize
                setter: setPaperClipSize
            type: string
        penPointSize:
            expose: true
            access_type: public_method
            serialized_name: penPointSize
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPenPointSize
                setter: setPenPointSize
            type: WalmartDSV\OfficeType\PenPointSizeAType
        swatchImages:
            expose: true
            access_type: public_method
            serialized_name: swatchImages
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSwatchImages
                setter: setSwatchImages
            type: array<WalmartDSV\OfficeType\SwatchImagesAType\SwatchImageAType>
            xml_list:
                inline: false
                entry_name: swatchImage
                skip_when_empty: true
                namespace: 'http://walmart.com/'
