WalmartDSV\PersonalCareType\SwatchImagesAType\SwatchImageAType:
    properties:
        swatchVariantAttribute:
            expose: true
            access_type: public_method
            serialized_name: swatchVariantAttribute
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSwatchVariantAttribute
                setter: setSwatchVariantAttribute
            type: string
        swatchImageUrl:
            expose: true
            access_type: public_method
            serialized_name: swatchImageUrl
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSwatchImageUrl
                setter: setSwatchImageUrl
            type: string
