WalmartDSV\CleaningAndChemicalType:
    properties:
        additionalVariantAttributeNames:
            expose: true
            access_type: public_method
            serialized_name: additionalVariantAttributeNames
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAdditionalVariantAttributeNames
                setter: setAdditionalVariantAttributeNames
            type: array<string>
            xml_list:
                inline: false
                entry_name: additionalVariantAttributeName
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        shortDescription:
            expose: true
            access_type: public_method
            serialized_name: shortDescription
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getShortDescription
                setter: setShortDescription
            type: string
        keyFeatures:
            expose: true
            access_type: public_method
            serialized_name: keyFeatures
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getKeyFeatures
                setter: setKeyFeatures
            type: array<string>
            xml_list:
                inline: false
                entry_name: keyFeaturesValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        brand:
            expose: true
            access_type: public_method
            serialized_name: brand
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBrand
                setter: setBrand
            type: string
        manufacturer:
            expose: true
            access_type: public_method
            serialized_name: manufacturer
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getManufacturer
                setter: setManufacturer
            type: string
        manufacturerPartNumber:
            expose: true
            access_type: public_method
            serialized_name: manufacturerPartNumber
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getManufacturerPartNumber
                setter: setManufacturerPartNumber
            type: string
        modelNumber:
            expose: true
            access_type: public_method
            serialized_name: modelNumber
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getModelNumber
                setter: setModelNumber
            type: string
        multipackQuantity:
            expose: true
            access_type: public_method
            serialized_name: multipackQuantity
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMultipackQuantity
                setter: setMultipackQuantity
            type: int
        countPerPack:
            expose: true
            access_type: public_method
            serialized_name: countPerPack
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCountPerPack
                setter: setCountPerPack
            type: int
        count:
            expose: true
            access_type: public_method
            serialized_name: count
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCount
                setter: setCount
            type: string
        pieceCount:
            expose: true
            access_type: public_method
            serialized_name: pieceCount
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPieceCount
                setter: setPieceCount
            type: int
        mainImageUrl:
            expose: true
            access_type: public_method
            serialized_name: mainImageUrl
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMainImageUrl
                setter: setMainImageUrl
            type: string
        productSecondaryImageURL:
            expose: true
            access_type: public_method
            serialized_name: productSecondaryImageURL
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getProductSecondaryImageURL
                setter: setProductSecondaryImageURL
            type: array<string>
            xml_list:
                inline: false
                entry_name: productSecondaryImageURLValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        recommendedSurfaces:
            expose: true
            access_type: public_method
            serialized_name: recommendedSurfaces
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getRecommendedSurfaces
                setter: setRecommendedSurfaces
            type: array<string>
            xml_list:
                inline: false
                entry_name: recommendedSurface
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        recommendedRooms:
            expose: true
            access_type: public_method
            serialized_name: recommendedRooms
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getRecommendedRooms
                setter: setRecommendedRooms
            type: array<string>
            xml_list:
                inline: false
                entry_name: recommendedRoom
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        recommendedLocations:
            expose: true
            access_type: public_method
            serialized_name: recommendedLocations
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getRecommendedLocations
                setter: setRecommendedLocations
            type: array<string>
            xml_list:
                inline: false
                entry_name: recommendedLocation
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        size:
            expose: true
            access_type: public_method
            serialized_name: size
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSize
                setter: setSize
            type: string
        material:
            expose: true
            access_type: public_method
            serialized_name: material
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMaterial
                setter: setMaterial
            type: WalmartDSV\MaterialType
        finish:
            expose: true
            access_type: public_method
            serialized_name: finish
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFinish
                setter: setFinish
            type: string
        color:
            expose: true
            access_type: public_method
            serialized_name: color
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getColor
                setter: setColor
            type: WalmartDSV\ColorType
        colorCategory:
            expose: true
            access_type: public_method
            serialized_name: colorCategory
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getColorCategory
                setter: setColorCategory
            type: array<string>
            xml_list:
                inline: false
                entry_name: colorCategoryValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        pattern:
            expose: true
            access_type: public_method
            serialized_name: pattern
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPattern
                setter: setPattern
            type: WalmartDSV\PatternType
        shape:
            expose: true
            access_type: public_method
            serialized_name: shape
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getShape
                setter: setShape
            type: string
        isAssemblyRequired:
            expose: true
            access_type: public_method
            serialized_name: isAssemblyRequired
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsAssemblyRequired
                setter: setIsAssemblyRequired
            type: string
        assemblyInstructions:
            expose: true
            access_type: public_method
            serialized_name: assemblyInstructions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssemblyInstructions
                setter: setAssemblyInstructions
            type: string
        assembledProductLength:
            expose: true
            access_type: public_method
            serialized_name: assembledProductLength
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductLength
                setter: setAssembledProductLength
            type: WalmartDSV\CleaningAndChemicalType\AssembledProductLengthAType
        assembledProductWidth:
            expose: true
            access_type: public_method
            serialized_name: assembledProductWidth
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductWidth
                setter: setAssembledProductWidth
            type: WalmartDSV\CleaningAndChemicalType\AssembledProductWidthAType
        assembledProductHeight:
            expose: true
            access_type: public_method
            serialized_name: assembledProductHeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductHeight
                setter: setAssembledProductHeight
            type: WalmartDSV\CleaningAndChemicalType\AssembledProductHeightAType
        assembledProductWeight:
            expose: true
            access_type: public_method
            serialized_name: assembledProductWeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductWeight
                setter: setAssembledProductWeight
            type: WalmartDSV\CleaningAndChemicalType\AssembledProductWeightAType
        variantGroupId:
            expose: true
            access_type: public_method
            serialized_name: variantGroupId
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVariantGroupId
                setter: setVariantGroupId
            type: string
        variantAttributeNames:
            expose: true
            access_type: public_method
            serialized_name: variantAttributeNames
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVariantAttributeNames
                setter: setVariantAttributeNames
            type: array<string>
            xml_list:
                inline: false
                entry_name: variantAttributeName
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        isPrimaryVariant:
            expose: true
            access_type: public_method
            serialized_name: isPrimaryVariant
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPrimaryVariant
                setter: setIsPrimaryVariant
            type: string
        isPrivateLabelOrUnbranded:
            expose: true
            access_type: public_method
            serialized_name: isPrivateLabelOrUnbranded
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPrivateLabelOrUnbranded
                setter: setIsPrivateLabelOrUnbranded
            type: string
        isProp65WarningRequired:
            expose: true
            access_type: public_method
            serialized_name: isProp65WarningRequired
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsProp65WarningRequired
                setter: setIsProp65WarningRequired
            type: string
        prop65WarningText:
            expose: true
            access_type: public_method
            serialized_name: prop65WarningText
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getProp65WarningText
                setter: setProp65WarningText
            type: string
        hasBatteries:
            expose: true
            access_type: public_method
            serialized_name: hasBatteries
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasBatteries
                setter: setHasBatteries
            type: string
        batteryTechnologyType:
            expose: true
            access_type: public_method
            serialized_name: batteryTechnologyType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBatteryTechnologyType
                setter: setBatteryTechnologyType
            type: string
        isAerosol:
            expose: true
            access_type: public_method
            serialized_name: isAerosol
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsAerosol
                setter: setIsAerosol
            type: string
        isChemical:
            expose: true
            access_type: public_method
            serialized_name: isChemical
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsChemical
                setter: setIsChemical
            type: string
        isPesticide:
            expose: true
            access_type: public_method
            serialized_name: isPesticide
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPesticide
                setter: setIsPesticide
            type: string
        hasExpiration:
            expose: true
            access_type: public_method
            serialized_name: hasExpiration
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasExpiration
                setter: setHasExpiration
            type: string
        shelfLife:
            expose: true
            access_type: public_method
            serialized_name: shelfLife
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getShelfLife
                setter: setShelfLife
            type: WalmartDSV\CleaningAndChemicalType\ShelfLifeAType
        hasPricePerUnit:
            expose: true
            access_type: public_method
            serialized_name: hasPricePerUnit
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasPricePerUnit
                setter: setHasPricePerUnit
            type: string
        pricePerUnitUom:
            expose: true
            access_type: public_method
            serialized_name: pricePerUnitUom
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPricePerUnitUom
                setter: setPricePerUnitUom
            type: string
        pricePerUnitQuantity:
            expose: true
            access_type: public_method
            serialized_name: pricePerUnitQuantity
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPricePerUnitQuantity
                setter: setPricePerUnitQuantity
            type: float
        hasStateRestrictions:
            expose: true
            access_type: public_method
            serialized_name: hasStateRestrictions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasStateRestrictions
                setter: setHasStateRestrictions
            type: string
        stateRestrictions:
            expose: true
            access_type: public_method
            serialized_name: stateRestrictions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getStateRestrictions
                setter: setStateRestrictions
            type: array<WalmartDSV\StateRestrictionType>
            xml_list:
                inline: false
                entry_name: stateRestriction
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        hasIngredientList:
            expose: true
            access_type: public_method
            serialized_name: hasIngredientList
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasIngredientList
                setter: setHasIngredientList
            type: string
        ingredientListImage:
            expose: true
            access_type: public_method
            serialized_name: ingredientListImage
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIngredientListImage
                setter: setIngredientListImage
            type: string
        ingredients:
            expose: true
            access_type: public_method
            serialized_name: ingredients
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIngredients
                setter: setIngredients
            type: string
        hasFuelContainer:
            expose: true
            access_type: public_method
            serialized_name: hasFuelContainer
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasFuelContainer
                setter: setHasFuelContainer
            type: string
        fabricContent:
            expose: true
            access_type: public_method
            serialized_name: fabricContent
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFabricContent
                setter: setFabricContent
            type: array<WalmartDSV\FabricContentValueType>
            xml_list:
                inline: false
                entry_name: fabricContentValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        isPowered:
            expose: true
            access_type: public_method
            serialized_name: isPowered
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPowered
                setter: setIsPowered
            type: string
        powerType:
            expose: true
            access_type: public_method
            serialized_name: powerType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPowerType
                setter: setPowerType
            type: string
        volts:
            expose: true
            access_type: public_method
            serialized_name: volts
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVolts
                setter: setVolts
            type: WalmartDSV\CleaningAndChemicalType\VoltsAType
        connections:
            expose: true
            access_type: public_method
            serialized_name: connections
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getConnections
                setter: setConnections
            type: array<string>
            xml_list:
                inline: false
                entry_name: connection
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        activeIngredients:
            expose: true
            access_type: public_method
            serialized_name: activeIngredients
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getActiveIngredients
                setter: setActiveIngredients
            type: array<WalmartDSV\ActiveIngredientType>
            xml_list:
                inline: false
                entry_name: activeIngredient
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        inactiveIngredients:
            expose: true
            access_type: public_method
            serialized_name: inactiveIngredients
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getInactiveIngredients
                setter: setInactiveIngredients
            type: array<string>
            xml_list:
                inline: false
                entry_name: inactiveIngredient
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        instructions:
            expose: true
            access_type: public_method
            serialized_name: instructions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getInstructions
                setter: setInstructions
            type: string
        form:
            expose: true
            access_type: public_method
            serialized_name: form
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getForm
                setter: setForm
            type: string
        scent:
            expose: true
            access_type: public_method
            serialized_name: scent
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getScent
                setter: setScent
            type: string
        fluidOunces:
            expose: true
            access_type: public_method
            serialized_name: fluidOunces
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFluidOunces
                setter: setFluidOunces
            type: WalmartDSV\CleaningAndChemicalType\FluidOuncesAType
        isRecyclable:
            expose: true
            access_type: public_method
            serialized_name: isRecyclable
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsRecyclable
                setter: setIsRecyclable
            type: string
        isFlammable:
            expose: true
            access_type: public_method
            serialized_name: isFlammable
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsFlammable
                setter: setIsFlammable
            type: string
        isCombustible:
            expose: true
            access_type: public_method
            serialized_name: isCombustible
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsCombustible
                setter: setIsCombustible
            type: string
        isBiodegradable:
            expose: true
            access_type: public_method
            serialized_name: isBiodegradable
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsBiodegradable
                setter: setIsBiodegradable
            type: string
        isEnergyStarCertified:
            expose: true
            access_type: public_method
            serialized_name: isEnergyStarCertified
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsEnergyStarCertified
                setter: setIsEnergyStarCertified
            type: string
        handleLength:
            expose: true
            access_type: public_method
            serialized_name: handleLength
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHandleLength
                setter: setHandleLength
            type: WalmartDSV\CleaningAndChemicalType\HandleLengthAType
        bladeWidth:
            expose: true
            access_type: public_method
            serialized_name: bladeWidth
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBladeWidth
                setter: setBladeWidth
            type: WalmartDSV\LengthUnitType
        bristleMaterial:
            expose: true
            access_type: public_method
            serialized_name: bristleMaterial
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBristleMaterial
                setter: setBristleMaterial
            type: string
        cleaningPath:
            expose: true
            access_type: public_method
            serialized_name: cleaningPath
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCleaningPath
                setter: setCleaningPath
            type: WalmartDSV\CleaningAndChemicalType\CleaningPathAType
        features:
            expose: true
            access_type: public_method
            serialized_name: features
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFeatures
                setter: setFeatures
            type: array<string>
            xml_list:
                inline: false
                entry_name: feature
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        keywords:
            expose: true
            access_type: public_method
            serialized_name: keywords
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getKeywords
                setter: setKeywords
            type: string
        swatchImages:
            expose: true
            access_type: public_method
            serialized_name: swatchImages
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSwatchImages
                setter: setSwatchImages
            type: array<WalmartDSV\CleaningAndChemicalType\SwatchImagesAType\SwatchImageAType>
            xml_list:
                inline: false
                entry_name: swatchImage
                skip_when_empty: true
                namespace: 'http://walmart.com/'
