WalmartDSV\CuisineType:
    properties:
        cuisineValue:
            expose: true
            access_type: public_method
            serialized_name: cuisineValue
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCuisineValue
                setter: setCuisineValue
            xml_list:
                inline: true
                entry_name: cuisineValue
                namespace: 'http://walmart.com/'
            type: array<string>
