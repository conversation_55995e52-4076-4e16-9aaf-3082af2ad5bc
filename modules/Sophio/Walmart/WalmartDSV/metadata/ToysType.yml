WalmartDSV\ToysType:
    properties:
        additionalVariantAttributeNames:
            expose: true
            access_type: public_method
            serialized_name: additionalVariantAttributeNames
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAdditionalVariantAttributeNames
                setter: setAdditionalVariantAttributeNames
            type: array<string>
            xml_list:
                inline: false
                entry_name: additionalVariantAttributeName
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        shortDescription:
            expose: true
            access_type: public_method
            serialized_name: shortDescription
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getShortDescription
                setter: setShortDescription
            type: string
        keyFeatures:
            expose: true
            access_type: public_method
            serialized_name: keyFeatures
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getKeyFeatures
                setter: setKeyFeatures
            type: array<string>
            xml_list:
                inline: false
                entry_name: keyFeaturesValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        brand:
            expose: true
            access_type: public_method
            serialized_name: brand
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBrand
                setter: setBrand
            type: string
        manufacturer:
            expose: true
            access_type: public_method
            serialized_name: manufacturer
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getManufacturer
                setter: setManufacturer
            type: string
        manufacturerPartNumber:
            expose: true
            access_type: public_method
            serialized_name: manufacturerPartNumber
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getManufacturerPartNumber
                setter: setManufacturerPartNumber
            type: string
        modelNumber:
            expose: true
            access_type: public_method
            serialized_name: modelNumber
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getModelNumber
                setter: setModelNumber
            type: string
        inflexKitComponent:
            expose: true
            access_type: public_method
            serialized_name: inflexKitComponent
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getInflexKitComponent
                setter: setInflexKitComponent
            type: string
        multipackQuantity:
            expose: true
            access_type: public_method
            serialized_name: multipackQuantity
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMultipackQuantity
                setter: setMultipackQuantity
            type: int
        countPerPack:
            expose: true
            access_type: public_method
            serialized_name: countPerPack
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCountPerPack
                setter: setCountPerPack
            type: int
        count:
            expose: true
            access_type: public_method
            serialized_name: count
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCount
                setter: setCount
            type: string
        pieceCount:
            expose: true
            access_type: public_method
            serialized_name: pieceCount
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPieceCount
                setter: setPieceCount
            type: int
        mainImageUrl:
            expose: true
            access_type: public_method
            serialized_name: mainImageUrl
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMainImageUrl
                setter: setMainImageUrl
            type: string
        productSecondaryImageURL:
            expose: true
            access_type: public_method
            serialized_name: productSecondaryImageURL
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getProductSecondaryImageURL
                setter: setProductSecondaryImageURL
            type: array<string>
            xml_list:
                inline: false
                entry_name: productSecondaryImageURLValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        color:
            expose: true
            access_type: public_method
            serialized_name: color
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getColor
                setter: setColor
            type: WalmartDSV\ColorType
        colorCategory:
            expose: true
            access_type: public_method
            serialized_name: colorCategory
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getColorCategory
                setter: setColorCategory
            type: array<string>
            xml_list:
                inline: false
                entry_name: colorCategoryValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        gender:
            expose: true
            access_type: public_method
            serialized_name: gender
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getGender
                setter: setGender
            type: string
        size:
            expose: true
            access_type: public_method
            serialized_name: size
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSize
                setter: setSize
            type: string
        ageGroup:
            expose: true
            access_type: public_method
            serialized_name: ageGroup
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAgeGroup
                setter: setAgeGroup
            type: array<string>
            xml_list:
                inline: false
                entry_name: ageGroupValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        ageRange:
            expose: true
            access_type: public_method
            serialized_name: ageRange
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAgeRange
                setter: setAgeRange
            type: WalmartDSV\AgeRangeType
        targetAudience:
            expose: true
            access_type: public_method
            serialized_name: targetAudience
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getTargetAudience
                setter: setTargetAudience
            type: array<string>
            xml_list:
                inline: false
                entry_name: targetAudienceValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        educationalFocus:
            expose: true
            access_type: public_method
            serialized_name: educationalFocus
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getEducationalFocus
                setter: setEducationalFocus
            type: array<string>
            xml_list:
                inline: false
                entry_name: educationalFocus
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        skillLevel:
            expose: true
            access_type: public_method
            serialized_name: skillLevel
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSkillLevel
                setter: setSkillLevel
            type: string
        awardsWon:
            expose: true
            access_type: public_method
            serialized_name: awardsWon
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAwardsWon
                setter: setAwardsWon
            type: array<string>
            xml_list:
                inline: false
                entry_name: awardsWonValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        theme:
            expose: true
            access_type: public_method
            serialized_name: theme
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getTheme
                setter: setTheme
            type: WalmartDSV\ThemeType
        character:
            expose: true
            access_type: public_method
            serialized_name: character
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCharacter
                setter: setCharacter
            type: WalmartDSV\CharacterType
        activity:
            expose: true
            access_type: public_method
            serialized_name: activity
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getActivity
                setter: setActivity
            type: WalmartDSV\ActivityType
        globalBrandLicense:
            expose: true
            access_type: public_method
            serialized_name: globalBrandLicense
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getGlobalBrandLicense
                setter: setGlobalBrandLicense
            type: array<string>
            xml_list:
                inline: false
                entry_name: globalBrandLicenseValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        numberOfPlayers:
            expose: true
            access_type: public_method
            serialized_name: numberOfPlayers
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getNumberOfPlayers
                setter: setNumberOfPlayers
            type: WalmartDSV\NumberOfPlayersType
        assembledProductLength:
            expose: true
            access_type: public_method
            serialized_name: assembledProductLength
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductLength
                setter: setAssembledProductLength
            type: WalmartDSV\ToysType\AssembledProductLengthAType
        assembledProductWidth:
            expose: true
            access_type: public_method
            serialized_name: assembledProductWidth
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductWidth
                setter: setAssembledProductWidth
            type: WalmartDSV\ToysType\AssembledProductWidthAType
        assembledProductHeight:
            expose: true
            access_type: public_method
            serialized_name: assembledProductHeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductHeight
                setter: setAssembledProductHeight
            type: WalmartDSV\ToysType\AssembledProductHeightAType
        assembledProductWeight:
            expose: true
            access_type: public_method
            serialized_name: assembledProductWeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssembledProductWeight
                setter: setAssembledProductWeight
            type: WalmartDSV\ToysType\AssembledProductWeightAType
        variantGroupId:
            expose: true
            access_type: public_method
            serialized_name: variantGroupId
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVariantGroupId
                setter: setVariantGroupId
            type: string
        variantAttributeNames:
            expose: true
            access_type: public_method
            serialized_name: variantAttributeNames
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVariantAttributeNames
                setter: setVariantAttributeNames
            type: array<string>
            xml_list:
                inline: false
                entry_name: variantAttributeName
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        isPrimaryVariant:
            expose: true
            access_type: public_method
            serialized_name: isPrimaryVariant
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPrimaryVariant
                setter: setIsPrimaryVariant
            type: string
        isPrivateLabelOrUnbranded:
            expose: true
            access_type: public_method
            serialized_name: isPrivateLabelOrUnbranded
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPrivateLabelOrUnbranded
                setter: setIsPrivateLabelOrUnbranded
            type: string
        isProp65WarningRequired:
            expose: true
            access_type: public_method
            serialized_name: isProp65WarningRequired
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsProp65WarningRequired
                setter: setIsProp65WarningRequired
            type: string
        prop65WarningText:
            expose: true
            access_type: public_method
            serialized_name: prop65WarningText
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getProp65WarningText
                setter: setProp65WarningText
            type: string
        smallPartsWarnings:
            expose: true
            access_type: public_method
            serialized_name: smallPartsWarnings
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSmallPartsWarnings
                setter: setSmallPartsWarnings
            type: array<int>
            xml_list:
                inline: false
                entry_name: smallPartsWarning
                skip_when_empty: false
                namespace: 'http://walmart.com/'
        hasExpiration:
            expose: true
            access_type: public_method
            serialized_name: hasExpiration
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasExpiration
                setter: setHasExpiration
            type: string
        shelfLife:
            expose: true
            access_type: public_method
            serialized_name: shelfLife
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getShelfLife
                setter: setShelfLife
            type: WalmartDSV\ToysType\ShelfLifeAType
        hasIngredientList:
            expose: true
            access_type: public_method
            serialized_name: hasIngredientList
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasIngredientList
                setter: setHasIngredientList
            type: string
        ingredientListImage:
            expose: true
            access_type: public_method
            serialized_name: ingredientListImage
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIngredientListImage
                setter: setIngredientListImage
            type: string
        hasBatteries:
            expose: true
            access_type: public_method
            serialized_name: hasBatteries
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasBatteries
                setter: setHasBatteries
            type: string
        batteryTechnologyType:
            expose: true
            access_type: public_method
            serialized_name: batteryTechnologyType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getBatteryTechnologyType
                setter: setBatteryTechnologyType
            type: string
        isAerosol:
            expose: true
            access_type: public_method
            serialized_name: isAerosol
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsAerosol
                setter: setIsAerosol
            type: string
        isChemical:
            expose: true
            access_type: public_method
            serialized_name: isChemical
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsChemical
                setter: setIsChemical
            type: string
        compositeWoodCertificationCode:
            expose: true
            access_type: public_method
            serialized_name: compositeWoodCertificationCode
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCompositeWoodCertificationCode
                setter: setCompositeWoodCertificationCode
            type: int
        hasWarranty:
            expose: true
            access_type: public_method
            serialized_name: hasWarranty
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasWarranty
                setter: setHasWarranty
            type: string
        warrantyURL:
            expose: true
            access_type: public_method
            serialized_name: warrantyURL
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWarrantyURL
                setter: setWarrantyURL
            type: string
        warrantyText:
            expose: true
            access_type: public_method
            serialized_name: warrantyText
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWarrantyText
                setter: setWarrantyText
            type: string
        hasStateRestrictions:
            expose: true
            access_type: public_method
            serialized_name: hasStateRestrictions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHasStateRestrictions
                setter: setHasStateRestrictions
            type: string
        stateRestrictions:
            expose: true
            access_type: public_method
            serialized_name: stateRestrictions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getStateRestrictions
                setter: setStateRestrictions
            type: array<WalmartDSV\StateRestrictionType>
            xml_list:
                inline: false
                entry_name: stateRestriction
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        requiresTextileActLabeling:
            expose: true
            access_type: public_method
            serialized_name: requiresTextileActLabeling
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getRequiresTextileActLabeling
                setter: setRequiresTextileActLabeling
            type: string
        countryOfOriginTextiles:
            expose: true
            access_type: public_method
            serialized_name: countryOfOriginTextiles
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCountryOfOriginTextiles
                setter: setCountryOfOriginTextiles
            type: string
        fabricContent:
            expose: true
            access_type: public_method
            serialized_name: fabricContent
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFabricContent
                setter: setFabricContent
            type: array<WalmartDSV\FabricContentValueType>
            xml_list:
                inline: false
                entry_name: fabricContentValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        fabricCareInstructions:
            expose: true
            access_type: public_method
            serialized_name: fabricCareInstructions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFabricCareInstructions
                setter: setFabricCareInstructions
            type: array<string>
            xml_list:
                inline: false
                entry_name: fabricCareInstruction
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        isAssemblyRequired:
            expose: true
            access_type: public_method
            serialized_name: isAssemblyRequired
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsAssemblyRequired
                setter: setIsAssemblyRequired
            type: string
        assemblyInstructions:
            expose: true
            access_type: public_method
            serialized_name: assemblyInstructions
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAssemblyInstructions
                setter: setAssemblyInstructions
            type: string
        material:
            expose: true
            access_type: public_method
            serialized_name: material
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMaterial
                setter: setMaterial
            type: WalmartDSV\MaterialType
        finish:
            expose: true
            access_type: public_method
            serialized_name: finish
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFinish
                setter: setFinish
            type: string
        shape:
            expose: true
            access_type: public_method
            serialized_name: shape
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getShape
                setter: setShape
            type: string
        occasion:
            expose: true
            access_type: public_method
            serialized_name: occasion
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getOccasion
                setter: setOccasion
            type: WalmartDSV\OccasionType
        sport:
            expose: true
            access_type: public_method
            serialized_name: sport
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSport
                setter: setSport
            type: WalmartDSV\SportType
        hairColorCategory:
            expose: true
            access_type: public_method
            serialized_name: hairColorCategory
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getHairColorCategory
                setter: setHairColorCategory
            type: string
        skinTone:
            expose: true
            access_type: public_method
            serialized_name: skinTone
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSkinTone
                setter: setSkinTone
            type: string
        flavor:
            expose: true
            access_type: public_method
            serialized_name: flavor
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFlavor
                setter: setFlavor
            type: string
        animalType:
            expose: true
            access_type: public_method
            serialized_name: animalType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAnimalType
                setter: setAnimalType
            type: string
        vehicleType:
            expose: true
            access_type: public_method
            serialized_name: vehicleType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getVehicleType
                setter: setVehicleType
            type: string
        displayTechnology:
            expose: true
            access_type: public_method
            serialized_name: displayTechnology
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getDisplayTechnology
                setter: setDisplayTechnology
            type: string
        screenSize:
            expose: true
            access_type: public_method
            serialized_name: screenSize
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getScreenSize
                setter: setScreenSize
            type: WalmartDSV\ToysType\ScreenSizeAType
        isPowered:
            expose: true
            access_type: public_method
            serialized_name: isPowered
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsPowered
                setter: setIsPowered
            type: string
        powerType:
            expose: true
            access_type: public_method
            serialized_name: powerType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getPowerType
                setter: setPowerType
            type: string
        capacity:
            expose: true
            access_type: public_method
            serialized_name: capacity
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getCapacity
                setter: setCapacity
            type: string
        seatingCapacity:
            expose: true
            access_type: public_method
            serialized_name: seatingCapacity
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSeatingCapacity
                setter: setSeatingCapacity
            type: int
        minimumWeight:
            expose: true
            access_type: public_method
            serialized_name: minimumWeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMinimumWeight
                setter: setMinimumWeight
            type: WalmartDSV\ToysType\MinimumWeightAType
        maximumWeight:
            expose: true
            access_type: public_method
            serialized_name: maximumWeight
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMaximumWeight
                setter: setMaximumWeight
            type: WalmartDSV\ToysType\MaximumWeightAType
        maximumSpeed:
            expose: true
            access_type: public_method
            serialized_name: maximumSpeed
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMaximumSpeed
                setter: setMaximumSpeed
            type: WalmartDSV\ToysType\MaximumSpeedAType
        isTravelSize:
            expose: true
            access_type: public_method
            serialized_name: isTravelSize
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsTravelSize
                setter: setIsTravelSize
            type: string
        isInflatable:
            expose: true
            access_type: public_method
            serialized_name: isInflatable
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getIsInflatable
                setter: setIsInflatable
            type: string
        fillMaterial:
            expose: true
            access_type: public_method
            serialized_name: fillMaterial
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFillMaterial
                setter: setFillMaterial
            type: array<string>
            xml_list:
                inline: false
                entry_name: fillMaterialValue
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        makesNoise:
            expose: true
            access_type: public_method
            serialized_name: makesNoise
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getMakesNoise
                setter: setMakesNoise
            type: string
        sportsLeague:
            expose: true
            access_type: public_method
            serialized_name: sportsLeague
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSportsLeague
                setter: setSportsLeague
            type: WalmartDSV\SportsLeagueType
        sportsTeam:
            expose: true
            access_type: public_method
            serialized_name: sportsTeam
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSportsTeam
                setter: setSportsTeam
            type: WalmartDSV\SportsTeamType
        athlete:
            expose: true
            access_type: public_method
            serialized_name: athlete
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getAthlete
                setter: setAthlete
            type: WalmartDSV\AthleteType
        features:
            expose: true
            access_type: public_method
            serialized_name: features
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getFeatures
                setter: setFeatures
            type: array<string>
            xml_list:
                inline: false
                entry_name: feature
                skip_when_empty: true
                namespace: 'http://walmart.com/'
        keywords:
            expose: true
            access_type: public_method
            serialized_name: keywords
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getKeywords
                setter: setKeywords
            type: string
        swatchImages:
            expose: true
            access_type: public_method
            serialized_name: swatchImages
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getSwatchImages
                setter: setSwatchImages
            type: array<WalmartDSV\ToysType\SwatchImagesAType\SwatchImageAType>
            xml_list:
                inline: false
                entry_name: swatchImage
                skip_when_empty: true
                namespace: 'http://walmart.com/'
