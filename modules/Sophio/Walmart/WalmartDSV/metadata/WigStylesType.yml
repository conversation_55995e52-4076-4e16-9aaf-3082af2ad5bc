WalmartDSV\WigStylesType:
    properties:
        wigStyle:
            expose: true
            access_type: public_method
            serialized_name: wigStyle
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/'
            accessor:
                getter: getWigStyle
                setter: setWigStyle
            xml_list:
                inline: true
                entry_name: wigStyle
                namespace: 'http://walmart.com/'
            type: array<string>
