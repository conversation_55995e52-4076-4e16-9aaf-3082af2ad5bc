<?php

namespace Sophio\Walmart\Models;

use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Illuminate\Database\Eloquent\Model;

class Feed extends Model
{
    use CrudTrait;

    protected $table = 'walmart.feeds';
    protected $fillable = ['feed_settings','requestId','requestbatchid','type','feedid'];
    public function transactionlog()
    {
        return $this->hasMany(Transaction::class, 'object_id')->latest();
    }

    public function feeditem()
    {
        return $this->hasMany(FeedItem::class, 'feed_id');
    }

    public function check_status($crud = false)
    {
        return '<a class="btn btn-sm btn-link"  href="' . backpack_url('walmart/'.strtolower($this->type).'feed/checkstatus/' . $this->id . '') . '" data-toggle="tooltip" title="Check Feed Status"><i class="la la-refresh"></i> Check</a>';
    }
    public function getFeedItemsCounts($status)
    {
        return $this->feeditem()->where('status','=',$status)->count();
    }
    public function getWalmartIdCounts($exist=true)
    {
        if($exist==true)
        {
            return $this->feeditem()->where('itemid','>',0)->count();
        }else{
            return $this->feeditem()->where('itemid','=',0)->count();
        }

    }
}