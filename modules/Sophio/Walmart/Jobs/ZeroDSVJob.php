<?php

namespace So<PERSON>o\Walmart\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Sophio\Common\Models\FBS\Supplier;
use Sophio\FBSFeedExporter\Library\Actions\WalmartInventoryFeedFile;
use Sophio\Walmart\Library\GeneralFeeds;
use Sophio\Walmart\Library\RequestBody\DSVInventoryFeed;
use Sophio\Walmart\Models\DSV;
use Sophio\Walmart\Models\FeedItemInventoryHistory;

class ZeroDSVJob
    implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $settings;
    public function __construct($settings)
    {
        $this->settings = $settings;
    }
    public function handle()
    {
        $ii=0;
        $settings=[
            'request_params'=> ['feedType' => 'DSV_INVENTORY'],
            'type'=>'Inventory',
            'content_type'=>'application/json',
            'feed_delay'=>0,
            'supplier'=> $this->settings['supplier']
        ];
        $supplier = Supplier::find($this->settings['supplier']);
        $dsv = DSV::where('ship_node',$supplier->SETTINGS['WALMARTSHIPNODE'])->where('qty_input','>',0)->chunk(8000,
            function($rows)use($settings,&$ii,$supplier)
            {
                $settings['filename']='dataexport/288065/WAL_zeroed_inventory_'.$supplier->PK.'_'.  Carbon::today()->toDateString().'_'.$ii.'.json';
                $settings['request_id'] = 'fbs_inventory_zero_' .$supplier->PK.'_'. date("Ymdh");
                $object = new DSVInventoryFeed();
                $inventory=[];
                $mi=[];
                foreach ($rows as $row) {
                    $inventory[] = [
                        'availabilityCode' => 'AC',
                        'quantity' => 0,
                        'productId' => (string)$row->gtin,
                        'shipNode' => (string)$row->ship_node
                    ];
                    $mi[$row->gtin] = $row->sku;

                }
                $object->setData($inventory);
                (new WalmartInventoryFeedFile()) ($settings['filename'], $object);
                $invfeed = new GeneralFeeds();
                $invfeed->setSettings($settings);
                $feeddb = $invfeed->sendFeed($object, false);
                if ($feeddb && $feeddb->feedid != "" && $feeddb->feedid != null && $feeddb->feedid != 0) {

                    Log::channel('walmart')->error('Dispatching to later check' . $feeddb->feedid);
                    CheckGeneralFeed::dispatch($feeddb)->delay(now()->addMinutes(30));
                }
                $inserts = [];
                $toi = 0;
                DB::disableQueryLog();
                foreach ($object->getData() as $item) {

                    $inserts[] = [
                        'feed_id' => $feeddb->id,
                        'contactpk' =>  isset($sups[$item['shipNode']])?$sups[$item['shipNode']]->contactpk:999,
                        'supplier_pk' =>   isset($sups[$item['shipNode']])?$sups[$item['shipNode']]->PK:0,
                        'marketSellerSku' => $mi[$item['productId']],
                        'qty_avail' =>0
                    ];
                    $toi++;
                    if ($toi == 1000) {
                        FeedItemInventoryHistory::insert($inserts);
                        $toi = 0;
                        $inserts = [];
                    }
                }
                if (count($inserts) > 0) {
                    FeedItemInventoryHistory::insert($inserts);
                }
                DB::enableQueryLog();
                $ii++;
            });

    }
}