<?php

namespace Sophio\Walmart\Jobs;

use App\Exceptions\ExceptionMailAction;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Sophio\Walmart\Models\Feed;

class CheckGeneralFeed implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    public $feed;
    public $timeout = 0;
    public $tries = 1;
    public $setttings;
    public function __construct(Feed $feed,$settings=['tenant_db'=>'sophio_fbs'])
    {
        $this->feed = $feed;
        $this->setttings = $settings;
        $this->onQueue('longtasks');
    }
    public function handle()
    {
        Log::channel('walmart')->error('Check Feed'.$this->feed->id);
        \Config::set('tenant_db', 'sophio_fbs');
        config(['logging.default'=>'walmart']);
        $offset = 0;
        $delay_again = false;
        while ($offset < $this->feed->no_items) {
            $feed_manager = new \Sophio\Walmart\Library\GeneralFeeds();
            $feed_manager->setSettings($this->setttings);
            try {
                $feed_check  = $feed_manager->getFeedStatus($this->feed,true, $offset);
                if($offset==0 && $feed_check && isset($feed_check->feedStatus)) {
                    if( $feed_check->feedStatus=="INPROGRESS" ||  $feed_check->feedStatus=="RECEIVED" ||  $feed_check->feedStatus=="SENT") {
                        $delay_again = true;
                    }else {

                    }

                }
                $offset += 50;
            }catch (\Throwable $e) {
                $em = new ExceptionMailAction();
                $em($e);
                $delay_again = true;
                return false;
            }

        }

        if($delay_again==true) {
            CheckGeneralFeed::dispatch($this->feed)->delay(now()->addMinutes(120));
        }

    }

    public function tags()
    {
        return ['walmartcheckgeneralfeed_'.$this->feed->id,'walmartcheckgeneralfeed'];
    }
    public function failed( $ex)
    {
        \Log::error("CheckGeneralFeed failed with ".$ex->getMessage());
        $this->delete();
    }
}