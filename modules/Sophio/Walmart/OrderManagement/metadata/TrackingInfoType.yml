WalmartAPI\Order\TrackingInfoType:
    properties:
        shipDateTime:
            expose: true
            access_type: public_method
            serialized_name: shipDateTime
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getShipDateTime
                setter: setShipDateTime
            type: GoetasWebservices\Xsd\XsdToPhp\XMLSchema\DateTime
        carrierName:
            expose: true
            access_type: public_method
            serialized_name: carrierName
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getCarrierName
                setter: setCarrierName
            type: WalmartAPI\Order\CarrierNameType
        methodCode:
            expose: true
            access_type: public_method
            serialized_name: methodCode
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getMethodCode
                setter: setMethodCode
            type: string
        carrierMethodCode:
            expose: true
            access_type: public_method
            serialized_name: carrierMethodCode
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getCarrierMethodCode
                setter: setCarrierMethodCode
            type: int
        trackingNumber:
            expose: true
            access_type: public_method
            serialized_name: trackingNumber
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getTrackingNumber
                setter: setTrackingNumber
            type: string
        trackingURL:
            expose: true
            access_type: public_method
            serialized_name: trackingURL
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getTrackingURL
                setter: setTrackingURL
            type: string
