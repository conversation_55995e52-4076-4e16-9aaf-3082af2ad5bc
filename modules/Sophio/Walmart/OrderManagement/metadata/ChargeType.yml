WalmartAPI\Order\ChargeType:
    properties:
        chargeType:
            expose: true
            access_type: public_method
            serialized_name: chargeType
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getChargeType
                setter: setChargeType
            type: string
        chargeName:
            expose: true
            access_type: public_method
            serialized_name: chargeName
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getChargeName
                setter: setChargeName
            type: string
        chargeAmount:
            expose: true
            access_type: public_method
            serialized_name: chargeAmount
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getChargeAmount
                setter: setChargeAmount
            type: WalmartAPI\Order\MoneyType
        tax:
            expose: true
            access_type: public_method
            serialized_name: tax
            xml_element:
                cdata: false
                namespace: 'http://walmart.com/mp/v3/orders'
            accessor:
                getter: getTax
                setter: setTax
            type: WalmartAPI\Order\TaxType
