<?php

namespace WalmartAPI\Order\OrderShipment;

/**
 * Class representing OrderShipmentAType
 */
class OrderShipmentAType
{
    /**
     * @var \WalmartAPI\Order\ShippingLineType[] $orderLines
     */
    private $orderLines = null;

    /**
     * Adds as orderLine
     *
     * @return self
     * @param \WalmartAPI\Order\ShippingLineType $orderLine
     */
    public function addToOrderLines(\WalmartAPI\Order\ShippingLineType $orderLine)
    {
        $this->orderLines[] = $orderLine;
        return $this;
    }

    /**
     * isset orderLines
     *
     * @param int|string $index
     * @return bool
     */
    public function issetOrderLines($index)
    {
        return isset($this->orderLines[$index]);
    }

    /**
     * unset orderLines
     *
     * @param int|string $index
     * @return void
     */
    public function unsetOrderLines($index)
    {
        unset($this->orderLines[$index]);
    }

    /**
     * Gets as orderLines
     *
     * @return \WalmartAPI\Order\ShippingLineType[]
     */
    public function getOrderLines()
    {
        return $this->orderLines;
    }

    /**
     * Sets a new orderLines
     *
     * @param \WalmartAPI\Order\ShippingLineType[] $orderLines
     * @return self
     */
    public function setOrderLines(array $orderLines)
    {
        $this->orderLines = $orderLines;
        return $this;
    }
}

