<?php

namespace WalmartAPI\Order;

/**
 * Class representing OrderLineStatusesType
 *
 *
 * XSD Type: orderLineStatusesType
 */
class OrderLineStatusesType
{
    /**
     * @var \WalmartAPI\Order\OrderLineStatusType[] $orderLineStatus
     */
    private $orderLineStatus = [
        
    ];

    /**
     * Adds as orderLineStatus
     *
     * @return self
     * @param \WalmartAPI\Order\OrderLineStatusType $orderLineStatus
     */
    public function addToOrderLineStatus(\WalmartAPI\Order\OrderLineStatusType $orderLineStatus)
    {
        $this->orderLineStatus[] = $orderLineStatus;
        return $this;
    }

    /**
     * isset orderLineStatus
     *
     * @param int|string $index
     * @return bool
     */
    public function issetOrderLineStatus($index)
    {
        return isset($this->orderLineStatus[$index]);
    }

    /**
     * unset orderLineStatus
     *
     * @param int|string $index
     * @return void
     */
    public function unsetOrderLineStatus($index)
    {
        unset($this->orderLineStatus[$index]);
    }

    /**
     * Gets as orderLineStatus
     *
     * @return \WalmartAPI\Order\OrderLineStatusType[]
     */
    public function getOrderLineStatus()
    {
        return $this->orderLineStatus;
    }

    /**
     * Sets a new orderLineStatus
     *
     * @param \WalmartAPI\Order\OrderLineStatusType[] $orderLineStatus
     * @return self
     */
    public function setOrderLineStatus(array $orderLineStatus = null)
    {
        $this->orderLineStatus = $orderLineStatus;
        return $this;
    }
}

