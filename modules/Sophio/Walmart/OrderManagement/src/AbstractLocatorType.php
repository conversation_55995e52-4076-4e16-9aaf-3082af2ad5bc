<?php

namespace WalmartAPI\Order;

/**
 * Class representing AbstractLocatorType
 *
 *
 * XSD Type: abstractLocator
 */
class AbstractLocatorType
{
    /**
     * @var string $id
     */
    private $id = null;

    /**
     * Gets as id
     *
     * @return string
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Sets a new id
     *
     * @param string $id
     * @return self
     */
    public function setId($id)
    {
        $this->id = $id;
        return $this;
    }
}

