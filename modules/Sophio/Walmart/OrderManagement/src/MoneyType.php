<?php

namespace WalmartAPI\Order;

/**
 * Class representing MoneyType
 *
 *
 * XSD Type: moneyType
 */
class MoneyType
{
    /**
     * Represents the Currency of the price
     *
     * @var string $currency
     */
    private $currency = null;

    /**
     * Represents the price
     *
     * @var float $amount
     */
    private $amount = null;

    /**
     * Gets as currency
     *
     * Represents the Currency of the price
     *
     * @return string
     */
    public function getCurrency()
    {
        return $this->currency;
    }

    /**
     * Sets a new currency
     *
     * Represents the Currency of the price
     *
     * @param string $currency
     * @return self
     */
    public function setCurrency($currency)
    {
        $this->currency = $currency;
        return $this;
    }

    /**
     * Gets as amount
     *
     * Represents the price
     *
     * @return float
     */
    public function getAmount()
    {
        return $this->amount;
    }

    /**
     * Sets a new amount
     *
     * Represents the price
     *
     * @param float $amount
     * @return self
     */
    public function setAmount($amount)
    {
        $this->amount = $amount;
        return $this;
    }
}

