<?php

namespace Sophio\HomeDepot\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Sophio\HomeDepot\Models\Hio
 *
 * @property int $omsid
 * @property string $labeled_age
 * @property string $auto_fit_type
 * @property string $automotive_part_terminology
 * @property string|null $brand_name
 * @property string|null $bullet01
 * @property string|null $bullet02
 * @property string|null $bullet03
 * @property string|null $bullet04
 * @property string $shipped_anywhere
 * @property string|null $country_of_origin_name
 * @property string $contain_mercury
 * @property string $contain_wood
 * @property string $contain_electronic
 * @property string $shipFromState
 * @property string|null $package_depth
 * @property string|null $package_weight
 * @property string|null $package_height
 * @property string|null $package_width
 * @property string $excludedShipStates
 * @property string $ceds
 * @property int $freight_class
 * @property string|null $initial_market_retail_pricing
 * @property string|null $initial_cost
 * @property string $is_tdh_importer
 * @property string $is_chemical
 * @property string $is_liquid
 * @property string $is_pesticide
 * @property string $is_aerosol
 * @property string $is_battery
 * @property string $is_gas
 * @property string $new_version
 * @property string $textile_labeling
 * @property string $physical_homedepot
 * @property string $regulated_VOC_type
 * @property string $children_mouths
 * @property string $chlidren_under12
 * @property string|null $product_weight
 * @property string|null $marketing_copy
 * @property string|null $mfg_model_num
 * @property string|null $mfg_name
 * @property string|null $mfg_part_num
 * @property int $boxes_shipped
 * @property string|null $name
 * @property string $prop_65_required
 * @property string $pro_65_warning
 * @property int $sell_pkg_qty
 * @property string|null $qty_uom
 * @property string $sellable_on_homedepot
 * @property string $ship_from_CHUB
 * @property string $children_exposed
 * @property string $item_nestable
 * @property string|null $mfg_code
 * @property string|null $added
 * @property string|null $modified
 * @property string|null $sent
 * @property string|null $actiononchub
 * @property string|null $new_upc
 * @property string|null $catalog_part_number
 * @property string|null $aaiabrandid
 * @method static \Illuminate\Database\Eloquent\Builder|Hio newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Hio newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Hio query()
 * @mixin \Eloquent
 */
class Hio  extends Model
{
    protected $table = 'homedepot.homedepot_inventory_onboarding';
}