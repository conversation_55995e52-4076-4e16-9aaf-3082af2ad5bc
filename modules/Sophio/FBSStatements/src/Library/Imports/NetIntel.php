<?php

namespace So<PERSON>o\FBSStatements\Library\Imports;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Sophio\Common\Services\LogTrack;
use Sophio\FBSStatements\Library\ImportStatement;
use Sophio\FBSStatements\Library\Templates\PartsAuthority;
use Sophio\FBSStatements\Library\Templates\Sophio1;

class NetIntel extends ImportStatement
{
    public function retrieveFileFromNetIntel()
    {
        \Log::channel('fbsstatement')->error('Getting file from NetIntel');
        $storage = Storage::disk('netintel');
        $remote_filename = str_ireplace('MONTH', ucfirst(strtolower($this->statement->stmntdate->monthName)), $this->statement->supplier->SETTINGS['STATEMENTFILENAME']);
        LogTrack::add('Trying to get ' . $remote_filename, 'statement.' . $this->statement->supplier->PK);
        \Log::channel('fbsstatement')->error('Trying to get ' . $remote_filename. 'statement.' . $this->statement->supplier->PK);
        try {
            $exists = $storage->exists($remote_filename);
            LogTrack::add('Found ' . $remote_filename, 'statement.' . $this->statement->supplier->PK);
            \Log::channel('fbsstatement')->error('Found ' . $remote_filename. 'statement.' . $this->statement->supplier->PK);
            $date_on_ftp = \Illuminate\Support\Carbon::parse($storage->lastModified($remote_filename));
            if(\Illuminate\Support\Carbon::now()->diffInMonths($date_on_ftp)>6) {
                \Log::channel('fbsstatement')->error('Error  ' . $remote_filename. 'statement.' . $this->statement->supplier->PK.' is too old');
                LogTrack::add('Remote file '.$remote_filename.' is dated '.$date_on_ftp.'- it is too old, will skip!', 'statement.' . $this->statement->supplier->PK);
                $exists = false;
            }
        } catch (\ErrorException $e) {
            \Log::channel('fbsstatement')->error('Error  ' . $remote_filename. 'statement.' . $this->statement->supplier->PK.' '.$e->getMessage());
            $exists = false;
        } catch (\Exception $e) {
            \Log::channel('fbsstatement')->error('Error  ' . $remote_filename. 'statement.' . $this->statement->supplier->PK.' '.$e->getMessage());
            $exists = false;
        }
        $this->local_path = $this->getLocalPrefix() . Carbon::now()->year . '_' . strtolower($remote_filename);
        $this->statement->pathsupfil = $this->local_path;
        $this->statement->save();
        if ($exists) {
            try {
                if (!Storage::disk($this->storageDisk)->exists($this->getLocalPrefix())) {
                    \Illuminate\Support\Facades\File::makeDirectory(config('filesystems.disks.'.$this->storageDisk.'.root').'/'.dirname( $this->getLocalPrefix()),0755,true);
                }

                $file = $storage->get($remote_filename);
                if ($file != null) {
                    LogTrack::add('Downloading to ' . $this->local_path, 'statement.' . $this->statement->supplier->PK);
                    \Log::channel('fbsstatement')->info('Downloading to ' . $this->local_path. 'statement.' . $this->statement->supplier->PK);
                    if(Storage::disk($this->storageDisk)->exists($this->local_path)) {
                        try{
                            Storage::disk($this->storageDisk)->delete($this->local_path);
                        }catch (\Exception $e) {
                            LogTrack::add('Could not overwrite existing file.Halting.', 'statement.' . $this->statement->supplier->PK);
                            report($e);
                            return false;
                        }
                    }
                    Storage::disk($this->storageDisk)->put($this->local_path, $file);
                    return true;
                }else{
                    \Log::channel('fbsstatement')->error('Failed to get  ' . $remote_filename. 'statement.' . $this->statement->supplier->PK);
                    return false;
                }

            } catch (\Exception $e) {

                LogTrack::add('Internal copying error.Halting.', 'statement.' . $this->statement->supplier->PK);
                \Log::channel('fbsstatement')->error('Internal copying error.Halting.'.'statement.' . $this->statement->supplier->PK. ' '.$e->getMessage());
                report($e);
                return false;
            }
        }else{
            \Log::channel('fbsstatement')->error('Failed to find ' . $remote_filename. 'statement.' . $this->statement->supplier->PK);
        }
        return false;
    }

    public function retrieveFileFromUpload($upload_request)
    {
        try{
            $remote_filename = $upload_request->file('file')->getClientOriginalName();
            $this->local_path = $this->getLocalPrefix() . Carbon::now()->year . '_' . $remote_filename;
            if (!Storage::disk($this->storageDisk)->exists($this->getLocalPrefix())) {
                \Illuminate\Support\Facades\File::makeDirectory(config('filesystems.disks.'.$this->storageDisk.'.root').'/'.dirname( $this->getLocalPrefix()),0755,true);
            }
            Storage::disk($this->storageDisk)->putFileAs($this->getLocalPrefix(), $upload_request->file('file'),Carbon::now()->year . '_' .  $remote_filename);
            $this->statement->pathsupfil = $this->local_path;
            $this->statement->save();
            return true;
        }catch (\Exception $e) {
            report($e);
            return false;
        }
    }

    public function parse()
    {
        \Log::channel('fbsstatement')->info('Parsing');
        \Log::channel('fbsstatement')->info(print_r(LogTrack::getAll(),true));

        if(isset($this->statement->supplier->SETTINGS['STATEMENTTYPE']) && $this->statement->supplier->SETTINGS['STATEMENTTYPE']=='PAR') {
            $this->template = new PartsAuthority(Storage::disk($this->storageDisk)->path($this->local_path), $this->statement->stmntdate, $this->statement->pk);
        }else{
            $this->template = new Sophio1(Storage::disk($this->storageDisk)->path($this->local_path), $this->statement->stmntdate, $this->statement->pk);
        }

        $this->template->open();
        $this->template->setDatabase($this->database);
        $this->template->setProfile($this->statement->supplier);
        $this->template->processRows();
        $this->flushToDB();
        $this->template->close();
    }

}