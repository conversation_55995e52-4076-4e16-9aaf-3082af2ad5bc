<?php

namespace So<PERSON><PERSON>\FBSStatements\Library;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;
use Sophio\Common\Models\FBS\StatementHeader;
use Sophio\Common\Models\FBS\StatementRaw;
use Sophio\FBSStatements\Library\Reporters\ReporterInterface;
use Sophio\FBSStatements\Library\Reporters\Shippings;
use Sophio\FBSStatements\Library\Reporters\Suppliers;
use Sophio\FBSStatements\Library\Reporters\SupplierUAS;

/**
 * @property Collection<int, StatementRaw> $rawStatements
 */
class Reporter
{

    protected StatementHeader $statement;
    protected ReporterInterface $reportClass;

    public function __construct(StatementHeader $statementHeader,$forcetype=false)
    {
        $this->statement = $statementHeader;
        switch ($statementHeader->stmnttype) {
            case 'Purchase':
                $this->reportClass = new Suppliers($statementHeader);
                /*
                if($statementHeader->supplier->SUP==="UAS" && $forcetype==false) {
                    $this->reportClass = new SupplierUAS($statementHeader);
                }else{
                    $this->reportClass = new Suppliers($statementHeader);
                }
                */
                break;
            case 'Shipping':
                $this->reportClass = new Shippings($statementHeader);
                break;
        }
    }

    public function calculate()
    {
        return $this->reportClass->calculate();
    }

    public function calculateAndSaveHTMLReport()
    {
        $report = $this->calculate();

         switch (  $this->statement->supplier->SUP) {
            case 'WHD':case 'PAR':case 'UAS':
                $this->statement->results = view('admin.fbs.statement.report_partial', [
                    'supplier' =>  $this->statement->supplier,
                    'reporter' => $report,
                    'import_file' => Storage::disk('fbs')->url($this->statement->pathsupfil),
                    'statement' =>    $this->statement,]);
                break;
            case 'STA':
            case 'FDX':
             case 'UPS':
            $this->statement->results = view('admin.fbs.statement.report_shipper_partial', [
                'supplier' =>  $this->statement->supplier,
                'reporter' => $report,
                'import_file' => Storage::disk('fbs')->url($this->statement->pathsupfil),
                'statement' =>    $this->statement,]);
                break;
        }

        $this->statement->save();
    }

    public function getStatement()
    {
        return $this->statement;
    }
}