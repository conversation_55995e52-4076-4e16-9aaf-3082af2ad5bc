SparkLinkAPI\StockCheckResponse\Header\HeaderAType:
    properties:
        account:
            expose: true
            access_type: public_method
            serialized_name: account
            accessor:
                getter: getAccount
                setter: setAccount
            xml_attribute: true
            type: string
        pwd:
            expose: true
            access_type: public_method
            serialized_name: pwd
            accessor:
                getter: getPwd
                setter: setPwd
            xml_attribute: true
            type: string
        ordertype:
            expose: true
            access_type: public_method
            serialized_name: Ordertype
            accessor:
                getter: getOrdertype
                setter: setOrdertype
            xml_attribute: true
            type: string
        discountcat:
            expose: true
            access_type: public_method
            serialized_name: discountcat
            accessor:
                getter: getDiscountcat
                setter: setDiscountcat
            xml_attribute: true
            type: string
        alternateflag:
            expose: true
            access_type: public_method
            serialized_name: alternateflag
            accessor:
                getter: getAlternateflag
                setter: setAlternateflag
            xml_attribute: true
            type: string
        branch:
            expose: true
            access_type: public_method
            serialized_name: branch
            accessor:
                getter: getBranch
                setter: setBranch
            xml_attribute: true
            type: string
        delmethod:
            expose: true
            access_type: public_method
            serialized_name: delmethod
            accessor:
                getter: getDelmethod
                setter: setDelmethod
            xml_attribute: true
            type: string
        errcode:
            expose: true
            access_type: public_method
            serialized_name: errcode
            accessor:
                getter: getErrcode
                setter: setErrcode
            xml_attribute: true
            type: string
        errmsg:
            expose: true
            access_type: public_method
            serialized_name: errmsg
            accessor:
                getter: getErrmsg
                setter: setErrmsg
            xml_attribute: true
            type: string
        area:
            expose: true
            access_type: public_method
            serialized_name: area
            accessor:
                getter: getArea
                setter: setArea
            xml_attribute: true
            type: string
        familyprflag:
            expose: true
            access_type: public_method
            serialized_name: familyprflag
            accessor:
                getter: getFamilyprflag
                setter: setFamilyprflag
            xml_attribute: true
            type: string
        currency:
            expose: true
            access_type: public_method
            serialized_name: currency
            accessor:
                getter: getCurrency
                setter: setCurrency
            xml_attribute: true
            type: string
        routing:
            expose: true
            access_type: public_method
            serialized_name: routing
            xml_element:
                cdata: false
            accessor:
                getter: getRouting
                setter: setRouting
            xml_list:
                inline: true
                entry_name: routing
            type: array<SparkLinkAPI\StockCheckResponse\Routing>
        shipto:
            expose: true
            access_type: public_method
            serialized_name: shipto
            xml_element:
                cdata: false
            accessor:
                getter: getShipto
                setter: setShipto
            xml_list:
                inline: true
                entry_name: shipto
            type: array<SparkLinkAPI\StockCheckResponse\Shipto>
        billto:
            expose: true
            access_type: public_method
            serialized_name: billto
            xml_element:
                cdata: false
            accessor:
                getter: getBillto
                setter: setBillto
            xml_list:
                inline: true
                entry_name: billto
            type: array<SparkLinkAPI\StockCheckResponse\Billto>
        promotions:
            expose: true
            access_type: public_method
            serialized_name: promotions
            xml_element:
                cdata: false
            accessor:
                getter: getPromotions
                setter: setPromotions
            xml_list:
                inline: true
                entry_name: promotions
            type: array<SparkLinkAPI\StockCheckResponse\Promotions>
