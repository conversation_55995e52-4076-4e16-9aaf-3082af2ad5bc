SparkLinkAPI\OrderStatusSummaryRequest\Routing\RoutingAType:
    properties:
        supplier:
            expose: true
            access_type: public_method
            serialized_name: supplier
            accessor:
                getter: getSupplier
                setter: setSupplier
            xml_attribute: true
            type: string
        rid:
            expose: true
            access_type: public_method
            serialized_name: rid
            accessor:
                getter: getRid
                setter: setRid
            xml_attribute: true
            type: string
        buyerId:
            expose: true
            access_type: public_method
            serialized_name: buyer_id
            accessor:
                getter: getBuyerId
                setter: setBuyerId
            xml_attribute: true
            type: string
        clientId:
            expose: true
            access_type: public_method
            serialized_name: client_id
            accessor:
                getter: getClientId
                setter: setClientId
            xml_attribute: true
            type: string
        sellerId:
            expose: true
            access_type: public_method
            serialized_name: seller_id
            accessor:
                getter: getSellerId
                setter: setSellerId
            xml_attribute: true
            type: string
