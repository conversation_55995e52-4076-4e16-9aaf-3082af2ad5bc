<?xml version="1.0" encoding="utf-8"?>
<xs:schema elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="SparkLink">
        <xs:complexType>
            <xs:sequence>
                <xs:element ref="orderstatdetconf" />
            </xs:sequence>
            <xs:attribute name="Rev" type="xs:string" use="required" />
            <xs:attribute name="TransId" type="xs:string" use="required" />
            <xs:attribute name="environment" type="xs:string" use="required" />
            <xs:attribute name="lang" type="xs:string" use="required" />
        </xs:complexType>
    </xs:element>
    <xs:element name="orderstatdetconf">
        <xs:complexType>
            <xs:sequence>
                <xs:element ref="orderheader" />
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="orderheader">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" maxOccurs="1" ref="promotions" />
                <xs:element minOccurs="0" maxOccurs="unbounded" ref="buyer" />
                <xs:element minOccurs="1" maxOccurs="unbounded" ref="part" />
            </xs:sequence>
            <xs:attribute name="account" type="xs:string" use="required" />
            <xs:attribute name="pwd" type="xs:string" use="required" />
            <xs:attribute name="errmsg" type="xs:string" />
            <xs:attribute name="errcode" type="xs:string" use="required" />
            <xs:attribute name="type" type="xs:string" />
            <xs:attribute name="lineitems" type="xs:string" use="required" />
            <xs:attribute name="branch" type="xs:string" use="required" />
            <xs:attribute name="dateentered" type="xs:string" use="required" />
            <xs:attribute name="dateinvoiced" type="xs:string" />
            <xs:attribute name="invoice" type="xs:string" />
            <xs:attribute name="orderno" type="xs:string" use="required" />
            <xs:attribute name="ponumber" type="xs:string" />
            <xs:attribute name="shipadd1" type="xs:string" />
            <xs:attribute name="shipadd2" type="xs:string" />
            <xs:attribute name="shipadd3" type="xs:string" />
            <xs:attribute name="shipadd4" type="xs:string" />
            <xs:attribute name="shipmethod" type="xs:string" />
            <xs:attribute name="status" type="xs:string" use="required" />
            <xs:attribute name="terms" type="xs:string" />
            <xs:attribute name="total" type="xs:string" use="required" />
            <xs:attribute name="coretotal" type="xs:string" use="required" />
            <xs:attribute name="comment" type="xs:string" />
            <xs:attribute name="area" type="xs:string" />
            <xs:attribute name="timeentered" type="xs:string" />
            <xs:attribute name="timeinvoiced" type="xs:string" />
            <xs:attribute name="delcost" type="xs:string" />
            <xs:attribute name="tax" type="xs:string" />
            <xs:attribute name="fees" type="xs:string" />
            <xs:attribute name="total_fet" type="xs:string" />
            <xs:attribute name="currency" type="xs:string" />
            <xs:attribute name="wonumber" type="xs:string" />
            <xs:anyAttribute processContents="skip"/>
        </xs:complexType>
    </xs:element>

    <xs:element name="part">
        <xs:complexType>
            <xs:attribute name="linenum" type="xs:string" use="required" />
            <xs:attribute name="linecode" type="xs:string" use="required" />
            <xs:attribute name="partno" type="xs:string" use="required" />
            <xs:attribute name="description" type="xs:string" use="required" />
            <xs:attribute name="list" type="xs:string" use="required" />
            <xs:attribute name="cost" type="xs:string" use="required" />
            <xs:attribute name="core" type="xs:string" use="required" />
            <xs:attribute name="qtyord" type="xs:string" use="required" />
            <xs:attribute name="qtyship" type="xs:string" use="required" />
            <xs:attribute name="qtyback" type="xs:string" use="required" />
            <xs:attribute name="fet" type="xs:string" />
            <xs:attribute name="fee" type="xs:string" />
            <xs:attribute name="tax" type="xs:string" />
            <xs:attribute name="UPC" type="xs:string" />
            <xs:anyAttribute processContents="skip"/>
        </xs:complexType>
    </xs:element>

    <xs:element name="buyer">
        <xs:complexType>
            <xs:attribute name="key" type="xs:string" use="required" />
            <xs:attribute name="value" type="xs:string" use="required" />
        </xs:complexType>
    </xs:element>

    <xs:element name="promotions">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" maxOccurs="unbounded" name="promotion">
                    <xs:complexType>
                        <xs:attribute name="name" type="xs:string" use="required" />
                        <xs:attribute name="linenum" type="xs:string" use="required" />
                        <xs:attribute name="savings" type="xs:string" use="required" />
                        <xs:attribute name="code" type="xs:string" use="required" />
                        <xs:attribute name="type" type="xs:string" use="optional" />
                        <xs:attribute name="shortdesc" type="xs:string" use="required" />
                        <xs:attribute name="longdesc" type="xs:string" use="optional" />
                        <xs:attribute name="errcode" type="xs:string" use="required" />
                        <xs:attribute name="errmsg" type="xs:string" use="required" />
                        <xs:anyAttribute processContents="skip"/>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
            <xs:attribute name="totalsavings" type="xs:string" use="required" />
            <xs:anyAttribute processContents="skip"/>
        </xs:complexType>
    </xs:element>
</xs:schema>