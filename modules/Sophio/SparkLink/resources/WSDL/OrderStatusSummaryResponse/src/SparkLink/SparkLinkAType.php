<?php

namespace SparkLinkAPI\OrderStatusSummaryResponse\SparkLink;

/**
 * Class representing SparkLinkAType
 */
class SparkLinkAType
{
    /**
     * @var string $rev
     */
    private $rev = null;

    /**
     * @var string $transId
     */
    private $transId = null;

    /**
     * @var string $environment
     */
    private $environment = null;

    /**
     * @var string $lang
     */
    private $lang = null;

    /**
     * @var \SparkLinkAPI\OrderStatusSummaryResponse\Orderstatconf $orderstatconf
     */
    private $orderstatconf = null;

    /**
     * Gets as rev
     *
     * @return string
     */
    public function getRev()
    {
        return $this->rev;
    }

    /**
     * Sets a new rev
     *
     * @param string $rev
     * @return self
     */
    public function setRev($rev)
    {
        $this->rev = $rev;
        return $this;
    }

    /**
     * Gets as transId
     *
     * @return string
     */
    public function getTransId()
    {
        return $this->transId;
    }

    /**
     * Sets a new transId
     *
     * @param string $transId
     * @return self
     */
    public function setTransId($transId)
    {
        $this->transId = $transId;
        return $this;
    }

    /**
     * Gets as environment
     *
     * @return string
     */
    public function getEnvironment()
    {
        return $this->environment;
    }

    /**
     * Sets a new environment
     *
     * @param string $environment
     * @return self
     */
    public function setEnvironment($environment)
    {
        $this->environment = $environment;
        return $this;
    }

    /**
     * Gets as lang
     *
     * @return string
     */
    public function getLang()
    {
        return $this->lang;
    }

    /**
     * Sets a new lang
     *
     * @param string $lang
     * @return self
     */
    public function setLang($lang)
    {
        $this->lang = $lang;
        return $this;
    }

    /**
     * Gets as orderstatconf
     *
     * @return \SparkLinkAPI\OrderStatusSummaryResponse\Orderstatconf
     */
    public function getOrderstatconf()
    {
        return $this->orderstatconf;
    }

    /**
     * Sets a new orderstatconf
     *
     * @param \SparkLinkAPI\OrderStatusSummaryResponse\Orderstatconf $orderstatconf
     * @return self
     */
    public function setOrderstatconf(\SparkLinkAPI\OrderStatusSummaryResponse\Orderstatconf $orderstatconf)
    {
        $this->orderstatconf = $orderstatconf;
        return $this;
    }
}

