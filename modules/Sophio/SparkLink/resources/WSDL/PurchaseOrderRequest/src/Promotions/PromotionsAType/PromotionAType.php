<?php

namespace SparkLinkAPI\PurchaseOrderRequest\Promotions\PromotionsAType;

/**
 * Class representing PromotionAType
 */
class PromotionAType
{
    /**
     * @var string $lineno
     */
    private $lineno = null;

    /**
     * @var string $code
     */
    private $code = null;

    /**
     * Gets as lineno
     *
     * @return string
     */
    public function getLineno()
    {
        return $this->lineno;
    }

    /**
     * Sets a new lineno
     *
     * @param string $lineno
     * @return self
     */
    public function setLineno($lineno)
    {
        $this->lineno = $lineno;
        return $this;
    }

    /**
     * Gets as code
     *
     * @return string
     */
    public function getCode()
    {
        return $this->code;
    }

    /**
     * Sets a new code
     *
     * @param string $code
     * @return self
     */
    public function setCode($code)
    {
        $this->code = $code;
        return $this;
    }
}

