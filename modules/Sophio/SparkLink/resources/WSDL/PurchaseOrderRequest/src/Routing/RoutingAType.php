<?php

namespace SparkLinkAPI\PurchaseOrderRequest\Routing;

/**
 * Class representing RoutingAType
 */
class RoutingAType
{
    /**
     * @var string $supplier
     */
    private $supplier = null;

    /**
     * @var string $rid
     */
    private $rid = null;

    /**
     * @var string $sellerId
     */
    private $sellerId = null;

    /**
     * @var string $buyerId
     */
    private $buyerId = null;

    /**
     * @var string $clientId
     */
    private $clientId = null;

    /**
     * Gets as supplier
     *
     * @return string
     */
    public function getSupplier()
    {
        return $this->supplier;
    }

    /**
     * Sets a new supplier
     *
     * @param string $supplier
     * @return self
     */
    public function setSupplier($supplier)
    {
        $this->supplier = $supplier;
        return $this;
    }

    /**
     * Gets as rid
     *
     * @return string
     */
    public function getRid()
    {
        return $this->rid;
    }

    /**
     * Sets a new rid
     *
     * @param string $rid
     * @return self
     */
    public function setRid($rid)
    {
        $this->rid = $rid;
        return $this;
    }

    /**
     * Gets as sellerId
     *
     * @return string
     */
    public function getSellerId()
    {
        return $this->sellerId;
    }

    /**
     * Sets a new sellerId
     *
     * @param string $sellerId
     * @return self
     */
    public function setSellerId($sellerId)
    {
        $this->sellerId = $sellerId;
        return $this;
    }

    /**
     * Gets as buyerId
     *
     * @return string
     */
    public function getBuyerId()
    {
        return $this->buyerId;
    }

    /**
     * Sets a new buyerId
     *
     * @param string $buyerId
     * @return self
     */
    public function setBuyerId($buyerId)
    {
        $this->buyerId = $buyerId;
        return $this;
    }

    /**
     * Gets as clientId
     *
     * @return string
     */
    public function getClientId()
    {
        return $this->clientId;
    }

    /**
     * Sets a new clientId
     *
     * @param string $clientId
     * @return self
     */
    public function setClientId($clientId)
    {
        $this->clientId = $clientId;
        return $this;
    }
}

