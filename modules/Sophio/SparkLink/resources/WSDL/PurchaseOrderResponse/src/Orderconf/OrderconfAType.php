<?php

namespace SparkLinkAPI\PurchaseOrderResponse\Orderconf;

/**
 * Class representing OrderconfAType
 */
class OrderconfAType
{
    /**
     * @var \SparkLinkAPI\PurchaseOrderResponse\Header[] $header
     */
    private $header = [
        
    ];

    /**
     * @var \SparkLinkAPI\PurchaseOrderResponse\Part[] $part
     */
    private $part = [
        
    ];

    /**
     * @var \SparkLinkAPI\PurchaseOrderResponse\Fee[] $fee
     */
    private $fee = [
        
    ];

    /**
     * Adds as header
     *
     * @return self
     * @param \SparkLinkAPI\PurchaseOrderResponse\Header $header
     */
    public function addToHeader(\SparkLinkAPI\PurchaseOrderResponse\Header $header)
    {
        $this->header[] = $header;
        return $this;
    }

    /**
     * isset header
     *
     * @param int|string $index
     * @return bool
     */
    public function issetHeader($index)
    {
        return isset($this->header[$index]);
    }

    /**
     * unset header
     *
     * @param int|string $index
     * @return void
     */
    public function unsetHeader($index)
    {
        unset($this->header[$index]);
    }

    /**
     * Gets as header
     *
     * @return \SparkLinkAPI\PurchaseOrderResponse\Header[]
     */
    public function getHeader()
    {
        return $this->header;
    }

    /**
     * Sets a new header
     *
     * @param \SparkLinkAPI\PurchaseOrderResponse\Header[] $header
     * @return self
     */
    public function setHeader(array $header = null)
    {
        $this->header = $header;
        return $this;
    }

    /**
     * Adds as part
     *
     * @return self
     * @param \SparkLinkAPI\PurchaseOrderResponse\Part $part
     */
    public function addToPart(\SparkLinkAPI\PurchaseOrderResponse\Part $part)
    {
        $this->part[] = $part;
        return $this;
    }

    /**
     * isset part
     *
     * @param int|string $index
     * @return bool
     */
    public function issetPart($index)
    {
        return isset($this->part[$index]);
    }

    /**
     * unset part
     *
     * @param int|string $index
     * @return void
     */
    public function unsetPart($index)
    {
        unset($this->part[$index]);
    }

    /**
     * Gets as part
     *
     * @return \SparkLinkAPI\PurchaseOrderResponse\Part[]
     */
    public function getPart()
    {
        return $this->part;
    }

    /**
     * Sets a new part
     *
     * @param \SparkLinkAPI\PurchaseOrderResponse\Part[] $part
     * @return self
     */
    public function setPart(array $part = null)
    {
        $this->part = $part;
        return $this;
    }

    /**
     * Adds as fee
     *
     * @return self
     * @param \SparkLinkAPI\PurchaseOrderResponse\Fee $fee
     */
    public function addToFee(\SparkLinkAPI\PurchaseOrderResponse\Fee $fee)
    {
        $this->fee[] = $fee;
        return $this;
    }

    /**
     * isset fee
     *
     * @param int|string $index
     * @return bool
     */
    public function issetFee($index)
    {
        return isset($this->fee[$index]);
    }

    /**
     * unset fee
     *
     * @param int|string $index
     * @return void
     */
    public function unsetFee($index)
    {
        unset($this->fee[$index]);
    }

    /**
     * Gets as fee
     *
     * @return \SparkLinkAPI\PurchaseOrderResponse\Fee[]
     */
    public function getFee()
    {
        return $this->fee;
    }

    /**
     * Sets a new fee
     *
     * @param \SparkLinkAPI\PurchaseOrderResponse\Fee[] $fee
     * @return self
     */
    public function setFee(array $fee = null)
    {
        $this->fee = $fee;
        return $this;
    }
}

