<?php

namespace SparkLinkAPI\PartSearchResponse\SparkLink\SparkLinkAType\PartsearchrespAType;

/**
 * Class representing PartAType
 */
class PartAType
{
    /**
     * @var string $linenum
     */
    private $linenum = null;

    /**
     * @var string $linecode
     */
    private $linecode = null;

    /**
     * @var string $partno
     */
    private $partno = null;

    /**
     * @var string $desc
     */
    private $desc = null;

    /**
     * @var string $minqty
     */
    private $minqty = null;

    /**
     * @var string $uom
     */
    private $uom = null;

    /**
     * @var string $qtyavail
     */
    private $qtyavail = null;

    /**
     * @var string $bin1
     */
    private $bin1 = null;

    /**
     * @var string $bin2
     */
    private $bin2 = null;

    /**
     * @var string $bin3
     */
    private $bin3 = null;

    /**
     * @var string $specialstatus
     */
    private $specialstatus = null;

    /**
     * @var string $class
     */
    private $class = null;

    /**
     * @var string $errcode
     */
    private $errcode = null;

    /**
     * @var string $errmsg
     */
    private $errmsg = null;

    /**
     * Gets as linenum
     *
     * @return string
     */
    public function getLinenum()
    {
        return $this->linenum;
    }

    /**
     * Sets a new linenum
     *
     * @param string $linenum
     * @return self
     */
    public function setLinenum($linenum)
    {
        $this->linenum = $linenum;
        return $this;
    }

    /**
     * Gets as linecode
     *
     * @return string
     */
    public function getLinecode()
    {
        return $this->linecode;
    }

    /**
     * Sets a new linecode
     *
     * @param string $linecode
     * @return self
     */
    public function setLinecode($linecode)
    {
        $this->linecode = $linecode;
        return $this;
    }

    /**
     * Gets as partno
     *
     * @return string
     */
    public function getPartno()
    {
        return $this->partno;
    }

    /**
     * Sets a new partno
     *
     * @param string $partno
     * @return self
     */
    public function setPartno($partno)
    {
        $this->partno = $partno;
        return $this;
    }

    /**
     * Gets as desc
     *
     * @return string
     */
    public function getDesc()
    {
        return $this->desc;
    }

    /**
     * Sets a new desc
     *
     * @param string $desc
     * @return self
     */
    public function setDesc($desc)
    {
        $this->desc = $desc;
        return $this;
    }

    /**
     * Gets as minqty
     *
     * @return string
     */
    public function getMinqty()
    {
        return $this->minqty;
    }

    /**
     * Sets a new minqty
     *
     * @param string $minqty
     * @return self
     */
    public function setMinqty($minqty)
    {
        $this->minqty = $minqty;
        return $this;
    }

    /**
     * Gets as uom
     *
     * @return string
     */
    public function getUom()
    {
        return $this->uom;
    }

    /**
     * Sets a new uom
     *
     * @param string $uom
     * @return self
     */
    public function setUom($uom)
    {
        $this->uom = $uom;
        return $this;
    }

    /**
     * Gets as qtyavail
     *
     * @return string
     */
    public function getQtyavail()
    {
        return $this->qtyavail;
    }

    /**
     * Sets a new qtyavail
     *
     * @param string $qtyavail
     * @return self
     */
    public function setQtyavail($qtyavail)
    {
        $this->qtyavail = $qtyavail;
        return $this;
    }

    /**
     * Gets as bin1
     *
     * @return string
     */
    public function getBin1()
    {
        return $this->bin1;
    }

    /**
     * Sets a new bin1
     *
     * @param string $bin1
     * @return self
     */
    public function setBin1($bin1)
    {
        $this->bin1 = $bin1;
        return $this;
    }

    /**
     * Gets as bin2
     *
     * @return string
     */
    public function getBin2()
    {
        return $this->bin2;
    }

    /**
     * Sets a new bin2
     *
     * @param string $bin2
     * @return self
     */
    public function setBin2($bin2)
    {
        $this->bin2 = $bin2;
        return $this;
    }

    /**
     * Gets as bin3
     *
     * @return string
     */
    public function getBin3()
    {
        return $this->bin3;
    }

    /**
     * Sets a new bin3
     *
     * @param string $bin3
     * @return self
     */
    public function setBin3($bin3)
    {
        $this->bin3 = $bin3;
        return $this;
    }

    /**
     * Gets as specialstatus
     *
     * @return string
     */
    public function getSpecialstatus()
    {
        return $this->specialstatus;
    }

    /**
     * Sets a new specialstatus
     *
     * @param string $specialstatus
     * @return self
     */
    public function setSpecialstatus($specialstatus)
    {
        $this->specialstatus = $specialstatus;
        return $this;
    }

    /**
     * Gets as class
     *
     * @return string
     */
    public function getClass()
    {
        return $this->class;
    }

    /**
     * Sets a new class
     *
     * @param string $class
     * @return self
     */
    public function setClass($class)
    {
        $this->class = $class;
        return $this;
    }

    /**
     * Gets as errcode
     *
     * @return string
     */
    public function getErrcode()
    {
        return $this->errcode;
    }

    /**
     * Sets a new errcode
     *
     * @param string $errcode
     * @return self
     */
    public function setErrcode($errcode)
    {
        $this->errcode = $errcode;
        return $this;
    }

    /**
     * Gets as errmsg
     *
     * @return string
     */
    public function getErrmsg()
    {
        return $this->errmsg;
    }

    /**
     * Sets a new errmsg
     *
     * @param string $errmsg
     * @return self
     */
    public function setErrmsg($errmsg)
    {
        $this->errmsg = $errmsg;
        return $this;
    }
}

