<?php

namespace SparkLinkAPI\PartSearchResponse\SparkLink\SparkLinkAType\PartsearchrespAType;

/**
 * Class representing HeaderAType
 */
class HeaderAType
{
    /**
     * @var string $account
     */
    private $account = null;

    /**
     * @var string $pwd
     */
    private $pwd = null;

    /**
     * @var string $branch
     */
    private $branch = null;

    /**
     * @var string $errcode
     */
    private $errcode = null;

    /**
     * @var string $errmsg
     */
    private $errmsg = null;

    /**
     * @var string $query
     */
    private $query = null;

    /**
     * @var string $area
     */
    private $area = null;

    /**
     * @var \SparkLinkAPI\PartSearchResponse\SparkLink\SparkLinkAType\PartsearchrespAType\HeaderAType\RoutingAType $routing
     */
    private $routing = null;

    /**
     * Gets as account
     *
     * @return string
     */
    public function getAccount()
    {
        return $this->account;
    }

    /**
     * Sets a new account
     *
     * @param string $account
     * @return self
     */
    public function setAccount($account)
    {
        $this->account = $account;
        return $this;
    }

    /**
     * Gets as pwd
     *
     * @return string
     */
    public function getPwd()
    {
        return $this->pwd;
    }

    /**
     * Sets a new pwd
     *
     * @param string $pwd
     * @return self
     */
    public function setPwd($pwd)
    {
        $this->pwd = $pwd;
        return $this;
    }

    /**
     * Gets as branch
     *
     * @return string
     */
    public function getBranch()
    {
        return $this->branch;
    }

    /**
     * Sets a new branch
     *
     * @param string $branch
     * @return self
     */
    public function setBranch($branch)
    {
        $this->branch = $branch;
        return $this;
    }

    /**
     * Gets as errcode
     *
     * @return string
     */
    public function getErrcode()
    {
        return $this->errcode;
    }

    /**
     * Sets a new errcode
     *
     * @param string $errcode
     * @return self
     */
    public function setErrcode($errcode)
    {
        $this->errcode = $errcode;
        return $this;
    }

    /**
     * Gets as errmsg
     *
     * @return string
     */
    public function getErrmsg()
    {
        return $this->errmsg;
    }

    /**
     * Sets a new errmsg
     *
     * @param string $errmsg
     * @return self
     */
    public function setErrmsg($errmsg)
    {
        $this->errmsg = $errmsg;
        return $this;
    }

    /**
     * Gets as query
     *
     * @return string
     */
    public function getQuery()
    {
        return $this->query;
    }

    /**
     * Sets a new query
     *
     * @param string $query
     * @return self
     */
    public function setQuery($query)
    {
        $this->query = $query;
        return $this;
    }

    /**
     * Gets as area
     *
     * @return string
     */
    public function getArea()
    {
        return $this->area;
    }

    /**
     * Sets a new area
     *
     * @param string $area
     * @return self
     */
    public function setArea($area)
    {
        $this->area = $area;
        return $this;
    }

    /**
     * Gets as routing
     *
     * @return \SparkLinkAPI\PartSearchResponse\SparkLink\SparkLinkAType\PartsearchrespAType\HeaderAType\RoutingAType
     */
    public function getRouting()
    {
        return $this->routing;
    }

    /**
     * Sets a new routing
     *
     * @param \SparkLinkAPI\PartSearchResponse\SparkLink\SparkLinkAType\PartsearchrespAType\HeaderAType\RoutingAType $routing
     * @return self
     */
    public function setRouting(\SparkLinkAPI\PartSearchResponse\SparkLink\SparkLinkAType\PartsearchrespAType\HeaderAType\RoutingAType $routing)
    {
        $this->routing = $routing;
        return $this;
    }
}

