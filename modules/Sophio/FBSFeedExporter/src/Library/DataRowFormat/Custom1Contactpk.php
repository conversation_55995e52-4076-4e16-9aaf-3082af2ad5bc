<?php

namespace Sophio\FBSFeedExporter\Library\DataRowFormat;
/**
 * UNUSED
 */
class Custom1Contactpk
{
    protected string $format = "CUSTOM1CONTACTPK";
    public static $HEADER = [
        'aaiabrandid',
        'brand_name',
        'brandowner',
        'product_number',
        'product_description',
        'cost',
        'coreprice',
        'qty_avail',
        'unit_of_measure',
        'weight',
        'length',
        'width',
        'height',
        'map',
        'linecode',
        'wd_min_qty',
        'zipcode',
        'contactpk',
    ];

    public function transformRow($row)
    {
        if (!is_array($row)) {
            $row = $row->toArray();
        }
        $array = [
            $row['aaiabrandid'],
            str_replace(['"',"\n"], '',$row['brand_name']),
            $row['brandowner'],
            $row['product_number'],
            str_replace(['"',"\n"], '', $row['product_description']),
            $row['cost'],
            $row['coreprice'],
            $row['qty_avail'],
            $row['unit_of_measure'],
            $row['weight'],
            $row['length'],
            $row['width'],
            $row['height'],
            $row['map'],
            $row['linecode'],
            $row['wd_min_qty'],
            $row['zipcode'],
            $row['contactpk'],
        ];
        return $array;
    }
}