<?php

namespace <PERSON><PERSON><PERSON>\FBSFeedExporter\Library;


use Illuminate\Support\Facades\Log;
use Sophio\Common\Actions\SystemLogUpdate;
use Sophio\Common\Enums\SystemLogStatus;
use Sophio\Common\Models\FBS\BuyerProfile;
use Sophio\Common\Services\LogTrack;

class DataFeedExportManager
{
    protected $database;
    protected $settings;

    public function __construct($database, $settings = [])
    {
        $this->database = $database;
        $this->settings = $settings;
    }

    public function generateForBuyerProfile($bp)
    {

        $export_class = "Sophio\FBSFeedExporter\Library\DataFeedExport\\" . $bp->export_class;
        Log::error($export_class);
        if (!class_exists($export_class)) {
            (new SystemLogUpdate()) ($this->settings['syslogid'], $export_class. ' not found!', SystemLogStatus::FAIL);
            Log::error($export_class. ' not found!');
            return false;
        }
        $export = new $export_class($bp, $this->settings);
        $export->generateFiles(true);
        return $export;
    }

    public function generateAndSendForBuyerProfile($bp)
    {
        $export_class = "Sophio\FBSFeedExporter\Library\DataFeedExport\\" . $bp->export_class;
        if (!class_exists($export_class)) {
            (new SystemLogUpdate()) ($this->settings['syslogid'], $export_class. ' not found!', SystemLogStatus::FAIL);
            return false;
        }
        $export = new $export_class($bp, $this->settings);

        $export->sendFiles();
    }

    public function generateForAllActive()
    {
        $bps = BuyerProfile::where('active', 1)->get();
        foreach ($bps as $bs) {
            $this->generateForBuyerProfile($bs);
        }
    }
    public function generateAndSendForAllActive()
    {
        $bps = BuyerProfile::where('active', 1)->get();
        foreach ($bps as $bs) {
            $this->generateAndSendForBuyerProfile($bs);
        }
    }
}