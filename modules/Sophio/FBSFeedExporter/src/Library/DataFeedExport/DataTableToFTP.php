<?php

namespace So<PERSON><PERSON>\FBSFeedExporter\Library\DataFeedExport;

use App\Events\AdminQueueMailEvent;
use Illuminate\Support\Carbon;
use Sophio\Common\Actions\SystemLogUpdate;
use Sophio\Common\Enums\SystemLogStatus;
use Sophio\Common\Models\FBS\BuyerProfile;
use Sophio\Common\Services\LogTrack;
use Sophio\FBSFeedExporter\Library\Actions\SendMailToCustomer;
use Sophio\FBSFeedExporter\Library\Actions\SendToFTP;
use Sophio\FBSFeedExporter\Library\Actions\SingleDataFeedFile;


class DataTableToFTP implements DataFeedInterface
{
    protected $buyer_profile;
    protected $model;
    protected $files;
    protected $settings;

    protected $export_type='FTP';
    public function __construct(BuyerProfile $buyer_profile,$settings)
    {
        config(['logging.default'=>'fbsexport']);
        $this->buyer_profile = $buyer_profile;
        $this->settings = $settings;
        $this->settings['min_stock'] = $buyer_profile->min_stock;
        $this->settings['filename'] = 'datafeed_'.$buyer_profile->customer_pk;
    }
    public function generateFiles($sendmail=false)
    {
        LogTrack::add('Customer '.$this->buyer_profile->customer->company,'for_customer.'.$this->buyer_profile->customer->pk);
        LogTrack::add('Data Feed Type: Inventory','for_customer.'.$this->buyer_profile->customer->pk,false);
        $filename = $this->buyer_profile->customer->pk.'/'.    $this->settings['filename'].'_'.Carbon::today()->toDateString().'.'.$this->buyer_profile->file_extension;
        $df_class = "Sophio\FBSFeedExporter\Library\DataRowFormat\\".$this->buyer_profile->dataformat;
        $table_class = $this->buyer_profile->sub_export_class;
        if($table_class ==null or $table_class=="") {
            $table_class = "Sophio\FBSFeedExporter\Library\DataTable\\FBN";
        }else{
            $table_class = "Sophio\FBSFeedExporter\Library\DataTable\\".$table_class;
        }
        $model = (new  $table_class)($this->buyer_profile->customer->xml['ACESPARTSDB'],$this->buyer_profile->customer_pk,$this->buyer_profile->customer->custtype);
        if(isset($this->buyer_profile->customer->xml['DATAFEED_TABLE']) && trim($this->buyer_profile->customer->xml['DATAFEED_TABLE'])!=="") {
            $model =  $model->bind($this->buyer_profile->customer->xml['DATAFEED_TABLE']);
        }

        $this->files[] = (new SingleDataFeedFile($this->settings))($filename,$df_class,$model,$this->buyer_profile->file_extension);
        $total  = $model->count();
        $brands = $model->distinct()->count('aaiabrandid');
        LogTrack::add('Your data feed contains '.$total.' rows from '.$brands.' brands.','for_customer.'.$this->buyer_profile->customer->pk);
        (new SystemLogUpdate()) ($this->settings['syslogid'], implode("\n",LogTrack::get('for_customer.'.$this->buyer_profile->customer->pk)), SystemLogStatus::INPROGRESS);
        if($sendmail===true) {
            $this->sendMailEvent();
        }

    }

    public function getFiles()
    {
        return $this->files;
    }
    public function sendFiles()
    {
        $this->generateFiles();
        $uploaded = (new SendToFTP())(  $this->buyer_profile,$this->files,$this->settings);
        if($uploaded==true) {
            LogTrack::add('Uploaded successfully!','for_customer.'.$this->buyer_profile->customer->pk,false);
            (new SystemLogUpdate()) ($this->settings['syslogid'], 'Uploaded successfully!', SystemLogStatus::INPROGRESS);
            (new SendMailToCustomer())($this->buyer_profile->customer, 'Your ' . $this->buyer_profile->customer->store->STORENAME .
                ' data feed has been created successfully', LogTrack::get('for_customer.'.$this->buyer_profile->customer->pk),config('sophio.admin.mail_senders.exporters'));
        }

    }

    public function sendMailEvent()
    {
        // we might disable sending email .e.g when doing daily export (we send a report)
        if(count($this->settings['emails'])<1)
        {
            return true;
        }
        $download_links = [];
        foreach ($this->files as $f)
        {
            $download_links[] = $f->getUrl();
        }
        event(new AdminQueueMailEvent('QueueJobGeneral', array_values($this->settings['emails']), [
            'subject' => 'Data Feed Export Job on ' . $this->settings['tenant_db'].' for '.$this->buyer_profile->customer->company,
            'job_name' => '',
            'tenant_db' => $this->settings['tenant_db'],
            'description' => 'The following data feed exports have been generated:<br>'.implode('<br>', $download_links)
        ]));
    }
}