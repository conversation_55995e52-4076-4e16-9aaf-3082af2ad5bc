<?php

namespace So<PERSON>o\FBSFeedExporter\Library\DataFeedExport;

use Carbon\Carbon;
use Sophio\Common\Models\FBS\BuyerProfile;
use Sophio\Common\Models\FBS\Supplier;

class HomeDepot  implements DataFeedInterface
{
    protected $buyer_profile;
    protected $model;
    protected $files = [];
    protected $settings = ['generateInventory' => true, 'generateCost' => true, 'fullInventory' => false, 'fullCost' => false];
    protected $mail_lines;
    protected $suppliers;

    public function __construct(BuyerProfile $buyer_profile, $settings = [])
    {
        $this->buyer_profile = $buyer_profile;
        $this->settings = array_merge($this->settings, $settings);
        $this->settings['min_stock'] = $buyer_profile->min_stock;
        $this->settings['folder'] = 'dataexport/'.$this->buyer_profile->customer_pk.'/';
 
    }
    public function generateFiles($sendmail=false)
    {

        if ($this->settings['generateInventory'] == true) {
            $this->generateInventory();
        }
        if ($this->settings['generateCost'] == true) {
            $today = Carbon::today();
            if($sendmail==true || $today->day==1)
            {
                $this->generateCost();
            }
        }
    }
    public function generateInventory()
    {
        $inventory_feed = new \Sophio\HomeDepot\Library\Feeds\InventoryFeeds( $this->settings );
        $inventory_feed->createFile();
        $inventory_feed->sendMailEvent();
    }
    public function generateCost(){
        $cost_feed = new \Sophio\HomeDepot\Library\Feeds\CostFeeds($this->settings);
        $cost_feed->createFile();
        $cost_feed->sendMailEvent();
    }
    public function getFiles()
    {
        return $this->files;
    }
    public function sendFiles()
    {
        $this->generateInventory();

        $today = Carbon::today();
        if($today->day==1) {
            $this->generateCost();
            // do sending
        }

    }
}