<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default Log Channel
    |--------------------------------------------------------------------------
    |
    | This option defines the default log channel that gets used when writing
    | messages to the logs. The name specified in this option should match
    | one of the channels defined in the "channels" configuration array.
    |
    */
    'default' => env('LOG_CHANNEL', 'stack'),
    /*
    |--------------------------------------------------------------------------
    | Log Channels
    |--------------------------------------------------------------------------
    |
    | Here you may configure the log channels for your application. Out of
    | the box, Laravel uses the Monolog PHP logging library. This gives
    | you a variety of powerful log handlers / formatters to utilize.
    |
    | Available Drivers: "single", "daily", "slack", "syslog",
    |                    "errorlog", "custom", "stack"
    |
    */
    'channels' => [
        'stack' => [
            'driver'   => 'stack',
            'channels' => ['info'],
        ],
        'info' => [
            'driver' => 'single',
            'path'   => storage_path('logs/laravel.log'),
            'level'  => 'info',
            'bubble'=> false
        ],
        /*
        'warning' => [
            'driver' => 'single',
            'path'   => storage_path('logs/laravel_warning.log'),
            'level'  => 'warning',
            'bubble'=> false
        ],
        'error' => [
            'driver' => 'single',
            'path'   => storage_path('logs/laravel_error.log'),
            'level'  => 'error',
            'bubble'=> false
        ],
        */
        'queries' => [
            'driver' => 'single',
            'path'   => storage_path('logs/queries.log'),
            'level'  => 'info',
        ],
        'walmart' => [
            'driver' => 'single',
            'path'   => storage_path('logs/walmart.log'),
            'level'  => 'info',
        ],
        'homedepot' => [
            'driver' => 'single',
            'path'   => storage_path('logs/homedepot.log'),
            'level'  => 'info',
        ],
        'mail' => [
            'driver' => 'single',
            'path'   => storage_path('logs/mail.log'),
            'level'  => 'info',
        ],
        'sparklink' => [
            'driver' => 'single',
            'path'   => storage_path('logs/sparklink.log'),
            'level'  => 'info',
        ],
        'catlink' => [
            'driver' => 'single',
            'path'   => storage_path('logs/catlink.log'),
            'level'  => 'info',
        ],
        'orderlink' => [
            'driver' => 'single',
            'path'   => storage_path('logs/orderlink.log'),
            'level'  => 'info',
        ],

        'shipstation' => [
            'driver' => 'single',
            'path'   => storage_path('logs/shipstation.log'),
            'level'  => 'info',
        ],
        'commercehub' => [
            'driver' => 'single',
            'path'   => storage_path('logs/commercehub.log'),
            'level'  => 'info',
        ],
        'corcentric' => [
            'driver' => 'single',
            'path'   => storage_path('logs/corcentric.log'),
            'level'  => 'info',
        ],
        'amazon' => [
            'driver' => 'single',
            'path'   => storage_path('logs/amazon.log'),
            'level'  => 'info',
        ],
        'tractor' => [
            'driver' => 'single',
            'path'   => storage_path('logs/tractor.log'),
            'level'  => 'info',
        ],
        'zoro' => [
            'driver' => 'single',
            'path'   => storage_path('logs/zoro.log'),
            'level'  => 'info',
        ],
        'fbsimport' => [
            'driver' => 'single',
            'path'   => storage_path('logs/fbsimport.log'),
            'level'  => 'info',
        ],
        'fbsorder' => [
            'driver' => 'single',
            'path'   => storage_path('logs/fbsorder.log'),
            'level'  => 'info',
        ],
        'federatedlink' => [
            'driver' => 'single',
            'path'   => storage_path('logs/federatedlink.log'),
            'level'  => 'info',
        ],
        'fbsexport' => [
            'driver' => 'single',
            'path'   => storage_path('logs/fbsexport.log'),
            'level'  => 'info',
        ],
        'fbsstatement' => [
            'driver' => 'single',
            'path'   => storage_path('logs/fbsstatement.log'),
            'level'  => 'info',
        ],
        'products' => [
            'driver' => 'single',
            'path'   => storage_path('logs/products.log'),
            'level'  => 'info',
        ],
        'imageprocessing' => [
            'driver' => 'single',
            'path'   => storage_path('logs/imageprocessing.log'),
            'level'  => 'info',
        ],
        'daily' => [
            'driver' => 'daily',
            'path'   => storage_path('logs/laravel.log'),
            'level'  => 'error',
            'days'   => 7,
        ],
        'slack' => [
            'driver'   => 'slack',
            'url'      => env('LOG_SLACK_WEBHOOK_URL'),
            'username' => 'Laravel Log',
            'emoji'    => ':boom:',
            'level'    => 'critical',
        ],
        'syslog' => [
            'driver' => 'syslog',
            'level'  => 'error',
        ],
        'errorlog' => [
            'driver' => 'errorlog',
            'level'  => 'error',
        ],
        'stripe' => [
            'driver' => 'single',
            'path'   => storage_path('logs/stripe.log'),
            'level'  => 'info',
        ],
    ],
];
