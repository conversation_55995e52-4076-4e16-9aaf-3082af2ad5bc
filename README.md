## About Sophio Admin

Current Admin runs on Laravel 9.x

## Requirements

* PHP 8.1
* Nginx
* Redis
* MariaDB 10.6

If you can use a different MySQl flavour or PHP 8.1, but don't introduce anything that is not compatible with these versions.

## PHP

``apt install php8.1-fpm php8.1-gd php8.1-redis php8.1-xml php8.1-intl php8.1-mysql php8.1-curl php8.1-soap php8.1-zip php8.1-mbstring``

``apt install redis imagemagick php8.1-imagick composer mariadb-client``

## NGINX

No special requirements, use a classic nginx config for a Laravel app.

The ``APP_ENV``, ``APP_DEBUG`` and ``APP_URL``  in `.env` file should be set according to the website domain and production mode.

## Install

``mkdir /var/www/fbsnew``

``cd /var/www/fbsnew``

`` <NAME_EMAIL>:sophio/sophioadmin.git .``

### Install modules

``cd /var/www/fbsnew/modules/Sophio``

#### Install Common module (mandatory)

This module is mandatory to be present before running `composer update`.

``<NAME_EMAIL>:sophio/common.git Common``

#### Install Orders module (optional)

Optional module for orders management. 

``<NAME_EMAIL>:sophio/fbsorder.git FBSOrder``

#### Install Returns module (optional)

Optional module for returns management.

``<NAME_EMAIL>:sophio/fbsreturns.git FBSReturns``

#### Install FBS Inventory import module (optional)

Optional module for inventory imports.

``<NAME_EMAIL>:sophio/fbsinventoryimporter.git FBSInventoryImporter``

#### Install X12 module (optional)

Optional module EDIX12 -   this is not yet in use, you can skip it

``<NAME_EMAIL>:sophio/edix12.git EDIX12``

#### Install uhin module (optional)

Optional module for orders management.

``git clone https://github.com/UHIN/x12-parser-library.git modules/uhin``


### Install dependencies:

``cd /var/www/fbsnew``

Copy the `auth.json` for getting access to Backpack repo. There are several repositories directly from github.


Copy `.env.example` to `.env` (or your `.env.local`), edit DB credentials , APP_URL.
The APP_DOMAIN is right now only used for the hosted FTP for users.

Run composer:

``composer install``

Make sure ``storage`` folder is writable by the nginx user (usually ``www-data``).


Create  folder ``public/storage`` and symlink here ``public/basset`` then run

``php artisan vendor:publish --provider="Backpack\Basset\BassetServiceProvider"``

``php artisan optimize:clear``

and

``php artisan basset:clear``

